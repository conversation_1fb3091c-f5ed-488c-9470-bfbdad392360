{"compilerOptions": {"baseUrl": ".", "module": "ESNext", "target": "es2016", "lib": ["DOM", "ESNext"], "strict": true, "esModuleInterop": true, "incremental": false, "skipLibCheck": true, "moduleResolution": "node", "resolveJsonModule": true, "noUnusedLocals": false, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "paths": {"@/*": ["./src/*"]}}, "vueCompilerOptions": {"experimentalDisableTemplateSupport": true}, "include": ["src/**/*", "types/**/*"], "exclude": ["dist", "node_modules"]}