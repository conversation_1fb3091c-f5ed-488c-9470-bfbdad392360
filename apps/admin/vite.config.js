import { defineConfig, loadEnv } from 'vite'
import { viteMockServe } from 'vite-plugin-mock'
import vue from '@vitejs/plugin-vue'
import { createHtmlPlugin } from 'vite-plugin-html'
import components from 'unplugin-vue-components/vite'
import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers'
import autoImport from 'unplugin-auto-import/vite'
import viteSvgIcons from 'vite-plugin-svg-icons'
import { resolve } from 'path'
import glob from 'glob'

const modules = glob.sync('./src/modules/*')
const config = defineConfig({
  plugins: [
    vue(),
    autoImport({
      // 依赖自动加载
      imports: ['vue', 'vue-router']
    }),
    components({
      extensions: ['vue'],
      // resolvers: [
      //   // antd 按需自动加载
      //   AntDesignVueResolver()
      // ],
      include: [/\.vue$/, /\.vue\?vue/]
    }),
    viteSvgIcons({
      iconDirs: modules.map(m => resolve(__dirname, `${m}/assets/svg-icons`)),
      symbolId: '[name]'
    }),
    viteMockServe({
      mockPath: './mock', //mock文件地址
      supportTs: false //打开后，可以读取 ts 文件模块。 请注意，打开后将无法监视.js 文件
    })
  ],
  server: {
    host: '0.0.0.0',
    port: 8080,
    fs: {
      strict: false
    },
    // proxy: {
    //   '/api': {
    //     target: 'https://ferrero-user-center.cs-dev.xcxd.net.cn',
    //     changeOrigin: true,
    //     rewrite: path => path.replace(/^\/api/, '')
    //   }
    // }
  },
  resolve: {
    // 路径别名
    alias: {
      '@': resolve(__dirname, './src'),
      '~': resolve(__dirname, './src/modules')
    },
    extensions: ['.js', '.vue']
  },
  css: {
    // 配置预编译器
    preprocessorOptions: {
      less: {
        additionalData: `@import "@/styles/variables.less";`
      }
    }
  },
  build: {
    sourcemap: false,
    rollupOptions: {
      output: {
        manualChunks: {
          'ant-design-vue': ['ant-design-vue'],
          'ant-design-vue-icon': ['@ant-design/icons-vue']
        }
      }
    },
    chunkSizeWarningLimit: 600,
    commonjsOptions: {
      ignoreTryCatch: false
    }
  }
})
export default ({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  const htmlPlugin = createHtmlPlugin({
    inject: {
      data: {
        title: env.VITE_ADMIN_TITLE
      }
    }
  })
  config.plugins.push(htmlPlugin)
  return config
}
