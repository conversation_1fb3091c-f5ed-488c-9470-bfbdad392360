<template>
  <div v-if="visible">
    <div class="ant-modal-root">
      <div class="ant-modal-mask"></div>
      <div class="ant-modal-wrap">
        <div class="ant-modal" style="width: 520px; transform-origin: 1373px 259px">
          <div class="ant-modal-content">
            <button type="button" class="ant-modal-close" @click="close">
              <span class="ant-modal-close-x">
                <span role="img" class="anticon anticon-close ant-modal-close-icon">
                  <svg
                    focusable="false"
                    class=""
                    data-icon="close"
                    width="1em"
                    height="1em"
                    fill="currentColor"
                    fill-rule="evenodd"
                    viewBox="64 64 896 896"
                  >
                    <path
                      d="M799.86 166.31c.02 0 .04.02.08.06l57.69 57.7c.04.03.05.05.06.08a.12.12 0 010 .06c0 .03-.02.05-.06.09L569.93 512l287.7 287.7c.04.04.05.06.06.09a.12.12 0 010 .07c0 .02-.02.04-.06.08l-57.7 57.69c-.03.04-.05.05-.07.06a.12.12 0 01-.07 0c-.03 0-.05-.02-.09-.06L512 569.93l-287.7 287.7c-.04.04-.06.05-.09.06a.12.12 0 01-.07 0c-.02 0-.04-.02-.08-.06l-57.69-57.7c-.04-.03-.05-.05-.06-.07a.12.12 0 010-.07c0-.03.02-.05.06-.09L454.07 512l-287.7-287.7c-.04-.04-.05-.06-.06-.09a.12.12 0 010-.07c0-.02.02-.04.06-.08l57.7-57.69c.03-.04.05-.05.07-.06a.12.12 0 01.07 0c.03 0 .05.02.09.06L512 454.07l287.7-287.7c.04-.04.06-.05.09-.06a.12.12 0 01.07 0z"
                    />
                  </svg>
                </span>
              </span>
            </button>
            <div class="ant-modal-header">
              <div class="ant-modal-title">投放</div>
            </div>
            <div class="ant-modal-body">
              <div class="flex flex-dc">
                <img :src="getDaisy" />
                <button class="ant-btn ant-btn-link link" type="button" @click="copyLink(link)">
                  <span>复制地址</span>
                </button>
                <button class="ant-btn ant-btn-link link" type="button" @click="downloadIamge(getDaisy)">
                  <span>下载小程序码</span>
                </button>
                <button class="ant-btn ant-btn-link link" type="button" @click="getUrlLink">
                  <span>生成URL Link</span>
                </button>
                <button v-if="urlLink" class="ant-btn ant-btn-link link" type="button" @click="copyLink(urlLink)">
                  <span style="margin-right: 10px">{{ urlLink }}</span>
                  <uc-ant-icon name="CopyOutlined" type="info" />
                </button>
              </div>
            </div>
            <div class="ant-modal-footer">
              <div>
                <button class="ant-btn" type="button" @click="close"><span>取 消</span></button>
                <button class="ant-btn ant-btn-primary" type="button" @click="close"><span>确 定</span></button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Clipboard from 'clipboard'
import { message } from 'ant-design-vue'
import { downloadIamge } from '@/utils/functions'
import { getUrlLinkApi } from './api'

const visible = ref(false)
const link = ref('')
const urlLink = ref('')

const getDaisy = computed(() => {
  let [page, scene] = link.value.split('?')
  if(page && page[0] === '/') page = page.substring(1)
  scene = scene ? scene.split('&').map(a => a.split('=').join(':')).join('#') : 1
  return `${import.meta.env.VITE_API_BASE_URL}/image/get-daisy?page=${page}&scene=${encodeURIComponent(scene)}`
})

const show = async value => {
  visible.value = true
  link.value = value
}

watch(link, () => {
  urlLink.value = ''
})

const close = () => {
  visible.value = false
}

const copyLink = text => {
  const el = document.createElement('div')
  el.setAttribute('data-clipboard-text', text)
  el.style.display = 'none'
  document.body.appendChild(el)
  const clipboard = new Clipboard(el)
  clipboard.on('success', () => {
    clipboard.destroy()
    message.success(`复制成功：${text}`)
  })
  clipboard.on('error', () => {
    clipboard.destroy()
    message.error('复制失败')
  })
  el.dispatchEvent(new Event('click'))
}

const getUrlLink = async () => {
  let [path, query] = link.value.split('?')
  if (path && path[0] !== '/') path = '/' + path
  urlLink.value = await getUrlLinkApi.get({ path, query })
}

defineExpose({
  show
})
</script>
