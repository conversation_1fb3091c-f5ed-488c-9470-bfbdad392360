import { message } from 'ant-design-vue'
import JsEncrypt from 'jsencrypt'

// 生成随机数
export const generateRandom = () => `${+new Date()}${Math.random().toString().slice(2, 6)}`

//递归迭代
export const deepLoopHandle = (list, callBack) => {
  list.forEach(item => {
    callBack && callBack(item)
    item.children && item.children.length > 0 && deepLoopHandle(item.children, callBack)
  })
}
// 清除对象值为空的属性
export function filterParams(obj) {
  var _newPar = {}
  for (var key in obj) {
    if (
      (obj[key] === 0 || obj[key] === false || obj[key]) &&
      obj[key].toString().replace(/(^\s*)|(\s*$)/g, '') !== ''
    ) {
      _newPar[key] = obj[key]
    }
  }
  return _newPar
}

// label颜色 图文组件
export function labelColor(label) {
  switch (label) {
    case '新品':
      return '#1890FF'
    case '特惠':
      return '#F5222D'
    case '推荐':
      return '#52C41A'
  }
}
// 号码加密
export function numberEncryption(val) {
  let tel = val
  tel = '' + tel
  const newTel = tel.slice(0, 3) + '****' + tel.slice(7)
  return newTel
}

// 必填检验
const requireKey = (isrequire, data) => {
  //是否为必填
  if (isrequire) {
    // 类型是否为引用类型 [] {}
    if (Object.prototype.isPrototypeOf(data)) {
      if (Array.prototype.isPrototypeOf(data)) {
        if (data.length === 0) return true
      } else {
        if (Object.keys(data).length === 0) return true
      }
    } else {
      // 参数是'' null undefined
      if (!data) return true
    }
  }
}
// 错误处理
const errDispose = (result, content) => {
  result = false
  message.error(content)
}

// form表单验证--{require:true,message:"xxx",value:xxx,validate(){}}
export function formRule(ruleArr = [{ require: true, message: 'xxx', value: '', validate() {} }]) {
  let result = true
  for (const item of ruleArr) {
    // 存在自定义验证函数
    if (item.validate) {
      // 自定义验证规则是否通过
      let isValidate = true
      item.validate(item.value, type => {
        if (type == 'error') {
          isValidate = false
          errDispose(result, item.message)
          return
        }
      })
      // 自定义验证规则不通过,结束循环
      if (!isValidate) {
        return
      }
      // 必填
      if (requireKey(item.require, item.value)) {
        errDispose(result, item.message)
        return
      }
    } else {
      // 必填
      if (requireKey(item.require, item.value)) {
        errDispose(result, item.message)
        return
      }
    }
  }
  return result
}

// 判断活动的状态 label颜色
export function activeState(label) {
  if (label == 'not_start') {
    return {
      label: '未开始',
      color: '#1890FF'
    }
  } else if (label == 'normal') {
    return {
      label: '进行中',
      color: '#52C41A'
    }
  } else {
    return {
      label: '已结束',
      color: '#d9d9d9'
    }
  }
}

/**
 * 构造树型结构数据
 * @param {*} data 数据源
 * @param {*} id id字段 默认 'id'
 * @param {*} parentId 父节点字段 默认 'parentId'
 * @param {*} children 孩子节点字段 默认 'children'
 */
export function handleTree(data, id, parentId, children) {
  let config = {
    id: id || 'id',
    parentId: parentId || 'parentId',
    childrenList: children || 'children'
  };

  var childrenListMap = {};
  var nodeIds = {};
  var tree = [];

  for (let d of data) {
    let parentId = d[config.parentId];
    if (childrenListMap[parentId] == null) {
      childrenListMap[parentId] = [];
    }
    nodeIds[d[config.id]] = d;
    childrenListMap[parentId].push(d);
  }

  for (let d of data) {
    let parentId = d[config.parentId];
    if (nodeIds[parentId] == null) {
      tree.push(d);
    }
  }

  for (let t of tree) {
    adaptToChildrenList(t);
  }

  function adaptToChildrenList(o) {
    if (childrenListMap[o[config.id]] !== null) {
      o[config.childrenList] = childrenListMap[o[config.id]];
    }
    if (o[config.childrenList]) {
      for (let c of o[config.childrenList]) {
        adaptToChildrenList(c);
      }
    }
  }
  return tree;
}

/**
 * 获取树指定节点的祖先label集合
 * @param {Array} treeData 树数据
 * @param {String/Number} id 要指定的子节点的id
 * @param {String} label 要获取的节点属性
 * @returns 获取树指定节点的祖先们id数组
 */
export const getParentLabelList = (treeData, id, label = 'title') => {
  if (!treeData || !id) {
    return ''
  }
  const arr = []
  const findParent = (data, nodeId, parentId = '') => {
    for (var i = 0, length = data.length; i < length; i++) {
      let node = data[i]
      if (node.id === nodeId) {
        arr.unshift(node[label])
        if (nodeId === treeData[0].id) {
          break
        }
        findParent(treeData, parentId)
        break
      } else {
        if (node.children?.length) {
          findParent(node.children, nodeId, node.id)
        }
        continue
      }
    }
    return arr
  }
  return findParent(treeData, id)
}

/**
 * 累计树的子节点的数据到树的根节点上
 * @param {Array} node 根节点
 * @param {String} attr 要累计数据的属性
 */
export function sumAttr(node, attr) {
  // 递归遍历子节点
  function traverse(node) {
    let sum = 0
    // 如果节点有 attr 属性，累加到 sum
    if (node[attr]) {
      sum += node[attr]
    }

    // 如果节点有子节点，继续遍历
    if (node.children && node.children.length > 0) {
      for (let child of node.children) {
        sum += traverse(child)
      }
    }

    node[attr] = sum

    return sum
  }

  node[attr] = traverse(node)
}

/**
 * 下载图片
 * @param {String} imgsrc 图片地址
 * @param {String} name 图片名
 */
export const downloadIamge = (imgsrc, name) => {
  let image = new Image()
  // 解决跨域 Canvas 污染问题
  image.setAttribute('crossOrigin', 'anonymous')
  image.onload = function () {
    let canvas = document.createElement('canvas')
    canvas.width = image.width
    canvas.height = image.height
    let context = canvas.getContext('2d')
    context.drawImage(image, 0, 0, image.width, image.height)
    let url = canvas.toDataURL('image/png') //得到图片的base64编码数据

    let a = document.createElement('a') // 生成一个a元素
    let event = new MouseEvent('click') // 创建一个单击事件
    a.download = name || 'photo' // 设置图片名称
    a.href = url // 将生成的URL设置为a.href属性
    a.dispatchEvent(event) // 触发a的单击事件
  }
  image.src = imgsrc
}

export const encrypt = (value, publicKey) => {
  const encrypter = new JsEncrypt()
  encrypter.setPublicKey(publicKey)
  return encrypter.encrypt(value)
}

export const uuid = () => {
  return 'xxxxxxxx'.replace(/[xy]/g, function (c) {
    var r = (Math.random() * 16) | 0,
      v = c == 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}