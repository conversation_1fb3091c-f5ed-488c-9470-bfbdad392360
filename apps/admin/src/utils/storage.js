/**
 * @file 缓存文件
 */
/* localStorage start */
// 移除 local 缓存
const removeLocalStorage = key => {
  localStorage.removeItem(`$${key}`)
}
// 设置 local 缓存
const setLocalStorage = (key, value) => {
  removeLocalStorage(key)
  localStorage.setItem(`$${key}`, JSON.stringify({ value: value ?? '' }))
}
// 获取 local 缓存
const getLocalStorage = key => JSON.parse(localStorage.getItem(`$${key}`))?.value ?? ''
/* localStorage end */

/* sessionStorage start */
// 移除 session 缓存
const removeSessionStorage = key => {
  sessionStorage.removeItem(`$${key}`)
}
// 设置 session 缓存
const setSessionStorage = (key, value) => {
  removeSessionStorage(key)
  sessionStorage.setItem(`$${key}`, JSON.stringify({ value: value ?? '' }))
}
// 获取 session 缓存
const getSessionStorage = key => JSON.parse(sessionStorage.getItem(`$${key}`))?.value ?? ''
/* sessionStorage end */

/**
 * 移除缓存
 * @param {string} key 键值
 * @param {unknown} value 存储值
 * @param {string} [type=local] 类型: session | local
 */
// 设置缓存
export const setStorage = (key, value, type = 'local') => {
  if (type === 'session') {
    setSessionStorage(key, value)
  } else {
    setLocalStorage(key, value)
  }
}
/**
 * 获取缓存
 * @param {string} key 键值
 * @param {string} [type=session] 类型: session | local
 */
// 获取缓存
export const getStorage = (key, type = 'local') => (type === 'session' ? getSessionStorage(key) : getLocalStorage(key))
/**
 * 移除缓存
 * @param {string} key 键值
 * @param {string} [type=session] 类型: session | local
 */
// 移除缓存
export const removeStorage = (key, type = 'local') => {
  if (type === 'session') {
    removeSessionStorage(key)
  } else {
    removeLocalStorage(key)
  }
}
