import axios from 'axios'
import { message } from 'ant-design-vue'
import { useStore } from '@/store/auth'
import router from '@/router'
import { isObject, values, flattenDeep, isString } from 'lodash'

const { clearToken } = useStore()

// 创建 axios 实例
const service = axios.create({
  timeout: 60000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
})

// request 请求拦截器
service.interceptors.request.use(
  (config) => {
    const { state } = useStore()
    state.token && (config.headers['Authorization'] = `Bearer ${state.token}`) // header 添加 token
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)
// respone 响应拦截器
service.interceptors.response.use(
  ({ data }) => {
    return data?.data || data
  },
  ({ response, message: msg }) => {
    switch (response.status) {
      case 401:
        clearToken()
        router.push({ name: 'auth-login' })
        return Promise.reject('auth-login')
      case 403:
        router.push('/403')
        return Promise.reject(response.status)
      case 404:
        message.error('请求路径错误')
        return Promise.reject(response.status)
      case 409:
        message.error('操作失败：提交了重复的内容')
        return Promise.reject(response.status)
      case 500:
        message.error('服务器发生了一个错误，请稍后再试')
        return Promise.reject(response.status)
    }

    let errorMessage = null
    if (isObject(response.data?.tips)) {
      errorMessage = flattenDeep(values(response.data?.tips)).join('，')
    } else {
      errorMessage = response.data?.tips
        ? response.data?.tips.toString()
        : response.data?.message || msg || '发生了一个错误，请稍后再试'
    }
    message.error(errorMessage)
    return Promise.reject(errorMessage)
  }
)

export default service
