export default {
  // 正整数
  number: v => (v * 1 ? parseInt(v) : undefined),
  // 自然数
  naturalNumber: v => (isNaN(parseInt(v)) ? undefined : parseInt(v)),
  // 千分符
  thousandSeparator: (v, dot = true, compute = true) => {
    if (v === undefined) return
    if (compute) v = v / 100
    let str = `${v}`
    let floatStr = `${v}`
    const hasDot = str.includes('.')
    const regexp = hasDot ? /(\d)(?=(\d{3})+\.)/g : /(\d)(?=(\d{3})+$)/g
    str = str.replace(regexp, '$1,')
    !hasDot && dot && (str += '.00')
    return str
  },
  // 数组字符串拼接
  arrJoinStr: (arr, attr, separator = ', ') =>
    arr?.reduce((prev, item, i) => prev + (i === 0 || !item[attr] ? '' : separator) + item[attr], '') ?? '',
  // 号码加密
  numberEncryption: v => {
    v = `${v}`
    if (!v || v.length != 11) return v
    const reg = /(\d{3})\d*(\d{4})/
    return v.replace(reg, '$1****$2')
  },
  // 替换中文，可包含数字/字母/特殊符号
  replaceChinese: v => v.replace(/[\u4E00-\u9FA5]/g, ''),
  // 活动结束时间 空状态
  transformActivityEndTime: v => v || '长期',
  // 手机号码 空状态
  transformPhone: v => v || '100****0000',
  // 价格金额转整数
  priceToInteger: v => Math.round(v * 100),
  //只能输入数字或字母
  numberOrLetter: v => v.replace(/[^\w\.\/]/gi, ''),
  // 保留两位小数并且转化为%
  toPercent: v => Math.round(+v * 100) + '%',
  // 百分比计算
  percentCalc: (a, b) => {
    const numa = Number(a)
    const numb = Number(b)
    if(numb == 0) return '∞'
    return (numa / numb * 100).toFixed(2) + '%'
  },
  encrypt(v) {
    return v.replace(/(\d{3})\d+(\d{4})/, '$1****$2')
  }
}
