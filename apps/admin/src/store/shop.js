/**
 * @file 店铺信息
 */
import { reactive, readonly } from 'vue'
import { setStorage, getStorage, removeStorage } from '@/utils/storage'

// 初始 store
export const useShop = () => {
  let state = reactive({})
  
  // 是否存在店铺信息
  const shop = getStorage('shop')
  shop && (state = shop)

  // 设置 shop
  const setShop = (shopConfig) => {
    state = shopConfig
    
    // 设置缓存
    setStorage('shop', state)
  }

  // 获得 shop
  const getShop = () => {
    return getStorage('shop')
  }
  // 清理 shop
  const clearShop = () => {
    state = reactive({})
    removeStorage('shop')
  }
  return { state: readonly(state), setShop,getShop, clearShop }
}
