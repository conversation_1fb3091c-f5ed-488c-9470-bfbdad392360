/**
 * @file 权限仓库
 */
import { reactive, readonly } from 'vue'
import { setStorage, getStorage, removeStorage } from '@/utils/storage'
// 初始 store
export const useStore = () => {
  const state = reactive({ token: '', userInfo: '' })
  // 是否存在 token
  const token = getStorage('token')
  token && (state.token = token)
  // 是否存在 userInfo
  const userInfo = getStorage('userInfo')
  userInfo && (state.userInfo = userInfo)
  // 设置 token
  const setToken = (value) => {
    state.token = value
    // 设置缓存
    setStorage('token', value)
  }
  // 清理 token
  const clearToken = () => {
    state.token = ''
    removeStorage('token')
  }
  // 设置userInfo
  const setUserInfo = (value) => {
    state.userInfo = value
    // 设置缓存
    setStorage('userInfo', value)
  }

  // 获取userInfo
  const getUserInfo = () => {
    return getStorage('userInfo')
  }
  return { state: readonly(state), setToken, clearToken, setUserInfo, getUserInfo }
}
