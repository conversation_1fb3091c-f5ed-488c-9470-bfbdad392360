import mpRoute from '@/enums/mp-route'
import config from '@/config'
import { useStore } from '@/store/auth'
import qs from 'qs'

const { state: storeInfo } = useStore()
export default {
  data() {
    return {
      layoutLabelCol: { span: 3 },
      layoutWrapperCol: { span: 21 },
      modalLabelCol: { span: 5 },
      modalWrapperCol: { span: 19 },
      inputPriceAttrs: {
        formatter: value => {
          value = `${value}`
          if (!value) return ''
          return parseFloat(value) / 100 + (value.at(-1) === '.' ? '.' : '')
        },
        parser: value => {
          value = `${value}`
          let newValue = value.match(/^\d+(\.\d*)?/)
          if (!newValue) return ''
          newValue = newValue[0]

          return Math.round(newValue * 100) + (newValue.at(-1) === '.' ? '.' : '')
        }
      }
    }
  },
  methods: {
    // 链接复制
    copyLinkByRoute(name, params = {}) {
      const text = mpRoute[name].path
      this.copyLink(text, params)
    },
    // 链接复制
    copyLink(text, params = {}) {
      this.$qrcode.show(this.pathAssemble(text, params))
    },
    pathAssemble(path, params = {}) {
      if (Object.keys(params).length === 0) {
        return path
      }

      let url = path

      if (url.includes('?')) {
        url += '&'
      } else {
        url += '?'
      }

      for (let key in params) {
        url += key + '=' + params[key] + '&'
      }

      return url.slice(0, -1)
    },
    // 设置分页
    stdPagination({ current_page, limit, total, items = [] } = {}) {
      const pagination = {
        showSizeChanger: true,
        showQuickJumper: true,
        pageSizeOptions: ['10', '20', '30', '40', '50', '100'],
        showTotal: total => `共${total}条`
      }
      Object.assign(pagination, {
        current: current_page * 1,
        pageSize: limit * 1,
        total: total || items.length || 0
      })
      return pagination
    },
    // 设置分页
    stdPaginationSimple({ current_page, limit, total, items = [] } = {}) {
      const pagination = {
        current: current_page * 1,
        pageSize: limit * 1,
        total: total || items.length || 0
      }
      return pagination
    },
    // 下载（导出）文件
    exportFile(path, query = {}) {
      if (query) {
        const needStringifyKeyArray = ['filters', 'relation_filters']
        const needJoinKeyArray = ['relations', 'sorts']
        needStringifyKeyArray.forEach(key => {
          if (query[key]) {
            query[key] = JSON.stringify(query[key])
          }
        })
        needJoinKeyArray.forEach(key => {
          if (query[key]) {
            query[key] = query[key].join(',')
          }
        })
      }
      const queryString = query ? `&${qs.stringify(query)}` : ''
      const url = `${config.api.baseURL}${path}?token=${storeInfo.token}${queryString}`

      let a = document.createElement('a')
      a.href = url
      a.click()
    }
  }
}
