<template>
  <!-- 面包屑导航 -->
  <a-breadcrumb class="m-bread-crumb">
    <a-breadcrumb-item>
      <router-link to="/">
        {{ shopInfo.shop_name }}
      </router-link>
    </a-breadcrumb-item>
    <a-breadcrumb-item v-for="item in crumbList" :key="item.path">
      {{ item.meta.title }}
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>
<script>
export default {
  name: 'BreadCrumb'
}
</script>
<script setup>
import { computed, ref } from 'vue'
import { useRoute, onBeforeRouteUpdate } from 'vue-router'
import { useShop } from '@/store/shop'

const { getShop } = useShop()
let shopInfo = ref(getShop())

const routes = ref(useRoute())
onBeforeRouteUpdate((to) => {
  shopInfo.value = getShop()
  routes.value = to
})
const crumbList = computed(() => routes.value.matched.filter((item) => item.path !== '/'))
</script>
<style scoped lang="less">
.m-bread-crumb {
  a {
    display: inline;
  }
  :deep(span:last-child > .ant-breadcrumb-link) {
    color: rgba(0, 0, 0, 0.65);
  }
}
</style>
