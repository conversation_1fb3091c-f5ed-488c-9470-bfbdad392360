<template>
  <a-modal 
    v-model:visible="visibleDialog" 
    :title="title"
    :mask-closable="!force"
    :closable="!force"
    :cancel-button-props="{disabled: force}"
    @ok="handleSubmit"
  >
    <a-form>
      <a-form-item v-if="needUsername" label="登录账号" required>
        <a-input v-model:value.trim="formState.username" placeholder="请输入登录账号" />
      </a-form-item>
      <a-form-item label="原始密码" required>
        <a-input v-model:value.trim="formState.old_password" placeholder="请输入原始密码" />
      </a-form-item>
      <a-form-item label="新设密码" required>
        <a-input-password v-model:value.trime="formState.password" :type="passwordType" placeholder="请输入新设密码" />
      </a-form-item>
      <a-form-item label="确认密码" required>
        <a-input-password
          v-model:value.trime="formState.password_again"
          :type="passwordType"
          placeholder="请再次输入新设密码"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { watch } from 'vue'
import { message } from 'ant-design-vue'
import { editPasswordApi, forceEditPasswordApi } from '@/modules/auth/api'
import { loginApi } from '@/modules/auth/api'
import { useFormState } from '@/composables/useFormState'
import { encrypt } from '@/utils/functions.js'
import { cloneDeep } from 'lodash'
import { Base64 } from 'js-base64'

const visibleDialog = defineModel('visible');

const props = defineProps({
  force: { // 强制修改
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '修改密码'
  },
  publicKey: {
    type: String,
    default: ''
  },
  needUsername: { // 是否需要填写用户账号
    type: Boolean,
    default: false
  }
})

const { formState, resetFormState, setFormRules, validateForm } = useFormState({
  username: '', // 用户名
  old_password: '', // 原始密码
  password: '', // 新设密码
  password_again: '' // 确认密码
})

const passwordValidator = (rule, value) => {
  const preg = new RegExp(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[.@#$%^&+=!])/)

  if (!value) {
    return Promise.reject('请输入新设密码')
  } else if(value === formState.value.old_password) {
    return Promise.reject('密码未改变')
  } else if(value.length < 8) {
    return Promise.reject('新设密码长度必须大于等于8位')
  } else if(preg.test(value) === false) {
    return Promise.reject('新设密码必须包含大小写字母，数字，和特殊字符（.@#$%^&+=!）')
  }

  return Promise.resolve(true)
}

const passwordAgainValidator = (rule, value) => {
  if(!value) {
    return Promise.reject('请再次输入新设密码')
  } else if(value !== formState.value.password) {
    return Promise.reject('两次输入的密码不一致')
  }
  return Promise.resolve(true)
}

const formRules = {}

if(props.needUsername) {
  formRules.username = {required: true, message: '请输入登录账号'}
}

setFormRules(Object.assign(formRules, {
  old_password: { required: true, message: '请输入原始密码' },
  password: { validator: passwordValidator },
  password_again: { validator: passwordAgainValidator }
}))

const encryptPublicKey = ref('')

watch(() => visibleDialog.value, (val) => {
  if(val){
    resetFormState()
    if(props.publicKey === '') {
      getPublicKey()
    } else {
      encryptPublicKey.value = props.publicKey
    }
  }
}, {
  immediate: true
})

const getPublicKey = () => {
  loginApi
  .get()
  .then((data) => {
    encryptPublicKey.value = Base64.decode(data)
  })
}


const handleSubmit = async () => {
  if (!(await validateForm()) || !visibleDialog.value) return

    // 加密
  //进行AES加密
  const loginForm = cloneDeep(formState.value)
  loginForm.old_password = encrypt(loginForm.old_password, encryptPublicKey.value)
  loginForm.password = encrypt(loginForm.password, encryptPublicKey.value)
  loginForm.password_again = encrypt(loginForm.password_again, encryptPublicKey.value)

  if(props.force) {
    loginForm.username = encrypt(loginForm.username, encryptPublicKey.value)
    await forceEditPasswordApi.post(loginForm)
  } else {
    await editPasswordApi.post(loginForm)
  }
  
  visibleDialog.value = false
  message.success('密码设置成功')
}
</script>

<style lang="less" scoped>

</style>