<template>
  <!-- layout -->
  <div v-if="useLayout" class="g-layout">
    <!-- 菜单栏 -->
    <side-bar v-model:collapsed="isCollapsed" />
    <div class="g-right">
      <!-- 头部 -->
      <div class="g-header">
        <bread-crumb />
        <a-space size="large" class="g-header-handle">
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <a-menu-item key="qrcode">
                  <a-button type="text" @click="onEditQrcode">店铺小程序码</a-button>
                </a-menu-item>
              </a-menu>
            </template>
            <span style="cursor: pointer">工具</span>
          </a-dropdown>
          <span class="user-name">欢迎您，{{ adminName }}</span>
          <uc-ant-icon title="修改密码" name="LockOutlined" @click="onEditPwd" />
          <!-- <uc-ant-icon title="新手帮助" name="QuestionCircleOutlined" @click="toHelp" /> -->
          <uc-ant-icon title="退出登录" name="PoweroffOutlined" @click="onExit" />
        </a-space>
      </div>
      <!-- 主体 -->
      <div class="g-main">
        <div class="overflow">
          <router-view />
        </div>
      </div>
    </div>
  </div>
  <div v-else>
    <router-view />
  </div>
  <!-- 修改密码 -->
  <passwordUpdate v-model:visible="visibleDialog" />

  <!-- 小程序码 -->
  <a-modal v-model:visible="visibleQrcode" title="小程序码" @ok="handleQrcode">
    <a-form>
      <a-form-item label="小程序码" required>
        <div class="flex">
          <uc-upload :list="[homeDaisy]" upload-text=" " :max-length="1" :disabled="true" />
          <a-button
            class="m-b-4"
            style="align-self: flex-end"
            type="link"
            @click="downloadIamge(homeDaisy)"
          >
            下载首页小程序码
          </a-button>
        </div>
      </a-form-item>
      <a-form-item label="店铺海报" required>
        <div class="flex">
          <uc-upload v-model:list="qrcodeFormState.poster" upload-text=" " :max-length="1" />
          <a-button
            class="m-b-4"
            style="align-self: flex-end"
            type="link"
            @click="downloadIamge(qrcodeFormState.poster[0])"
          >
            下载店铺海报
          </a-button>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import passwordUpdate from '@/views/layout/components/password-update'
import { useStore } from '@/store/auth'
import SideBar from './components/side-bar'
import BreadCrumb from './components/bread-crumb'
import { onBeforeRouteUpdate } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { downloadIamge } from '@/utils/functions'
import { qrcodeSettingApi, qrcodeSettingUpdateApi } from '@/api/common'

const { clearToken } = useStore() // store
const router = useRouter() // 路由操作
const isCollapsed = ref(false) // 是否收缩
const minWidth = computed(() => `${isCollapsed.value ? 1200 : 1080}px`)

// 退出登录
const onExit = () => {
  clearToken()
  router.push({ name: 'auth-login' })
}

const adminName = ref('') // 用户名称
// 设置页面信息
const setWebInfo = () => {
  const { state } = useStore()
  adminName.value = state.userInfo.admin_name
}

const toHelp = () => {
  window.open('https://docs.qq.com/doc/DQU94VGZCUmtreE5W')
}

setWebInfo()
onBeforeRouteUpdate(setWebInfo)

const useLayout = ref(false)
const route = useRoute()

const visibleDialog = ref(false) // 修改密码模态框显示状态

const footerHeight = computed(() => (route.meta.footer ? '72px' : 0))
// 点击修改密码
const onEditPwd = () => {
  visibleDialog.value = true
}

watch(
  () => route,
  value => {
    const { meta = {} } = value
    if (meta.useLayout === false) {
      useLayout.value = false
    } else {
      useLayout.value = true
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const visibleQrcode = ref(false) // 小程序码模态框显示状态
const homeDaisy = ref(`${import.meta.env.VITE_API_BASE_URL}/image/get-daisy?page=pages/home/<USER>
const qrcodeSetting = ref({}) //店铺二维码设置
const {
  formState: qrcodeFormState,
  setFormRules: setQrcodeFormRules,
  validateForm: validateQrcodeForm
} = useFormState({
  poster: [] // 店铺海报
})

setQrcodeFormRules({
  poster: { required: true, message: '请上传海报' }
})

const onEditQrcode = async () => {
  qrcodeSetting.value = await qrcodeSettingApi.get()
  if (qrcodeSetting.value.value.poster) qrcodeFormState.value.poster = [qrcodeSetting.value.value.poster]
  visibleQrcode.value = true
}
const handleQrcode = async () => {
  if (!(await validateQrcodeForm()) || !visibleQrcode.value) return
  await qrcodeSettingUpdateApi.post({
    key: qrcodeSetting.value.key,
    value: {
      ...qrcodeSetting.value.value,
      poster: qrcodeFormState.value.poster[0]
    }
  })
  visibleQrcode.value = false
  message.success('保存成功')
}
</script>
<style scoped lang="less">
.g-layout {
  display: flex;
  width: 100%;
  height: 100%;
  overflow: hidden;
  .g-right {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    width: 50%;
    .g-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      height: 48px;
      padding: 0 24px;
      position: relative;
      z-index: 10;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      &-handle {
        .user-name {
          color: #333333;
        }
        :deep(.anticon) {
          font-size: 20px;
          cursor: pointer;
        }
      }
    }
    .g-main {
      position: relative;
      flex-grow: 1;
      width: 100%;
      height: 50%;
      background: #f0f2f5;
      overflow-y: auto;
      overflow-x: scroll;
      margin-bottom: v-bind(footerHeight);
      .overflow {
        min-width: v-bind(minWidth);
        width: 100%;
        height: 100%;
        padding: 24px;
        overflow-y: auto;
      }
    }
  }
}
</style>
