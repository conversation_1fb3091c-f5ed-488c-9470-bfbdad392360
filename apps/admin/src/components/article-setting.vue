<script lang="ts" setup>
const title = defineModel('title', { default: '' })
const content = defineModel('content', { default: '' })
defineProps({
  category: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <a-card class="g-mg-bt m-b-22 article-setting">
    <a-form>
      <a-form-item class="m-b-0 required">
        <a-input v-model:value.trim="title" placeholder="请输入标题" allow-clear />
      </a-form-item>
    </a-form>
  </a-card>
  <uc-rich-text v-model="content" placeholder="请输入内容" :height="600" />
</template>

<style lang="less" scoped>
.article-setting {
  :deep(.ant-form .ant-form-item .ant-input) {
    max-width: 100% !important;
  }
}
</style>
