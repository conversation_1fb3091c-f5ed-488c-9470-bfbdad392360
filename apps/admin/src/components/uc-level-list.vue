<template>
  <a-space direction="vertical" :size="10">
    <a-space v-for="(item, index) in listData" :key="index" :size="10" wrap class="m-b-0">
      <slot name="item" v-bind="{ item, index }"></slot>
      <a-button
        shape="circle"
        size="small"
        class="delete-btn"
        type="link"
        :disabled="disabled || modelValue.length <= min || item.disabled"
      >
        <template #icon>
          <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onDelete(index)" />
        </template>
      </a-button>
    </a-space>
    <a-button v-if="addText" type="link" class="p-0" :disabled="disabled || modelValue.length >= max" @click="onAdd">
      {{ addText }}
    </a-button>
  </a-space>
</template>
<script>
export default {
  name: 'UcLevelList'
}
</script>
<script setup>
const emit = defineEmits(['update:modelValue', 'add', 'delete'])
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
    // disabled other dimension :item.disabled
  },
  disabled: {
    type: Boolean,
    default: false
  },
  addText: {
    type: String,
    default: ''
  },
  min: {
    type: Number,
    default: 1
  },
  max: {
    type: Number,
    default: 5
  },
  disabledDelete: {
    type: Boolean,
    default: false
  }
})

const listData = computed(() => props.modelValue)
watch(
  listData,
  value => {
    emit('update:modelValue', value)
  },
  { deep: true }
)

const onDelete = index => {
  !props.disabledDelete && listData.value.splice(index, 1)
  emit('delete', index)
}

const onAdd = () => {
  emit('add')
}
</script>
<style lang="less" scoped>
.delete-btn {
  padding-top: 2px;
  border: none;
  :deep(.anticon) {
    font-size: 20px;
  }
  &:disabled {
    :deep(.anticon) {
      color: #d9d9d9 !important;
    }
  }
}
</style>
