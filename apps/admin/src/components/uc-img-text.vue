<template>
  <div class="m-column-item relative">
    <div v-if="typeof url === 'string'" class="img relative m-r-10">
      <a-image
        v-if="url !== null"
        :src="url ? url : fallbackErr"
        :width="50"
        :height="50"
        style="height: 100%"
        :fallback="fallbackErr"
      />
      <span v-if="status.label" class="status" :style="`background:${status.color};`">{{ status.label }}</span>
    </div>
    <div v-else-if="Array.isArray(url)" class="img-box">
      <a-image-preview-group>
        <div v-for="(item, index) in url" v-show="index === 0" :key="index">
          <a-image :src="item" :width="50" :height="50" style="height: 100%" :fallback="fallbackErr" />
        </div>
      </a-image-preview-group>
    </div>

    <div v-if="tag" class="remind flex flex-center absolute p-lr-3 color-white bgc-primary">
      {{ tag }}
    </div>
    <div class="u-text">
      <p class="u-tit">
        <span v-if="dynamictitle" :style="{ color: dynamiccolor }">{{ dynamictitle }}&nbsp;</span>
        <span :title="title">{{ title }}</span>
      </p>
      <p class="u-subtit">
        <!-- 单个 -->
        <span v-if="label" :style="`color:${labelColor};`">{{ label }}</span>
        <!-- 多个 -->
        <slot name="label"></slot>
        <span>{{ symbol }}</span>
        <span :style="`color:${subtitColorComp};`">{{ subtit }}</span>
      </p>
    </div>
  </div>
</template>
<script setup>
import { fallbackErr } from '@/utils/imgs'

const props = defineProps({
  // 是否显示 tag
  tag: {
    type: [String, Number]
  },
  //是否可查看详图
  batch: {
    type: Boolean,
    default: false
  },
  // 图片地址
  url: {
    type: [Array, String],
    default: null
  },
  // 标题
  title: {
    type: String,
    default: ''
  },
  //动态标题和颜色
  dynamictitle: {
    type: String,
    default: ''
  },
  dynamiccolor: {
    type: String,
    default: ''
  },
  // 副标题
  subtit: {
    type: String,
    default: ''
  },
  subtitColor: {
    type: String,
    default: ''
  },
  //标签
  label: {
    type: String,
    default: ''
  },
  labelColor: {
    type: String,
    default: ''
  },
  //符号
  symbol: {
    type: String,
    default: ''
  },
  // 状态
  status: {
    type: Object,
    default: () => ({ label: '', color: '' })
  }
})

// 副标题颜色
const subtitColorComp = computed(() => props.subtitColor || (props.url ? 'rgba(0,0,0,0.85)' : 'rgba(0,0,0,0.65)'))
</script>
<style scoped lang="less">
.m-column-item {
  .img {
    :deep(.ant-image) {
      top: 0px;
      left: 0px;
      background-color: #f5f5f5;
      img {
        object-fit: contain;
      }
    }
    .status {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      text-align: center;
      color: #fff;
      font-size: 12px;
    }
  }

  .img-box {
    width: 50px;
    height: 50px;
    margin-right: 10px;
    :deep(.ant-image) {
      top: 0px;
      left: 0px;
      background-color: #f5f5f5;
      img {
        object-fit: contain;
      }
    }
  }
  display: flex;
  align-items: center;
  .u-img {
    width: 50px;
    height: 50px;
    margin-right: 10px;
  }
  .u-text {
    flex-grow: 1;
    width: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    font-size: 14px;
    .u-tit {
      .clamp();
      margin-bottom: 0;
      font-weight: 500;
    }
    .u-subtit {
      .clamp();
      margin-bottom: 0;
    }
  }
  .remind {
    top: 0;
    left: 0;
    min-width: 20px;
    height: 18px;
  }
}
</style>
