<template>
  <a-input-group compact>
    <a-select v-model:value="dateType" class="w-120" :options="dateSelectVuale.options()" @change="changeDateType" />
    <a-date-picker
      v-if="dateType == dateSelectVuale.day"
      v-model:value="dateRangeProxy"
      :disabled-date="disabledDay"
      placeholder="请选择日期"
      :allow-clear="allowClear"
      @change="dayChange"
    />
    <a-week-picker
      v-if="dateType == dateSelectVuale.week"
      v-model:value="dateRangeProxy"
      :disabled-date="disabledWeek"
      placeholder="请选择周"
      :allow-clear="allowClear"
      @change="weekChange"
    />
    <a-month-picker
      v-if="dateType == dateSelectVuale.month"
      v-model:value="dateRangeProxy"
      :disabled-date="disabledMonth"
      placeholder="请选择月份"
      :allow-clear="allowClear"
      @change="monthChange"
    />
  </a-input-group>
</template>
<script>
export default {
  name: 'UcDatePicker'
}
</script>
<script setup>
import moment from 'moment'
import { dateSelectVuale } from '@/enums/date'

const props = defineProps({
  // 日期范围
  modelValue: {
    type: Array,
    default: undefined
  },
  // 间隔
  step: {
    type: Number,
    default: 0
  },
  // 允许清空
  allowClear: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['update:modelValue', 'change'])

const defaultDate = moment().subtract(1, 'days')
const dateRange = ref(props.modelValue)
const dateType = ref(dateSelectVuale.day)
const dateRangeProxy = ref(defaultDate)

watch(
  () => props.modelValue,
  newValue => {
    dateRange.value = newValue
  }
)

watch(
  () => dateRange.value,
  newValue => {
    emit('update:modelValue', newValue)
  }
)

const changeDateType = type => {
  dateRange.value = undefined
  dateRangeProxy.value = undefined
}

const dayChange = date => {
  dateRange.value = date
    ? [
        moment(date).subtract(props.step, 'days').format('YYYY-MM-DD') + ' 00:00:00',
        moment(date).format('YYYY-MM-DD') + ' 23:59:59'
      ]
    : undefined
  nextTick(() => {
    emit('change')
  })
}

const weekChange = date => {
  dateRange.value = date
    ? [
        moment(date).subtract(props.step, 'week').weekday(0).format('YYYY-MM-DD') + ' 00:00:00',
        moment(date).weekday(6).format('YYYY-MM-DD') + ' 23:59:59'
      ]
    : undefined
  nextTick(() => {
    emit('change')
  })
}

const monthChange = date => {
  dateRange.value = date
    ? [
        moment(date).subtract(props.step, 'month').startOf('month').format('YYYY-MM-DD') + ' 00:00:00',
        moment(date).endOf('month').format('YYYY-MM-DD') + ' 23:59:59'
      ]
    : undefined
  nextTick(() => {
    emit('change')
  })
}

const disabledDay = date => {
  return date && date.format('YYYY-MM-DD') >= moment().format('YYYY-MM-DD')
}

const disabledWeek = date => {
  return date && date >= moment().weekday(0)
}

const disabledMonth = date => {
  return date && date >= moment().startOf('month')
}

const reset = () => {
  dateRange.value = undefined
  dateType.value = dateSelectVuale.day
  dateRangeProxy.value = defaultDate
}

const clear = () => {
  dateRange.value = undefined
  dateRangeProxy.value = undefined
}

defineExpose({
  dateType,
  reset,
  clear
})
</script>
