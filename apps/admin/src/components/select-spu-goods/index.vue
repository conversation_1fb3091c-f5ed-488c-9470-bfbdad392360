<template>
  <a-modal v-bind="{ visible }" title="选择商品" width="1000px" @cancel="onClose">
    <a-form layout="inline" class="m-b-20">
      <a-form-item name="title">
        <a-input-group class="flex" compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            :options="idOrTitleType.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键词" class="w-300" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-model:value="formState.category_id"
          placeholder="商品分类"
          :options="categories"
          :field-names="{ label: 'title', value: 'id' }"
          change-on-select
          style="width: 276px"
        />
      </a-form-item>
      <a-form-item class="m-r-0">
        <a-button type="primary" class="m-r-10" @click="setPage()">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </a-form>
    <!-- 列表 -->
    <template v-if="grid">
      <a-card v-if="loading" v-bind="{ loading }" />
      <ul v-else class="m-list">
        <li v-for="item in listData" :key="item.id" :class="{ active: item.selectState }" @click="onClick(item)">
          <uc-img-text
            :url="item.photo_urls[0]"
            :title="item.title"
            :subtit="getCategory(categories, item.category_id)"
            :label="item.id"
            symbol=":&nbsp"
            subtit-color="#f56c6c"
          />
          <img class="u-triangle" :src="assets.selected" alt="选中三角" />
        </li>
      </ul>
      <div class="flex flex-sb flex-cc">
        <div v-if="isMultiple">
          <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="updateSelectState">
            全选筛选条件下所有商品
          </a-checkbox>
        </div>
        <a-pagination v-bind="stdPagination(data)" @change="onShowSizeChange" @show-size-change="onShowSizeChange" />
      </div>
    </template>
    <template v-else>
      <a-table
        :data-source="listData"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        :row-selection="rowSelection"
        @change="setPage"
      >
        <a-table-column title="商品图片" width="150px">
          <template #default="{ record }">
            <a-image
              :src="record.photo_urls[0]"
              :width="50"
              :height="50"
              style="height: 100%"
            />
          </template>
        </a-table-column>
        <a-table-column title="商品名称" width="250px">
          <template #default="{ record }">
            {{ record.title }}
          </template>
        </a-table-column>
        <a-table-column title="商品id" width="100px">
          <template #default="{ record }">
            {{ record.id }}
          </template>
        </a-table-column>
        <a-table-column title="商品分类" width="250px">
          <template #default="{ record }">
            {{ getCategory(categories, record.category_id) }}
          </template>
        </a-table-column>
      </a-table>
    </template>
    <template #footer>
      <div class="flex flex-sb flex-cc">
        <div class="flex flex-cc">
          <div>
            已选择 <span class="color-danger">{{ grid ? getTempSelectNum : selectedKeys.length }}</span> 个商品
          </div>
          <a-button v-if="grid" type="link" @click="updateSelectState(false)">
            清空
          </a-button>
        </div>
        <div>
          <a-button @click="onClose">
            取消
          </a-button>
          <a-button type="primary" @click="onSubmit">
            确认
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: 'SelectSpuGoods'
}
</script>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { getCategory, useCategories } from '@/modules/goods/useCategory'
import { useTransformList, useSpecAttr } from '@/modules/promotion/useTransform'
import { idOrTitleType , goodsSelectType } from '@/modules/promotion/enums'
import assets from '@/modules/promotion/assets.config'
import { goodsApi } from '@/modules/promotion/api'
import { message } from 'ant-design-vue'

const emit = defineEmits(['update:visible', 'ok', 'cancel'])
const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: goodsSelectType.multiple
  },
  // 是否网格展示
  grid: {
    type: Boolean,
    default: false
  },
})

// 商品分类
const { categories } = useCategories()

const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: 'id',
  conditionValue: undefined,
  category_id: []
})

const getId = () => (formState.value.conditionKey === idOrTitleType.id ? formState.value.conditionValue : undefined)
const getTitle = () =>
  formState.value.conditionKey === idOrTitleType.title ? formState.value.conditionValue : undefined
const getCategoryId = () =>
  formState.value.category_id.length ? formState.value.category_id[formState.value.category_id.length - 1] : undefined

const { data, setPage, page, loading } = usePaginatorApiRequest(
  ({ offset, limit }) =>
    goodsApi.paginator({
      filters: useTransformQuery(
        {
          id: getId(),
          title: getTitle(),
          category_id: getCategoryId()
        },
        { title: 'like' }
      ),
      // relations: ['specs'],
      offset,
      limit
    }),
  { current: 1, pageSize: props.grid ? 20 : 10 }
)

const onShowSizeChange = (current, pageSize) => {
  setPage({ current, pageSize })
}

// 关闭显示状态
const onClose = () => {
  selectedGoods.value = []
  selectedKeys.value = []
  emit('update:visible', false)
  emit('cancel')
}

const listData = ref([]) // 表格数据

watch(
  () => props.visible,
  value => {
    if (value) {
      tempData.value.length = 0
      onRestFormState(() => {
        setPage()
      })
      resetFormState()
    }
  }
)
watch(
  data,
  ({ items }) => {
    listData.value = useTransformList(items)
  },
  { deep: true }
)

const tempData = ref([]) // 临时数据

// 获取临时选中的数量
const getTempSelectNum = computed(
  () => listData.value.reduce((prev, item) => prev + +item.selectState, 0) + tempData.value.length
)

// 设置选中状态
const updateSelectState = e => {
  const state = typeof e === 'boolean' ? e : e.target.checked
  listData.value.forEach(item => {
    item.selectState = state
  })
  // if (state && listData.value.length === page.value.pageSize) {
  if (state) {
    goodsApi
      .list({
        filters: useTransformQuery(
          {
            id: getId(),
            title: getTitle(),
            category_id: getCategoryId()
          },
          { title: 'like' }
        )
      })
      .then(data => {
        tempData.value = data.filter(item => !listData.value.some(it => it.id === item.id))
      })
  } else {
    tempData.value.length = 0
  }
}

// 全选框的部分选中状态
const indeterminate = computed(() => {
  const len = listData.value.filter(item => item.selectState).length
  return len < listData.value.length && len > 0
})

// 全选状态,和 indeterminate 配合
const checkAll = computed(() => listData.value.some(item => item.selectState))

// 是否是多选
const isMultiple = computed(() => props.mode === goodsSelectType.multiple)
// 点击列表
const onClick = item => {
  if (isMultiple.value) {
    item.selectState = !item.selectState
  } else {
    listData.value.forEach(it =>  it.selectState = false)
    item.selectState = true
  }
}

// 点击确定按钮
const onSubmit = () => {
  const list = props.grid
    ? listData.value.filter(item => item.selectState).concat(tempData.value)
    : selectedGoods.value
  if (!list.length) {
    message.error('请选择商品')
  } else {
    emit('ok', list)
    onClose()
  }
}

// table列表选择的id
const selectedGoods = ref([])
const selectedKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedKeys,
  type: props.mode === goodsSelectType.multiple ? 'checkbox' : 'radio',
  onChange: (selectedRowKeys) => {
    selectedKeys.value = selectedRowKeys
    const ids = selectedGoods.value.map(item => item.id)
    const diffIds = selectedRowKeys.filter(key => !ids.includes(key))
    const addGoods = listData.value.filter(item => diffIds.includes(item.id))
    selectedGoods.value.push(...addGoods)
  }
}
</script>
<style lang="less" scoped>
.m-list {
  display: flex;
  flex-wrap: wrap;
  li {
    position: relative;
    width: 229px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin: 0 12px 10px 0;
    padding: 10px;
    break-inside: avoid;
    .m-column-item {
      .u-tit > span {
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 5px;
      }
      .u-subtit > span:last-child {
        font-size: 14px;
        font-weight: 500;
      }
    }
    cursor: pointer;
    &:nth-child(4n + 4) {
      margin-right: 0;
    }
    .u-triangle {
      position: absolute;
      right: 0;
      bottom: 0;
      display: none;
      width: 22px;
    }
    &.active {
      border-color: @color-primary;
      .u-triangle {
        display: block;
      }
    }
  }
}
</style>
