<template>
  <a-popover v-if="type === 'popover'" v-model:visible="visible" :title="title" :trigger="trigger">
    <template #content>
      <div>
        <a-checkbox v-model:checked="checkAll" :indeterminate="indeterminate" @change="onCheckAllChange">
          全选
        </a-checkbox>
      </div>
      <div v-for="item, index in checkedFields" :key="index" class="m-tb-2">
        <a-checkbox v-model:checked="item.checked" @change="(e) => onChange(e, item)">
          {{ item.value }}
        </a-checkbox>
      </div>
      <div class="t-right m-tr-8">
        <a-button type="primary" size="small" @click="confirm"> 确定 </a-button>
      </div>
    </template>
    <a-button> {{ buttonTitle }} </a-button>
  </a-popover>
  <div v-else>
    <a-button @click="visible = true"> {{ buttonTitle }} </a-button>
    <a-modal v-model:visible="visible" width="650px" :title="title" @ok="confirm">
      <div class="export-item">
        <div class="export-item__title">
          导出条件<span class="export-item__tips">（如需修改以下条件，请关闭本窗口，重选条件）</span>
        </div>
        <div class="export-item__content">
          <a-descriptions :column="2">
            <a-descriptions-item v-for="item, index in search" :key="index" :label="item.label">
              <span class="color-grey">{{ item.value || '全部' }}</span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
      <div class="export-item">
        <div class="export-item__title">
          报表字段<span class="export-item__tips">（如需自定义报表，请选择/取消字段）</span>
        </div>
        <div class="export-item__content">
          <!-- <div>
            <a-tag
              v-model:checked="checkAll"
              class="export-item__tag"
              :color="checkAll ? 'blue' : 'default'"
              @click="handleAllClick"
            >
              {{ !checkAll ? '全部选择' : '取消全选' }}
            </a-tag>
          </div> -->
          <div>
            <a-tag
              v-for="item, index in checkedFields"
              :key="index"
              v-model:checked="item.checked"
              :color="item.checked ? 'blue' : 'default'"
              class="export-item__tag"
              @click="handleClick(item)"
            >
              {{ item.value }}
            </a-tag>
          </div>
        </div>
      </div>
    </a-modal>
  </div>
  <a-modal v-model:visible="confirmVisible" :title="confirmTitle" @ok="handleConfirm">
    <a-form>
      <a-form-item>
        <a-input-password
          v-model:value="password"
          placeholder="请输入密码"
          autocomplete="off"
          @keyup.enter.prevent="handleConfirm"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { usePasswordConfirm } from '@/composables/usePasswordConfirm';
const visible = ref(false)

const emits = defineEmits(['ok'])

const props = defineProps({
  type: {
    type: String,
    default: 'modal' // popover | modal
  },
  title: {
    type: String,
    default: '选择导出字段'
  },
  trigger: {
    type: String, // click | hover
    default: 'click'
  },
  buttonTitle: {
    type: String,
    default: '导出'
  },
  fields: {
    type: Array,
    // required: true,
    default: () => []
  },
  format: {
    type: String,
    default: 'string' // array | string
  },
  search: {
    type: Array,
    default: () => []
  }
})
const checkedList = ref([])
const checkedFields = ref()
const checkAll = ref(false)
const indeterminate = ref(true)

watch(() => props.fields, (val) => {
  checkedFields.value = val
  checkedList.value = [];
  val.forEach(item => {
    if (item.checked) {
      checkedList.value.push(item.key)
    }
  })
}, {
  immediate: true
})

const checkedListChange = (checked, item) => {
  if (checked) {
    checkedList.value.push(item.key)
  } else {
    for (let i = 0; i < checkedList.value.length; i++) {
      if (checkedList.value[i] === item.key) {
        checkedList.value.splice(i, 1)
      }
    }
  }
}

const checkedListAllChange = (checked) => {
  if (checked) {
    checkedList.value = []
    checkedFields.value.forEach(item => {
      item.checked = true
      checkedList.value.push(item.key)
    })
  } else {
    checkedList.value = []
    checkedFields.value.forEach(item => {
      item.checked = false
    })
  }
}

const onChange = (e, item) => {
  checkedListChange(e.target.checked, item)
}

const onCheckAllChange = (e) => {
  checkedListAllChange(e.target.checked)
}

watch(
  () => checkedList.value,
  val => {
    indeterminate.value = !!val.length && val.length < checkedFields.value.length;
    checkAll.value = val.length === checkedFields.value.length;
  }, {
  deep: true
}
)

const handleAllClick = () => {
  checkAll.value = !checkAll.value
  checkedListAllChange(checkAll.value)
}

const handleClick = (item) => {
  const checked = item.checked = !item.checked
  checkedListChange(checked, item)
}

const confirm = () => {
  if (checkedList.value.length === 0) {
    message.error('请选择需要导出的字段')
    return false
  }

  confirmVisible.value = true

  done((token) => {

    let value = checkedList.value

    if (props.format === 'string') {
      value = value.join(',')
    }

    emits('ok', value, token)

  })
}

const { visible: confirmVisible, title: confirmTitle, password, handleConfirm, done } = usePasswordConfirm()
</script>

<style lang="less" scoped>
.export-item {
  width: 100%;
  margin-top: 20px;

  &__title {
    position: relative;
    color: @color-primary;
    font-size: 16px;
    font-weight: 600;
    padding-left: 8px;
  }

  &__title::before {
    position: absolute;
    content: '';
    height: 70%;
    width: 2px;
    background-color: @color-primary;
    left: 0;
    top: 16%;
  }

  &__tips {
    margin-left: 4px;
    font-size: 14px;
    color: #aaa;
    font-weight: normal;
  }

  &__content {
    width: 100%;
    margin-top: 20px;
  }

  &__tag {
    cursor: pointer;
    margin-bottom: 16px;
    padding-left: 10px;
    padding-right: 10px;
    height: 28px;
    line-height: 28px;
  }
}

.export-item:first-child {
  margin: 0;
  ;
}
</style>
