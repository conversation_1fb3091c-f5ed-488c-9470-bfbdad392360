<template>
  <!-- 详情页基础组件 -->
  <div class="layout-form">
    <a-form :label-col="layoutLabelCol" :wrapper-col="layoutWrapperCol">
      <a-space direction="vertical" :size="20">
        <slot></slot>
      </a-space>
    </a-form>
    <div v-if="isSave || isCancel" class="submit-bar">
      <a-space :size="10">
        <a-button v-if="isCancel" @click="onCancel"> 取消 </a-button>
        <a-button v-if="isSave" type="primary" :loading="loading" @click="onSave"> 保存 </a-button>
      </a-space>
    </div>
    <div class="submit-bar-place"></div>
  </div>
</template>
<script>
import { useLoading } from '@/composables/useToggles'

export default {
  name: 'UcLayoutForm'
}
</script>
<script setup>
const router = useRouter()
const emit = defineEmits(['cancel', 'submit'])
const { loading, setLoading } = useLoading()

defineProps({
  isSave: {
    // 保存按钮显示
    type: Boolean,
    default: true
  },
  isCancel: {
    // 取消按钮显示
    type: Boolean,
    default: true
  }
})
// 点击取消
const onCancel = () => {
  router.back()
  emit('cancel')
}
// 点击保存
const onSave = async () => {
  setTimeout(async () => {
    setLoading(true)
    try {
      await emit('submit')
    } finally {
      setLoading(false)
    }
  }, 0)
}
</script>
<style scoped lang="less">
.layout-form {
  position: relative;
  :deep(.ant-space) {
    width: 100%;
    display: flex;
  }
  :deep(.ant-input-number) {
    width: 100%;
    max-width: 500px;
  }

  :deep(.ant-form .ant-form-item) {
    .ant-select,
    .ant-input,
    .ant-cascader-picker,
    .ant-input-wrapper {
      max-width: 500px !important;
    }
  }

  .submit-bar {
    &,
    &-place {
      height: 60px;
    }
    position: fixed;
    bottom: 6px;
    left: 0;
    z-index: 9;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    width: 100%;
    background: #fff;
    box-shadow: 0 0px 8px #f0f1f2;
    :deep(.ant-space) {
      padding-right: 10px;
      justify-content: flex-end;
    }
  }
}
</style>
