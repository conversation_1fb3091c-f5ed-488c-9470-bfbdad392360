<template>
  <a-select
    v-model:value.trim="activity_dispatch_coupon_id"
    class="select-width"
    :options="activityList"
    placeholder="请选择活动发券"
    allow-clear
    show-search
    :show-arrow="false"
    :filter-option="false"
    :not-found-content="null"
    @search="handleSearchActivityCoupon"
  />
</template>
<script>
export default {
  name: 'CoupActSelect'
}
</script>
<script setup>
import { debounce } from 'lodash'
import { activityCouponApi } from '@/modules/promotion/api'

const props = defineProps({
  // 编辑器内容
  modelValue: {
    type: Number
  }
})
const emit = defineEmits(['update:modelValue', 'import'])

const activity_dispatch_coupon_id = ref(props.modelValue)
const activityList = ref([]) // 活动发券列表

watch(
  () => activity_dispatch_coupon_id.value,
  newValue => {
    emit('update:modelValue', newValue)
  }
)

watch(
  () => props.modelValue,
  newValue => {
    activity_dispatch_coupon_id.value = newValue
  }
)

// 搜索活动发券
const handleSearchActivityCoupon = debounce(async title => {
  useActivityCoupon({
    title: `%${title}%`
    // status: 'normal'
  })
}, 500)

// 处理活动发券
async function useActivityCoupon(filters) {
  const res = await activityCouponApi.paginator({
    filters,
    offset: 1,
    limit: 20
  })

  activityList.value = res.items.map(item => ({
    value: item.id,
    label: item.title
  }))
}

useActivityCoupon()
</script>
