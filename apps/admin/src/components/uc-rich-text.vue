<template>
  <!-- 富文本 -->
  <div :id="'m-rich-' + index" class="m-rich-text"></div>
</template>
<script setup>
import Wangeditor from 'wangeditor'
import BaseConfig from '@/config'
import { useStore } from '@/store/auth'

const props = defineProps({
  // 索引-用于创建富文本编辑器
  index: {
    type: Number,
    default: 2
  },
  // 内容值
  modelValue: {
    type: String,
    default: ''
  },
  // 内容类型
  contentType: {
    type: String,
    default: 'html' // html|text
  },
  // 高度
  height: {
    type: Number,
    default: 300
  },
  // 占位符
  placeholder: {
    type: String,
    default: '请输入活动说明'
  },
  // 禁用状态
  disabled: {
    type: Boolean,
    default: false
  },
  // 层级
  zIndex: {
    type: Number,
    default: 5
  },
  // 最小限制数
  minLength: {
    type: Number,
    default: 1
  },
  // 最大限制数
  maxLength: {
    type: Number,
    default: Number.MAX_SAFE_INTEGER
  },
  // 校验是否通过
  isValidator: {
    type: Boolean,
    default: false
  },
  // 菜单栏配置
  menus: {
    type: Array,
    default: () => [
      'head',
      'bold',
      'fontSize',
      'fontName',
      'italic',
      'underline',
      'strikeThrough',
      'indent',
      'lineHeight',
      'foreColor',
      'backColor',
      'link',
      'list',
      'justify',
      // 'quote',
      'emoticon',
      'image',
      'video',
      'splitLine',
      'undo',
      'redo'
    ]
  },
  // 颜色配置
  colors: {
    type: Array,
    default: () => [
      '#005627',
      '#FE6F21',
      '#B1E3C8',
      '#FFD7C0',
      '#000000',
      '#262626',
      '#595959',
      '#8C8C8c',
      '#cccccc',
      '#EEEEEE',
      '#F5F5F5',
      '#FFFFFF',
      '#A8071A',
      '#F5222D',
      '#FF7875',
      '#FFCCC7',
      '#AD4E00',
      '#FA8C16',
      '#FFC069',
      '#FFE7BA',
      '#AD8B00',
      '#FADB14',
      '#FFF566',
      '#FFFFB8',
      '#237804',
      '#52C41A',
      '#95DE64',
      '#D9F7BE',
      '#006D75',
      '#13C2C2',
      '#5CDBD3',
      '#B5F5EC',
      '#0050B3',
      '#1890FF',
      '#69C0FF',
      '#BAE7FF',
      '#9E1068',
      '#EB2F96',
      '#FF85C0',
      '#FFD6E7'
    ]
  },
  // 上传地址
  uploadUrl: {
    type: String
  },
  // 上传限制
  uploadLimit: {
    type: Number
  },
  // 上传允许文件
  uploadAllow: {
    type: Array
  },
  toolbarWidth: {
    type: String,
    default: '100%'
  }
})
const editor = ref(null) // 编辑器实例
const emit = defineEmits(['update:isValidator', 'update:modelValue', 'change'])

// 监听 props value
watch(
  () => props.modelValue,
  value => {
    nextTick(() => {
      editor.value && editor.value.txt.html(value)
    })
  },
  { immediate: true }
)
watch(
  () => props.disabled,
  value => {
    nextTick(() => {
      editor.value && editor.value[value ? 'disable' : 'enable']()
    })
  },
  { immediate: true }
)

const {
  upload: {
    url,
    image: { limit, allow }
  }
} = BaseConfig

onMounted(() => {
  editor.value = new Wangeditor(`#m-rich-${props.index}`) // 创建 Wangeditor 实例

  const {
    height,
    placeholder,
    zIndex,
    minLength,
    maxLength,
    menus,
    colors,
    uploadUrl = url,
    uploadLimit = limit,
    uploadAllow = allow
  } = props
  const onblur = html => {
    const contentElem = editor.value.$textElem.elems[0]
    const contLen = contentElem.textContent.trim().length
    const isValidator = contLen >= minLength && contLen <= maxLength
    emit('update:isValidator', isValidator)
    emit('update:modelValue', html)
    emit('change', html)
  }
  const { state } = useStore()
  // 配置文件
  const config = {
    menuTooltipPosition: 'down',
    menus,
    colors,
    styleWithCSS: true,
    uploadImgShowBase64: true,
    height: height - 41,
    placeholder,
    onchangeTimeout: 300,
    zIndex,
    pasteIgnoreImg: true,
    uploadImgServer: uploadUrl,
    uploadImgMaxSize: uploadLimit,
    uploadImgAccept: uploadAllow,
    uploadImgHeaders: { Authorization: `Bearer ${state.token}` },
    uploadImgTimeout: 60 * 1e3, //60 s
    uploadImgHooks: {
      customInsert: (insertImgFn, res) => {
        for (const key in res.data) {
          insertImgFn(res.data[key].url)
        }
      }
    },
    uploadFileName: 'file'
  }
  Object.assign(editor.value.config, config)
  editor.value.create()
  editor.value.$textElem.elems[0].addEventListener('blur', e => {
    nextTick(() => {
      editor.value && onblur(editor.value.txt.html())
    })
  })
  editor.value.$toolbarElem.elems[0].addEventListener('mousedown', e => {
    e.stopPropagation()
    // e.preventDefault()
  })
})
onBeforeUnmount(() => {
  editor.value.destroy() // 销毁编辑器
  editor.value = null
})
</script>
<style scoped lang="less">
@init-font-size: 14px;
.m-rich-text {
  width: 100%;
  background: #fff;
  :deep(.w-e-toolbar p),
  :deep(.w-e-text-container p),
  :deep(.w-e-menu-panel p) {
    color: rgba(0, 0, 0, 0.85);
    font-size: @init-font-size !important;
  }
  :deep(.w-e-text-container) {
    border: none !important;
    border-top: 1px solid #eee !important;
    .placeholder {
      font-size: @init-font-size;
    }
  }
  :deep(.w-e-toolbar) {
    width: v-bind(toolbarWidth);
    border: none !important;
  }
  :deep(.w-e-icon-image + .w-e-panel-container .w-e-item:last-child) {
    display: none;
  }
  :deep(.w-e-text) {
    padding: 8px 10px;
    p,
    h1,
    h2,
    h3,
    h4,
    h5,
    table,
    pre {
      margin: 0;
      line-height: 1.8;
    }
    ul li {
      list-style: disc;
    }
    ol li {
      list-style: decimal;
    }
    img {
      display: block;
    }
  }
}
</style>
