<template>
  <!-- 上传图片/视频 -->
  <div class="m-upload">
    <!-- label -->
    <div v-if="showLabel" class="u-label">
      {{ labelText }}
    </div>
    <div style="display: flex">
      <draggable v-if="isdrag" v-model="fileList" style="display: flex" item-key="id" @change="draggableChange">
        <template #item="{ element }">
          <div class="dragbox">
            <img v-if="element.url" class="drap-img" mode="contain" :src="element.url" />
            <uc-ant-icon v-else class="drag-load" name="LoadingOutlined" />
            <div v-if="element.url" class="drag-icon">
              <uc-ant-icon
                name="EyeOutlined"
                @click="
                  () => {
                    previewVisible = true
                    previewImage = element.url
                  }
                "
              />
              <uc-ant-icon name="DeleteOutlined" @click="remove(element)" />
            </div>
            <div v-if="element.url" class="drap-shade"></div>
          </div>
        </template>
      </draggable>

      <!-- 上传 -->
      <a-upload
        v-model:file-list="fileList"
        v-bind="{ multiple, headers, beforeUpload, remove }"
        :show-upload-list="size !== 'small' && !isdrag"
        :action="fileAction"
        :disabled="disabled || isLoading"
        :accept="fileAccept"
        list-type="picture-card"
        :class="{ sola: maxLength === 1 }"
        v-on="{ preview, change }"
      >
        <div v-if="fileList.length < maxLength">
          <uc-ant-icon v-if="!disabledIcon" :name="isLoading ? 'LoadingOutlined' : 'PlusOutlined'" />
          <div v-if="size !== 'small'" class="ant-upload-text">
            {{ uploadText || (type === 'image' ? '上传图片' : '上传视频') }}
          </div>
        </div>
        <img v-if="size === 'small' && fileList[0]" :src="fileList[0].url" alt mode="contain" />
      </a-upload>
    </div>

    <!-- 预览 -->
    <a-modal :visible="previewVisible" :footer="null" class="m-preview" @cancel="onCancelPreview">
      <video v-if="type === 'video'" :src="previewImage" controls></video>
      <img v-else class="u-img-preview" alt="example" :src="previewImage" />
    </a-modal>
  </div>
</template>
<script>
export default {
  name: 'UcUpload'
}
</script>
<script setup>
import { computed, ref, watch } from 'vue'
import config from '@/config'

import { message } from 'ant-design-vue'
import { useStore } from '@/store/auth'
import draggable from 'vuedraggable'

const props = defineProps({
  //图片是否能拖拽
  isdrag: {
    type: Boolean,
    default: false
  },
  // 上传类型
  type: {
    type: String,
    default: 'image' // image|video
  },
  // 方向
  layout: {
    type: String,
    default: 'horizontal' // horizontal|vertical
  },
  // 大小
  size: {
    type: [String, Array],
    default: 'middle' // large | middle | small
  },
  // 上传提示文本
  uploadText: {
    type: String,
    default: ''
  },
  // 禁止状态
  disabled: {
    type: Boolean,
    default: false
  },
  // 多选
  multiple: {
    type: Boolean,
    default: false
  },
  // 文件上传类型
  accept: {
    type: String,
    default: ''
  },
  // 最大上传数量
  maxLength: {
    type: Number,
    default: Number.POSITIVE_INFINITY
  },
  // 文件列表
  list: {
    type: Array,
    default: () => []
  },
  // 上传图片地址
  action: {
    type: String,
    default: ''
  },
  // 左上角标签显示状态
  showLabel: {
    type: Boolean,
    default: false
  },
  // 左上角标签内容
  labelText: {
    type: String,
    default: '主图'
  },
  // 禁止图标
  disabledIcon: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['drap', 'update:list'])
const previewVisible = ref(false) // 模态框显示状态
const previewImage = ref('') // 预览图片
// 点击预览图片
const preview = file => {
  previewImage.value = file.url || file.preview
  if (props.accept == 'video/mp4') {
    const url = file.url.split('?')[0]
    url && (previewImage.value = url)
  }
  previewVisible.value = true
}
const { state } = useStore()
const allSize = { large: [200, 150], middle: [104, 104], small: [32, 32] } // 所有大小
const fileAccept = computed(() => props.accept || config.upload[props.type].accept) // 上传文件类型
// 临时上传测试
const fileAction = computed(() => config.upload.url) // 上传文件地址
const headers = Object.freeze({
  Authorization: `Bearer ${state.token}`
}) // 头部数据

const fileList = ref([]) // 列表
const isLoading = ref(false) // 上传中
// 上传区域的大小
const uploadAreaSize = computed(() => {
  const size = (typeof props.size === 'string' ? allSize[props.size] : props.size).map(num => `${num}px`)
  const [width, height] = props.layout === 'horizontal' ? size : size.reverse()
  return { width, height }
})
// 上传文件之前
const beforeUpload = file => {
  // 校验文件
  const { type, size } = file
  const { accept, limit } = config.upload[props.type]
  // 类型
  if (!accept.includes(type)) {
    message.error(`只能上传 ${accept} 类型的文件`)
    return false
  }
  // 大小
  if (size > limit) {
    message.error(`大小不超过${limit / 1024 / 1024}M`)
    return false
  }
}
// 初始化内容
const initFileList = () => {
  fileList.value = props.list.map((url, uid) => {
    url = props.type === 'video' ? `${url}?${config.upload.video.snapshot}` : url
    return {
      uid,
      url,
      status: 'done',
      name: `upload${uid}`
    }
  })
}
// 状态改变
const change = ({ file: { status, response, uid }, fileList: lists, event }) => {
  const { size, maxLength, type } = props
  switch (status) {
    case 'uploading':
      isLoading.value = true
      return
    case 'error':
      fileList.value = fileList.value.filter(item => item.uid !== uid)
      message.error(response?.data?.message ?? '上传文件失败')
      isLoading.value = false
      return
    case 'done':
      let list = []
      const isDone = lists.some(({ status, response, url }) => {
        url = url = url || response?.data?.file?.url
        url && list.push(url)
        return status !== 'done'
      })
      if ((size === 'small' || type === 'video') && maxLength === 1) {
        list = [response?.data?.file?.url]
      }
      if (!isDone) {
        emit('update:list', list)
        initFileList()
        isLoading.value = false
      }
      return
    default:
      fileList.value = fileList.value.filter(item => item.uid !== uid)
      return
  }
}
// 点击删除
const remove = ({ uid }) => {
  const list = fileList.value.filter(item => item.uid != uid).map(item => item.url)
  emit('update:list', list)
  return false
}
// 关闭预览弹窗
const onCancelPreview = () => {
  previewVisible.value = false
}

// 监听列表变化
watch(
  () => props.list,
  () => {
    initFileList()
  },
  {
    deep: true,
    immediate: true
  }
)
//拖拽改变
const draggableChange = () => {
  const list = fileList.value.map(item => item.url)
  emit('update:list', list)
}
</script>
<style scoped lang="less">
.m-upload {
  position: relative;
  .dragbox {
    width: 102px;
    height: 102px;
    margin: 0 10px 10px 0;
    border: 1px solid #d9d9d9;
    position: relative;
    box-sizing: border-box;
    .drap-img {
      width: 100%;
      height: 100%;
    }
    &:hover {
      .drap-shade {
        background: rgba(0, 0, 0, 0.85);
        position: absolute;
        top: 0%;
        left: 0%;
        width: 100%;
        height: 100%;
        opacity: 0.6;
      }
      .drag-icon {
        opacity: 1;
      }
    }
  }

  .drag-icon {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    white-space: nowrap;
    transform: translate(-50%, -50%);
    opacity: 0;
    transition: all 0.3s;
    width: 60%;
    display: flex;
    justify-content: space-around;
    color: #fff;
    font-size: 16px;
  }
  .drag-load {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    white-space: nowrap;
    transform: translate(-50%, -50%);
  }
  .u-label {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    // width: 60px;
    padding: 0 6px;
    height: 22px;
    line-height: 22px;
    background: @color-primary;
    text-align: center;
    color: #fff;
    font-size: 12px;
  }
  :deep(.ant-upload-text) {
    color: #666;
  }
  :deep(.ant-upload-picture-card-wrapper) {
    display: flex;
    .ant-upload-list-picture-card-container,
    .ant-upload-list-picture-card .ant-upload-list-item,
    .ant-upload.ant-upload-select-picture-card {
      width: v-bind('uploadAreaSize.width');
      height: v-bind('uploadAreaSize.height');
      margin: 0 10px 10px 0;
      padding: 0;
    }
    .ant-upload-list-item-info > span {
      .flex-center();
      .ant-upload-list-item-thumbnail > img {
        object-fit: contain;
      }
    }
    .ant-upload.ant-upload-select-picture-card > .ant-upload {
      padding: 0;
    }
  }

  .sola {
    width: calc(v-bind('uploadAreaSize.width') + 10px);
    height: calc(v-bind('uploadAreaSize.height') + 10px);
  }
}
.m-preview {
  .u-img-preview {
    width: 100%;
  }
}
video {
  width: 100%;
}
</style>
