<template>
  <span>
    <a-tag
      v-for="(tag, tagIndex) in tagsData"
      :key="tagIndex"
      color="blue"
      class="custom-template__tag cursor-move m-b-6"
      draggable="true"
      @dragstart="onDragStart(tagIndex)"
      @dragover.prevent
      @drop="onDrop(tagIndex)"
      @click="onEdit(tagIndex, tag)"
    >
      <span>
        {{ type === customRecommendTextType.select ? tag.join(' ') : tag }}
        <a-popconfirm
          v-if="closable"
          placement="top"
          :title="closeTitle"
          ok-text="确定"
          cancel-text="取消"
          @click.stop
          @confirm="onDelete(tagIndex)"
        >
          <uc-ant-icon name="CloseOutlined" />
        </a-popconfirm>
      </span>
    </a-tag>
    <a-modal :visible="moveVisible" width="300px" class="ant-modal-confirm-confirm" :closable="false" :footer="null">
      <div class="ant-modal-confirm-body">
        <ExclamationCircleOutlined class="anticon anticon-exclamation-circle" />
        <span class="ant-modal-confirm-title">确认移动吗?</span>
        <div class="m-t-30 t-right">
          <a-button @click="moveCancel"> 取消 </a-button>
          <a-button class="m-l-10" type="primary" @click="moveOk"> 确定 </a-button>
        </div>
      </div>
    </a-modal>
  </span>
</template>

<script setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { customRecommendTextType } from '@/modules/custom/enums'
const emits = defineEmits('ok', 'cancel', 'edit', 'close')
const tagsData = defineModel()

const props = defineProps({
  index: {
    type: Number,
    default() {
      return 1
    }
  },
  closable: {
    type: Boolean,
    default: false
  },
  closeTitle: {
    type: String,
    default: '确定删除吗'
  },
  type: {
    type: String,
    default: ''
  }
})

const onEdit = (tagIndex, tag) => {
  emits('edit', { index: props.index, tagIndex, tag })
}
const onDelete = tagIndex => {
  emits('close', { index: props.index, tagIndex })
}

let draggedIndex = -1
let draggedTagIndex = -1
let originTagIndex = 0
const onDragStart = tagIndex => {
  draggedIndex = props.index
  draggedTagIndex = tagIndex
}

const moveVisible = ref(false)
const moveOk = () => {
  emits('ok')
  moveCompleted()
}

const moveCancel = () => {
  emits('cancel')
  const draggedItem = tagsData.value[originTagIndex]
  tagsData.value.splice(originTagIndex, 1)
  tagsData.value.splice(draggedTagIndex, 0, draggedItem)
  moveCompleted()
}

const moveCompleted = () => {
  moveVisible.value = false
  draggedIndex = draggedTagIndex = -1
}

const onDrop = tagIndex => {
  if (draggedIndex === -1 || draggedTagIndex === -1 || draggedIndex !== props.index || draggedTagIndex === tagIndex) {
    draggedIndex = draggedTagIndex = -1
    return
  }
  originTagIndex = tagIndex
  // 将拖动的元素移动到目标位置
  const draggedItem = tagsData.value[draggedTagIndex]
  tagsData.value.splice(draggedTagIndex, 1)
  tagsData.value.splice(originTagIndex, 0, draggedItem)
  moveVisible.value = true
}
</script>
