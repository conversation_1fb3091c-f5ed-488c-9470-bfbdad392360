<template>
  <div>
    <div @click="openModal">
      <a v-if="!value.jump_type">{{ tip }} <RightOutlined /></a>
      <a v-else>
        {{ jumpLabel }} {{ value.appid }} {{ jumpValue }}
      </a>
    </div>
    <a-modal :title="title" v-bind="{ visible }" @cancel="onCancel" @ok="onSubmit">
      <a-form
        ref="addForm"
        layout="horizontal"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 20 }"
        :rules="rules"
        :model="formState"
      >
        <a-form-item label="跳转方式" name="jump_type" class="required">
          <a-select v-model:value="formState.jump_type" :options="jumpTypeList" class="select-width" @change="onChangeJumpType" />
        </a-form-item>
        <a-form-item v-if="formState.jump_type == jumpType.miniprogram" label="appid" name="appid" class="required">
          <a-input v-model:value.trim="formState.appid" placeholder="请输入appid" />
        </a-form-item>
        <a-form-item v-if="showJumpLink" label="跳转链接" name="jump_link" class="required">
          <a-input v-model:value.trim="formState.jump_link" placeholder="请输入链接URL" />
        </a-form-item>
        <a-form-item v-if="formState.jump_type == jumpType.prize" label="活动发券" name="jump_link" class="required">
          <a-select
            v-model:value.trim="formState.jump_link"
            class="select-width"
            :options="activityList"
            placeholder="请选择活动发券"
            :disabled="formState.jump_type != jumpType.prize"
            show-search
            :show-arrow="false"
            :filter-option="false"
            :not-found-content="null"
            @search="onSearchActivityCoupon"
          />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { ref } from 'vue'
import { RightOutlined } from '@ant-design/icons-vue';
import { activityCouponApi } from '@/api/common'
import { debounce } from 'lodash'
import { jumpType } from '@/enums/jump';

const props = defineProps({
  title: {
    type: String,
    default: "跳转配置"
  },
  tip: {
    type: String,
    default: "跳转地址"
  },
  // 特殊情况忽略一些跳转类型
  ignoreJumps: {
    type: Array,
    default: ()=> []
  },
  value: {
    type: Object,
    default: ()=> {}
  }
});
const emit = defineEmits(['update:value'])

const jumpTypeList = computed(()=> jumpType.options().filter(item=> !props.ignoreJumps.includes(item.value)))

const jumpLabel = computed(()=> jumpTypeList.value.find(item=> item.value === props.value.jump_type)?.label)

const jumpValue = computed(()=> {
  if(props.value.jump_type === 'prize') {
    return activityList.value.find(item=> item.value === props.value.jump_link)?.label
  }
  return props.value.jump_link
})

// 校验跳转链接
const validJumpLink = () => {
  if (formState.value.jump_type === 'prize' && !formState.value.jump_link) return Promise.reject('请选择活动发券')
  if (!formState.value.jump_link) return Promise.reject('请输入链接URL')
  return Promise.resolve(true)
}

const rules = ref({
  jump_type: { required: true, message: "请输入跳转方式", trigger: "blur" },
  appid: { required: true, message: "请输入appid", trigger: "blur" },
  jump_link: [{ required: true, validator: validJumpLink, trigger: 'blur' }]
});

const { formState, setFormState , resetFormState } = useFormState();

const showJumpLink = computed(()=> !['contact' , 'prize' , 'no'].includes(formState.value.jump_type))

const setJumpFormState = ()=> {
  const { jump_type , jump_link , appid } = props.value
  setFormState({ jump_type , jump_link , appid })
}

const visible = ref(false)
const openModal = ()=> {
  visible.value = true
  setJumpFormState()
}
const onCancel = ()=> {
  visible.value = false
}

const addForm = ref(null)
const onSubmit = () => {
  //触发表单验证
  addForm.value.validate().then(
    () => {
      emit('update:value', formState.value)
      onCancel()
    }
  )
}

const onChangeJumpType = (value)=> {
  resetFormState()
  setFormState({ jump_type: value })
}

// 优惠券活动
const activityList = ref([])
const useActivityCoupon = async (filters = {}) => {
  const res = await activityCouponApi.paginator({
    filters,
    offset: 1,
    limit: 20,
  })
  activityList.value = res.items.map(item => ({ value: item.id , label: item.title }))
}

const onSearchActivityCoupon = debounce(title => useActivityCoupon({ title: `%${title}%`, }), 500)

onMounted(()=> {
  useActivityCoupon()
})
watch(()=> props.value, ()=> {
  setJumpFormState()
})
</script>

