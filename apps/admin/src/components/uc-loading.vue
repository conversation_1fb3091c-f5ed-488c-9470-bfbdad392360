<template>
  <div v-if="show" class="index-wrap">
    <div>
      <a-spin size="large" />
    </div>
  </div>
</template>

<script setup>
// import { defineProps } from 'vue'
const emit = defineEmits(['update:show'])
const prop = defineProps({
  show: {
    default: false,
    type: <PERSON><PERSON>an
  }
})
</script>
<style lang="less" scoped>
.index-wrap {
  width: 100%;
  height: 100%;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 99999;
  background: rgba(225, 225, 225, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
