<template>
  <!-- 动态设置 ant-design-vue 图标 -->
  <component :is="CurIcon" />
</template>
<script>
import { createVNode, computed } from 'vue'
import * as icons from '@ant-design/icons-vue'
export default {
  name: 'UcAntIcon'
}
</script>
<script setup>
const props = defineProps({
  // 名称
  name: {
    type: String,
    required: true
  },
  // 类型
  type: {
    type: String,
    default: '' // primary|success|info|warning|danger
  }
})
const CurIcon = computed(() => createVNode(icons[props.name], { class: ['cur-icon', props.type] }))
</script>
<style lang="less">
.cur-icon {
  @type: {
    primary: @color-primary;
    success: @color-success;
    info: @color-info;
    warning: @color-warning;
    danger: @color-danger;
  };

  each(@type, {
      &.@{key} {
        color: @value !important;
      }
    }
  );
}
</style>
