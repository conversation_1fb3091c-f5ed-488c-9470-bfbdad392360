<template>
  <a-image-preview-group>
    <a-space>
      <a-image v-for="(url, index) of urls" :key="index" :width="width" :height="height" :src="url" />
    </a-space>
  </a-image-preview-group>
</template>

<script setup>
const props = defineProps({
  // 名称
  urls: {
    type: Array,
    required: true
  },
  width: {
      type: Number,
      default: 50
  },
  height: {
      type: Number,
      default: 50
  }
})
</script>

<style>
.ant-image-img {
  object-fit: contain;
  height: 100%;
}
</style>