<template>
  <a-modal v-bind="{ visible }" title="选择商品" width="1000px" @cancel="onClose">
    <a-form layout="inline" class="m-b-20">
      <a-form-item name="title">
        <a-input-group class="flex" compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            :options="skuOrTitleType.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键词" class="w-300" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-model:value="formState.category_id"
          placeholder="商品分类"
          :options="categories"
          :field-names="{ label: 'title', value: 'id' }"
          change-on-select
          class="w-120"
        />
      </a-form-item>
      <a-form-item>
        <a-input v-model:value="formState.minPrice" placeholder="最低价格" class="w-100" />
        <span class="p-10">-</span>
        <a-input v-model:value="formState.maxPrice" placeholder="最高价格" class="w-100" />
      </a-form-item>
      <a-form-item class="m-r-0">
        <a-button type="primary" class="m-r-10" @click="setPage()"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </a-form>
    <!-- 列表 -->
    <template v-if="grid">
      <a-card v-if="loading" v-bind="{ loading }" />
      <ul v-else class="m-list">
        <li v-for="item in listData" :key="item.id" :class="{ active: item.selectState }" @click="onClick(item)">
          <uc-img-text
            :url="item.photo_url"
            :title="`${getSpecs(item)} - ${item.goods.title}`"
            :subtit="`¥${$formatters.thousandSeparator(item.price)}`"
            subtit-color="#f56c6c"
          />
          <img class="u-triangle" :src="assets.selected" alt="选中三角" />
        </li>
      </ul>
      <div v-if="isMultiple" class="flex flex-sb flex-cc">
        <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="updateSelectState">
          全选筛选条件下所有商品
        </a-checkbox>
        <a-pagination v-bind="stdPagination(data)" @change="onShowSizeChange" @show-size-change="onShowSizeChange" />
      </div>
    </template>
    <template v-else>
      <a-table
        :data-source="listData"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        :row-selection="rowSelection"
        :scroll="{ y: 500 }"
        @change="setPage"
      >
        <a-table-column title="商品图片" width="150px">
          <template #default="{ record }">
            <a-image :src="record.photo_url" :width="50" :height="50" style="height: 100%" />
          </template>
        </a-table-column>
        <a-table-column title="商品名称" width="250px">
          <template #default="{ record }">
            {{ record.goods.title }}
          </template>
        </a-table-column>
        <a-table-column title="商品规格" width="150px">
          <template #default="{ record }">
            {{ getSpecs(record) }}
          </template>
        </a-table-column>
        <a-table-column title="销售价格" width="100px" align="right">
          <template #default="{ record }">
            {{ `¥${$formatters.thousandSeparator(record.price)}` }}
          </template>
        </a-table-column>
        <slot name="stock-column"></slot>
      </a-table>
    </template>
    <template #footer>
      <div class="flex flex-sb flex-cc">
        <div class="flex flex-cc">
          <div>
            已选择 <span class="color-danger">{{ grid ? getTempSelectNum : selectedKeys.length }}</span> 个商品
          </div>
          <a-button v-if="grid" type="link" @click="updateSelectState(false)"> 清空 </a-button>
        </div>
        <div>
          <a-button @click="onClose"> 取消 </a-button>
          <a-button type="primary" @click="onSubmit"> 确认 </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: 'SelectSkuGoods'
}
</script>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useCategories } from '@/modules/goods/useCategory'
import { useTransformList, useSpecAttr } from '@/modules/promotion/useTransform'
import { skuOrTitleType, goodsSelectType } from '@/modules/promotion/enums'
import assets from '@/modules/promotion/assets.config'
import { goodsSpecsApi } from '@/modules/promotion/api'
import { message } from 'ant-design-vue'

const emit = defineEmits(['update:visible', 'ok', 'cancel'])
const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: goodsSelectType.multiple
  },
  // 是否网格展示
  grid: {
    type: Boolean,
    default: false
  },
  // 过滤商品
  filterGoods: {
    type: String,
    default: () => {}
  },
  // 库存初始值
  initialStock: {
    type: Number,
    default: 0
  },
  activityMode: {
    type: String,
    default: ''
  }
})

// 商品分类
const { categories } = useCategories()

const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: 'sku',
  conditionValue: undefined,
  category_id: [],
  minPrice: undefined,
  maxPrice: undefined
})
const getSpecs = computed(() => item => useSpecAttr(item.attrs)) // 获取规格

const getSku = () => (formState.value.conditionKey === 'sku' ? formState.value.conditionValue : undefined)

const getCategoryId = () =>
  formState.value.category_id.length
    ? { goods: { category_id: formState.value.category_id[formState.value.category_id.length - 1] } }
    : {}

// 获取标题
const getTitle = () => {
  const option = {}
  const title = formState.value.conditionKey == 'title' ? formState.value.conditionValue : undefined
  title && (option.goods = { title: `%${title}%` })
  return option
}

// 获取价格
const getPrice = () => {
  const price = []
  if (formState.value.minPrice) {
    price[0] = formState.value.minPrice * 100
  }
  if (formState.value.maxPrice) {
    price[1] = formState.value.maxPrice * 100
  }
  return price
}

const { data, setPage, page, loading } = usePaginatorApiRequest(
  ({ offset, limit }) =>
    goodsSpecsApi.paginator({
      filters: useTransformQuery(
        {
          price: getPrice(),
          sku: getSku(),
          ...props.filterGoods
        },
        { title: 'like', price: 'range', sku: 'like' }
      ),
      relation_filters: {
        ...getCategoryId(),
        ...getTitle()
      },
      relations: ['goods', 'stock'],
      offset,
      limit
    }),
  { current: 1, pageSize: props.grid ? 20 : 10 }
)

const onShowSizeChange = (current, pageSize) => {
  setPage({ current, pageSize })
}

// 获取规格
const getSpecName = computed(() => item => useSpecAttr(item.attrs))

// 关闭显示状态
const onClose = () => {
  selectedGoods.value = []
  selectedKeys.value = []
  emit('update:visible', false)
  emit('cancel')
}

const listData = ref([]) // 表格数据

watch(
  () => props.visible,
  value => {
    if (value) {
      tempData.value.length = 0
      onRestFormState(() => {
        setPage()
      })
      resetFormState()
    }
  }
)
watch(
  data,
  ({ items }) => {
    listData.value = useTransformList(items).map(item => ({
      ...item,
      initialStock: props.initialStock
    }))
  },
  { deep: true }
)

const tempData = ref([]) // 临时数据

// 获取临时选中的数量
const getTempSelectNum = computed(
  () => listData.value.reduce((prev, item) => prev + +item.selectState, 0) + tempData.value.length
)

// 设置选中状态
const updateSelectState = e => {
  const state = typeof e === 'boolean' ? e : e.target.checked
  listData.value.forEach(item => {
    item.selectState = state
  })
  // if (state && listData.value.length === page.value.pageSize) {
  if (state) {
    goodsSpecsApi
      .list({
        filters: useTransformQuery(
          {
            price: [+(formState.value.minPrice || 0) * 100, +(formState.value.maxPrice || 0) * 100],
            sku: getSku()
          },
          { title: 'like', price: 'range' }
        ),
        relation_filters: {
          ...getCategoryId(),
          ...getTitle()
        },
        relations: ['goods']
      })
      .then(data => {
        tempData.value = data.filter(item => !listData.value.some(it => it.sku === item.sku))
      })
  } else {
    tempData.value.length = 0
  }
}

// 全选框的部分选中状态
const indeterminate = computed(() => {
  const len = listData.value.filter(item => item.selectState).length
  return len < listData.value.length && len > 0
})

// 全选状态,和 indeterminate 配合
const checkAll = computed(() => listData.value.some(item => item.selectState))

// 点击列表
const onClick = item => {
  if (isMultiple.value) {
    item.selectState = !item.selectState
  } else {
    listData.value.forEach(it => (it.selectState = false))
    item.selectState = true
  }
}

// 点击确定按钮
const onSubmit = () => {
  const list = props.grid ? listData.value.filter(item => item.selectState).concat(tempData.value) : selectedGoods.value
  console.log('list ==> ', list)
  if (!list.length) {
    message.error('请选择商品')
  } else {
    emit('ok', list)
    if (props.activityMode === '') {
      onClose()
    }
  }
}

// 是否是多选
const isMultiple = computed(() => props.mode === goodsSelectType.multiple)

// table列表选择的id
const selectedGoods = ref([])
const selectedKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedKeys,
  type: props.mode === goodsSelectType.multiple ? 'checkbox' : 'radio',
  onChange: selectedRowKeys => {
    selectedKeys.value = selectedRowKeys
    const ids = selectedGoods.value.map(item => item.id)
    const diffIds = selectedRowKeys.filter(key => !ids.includes(key))
    const addGoods = listData.value.filter(item => diffIds.includes(item.id))
    selectedGoods.value.push(...addGoods)
  }
}
</script>
<style lang="less" scoped>
.m-list {
  display: flex;
  flex-wrap: wrap;

  li {
    position: relative;
    width: 229px;
    border: 1px solid #ccc;
    border-radius: 4px;
    margin: 0 12px 10px 0;
    padding: 10px;
    break-inside: avoid;

    .m-column-item {
      .u-tit>span {
        font-size: 12px;
        font-weight: 400;
        margin-bottom: 5px;
      }

      .u-subtit>span:last-child {
        font-size: 14px;
        font-weight: 500;
      }
    }

    cursor: pointer;

    &:nth-child(4n + 4) {
      margin-right: 0;
    }

    .u-triangle {
      position: absolute;
      right: 0;
      bottom: 0;
      display: none;
      width: 22px;
    }

    &.active {
      border-color: @color-primary;

      .u-triangle {
        display: block;
      }
    }
  }
}
</style>
