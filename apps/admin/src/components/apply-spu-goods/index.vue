<template>
  <a-card v-bind="{ title }">
    <template #extra>
      <a-space :size="10">
        <a-button type="primary" :disabled="disabled" @click="onAdd">
          添加商品
        </a-button>
        <a-button type="primary" :disabled="disabled" @click="updateVisibleModal(true)">
          选择商品
        </a-button>
      </a-space>
    </template>
    <a-table :data-source="tableList" :pagination="{ showTotal: total => `共${total}条` }" row-key="id">
      <a-table-column title="商品信息">
        <template #default="{ record }">
          <uc-img-text
            :url="record.photo_urls[0]"
            :title="record.title"
            :subtit="getCategory(categories, record.category_id)"
            :label="record.id"
            symbol=":&nbsp"
          />
        </template>
      </a-table-column>
      <a-table-column title="最低价" width="120px" align="right">
        <template #default="{ record }">
          {{ $formatters.thousandSeparator(record.floor_price) }}
        </template>
      </a-table-column>
      <a-table-column title="可售/已售" width="150px">
        <template #default="{ record }">
          {{ record.surplus_num + '/' + record.sale_num }}
        </template>
      </a-table-column>
      <a-table-column title="操作" width="70px">
        <template #default="{ index }">
          <a-button type="link" class="color-danger" :disabled="disabled" @click="onDelete(index)">
            移除
          </a-button>
        </template>
      </a-table-column>
    </a-table>
  </a-card>
  <select-spu-goods v-model:visible="visibleModal" :grid="grid" @ok="onSubmit" />
  <a-modal title="添加商品" :visible="visibleAdd" width="520px" @ok="onSure" @cancel="updateVisibleAdd(false)">
    <a-textarea v-model:value="addTextarea" :rows="10" placeholder="请输入商品 id，一行一个" />
  </a-modal>
</template>
<script>
export default {
  name: 'ApplySpuGoods'
}
</script>
<script setup>
import { message } from 'ant-design-vue'
import { goodsApi } from '@/modules/promotion/api'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { getCategory, useCategories } from '@/modules/goods/useCategory'

const emit = defineEmits(['update:list', 'import'])
const props = defineProps({
  // 标题
  title: {
    type: String,
    default: ''
  },
  // 表格数据
  list: {
    type: Array,
    default: () => []
  },
  // 禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否网格展示
  grid: {
    type: Boolean,
    default: false
  },
})

const tableList = ref([]) // 表格数据

// 商品分类
const { categories } = useCategories()

// 加载spu表格数据
const loadSpuTableData = id =>
  goodsApi.list({
    filters: useTransformQuery(
      {
        id
      },
      {}
    ),
  })

watch(
  () => props.list,
  value => {
    if (value.length > 0 && !tableList.value.length) {
      loadSpuTableData(value).then(data => {
        tableList.value = data
      })
    }
  },
  {
    immediate: true,
    deep: true
  }
)

// 更新列表
const updateList = () => {
  const ids = tableList.value.map(item => item.id)
  emit('update:list', ids)
}

// 点击移除按钮
const onDelete = index => {
  tableList.value.splice(index, 1)
  updateList()
}

const visibleModal = ref(false) // 选择模态框

const updateVisibleModal = value => {
  visibleModal.value = value
}

// 操作模块确认
const onSubmit = list => {
  list.forEach(item => {
    !tableList.value.some(it => it.id === item.id) && tableList.value.push(item)
  })
  updateList()
}

const visibleAdd = ref(false) // 添加商品模态框显示状态
const addTextarea = ref('') // 新增的

// 更新添加商品模态框状态
const updateVisibleAdd = value => {
  visibleAdd.value = value
}
// 点击添加商品按钮
const onAdd = () => {
  addTextarea.value = ''
  updateVisibleAdd(true)
}

// 点击添加商品模态框确认
const onSure = () => {
  if (addTextarea.value) {
    const ids = addTextarea.value.split('\n')
    loadSpuTableData(ids).then(data => {
      if (data.length) {
        const list = data.filter(item => !tableList.value.some(it => it.id === item.id))
        tableList.value.push(...list)
        updateList()
      } else {
        message.error('此 id 不存在spu')
      }
      updateVisibleAdd(false)
    })
  } else {
    message.error('请输入商品 id')
  }
}
</script>
