<template>
  <div class="uc-svg-icon">
    <svg class="svg-icon" aria-hidden="true">
      <use :xlink:href="symbolId" />
    </svg>
  </div>
</template>

<script>
export default defineComponent({
  name: 'UcSvgIcon',
  props: {
    name: {
      type: String,
      required: true
    },
    size: {
      type: String,
      default: '20px'
    },
    color: {
      type: String,
      default: '#f8f8f8'
    }
  },
  setup(props) {
    const symbolId = computed(() => `#${props.name}`)
    return { symbolId }
  }
})
</script>

<style scoped lang="less">
.uc-svg-icon {
  font-size: v-bind(size) !important;
  color: v-bind(color);
  .svg-icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
    color: inherit;
  }
}
</style>
