<script lang="ts" setup>
import { useDefineTemplate } from '@/composables/useDefineTemplate'

const [DefineTemplate, ReuseTemplate] = useDefineTemplate()

const props = defineProps({
  size: {
    type: Number,
    default: 40
  },
  src: {
    type: String,
    default: ''
  },
  nickname: {
    type: String,
    default: ''
  },
  tooltip: {
    type: Boolean,
    default: false
  },
  shape: {
    type: String,
    default: 'circle' // circle | square
  }
})
const getRoundedClass = computed(() => {
  switch (props.shape) {
    case 'circle':
      return 'rounded-1/2'
    case 'square':
      return 'rounded-6'
    default:
      return ''
  }
})
</script>

<template>
  <DefineTemplate>
    <a-avatar v-if="src" v-bind="{ size, src }" />
    <div
      v-else-if="nickname"
      class="bgc-primary color-white flex flex-center"
      :class="[getRoundedClass]"
      :style="{ width: `${size}px`, height: `${size}px` }"
    >
      {{ nickname[0] }}
    </div>
  </DefineTemplate>
  <a-tooltip v-if="tooltip">
    <template #title>
      {{ nickname }}
    </template>
    <ReuseTemplate />
  </a-tooltip>
  <ReuseTemplate v-else />
</template>
