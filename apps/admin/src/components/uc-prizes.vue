<template>
  <div class="u-prizes">
    <div class="prize-item margin-bottom-14">
      <div v-for="(prize, index) in props.list" :key="index" class="flex flex-cc flex-wrap margin-bottom-14">
        <a-select
          v-model:value="prize.type"
          :disabled="props.disabled"
          style="width: 120px"
          show-search
          :allow-clear="true"
          :filter-option="filterOption"
          placeholder="选择类型"
          class="margin-right-10 margin-bottom-10"
          @change="handleUpdate"
        >
          <a-select-option v-for="(op, opindex) in prizeTypeOptions" :key="opindex" :value="op.value">
            {{ op.label }}
          </a-select-option>
        </a-select>
        <a-input
          v-model:value.trim="prize.number"
          :disabled="props.disabled"
          style="width: 120px"
          placeholder="奖品数量"
          class="margin-right-10 margin-bottom-10"
          allow-clear
          @input="handleUpdate()"
        />
        <a-select
          v-model:value="prize.name"
          :disabled="props.disabled"
          placeholder="选择礼品"
          class="flex-auto margin-right-10"
          @change="handleUpdate"
        >
          <a-select-option v-for="(op, opindex) in prizeOptions" :key="opindex" :value="op.value">
            {{ op.label }}
          </a-select-option>
        </a-select>
        <uc-ant-icon
          v-if="!props.disabled"
          name="CloseCircleOutlined"
          :style="props.list.length == 1 ? 'cursor:not-allowed;' : ''"
          :type="props.list.length > 1 ? 'danger' : 'info'"
          @click="handleRemove(index)"
        />
      </div>
    </div>
    <a-button :disabled="props.disabled" style="padding: 0" type="link" @click="handleAdd">
      添加奖励层级
    </a-button>
  </div>
</template>
<script>
export default {
  name: 'UcPrizes'
}
</script>
<script setup>
import { ref } from 'vue'
const emit = defineEmits(['add', 'remove', 'update'])
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  disabled: {
    type: Boolean,
    required: false
  }
})
const prizeTypeOptions = ref([{ label: '实物礼品' }, { label: '会员积分' }])
const prizeOptions = ref([
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' },
  { sku: '23988765214873', label: '芭比娃娃' },
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' },
  { sku: '23988765214873', label: '芭比娃娃' },
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' },
  { sku: '23988765214873', label: '芭比娃娃' },
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' },
  { sku: '23988765214873', label: '芭比娃娃' },
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' },
  { sku: '23988765214873', label: '芭比娃娃' },
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' },
  { sku: '23988765214873', label: '芭比娃娃' },
  { sku: '9823642339487', label: '新款ipone13pro' },
  { sku: '45678328989', label: '手机壳' }
])

const filterOption = (input, option) => {
  // console.log('input, option', input, option)
}
const handleAdd = () => emit('add')
const handleRemove = (index) => props.list.length > 1 && emit('remove', index)
const handleUpdate = () => emit('update', props.list)
</script>
<style scoped lang="less"></style>
