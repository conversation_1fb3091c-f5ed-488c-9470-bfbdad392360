export default {
  title: import.meta.env.ADMIN_TITLE,

  api: {
    limit: 10,
    baseURL: `${import.meta.env.VITE_API_BASE_URL}`
  },
  upload: {
    url: `${import.meta.env.VITE_API_BASE_URL}/image-upload`,
    image: {
      limit: 1024 * 1024 * 8,
      allow: ['jpg', 'jpeg', 'png', 'svg', 'bmp', 'gif'],
      accept: 'image/jpg, image/jpeg, image/png, image/svg+xml, image/bmp, image/gif',
      tips: '图片格式支持：png,jpeg,jpg；不可大于2M；建议使用png格式图片，以保持最佳效果；',
      label: '图片'
    },
    video: {
      limit: 1024 * 1024 * 100,
      allow: ['mp4'],
      accept: 'video/mp4',
      tips: '视频格式支持：mp4；不可大于100M；',
      snapshot:
        import.meta.env.VITE_FILESYSTEM_DRIVER === 'cos'
          ? 'ci-process=snapshot&time=1&format=jpg'
          : 'x-oss-process=video/snapshot,t_0,f_jpg,w_0,h_0,m_fast,ar_auto',
      label: '视频'
    }
  },
  featureConfig: {
    member: {
      hideCredit: import.meta.env.VITE_HIDE_MEMBER_CREDIT_DRIVER === 'true',
      hideGrade: import.meta.env.VITE_HIDE_MEMBER_GRADE_DRIVER === 'true'
    }
  }
}
