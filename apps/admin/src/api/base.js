import config from '@/config'
import axiosInstance from '@/utils/request'
// 基础 api
export class ApiBase {
  constructor() {
    this.baseURL = config.api.baseURL
    this.resourcePath = ''
    this.fullPath = ''
    this.notAllowUpdateProps = []
    this.overrideMethod = undefined
  }

  setOverrideMethod(method) {
    this.overrideMethod = method
    return this
  }

  async request(path, method = 'GET', data = undefined, query = undefined) {
    const url = `${this.baseURL}${path}`
    const opts = { url, method }
    if (method === 'GET') {
      opts.params = data
    } else {
      opts.data = data
      opts.params = query
    }
    if (this.overrideMethod) {
      opts.headers = {
        'X-HTTP-METHOD-OVERRIDE': this.overrideMethod
      }
      this.overrideMethod = undefined
    }
    const response = await axiosInstance.request(opts)

    return response
  }

  buildQueryParams(queryParams) {
    const {
      filters,
      relations,
      relation_filters,
      sorts,
      fields,
      limit = config.api.limit,
      offset = 1,
      all,
      ...residue
    } = queryParams

    if (relation_filters) {
      Object.keys(relation_filters).forEach(key => {
        let relationObj = relation_filters[key]
        Object.keys(relationObj).forEach(relationKey => {
          if (relationObj[relationKey] === undefined) {
            delete relationObj[relationKey]
          }
        })
        if (JSON.stringify(relationObj) === '{}') {
          delete relation_filters[key]
        }
      })
    }

    if (residue) {
      Object.keys(residue).forEach(key => !residue[key] && delete residue[key])
    }

    const restParams = {
      filters: filters ? JSON.stringify(filters) : undefined,
      relations: relations ? relations.join(',') : undefined,
      relation_filters: relation_filters ? JSON.stringify(relation_filters) : undefined,
      sorts: sorts ? sorts.join(',') : undefined,
      fields: fields ? JSON.stringify(fields) : undefined,
      ...residue
    }

    if (!all) {
      restParams.limit = limit
      restParams.offset = offset
    }
    const newParams = {}
    Object.keys(restParams).forEach(key => {
      if (restParams[key] !== undefined) {
        newParams[key] = restParams[key]
      }
    })
    newParams
    return newParams
  }

  getNotAllowedProps() {
    return this.notAllowUpdateProps || []
  }

  async paginator(queryParams = {}) {
    return this.request(this.resourcePath, 'GET', this.buildQueryParams(queryParams))
  }

  async paginatorByPath(path, queryParams) {
    return this.request(`${this.resourcePath}/${queryParams.pathId}/${path}`, 'GET', this.buildQueryParams(queryParams))
  }

  async list(queryParams = {}) {
    return await this.paginator({
      ...queryParams,
      all: true
    })
  }

  async get(id, relations = {}) {
    return this.request(`${this.resourcePath}/${id}`, 'GET', this.buildQueryParams(relations))
  }

  async createByPath(path, data) {
    this.getNotAllowedProps().forEach(prop => delete data[prop])
    return this.request(`${this.resourcePath}/${path}`, 'POST', data)
  }


  async create(data) {
    this.getNotAllowedProps().forEach(prop => delete data[prop])
    return this.request(`${this.resourcePath}`, 'POST', data)
  }

  async update(id, data) {
    this.getNotAllowedProps().forEach(prop => delete data[prop])
    return this.request(`${this.resourcePath}/${id}`, 'PATCH', data)
  }

  async updateByPath(path, data) {
    this.getNotAllowedProps().forEach(prop => delete data[prop])
    return this.request(`${this.resourcePath}/${data.id}/${path}`, 'PATCH', data)
  }

  async replace(id, data) {
    this.getNotAllowedProps().forEach(prop => delete data[prop])
    return this.request(`${this.resourcePath}/${id}`, 'PUT', data)
  }

  async delete(id) {
    return this.request(`${this.resourcePath}/${id}`, 'DELETE')
  }
}

// 基础 factory
export const baseFactory = baseURL => {
  return {
    restful(path, notAllowUpdateProps = []) {
      const baseApi = new ApiBase()
      baseApi.baseURL = baseURL
      baseApi.resourcePath = path
      baseApi.fullPath = baseURL + path
      baseApi.notAllowUpdateProps = notAllowUpdateProps
      return baseApi
    },
    command(path) {
      const baseApi = new ApiBase()
      baseApi.baseURL = baseURL
      return {
        get: (query = {}) => baseApi.request(path, 'GET', query),
        post: (data, query = {}) => baseApi.request(path, 'POST', data, query),
        // put: (data) => baseApi.request(path, 'PUT', data),
        patch: (data) => baseApi.request(path, 'PATCH', data),
      }
    }
  }
}
