const adminRouteConfig = {
  articleList: {title: '条款政策', path: 'pages/cms/article-list/index'},
  articleDetail: {title: '条款政策详情', path: 'pages/cms/article-detail/index'},
  goodsList: {title: '商品列表', path: 'pages/goods/list/index'},
  goodsDetail: {title: '商品详情', path: 'pages/goods/detail/index'},
  specialPage: {title: '专题页', path: 'pages/special-page/index'},
  couponReceive: {title: '领取优惠券', path: 'pages/coupon/receive/index'},
  fullMinusList: {title: '满减促销', path: 'pages/promotion/full-minus/list/index'},
  giftList: {title: '赠品促销', path: 'pages/promotion/gift/list/index'},
  crowdfundingDetail: {title: '拼团商品详情', path: 'pages/promotion/crowdfunding/detail/index'},
  discountList: {title: '限时折扣', path: 'pages/promotion/discount/list/index'},
  freightList: {title: '运费促销', path: 'pages/promotion/freight/list/index'},
  siteList: {title: '现场活动', path: 'pages/promotion/site/list/index'},
  wholesaleList: {title: '团购列表', path: 'pages/promotion/wholesale/list/index'},
  signin: {title: '签到有礼', path: 'pages/signin/index'},
  turntable: {title: '幸运大转盘', path: 'pages/draw/turntable/index'},
  invite: {title: '邀请有礼', path: 'pages/invitation/index'},
  authRegister: {title: '注册', path: 'pages/member-register/index'},
  distributorApply: {title: '分销商申请链接', path: 'pages/distribution/apply/index'},
  customVoiceVerify: { title: '语音定制验证', path: 'pages/custom/voice-verify/index' },
  giftBoxList: { title: '定制礼盒列表', path: 'pages/promotion/gift-box/index' },
  internalPurchase: { title: '内购', path: 'pages/internal-purchase/product/index'}
}

export default adminRouteConfig
