/**
 * 跳转方式[弹窗]
 */
export const jumpType = Object.freeze({
  /**
   * 网页
   */
  webview: 'webview',
  /**
   * 页面
   */
  page: 'page',
  /**
   * 小程序
   */
  miniprogram: 'miniprogram',
  /**
   * 客服
   */
  contact: 'contact',
  /**
   * 不跳转
   */
  no: 'no',
  /**
   * 分享
   */
  share: 'share',
  /**
   * 领券
   */
  prize: 'prize',

  options() {
    return [
      { label: 'Page', value: this.page },
      { label: '网页', value: this.webview },
      { label: '小程序', value: this.miniprogram },
      { label: '客服', value: this.contact },
      { label: '分享', value: this.share },
      { label: '领券', value: this.prize },
      { label: '不跳转', value: this.no }
    ]
  }
})