/**
 * 日期选择形式
 */
export const dateSelectVuale = Object.freeze({
  /**
   * 月
   */
  month: 'month',

  /**
   * 周
   */
  week: 'week',

  /**
   * 日
   */
  day: 'day',

  options() {
    return [
      { label: '日', value: this.day },
      { label: '周', value: this.week },
      { label: '月', value: this.month },
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})