import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import mixin from './mixin'
import './styles/base.less'
import 'virtual:svg-icons-register'

import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import formatters from './utils/formatters'
import Tmap from '@map-component/vue-tmap'
import assets from './config/assets.config'
import qrcode from './plugins/qrcode'

const app = createApp(App)

app.config.productionTip = false
app.config.globalProperties.assets = assets

// disable antd warning
console.warn = () => null

app.use({
  install: app => {
    // 全局数据formatters
    app.config.globalProperties.$formatters = formatters
  }
})
app.use(qrcode)

app.use(router).use(Antd).use(Tmap).mixin(mixin).mount('#app')
