import { useStore } from '@/store/auth'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'
import routes from '@/router/module-routes'

// 全局前置守卫
export const beforeEach = ({ name }) => {
  // NProgress 进度条
  NProgress.start()
  const { state } = useStore()
  if (state.token) {
    const { permissions } = state.userInfo // 权限
    const defaultRouteName =
      permissions.length == 1 && permissions[0] != '*'
        ? permissions[0]
        : import.meta.env.VITE_DEFAULT_ROUTE || 'daily-data'
    return name === defaultRouteName ||
      permissions.includes('*') ||
      permissions.includes(name) ||
      routes.some(item => item?.children?.find(it => it.name === name)?.hidden)
      ? name !== 'auth-login' || { name: defaultRouteName }
      : name === '403' || { name: '403' }
  } else {
    return name === 'auth-login' || { name: 'auth-login' }
  }
}
// 全局后置守卫
export const afterEach = () => {
  NProgress.done()
}
