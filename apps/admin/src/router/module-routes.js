import ignoreFactory from 'ignore'

// 定义模块菜单排序
const modules = [
  'auth',
  'data',
  'page-config',
  'gift',
  'goods',
  'order',
  'custom',
  'forum',
  'activity',
  'promotion',
  'invite',
  'new-distribution',
  'product-register',
  'member',
  'admin',
  'cms',
  'security',
  'setting',
  'internal-purchase'
]

const routes = []
const routeModulesImported = import.meta.globEager(`../modules/**/routes.js`)
const extraPluginModulesImported = import.meta.globEager(`../extra-plugins/**/routes.js`)

const hiddenRules = import.meta.env.VITE_ADMIN_HIDDEN_MENU || ''
const ignore = ignoreFactory().add(hiddenRules.split('|'))

const loadModules = function (modules, modulesImported, basePath = '../modules') {
  modules.forEach(m => {
    const key = `${basePath}/${m}/routes.js`
    const module = modulesImported[key].default
    module.children = module.children.filter(item => {
      const path = module.path + '/' + item.path
      if (!ignore.ignores(path)) {
        return true
      }
      return false
    })

    if (module.children.length > 0) {
      const existingModuleIndex = routes.findIndex(r => r.path === module.path)
      if (existingModuleIndex !== -1) {
        // 合并 children
        routes[existingModuleIndex].children = [
          ...routes[existingModuleIndex].children,
          ...module.children
        ]
      } else {
        routes.push(module)
      }
    }
  })
}

loadModules(modules, routeModulesImported)
loadModules(
  Object.keys(extraPluginModulesImported).map(x => x.split('/')[2]),
  extraPluginModulesImported,
  '../extra-plugins'
)

export default routes
