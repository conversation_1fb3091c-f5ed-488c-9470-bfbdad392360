import { createRouter, createWebHistory } from 'vue-router'
import { beforeEach, afterEach } from './guard'
import moduleRoutes from './module-routes'
import { useStore } from '@/store/auth'

const baseComponent = () => import('@/views/layout/base') // 基础组件

// 添加 component
moduleRoutes.forEach(item => {
  !item.component && (item.component = baseComponent)
})

const routes = [
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  },
  {
    path: '/',
    name: 'admin_home',
    component: () => import('@/views/layout'),
    redirect: () => {
      const permissions = useStore().getUserInfo().permissions
      if (permissions && permissions.length == 1 && permissions[0] != '*') {
        return { name: permissions[0] }
      }
      return { name: import.meta.env.VITE_DEFAULT_ROUTE || 'daily-data' }
    },
    children: [
      {
        path: '/403',
        name: '403',
        component: () => import('@/views/error-status/403')
      },
      ...moduleRoutes
    ]
  },
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error-status/404')
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})
router.beforeEach(beforeEach)
router.afterEach(afterEach)

export default router
