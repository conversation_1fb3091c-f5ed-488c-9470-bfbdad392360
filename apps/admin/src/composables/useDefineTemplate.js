import { defineComponent, shallowRef } from 'vue'

function cacheStringFunction(fn) {
  const cache = Object.create(null)
  return str => {
    const hit = cache[str]
    return hit || (cache[str] = fn(str))
  }
}

const camelize = cacheStringFunction(str => str.replace(/-(\w)/g, (_, c) => (c ? c.toUpperCase() : '')))

function keysToCamelKebabCase(obj) {
  const newObj = {}
  Object.keys(obj).forEach(key => {
    newObj[camelize(key)] = obj[key]
  })
  return newObj
}

/**
 * 创建模板
 */
export function useDefineTemplate(options = {}) {
  const { inheritAttrs = true } = options

  const render = shallowRef()

  // eslint-disable-next-line vue/one-component-per-file
  const defineTemplate = defineComponent({
    setup(_, { slots }) {
      return () => {
        render.value = slots.default
      }
    }
  })
  // eslint-disable-next-line vue/one-component-per-file
  const reuseTemplate = defineComponent({
    inheritAttrs,
    setup(_, { attrs, slots }) {
      return () => {
        if (!render.value && process.env.NODE_ENV !== 'production')
          throw new Error('[VueUse] Failed to find the definition of reusable template')
        const vnode = render.value?.({ ...keysToCamelKebabCase(attrs), $slots: slots })

        return inheritAttrs && vnode?.length === 1 ? vnode[0] : vnode
      }
    }
  })

  return [defineTemplate, reuseTemplate]
}
