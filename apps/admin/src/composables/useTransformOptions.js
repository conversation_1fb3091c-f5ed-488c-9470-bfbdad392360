export const useTransformOptions = (items, labelKey, valueKey, otherAttributes = []) => {
  const options = []
  items.forEach(item => {
    let rawItem = {}
    Object.assign(rawItem, {
      label: eval('item.' + labelKey),
      value: eval('item.' + valueKey)
    })

    otherAttributes.forEach(attribute => {
      Object.assign(rawItem, {
        [attribute]: item[attribute]
      })
    })
    options.push(rawItem)
  })

  return options
}
