import moment from 'moment'

// 禁止时间
export const useDisabledDate = (
  formState,
  apartDay = 90,
  name = { startTimeName: 'start_time', endTimeName: 'end_time' }
) => {
  // 禁止开始时间
  const disabledStartTime = curStartTime => {
    
    const formTime = formState.value[name.endTimeName]
    const nowTime = moment()
    const now = moment(curStartTime).format('X')
    if (formTime) {
      const time = moment(formTime)
      return now > time.format('X') || now < nowTime.format('X') // || now < time.add(-apartDay, 'day').format('X')
    }
    return now < nowTime.format('X')
  }

  // 禁止结束时间
  const disabledEndTime = (curStartTime, customTime, isCustom = false) => {
    const formTime = isCustom ? customTime : formState.value[name.startTimeName]
    const nowTime = moment()
    if (formTime) {
      const now = +moment(curStartTime)
      const time = moment(formTime)
      return now < +time || now < +nowTime //|| now > +time.add(apartDay, 'day')
    }
  }

  // 禁止时间 传入时间之前的
  const disabledBeforeTime = (currentTime, beforeTime) => {
    if (beforeTime) {
      const current = +moment(currentTime)
      const before = moment(beforeTime)
      // console.log('current2', current, +before)
      return currentTime > +before // || now > +time.add(apartDay, 'day')
    }
  }
  // 禁止结束时间 现在之前
  const disabledEndTimeBeforeNow = curStartTime => {
    const now = +moment(curStartTime)
    const time = moment()
    return now < +time || now > +time.add(apartDay, 'day')
  }
  return { disabledStartTime, disabledEndTime, disabledEndTimeBeforeNow, disabledBeforeTime }
}
