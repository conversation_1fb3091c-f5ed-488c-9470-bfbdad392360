import { ref } from 'vue'

export function useToggle(inital = false) {
  const toggle = ref(inital)
  const setToggle = (value) => (toggle.value = value)
  return {
    toggle,
    setToggle
  }
}

export function useLoading(inital = false) {
  const { toggle: loading, setToggle: setLoading } = useToggle(inital)
  return {
    loading,
    setLoading
  }
}

export function useModalVisible(inital = false) {
  const { toggle: modalVisible, setToggle: setModalVisible } = useToggle(inital)
  return {
    modalVisible,
    setModalVisible
  }
}
