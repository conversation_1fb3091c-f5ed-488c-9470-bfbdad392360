import { loginApi, confirmApi } from '@/modules/auth/api'
import { encrypt } from '@/utils/functions.js'
import { message } from 'ant-design-vue'
import { Base64 } from 'js-base64'
export function usePasswordConfirm(title = '操作确认') {
  const visible = ref(false)
  const password = ref('')
  const doneFunc = ref()
  const encryptPublicKey = ref('')
  
  const handleConfirm = () => {
    if(!password.value) {
      message.error('请输入密码')
      return false
    }
    
    visible.value = false

    confirmApi
    .post({
      password: encrypt(password.value, encryptPublicKey.value)
    })
    .then((res) => {
      if(res.token && doneFunc.value) {
        password.value = ''
        doneFunc.value(res.token)
      }
    })
    
  }

  const done = (callback) => {
    doneFunc.value = callback
  }

  const getPublicKey = () => {
    if(encryptPublicKey.value) {
      return
    }
    loginApi
    .get()
    .then((data) => {
      encryptPublicKey.value = Base64.decode(data)
    })
  }
  getPublicKey()
  
  return {visible, password, title, handleConfirm, done}
}