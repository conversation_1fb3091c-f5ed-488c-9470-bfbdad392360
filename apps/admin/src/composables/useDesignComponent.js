import { computed } from 'vue'
import { useDesignManager } from './useDesignManager'

export const useDesignComponent = props => {
  const { currentMobileConfig, currentMobileConfigValue, resetCurrentMobile, currentMobileConfigIndex, mobileConfig } =
    useDesignManager()
  const useDesignInput = () => {
    const componentKey = computed(() => props.component && props.component.key)
    const inputValueLength = computed(
      () =>
        (currentMobileConfigValue.value[componentKey.value] &&
          currentMobileConfigValue.value[componentKey.value].length) ||
        0
    )
    return { inputValueLength }
  }
  const useDesignButton = () => {
    const btnList = computed(() => currentMobileConfigValue.value.imageList)
    const currentBtn = computed(() => btnList.value[props.componentIndex])
    const removeBtn = () => {
      if (btnList.value.length > 0) {
        const list = currentMobileConfig.value.data[0].list
        const item = list.find(v => v.addConfig && v.addConfig.key)
        item.list.splice(props.componentIndex, 1)
        btnList.value.splice(props.componentIndex, 1)
        resetCurrentMobile()
      }
    }
    const confirm = list => {
      btnList.value[props.componentIndex].hotList = list
    }
    return { currentBtn, btnList, removeBtn, confirm }
  }
  const useDesignAddConfig = () => {
    const removeListItem = type => {
      if (currentMobileConfig.value.value[type].length > 0) {
        currentMobileConfig.value.value[type].splice(props.componentIndex, 1)
        mobileConfig.value[currentMobileConfigIndex.value] = currentMobileConfig.value
      }
    }
    return { removeListItem }
  }
  return { useDesignInput, useDesignButton, useDesignAddConfig }
}
