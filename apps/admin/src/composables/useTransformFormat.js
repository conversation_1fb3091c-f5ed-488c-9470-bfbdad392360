export const useTransformMedia = (value, type) => {
  let result
  switch (type) {
    case 'string':
      result = value[0]
      break
    case 'array':
      result = value ? [value] : []
      break
  }
  return result
}

export const useBatchTransformMedia = (batchTransformObject, keys = [], type, all = false) => {
  let transformObject = {}
  if (all) {
    Object.keys(batchTransformObject).forEach(
      key => (transformObject[key] = useTransformMedia(batchTransformObject[key], type))
    )
  } else {
    keys.forEach(key => (transformObject[key] = useTransformMedia(batchTransformObject[key], type)))
  }
  return {
    transformObject
  }
}

export const priceToInt = v => Math.round(v * 100)

/**
 * 转换图片
 */
export const useTransformImg = () => {
  /**
   * 转换单个图片
   * @param { string | string[] } value 需要的转换值
   * @param { string } type 转换后类型, array: 转换成数组, string: 转换成字符串
   * @returns { string | string[] } 转换后的值
   */
  const transformImg = (value, type = 'array') => (type === 'array' ? (value ? [value] : []) : value[0])

  /**
   * 批量转换图片
   * @param { Object } transformObject 转换对象
   * @param { string } type 转换后类型, array: 转换成数组, string: 转换成字符串
   * @param { string[] } fields 转换字段,不传则转换 transformObject 所有属性
   * @returns { void }
   */
  const batchTransformImg = (transformObject, type = 'array', fields = []) => {
    // 自定义转换
    if (fields.length) {
      fields.forEach(key => {
        transformObject[key] = transformImg(transformObject[key], type)
      })
    } else {
      Object.entries(transformObject).forEach(([key, value]) => {
        transformObject[key] = transformImg(value, type)
      })
    }
  }

  return { transformImg, batchTransformImg }
}
