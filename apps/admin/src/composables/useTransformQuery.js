import { toRaw, isRef, isReactive } from 'vue'
import { cloneDeep } from 'lodash'
import moment from 'moment'

const operators = ['>', '<', '!', '>=', '<=', '!=']

export function useTransformQuery(query, transfomers = {}) {
  const rawQuery = cloneDeep(isRef(query) ? query.value : isReactive(query) ? toRaw(query) : query)

  Object.keys(transfomers).forEach(key => {
    // console.log(transfomers);
    let value = rawQuery[key]

    if (value === undefined || value === null) {
      return
    }

    if (typeof transfomers[key] === 'function') {
      transfomers[key](value, rawQuery)
      return
    }
    transfomers[key].split(/\|/g).forEach(transfomer => {
      switch (transfomer) {
        case 'like':
          value = `%${value}%`
          break
        case 'date':
          value = moment(value).format('YYYY-MM-DD')
          break
        case 'dateRange':
          if (value && value.length == 2) {
            value = `>=${moment(value[0]).format('YYYY-MM-DD')} 00:00:00<=${moment(value[1]).format(
              'YYYY-MM-DD'
            )} 23:59:59`
          }
          break
        case 'datetime':
          value = moment(value).format('YYYY-MM-DD HH:mm:ss')
          break
        case 'number':
          value = value * 1
          break
        case 'bool':
          value = value ? true : false
          break
        case 'string':
          value = `${value}`
          break
        case 'range':
          value = (value[0] ? `>=${value[0]} ` : '') + (value[1] ? `<=${value[1]}` : '')
          break
        default:
          if (operators.indexOf(transfomer) >= 0) {
            value = `${transfomer}${value}`
          }
          break
      }
    })
    rawQuery[key] = value
  })
  return rawQuery
}
