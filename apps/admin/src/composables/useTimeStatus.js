// 通用时间状态列表
const TIME_STATUS_NOSTART = 'not_start'
const TIME_STATUS_NORMAL = 'normal'
const TIME_STATUS_ENDED = 'ended'
const timeStatusList = Object.freeze([
  { label: '未开始', value: TIME_STATUS_NOSTART, colorType: 'processing' },
  { label: '进行中', value: TIME_STATUS_NORMAL, colorType: 'success' },
  { label: '已结束', value: TIME_STATUS_ENDED, colorType: 'default' }
])

// 过滤状态
const statusFilter = (status = TIME_STATUS_NOSTART) => {
  let result = timeStatusList.find(({ value }) => value === status) ?? {}
  switch (status) {
    case TIME_STATUS_NOSTART:
      result.isNostart = true
      break
    case TIME_STATUS_NORMAL:
      result.isNormal = true
      break
    case TIME_STATUS_ENDED:
      result.isEnded = true
      break
  }
  return result
}

// 时间状态
export const useTimeStatus = () => {
  return { statusList: timeStatusList, statusFilter, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED }
}

export function useTimeStatusFilter(arr, value) {
  if (arr) {
    return arr.find(item => item.value == value).label
  }
}
