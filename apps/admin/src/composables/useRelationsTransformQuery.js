import moment from 'moment'
import { toRaw, isRef, isReactive } from 'vue'
import _ from 'lodash'

const operators = ['>', '<', '!', '>=', '<=', '!=']

/**
 *
 * e.g.  column: operator;column: type|operator; rels.column:operator; rels.column: type|operator
 *
 * @param {*} query
 * @param {*} transformer
 * @returns
 */
export function useRelationsTransformQuery(query, transformer) {
  let rawQuery = _.cloneDeep(isRef(query) ? query.value : isReactive(query) ? toRaw(query) : query)
  let condition = {}
  let relsQuery = {}
  if (rawQuery['conditionKey']) {
    if (rawQuery['conditionValue']) {
      rawQuery[rawQuery['conditionKey']] = rawQuery['conditionValue']
    }
    delete rawQuery['conditionKey']
    delete rawQuery['conditionValue']
  }
  Object.keys(transformer).forEach(key => {
    let value
    let result = key.split('.')
    if (typeof transformer[key] === 'function') {
      value = transformer[key](value, rawQuery)
    } else {
      value = rawQuery[key]
      if (result[1]) value = rawQuery[result[1]]
      if (value === undefined || value === '') return

      transformer[key].split(/\|/g).forEach(transformer => {
        switch (transformer) {
          case 'like':
            value = `%${value}%`
            break
          case 'date':
            value = moment(value).format('YYYY-MM-DD')
            break
          case 'datetime':
            value = moment(value).format('YYYY-MM-DD HH:mm:ss')
            break
          case 'datetimeRange':
            value =
              value && value.length == 2
                ? `>=${moment(value[0]).format('YYYY-MM-DD HH:mm:ss')}<=${moment(value[1]).format(
                    'YYYY-MM-DD HH:mm:ss'
                  )}`
                : undefined
            break
          case 'dateRange':
            value =
              value && value.length == 2
                ? `>=${moment(value[0]).format('YYYY-MM-DD')} 00:00:00<=${moment(value[1]).format(
                    'YYYY-MM-DD'
                  )} 23:59:59`
                : undefined
            break
          case 'number':
            value = value * 1
            break
          case 'bool':
            value = value ? true : false
            break
          case 'string':
            value = `${value}`
            break
          case '<>':
            value = `! ${value}`
            break
          case 'in':
            value = value
            break
          case 'priceRange':
            value = (value[0] ? `>=${value[0] * 100} ` : '') + (value[1] ? `<=${value[1] * 100}` : '')
            break
          case 'range':
            value = (value[0] ? `>=${value[0]} ` : '') + (value[1] ? `<=${value[1]}` : '')
            break
          default:
            if (operators.indexOf(transformer) >= 0) {
              value = `${transformer}${value}`
            }
            break
        }
      })
    }

    if (value != undefined || value != null) {
      if (result[1]) {
        if (!relsQuery[result[0]]) {
          relsQuery[result[0]] = {}
        }
        relsQuery[result[0]][result[1]] = value
      } else {
        condition[key] = value
      }
    }
  })
  return {
    relation_filters: relsQuery,
    filters: condition
  }
}

export function useTransformExportQuery(query, transformer) {
  const params = useTransformQuery(query, transformer)
  const exportQuery = useDeepCopy(params.query)
  Object.keys(params.relsQuery).forEach(rel => {
    const relQuery = params.relsQuery[rel]
    Object.keys(relQuery).forEach(key => {
      exportQuery[`${rel}_${key}`] = relQuery[key]
    })
  })
  return exportQuery
}
