import {ref, onMounted, onActivated } from 'vue'
import { cloneDeep, zipObject, keys, values } from 'lodash'
import { useLoading } from './useToggles'
import config from '@/config'

export function useApiRequest(request, calledOnMounted = true) {
  const { loading, setLoading } = useLoading()
  const data = ref(null)
  const getData = async () => {
    setLoading(true)
    try {
      data.value = await request()
      setLoading(false)
    } catch (error) {
      setLoading(false)
      throw error
    }
  }


  const route = useRoute()
  if(route && route.meta && route.meta.keepAlive) {
    onActivated(getData)
  } else {
    calledOnMounted && onMounted(getData)
  }


  return {
    data,
    getData,
    loading
  }
}

/**
 * pass { order: orderApi.get(1), user: userApi.get(1) } to batchRequestObject
 * then data is { order: Order, user: User }
 *
 * @param {object} batchRequestObject
 * @param {boolean} calledOnMounted
 */
export function useBatchApiRequest(batchRequestObject, calledOnMounted = true) {
  return useApiRequest(async () => {
    return zipObject(keys(batchRequestObject), await Promise.all(values(batchRequestObject)))
  }, calledOnMounted)
}

export function usePaginatorApiRequest(
  request,
  initalPage = { current: 1, pageSize: config.api.limit },
  calledOnMounted = true
) {
  const page = ref(cloneDeep(initalPage))

  const { loading, setLoading } = useLoading()
  const data = ref({ items: [], offset: initalPage.current, limit: initalPage.pageSize, total: 0 })

  const getData = async (curPage = initalPage) => {
    if (curPage.pageSize != page.value.pageSize || curPage.current != page.value.current) {
      page.value = curPage
    }
    setLoading(true)
    try {
      data.value = await request({ offset: page.value.current, limit: page.value.pageSize })
      setLoading(false)
    } catch (error) {
      setLoading(false)
      throw error
    }
  }
  const refresh = () => getData(page.value)

  const route = useRoute()
  if(route && route.meta && route.meta.keepAlive) {
    onActivated(refresh)
  } else {
    calledOnMounted && onMounted(getData)
  }

  return {
    data,
    getData,
    loading,
    page,
    refresh,
    setPage: ({ current, pageSize } = initalPage) => {
      page.value = { current, pageSize }
      getData(page.value)
    }
  }
}
