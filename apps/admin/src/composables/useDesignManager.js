import { designConfig } from './useConfig'
import { computed, ref } from 'vue'
import { apiFactory } from '@/api'
import _ from 'lodash'

const mobileConfig = ref([])
const navigationConfig = ref({
  name: '顶部导航栏',
  key: 'Navigation',
  value: {
    title: '卡西欧(CASIO)',
    bgColor: '#000000',
    default_bgColor: '#000000',
    textColor: '#ffffff',
    default_textColor: '#ffffff'
  },
  data: [
    {
      title: '功能设置',
      list: [
        {
          title: '功能设置',
          list: [
            {
              title: '页面标题',
              type: 'input',
              key: 'title',
              maxLength: 20
            }
          ]
        }
      ]
    }
  ]
})
const pageConfig = ref({
  name: '页面设置',
  value: {
    title: '',
    bgColor: '#ffffff',
    default_bgColor: '#ffffff',
    share_title: '',
    share_url: '',
    channel: [],
    pageType: 'mobile',
    alias: ''
  },
  data: [
    {
      title: '功能设置',
      list: [
        {
          title: '基本信息',
          list: [
            {
              title: '文章标题',
              type: 'input',
              key: 'title',
              maxLength: 20
            },
            {
              title: '分享标题',
              type: 'input',
              key: 'share_title',
              maxLength: 20,
              pcHide: true
            },
            {
              title: '分享图片',
              type: 'ec_upload',
              key: 'share_url',
              pcHide: true
            },
            {
              title: '文章封面',
              type: 'ec_upload',
              key: 'cover_url',
              pcHide: true
            },
            {
              title: '投放渠道',
              type: 'checkbox',
              key: 'channel',
              options: []
            },
            {
              title: '渠道别名',
              type: 'input',
              key: 'alias',
              maxLength: 20
            }
          ]
        }
      ]
    },
    {
      title: '样式设置',
      list: [
        {
          title: '页面样式',
          list: [
            {
              title: '页面背景颜色',
              type: 'popover_color',
              key: 'bgColor'
            }
          ]
        }
      ]
    }
  ]
})
const currentMobileConfigIndex = ref(-1)
const currentMobileConfig = ref(null)
const currentMobileConfigValue = computed(() => currentMobileConfig.value && currentMobileConfig.value.value)
const currentTabKey = ref(0)
const COMPONENT_TYPES = {
  INPUT: 'input',
  RADIO: 'radio',
  POPOVER_COLOR: 'popover_color',
  NOTICE: 'notice',
  LINK: 'link',
  RADIO_ICON: 'radio_icon',
  SPACING: 'spacing',
  UPLOAD_IMAGE: 'upload_image',
  UPLOAD_VIDEO: 'upload_video',
  NAME: 'name',
  PRODUCT: 'product',
  BUTTON: 'button',
  EC_UPLOAD: 'ec_upload',
  TAB: 'tab',
  NUM_INPUT: 'num_input',
  CHECKBOX: 'checkbox'
}

const showHotArea = ref(false)
const pictureHotList = ref([])
const designDetail = ref('')

const PUBLISH_PUBLISHED_STATUS = 'published'
const PUBLISH_UNPUBLISHED_STATUS = 'unpublished'

const SEARCH = 'Search'
const PICTURE = 'Picture'
const SWIPER = 'Swiper'
const PRODUCT = 'Product'
const NOTICE = 'Notice'
const DIVIDER = 'Divider'
const VIDEO = 'Video'
const DESIGN_BUTTON = 'DesignButton'
const TAB_LIST = 'tabList'
const LABELTAB = 'LabelTab'
const TICKER = 'Ticker'

const COMPONENT_KEYS = {
  SEARCH,
  PICTURE,
  SWIPER,
  PRODUCT,
  NOTICE,
  DIVIDER,
  VIDEO,
  DESIGN_BUTTON,
  TAB_LIST,
  LABELTAB,
  TICKER
}
export const useDesignManager = () => {
  const setCurrentMobileConfig = config => {
    currentMobileConfig.value = config
    currentTabKey.value = 0
  }
  const setMobileConfigIndex = index => {
    currentMobileConfigIndex.value = index
    const item = mobileConfig.value[index]
    setCurrentMobileConfig(item)
  }
  const setCustomConfig = (item, index = -2) => {
    currentMobileConfigIndex.value = index
    setCurrentMobileConfig(item)
  }
  const removeMobileConfig = index => {
    mobileConfig.value.splice(index, 1)
    currentMobileConfigIndex.value = -1
    currentMobileConfig.value = null
  }

  const useDesignManagerInterface = () => {
    // 合并mobileConfig
    const resetMobileConfig = setting => {
      setting.map(el => {
        let item = designConfig[0].labels.find(v => v.key == el.key)
        el.data = item.data
        el.value = {
          ...item.value,
          ...el.value
        }
      })
      mobileConfig.value = setting
    }
    const setMobileConfig = document => {
      mobileConfig.value = document.setting
      resetMobileConfig(_.cloneDeep(document.setting))
      if (document.page_configs) {
        if (document.page_configs.navigationConfig) {
          navigationConfig.value.value = {
            ...navigationConfig.value.value,
            ...document.page_configs.navigationConfig.value
          }
        }
        if (document.page_configs.pageConfig) {
          pageConfig.value.value = {
            ...pageConfig.value.value,
            ...document.page_configs.pageConfig.value
          }
        }
      }
      setMobileConfigIndex(-1)
    }
    const loadDesignDetail = async id => {
      const data = await apiFactory.restful('/distribution/articles').get(id, {})
      designDetail.value = data
      console.log(data, 1)
      setMobileConfig(data)
    }
    const resetDesignParams = () => {
      const newMobileConfig = _.cloneDeep(mobileConfig.value)
      newMobileConfig.map(el => {
        if (el.data) delete el.data
        if (el.icon) delete el.icon
      })
      const newPageConfig = _.cloneDeep(pageConfig.value)
      if (newPageConfig.data) delete newPageConfig.data
      if (newPageConfig.icon) delete newPageConfig.icon
      const newNavigationConfig = _.cloneDeep(navigationConfig.value)
      if (newNavigationConfig.data) delete newNavigationConfig.data
      if (newNavigationConfig.icon) delete newNavigationConfig.icon
      return {
        newMobileConfig,
        newPageConfig,
        newNavigationConfig
      }
    }
    const createDesign = async (api, isrRelease = false, showTitle = true) => {
      const { newMobileConfig, newPageConfig, newNavigationConfig } = resetDesignParams()
      const params = {
        setting: newMobileConfig,
        page_configs: {
          pageConfig: newPageConfig,
          navigationConfig: newNavigationConfig
        },
        channel: `,${newPageConfig.value.channel.join()},`,
        alias: newPageConfig.value.alias,
        pageType: newPageConfig.value.pageType
      }
      if (showTitle) params.title = pageConfig.value.value.title
      if (isrRelease) params.publishStatus = PUBLISH_PUBLISHED_STATUS
      return await api.create(params)
    }
    const updateDesign = async (api, id, isrRelease = false) => {
      const { newMobileConfig, newPageConfig, newNavigationConfig } = resetDesignParams()
      const params = {
        setting: newMobileConfig,
        page_configs: {
          pageConfig: newPageConfig,
          navigationConfig: newNavigationConfig
        },
        title: newPageConfig.value.title,
        channel: `,${newPageConfig.value.channel.join()},`,
        alias: newPageConfig.value.alias,
        pageType: newPageConfig.value.pageType
      }
      if (isrRelease) params.publishStatus = PUBLISH_PUBLISHED_STATUS
      return await api.update(id, params)
    }
    return { loadDesignDetail, createDesign, updateDesign }
  }
  const resetCurrentMobile = () => {
    mobileConfig.value[currentMobileConfigIndex.value] = currentMobileConfig.value
  }
  return {
    designConfig,
    currentMobileConfig,
    mobileConfig,
    setMobileConfigIndex,
    currentMobileConfigIndex,
    removeMobileConfig,
    setCurrentMobileConfig,
    COMPONENT_TYPES,
    showHotArea,
    pictureHotList,
    navigationConfig,
    designDetail,
    pageConfig,
    setCustomConfig,
    useDesignManagerInterface,
    PUBLISH_PUBLISHED_STATUS,
    PUBLISH_UNPUBLISHED_STATUS,
    COMPONENT_KEYS,
    currentMobileConfigValue,
    resetCurrentMobile,
    currentTabKey
  }
}
