import { ref } from 'vue'
import { cloneDeep } from 'lodash'
import { useForm } from 'ant-design-vue/lib/form'
import { message } from 'ant-design-vue'
import { getStorage, setStorage } from '@/utils/storage'

const toAntdFormRules = rules => {
  Object.keys(rules).forEach(key => {
    if (typeof rules[key] !== 'array') {
      rules[key] = [rules[key]]
    }
  })
  return rules
}

export function clearFormState(key) {
  const nsKey = `formstate.${key}`
  setStorage(nsKey, null)
}

export function useFormState(initalSate = {}) {
  const formState = ref(cloneDeep(initalSate))
  const formRef = ref(null)
  const changeCallbacks = []
  const resetCallbacks = []

  let formModel = undefined

  const setFormState = state => {
    formState.value = cloneDeep(state)
    changeCallbacks.forEach(fn => fn(formState.value))
  }

  const resetFormState = () => {
    const initState = cloneDeep(initalSate)
    setFormState(initState)
    changeCallbacks.forEach(fn => fn(initState))
    resetCallbacks.forEach(fn => fn(initState))
  }

  const onFormStateChange = fn => changeCallbacks.push(fn)
  const onRestFormState = fn => resetCallbacks.push(fn)

  const setFormRules = (rules, reset = false) => {
    if (!formModel || reset) {
      formModel = useForm(formState, toAntdFormRules(rules))
    }
  }

  const validateForm = async () => {
    if (!formModel) {
      return true
    }
    try {
      await formModel.validate()
    } catch ({ errorFields }) {
      message.error(errorFields[0].errors[0])
      return false
    }
    return true
  }

  return {
    formState,
    setFormState,
    resetFormState,
    onFormStateChange,
    onRestFormState,
    formRef,
    setFormRules,
    validateForm
  }
}
