* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body,
#app {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: 'Chinese Quote', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB',
    'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
    'Segoe UI Symbol';
  color: rgba(0, 0, 0, 0.65);
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
  position: relative;
  z-index: 9999;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: @color-primary;
}

ul,
ol,
dl,
li {
  list-style: none;
}

h1,
h2,
h3 {
  font-style: normal;
  font-weight: normal;
}

a,
a:hover {
  text-decoration: none;
}

// 图片
img {
  @modes: fill, contain, cover;

  each(@modes, {
      &[mode="@{value}"],
      &.mode-@{value} {
        width: 100%;
        height: 100%;
        object-fit: @value !important;
      }
    }
  );
}
.overflow-auto {
  overflow: auto;
}
// 溢出隐藏
.hide {
  overflow: hidden;
}
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 样式重置 start */
// 表格
.ant-table .ant-table-tbody .ant-table-row-cell-break-word .ant-btn {
  padding: 0 0 0 5px;

  &:first-child:not(.danger) {
    padding-left: 0;
  }

  &.danger {
    color: #e54d42;

    &[disabled] {
      color: rgba(0, 0, 0, 0.25);
    }
  }
}

.ant-btn.ant-btn-success {
  border-color: @color-success;
  background: @color-success;
  color: @color-white;

  &:hover,
  &:focus {
    border-color: @color-success;
    background: @color-success;
    color: @color-white;
  }
}

// 新增/编辑富文本
.card-rich-text .ant-card-body {
  padding: 0;
}

// 必填
.ant-form-item.required .ant-form-item-label label::before {
  content: '*';
  display: inline-block;
  margin-right: 4px;
  color: @color-danger;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
}

// 分页
.ant-table + .ant-table-pagination.ant-pagination {
  margin-bottom: 0;
}
.ant-pagination-options .ant-pagination-options-size-changer.ant-select {
  margin-right: 0;
}
.ant-pagination-options-quick-jumper {
  margin-left: 8px;
}

// antd input center
.input-center input {
  text-align: center !important;
}

/* 样式重置 end */

// 文本的粗细
@weights: {
  400: 400;
  500: 500;
  600: 600;
  700: 700;
  bold: bold;
};

each(@weights, {
    .fw-@{key} {
      font-weight: @value !important;
    }
  }
);

// 字体大小
@sizes: {
  14: 14px;
  16: 16px;
  18: 18px;
  20: 20px;
  22: 22px;
  24: 24px;
  26: 26px;
  28: 28px;
  30: 30px;
  32: 32px;
  34: 34px;
  36: 36px;
  38: 38px;
  40: 40px;
  42: 42px;
};
each(@sizes, {
    .fs-@{key} {
      font-size: @value !important;
    }
  }
);

.t-center {
  text-align: center !important;
}
.t-right {
  text-align: right !important;
}
.hand {
  cursor: pointer;
}

// flex
.flex {
  display: flex !important;

  &-1 {
    flex: 1;
  }
  &-auto {
    flex: auto;
  }
  &-center {
    align-items: center;
    justify-content: center;
  }

  &-sa {
    justify-content: space-around;
  }

  &-sb {
    justify-content: space-between;
  }

  &-sc {
    justify-content: center;
  }

  &-cc {
    align-items: center;
  }

  &-right {
    justify-content: flex-end;
  }

  &-dc {
    flex-direction: column;
  }

  &-rr {
    flex-direction: row-reverse;
  }

  &-cr {
    flex-direction: column-reverse;
  }

  &-wrap {
    flex-wrap: wrap;
  }
}

// 宽度
@widths: {
  vw: 100vw;
  fill: 100%;
  30: 30px;
  80: 80px;
  90: 90px;
  100: 100px;
  120: 120px;
  140: 140px;
  150: 150px;
  160: 160px;
  175: 175px;
  180: 180px;
  200: 200px;
  210: 210px;
  217: 217px;
  220: 220px;
  240: 240px;
  250: 250px;
  300: 300px;
  310: 310px;
  340: 340px;
  365: 365px;
  370: 370px;
  390: 390px;
  420: 420px;
  500: 500px;
  700: 700px;
};
each(@widths,{
  .w-@{key} {
    width: @value !important;
  }
});

@min-widths: {
  175: 175px;
};
each(@widths,{
  .min-w-@{key} {
    min-width: @value !important;
  }
});

// 高度
@heights: {
  vh: 100vh;
  32: 32px;
  fill: 100%;
};
each(@heights,{
  .h-@{key} {
    height: @value !important;
  }
});
@min-heights: {
  vh: 100vh;
};
each(@min-heights,{
  .min-h-@{key} {
    min-height: @value !important;
  }
});
/* 高度 end */

// 内外边距
@gaps: {
  0: 0;
  2: 2px;
  3: 3px;
  4: 4px;
  6: 6px;
  8: 8px;
  10: 10px;
  12: 12px;
  14: 14px;
  16: 16px;
  18: 18px;
  20: 20px;
  22: 22px;
  24: 24px;
  26: 26px;
  28: 28px;
  30: 30px;
  32: 32px;
  34: 34px;
  36: 36px;
  38: 38px;
  40: 40px;
  48: 48px;
  50: 50px;
  60: 60px;
  80: 80px;
};

each(@gaps, {
    .m-@{key} {
      margin: @value !important;
    }

    .m-t-@{key} {
      margin-top: @value !important;
    }

    .m-b-@{key} {
      margin-bottom: @value !important;
    }

    .m-l-@{key} {
      margin-left: @value !important;
    }

    .m-r-@{key} {
      margin-right: @value !important;
    }

    .m-tb-@{key} {
      margin-top: @value !important;
      margin-bottom: @value !important;
    }

    .m-lr-@{key} {
      margin-left: @value !important;
      margin-right: @value !important;
    }

    .p-@{key} {
      padding: @value !important;
    }

    .p-t-@{key} {
      padding-top: @value !important;
    }

    .p-b-@{key} {
      padding-bottom: @value !important;
    }

    .p-l-@{key} {
      padding-left: @value !important;
    }

    .p-r-@{key} {
      padding-right: @value !important;
    }

    .p-tb-@{key} {
      padding-top: @value !important;
      padding-bottom: @value !important;
    }

    .p-lr-@{key} {
      padding-left: @value !important;
      padding-right: @value !important;
    }
  }
);

// 背景色
@bgcs: {
  white: #fff;
  default: #f0f2f5;
  red: #f5222d;
  primary: #1890ff;
};
each(@bgcs,{
  .bgc-@{key}{
    background-color: @value;
  }
});

// 字体色
@colors: {
  white: #fff;
  green: #52c41a;
  orange: #faad14;
  yellow: #ffe58f;
  red: #f5222d;
  danger: #ff4d4f;
  grey: #999999;
  primary: #1890ff;
  85: rgba(0, 0, 0, 0.85);
  65: rgba(0, 0, 0, 0.65);
  45: rgba(0, 0, 0, 0.45);
  25: rgba(0, 0, 0, 0.25);
};
each(@colors,{
  .color-@{key}{
    color: @value !important;
  }
  .color-@{key}[disabled]{
    color: #bfbfbf!important;
  }
});

/* 字体色 end */

/* 定位 start */
@positions: {
  relative: relative;
  absolute: absolute;
};
each(@positions,{
  .@{key}{
    position: @value !important;
  }
});
@positions-point: {
  0: 0;
};
each(@positions-point, {
  .top-@{key} {
    top: @value !important;
  }
  .right-@{key} {
    right: @value !important;
  }
  .bottom-@{key} {
    bottom: @value !important;
  }
  .left-@{key} {
    left: @value !important;
  }
});
/* 定位 end */

// 时间分割
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}

a.ant-btn.ant-btn-link {
  padding-top: 4px !important;
  line-height: 1.5715 !important;
}

@cursors: pointer, move;
each(@cursors, {
  .cursor-@{value}{
    cursor: @value;
  }
});

@borders: {
  1: 1px;
  2: 2px;
};
each(@borders, {
  .border-@{key}{
    border: @value solid transparent !important;
  }
});

.delete-btn {
  padding-top: 2px !important;
  border: none !important;
  .anticon {
    font-size: 20px !important;
  }
  &:disabled {
    .anticon {
      color: #d9d9d9 !important;
    }
  }
}

@backgroundColors: {
  default: #f5f5f5;
  white: #fff;
};
each(@backgroundColors, {
  .bg-c-@{key} {
    background-color: @value !important;
  }
});

@opacitys: {
  0: 0;
};
each(@opacitys, {
  .opacity-@{key} {
    opacity: @value !important;
  }
});

.rounded {
  &-1\/2 {
    border-radius: 50%;
  }
  &-6 {
    border-radius: 6px;
  }
}

@zIndex: 9;
each(@zIndex, {
  .z-index-@{value} {
    z-index: @value;
  }
});


.number {
  text-align: right;
}