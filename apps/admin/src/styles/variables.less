// 行为颜色
@color-white: #fff;
@color-primary: #409eff;
@color-success: #67c23a;
@color-info: #909399;
@color-warning: #e6a23c;
@color-danger: #f56c6c;
@color-disabled: #f5f5f5;

// 字体颜色
@font-color-85: rgba(0, 0, 0, 0.85);
@font-color-65: rgba(0, 0, 0, 0.65);

// 溢出隐藏
.clamp() {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

// 行内块
.inline-block() {
  display: inline-block;
  vertical-align: middle;
}

// 弹性居中
.flex-center() {
  display: flex;
  justify-content: center;
  align-items: center;
}

// modal内表单元素
.ant-modal-body .ant-form-item {
  .ant-input-number {
    width: 100%;
  }
}

.bg-upload(@width: auto, @height: 32px ) {
  .bg-upload {
    :deep(.ant-upload-picture-card-wrapper) {
      width: 100%;
      height: @height;

      .ant-upload-list-picture-card {
        display: none;
      }

      .ant-upload-select-picture-card {
        width: 100% !important;
        height: 100% !important;
        margin: 0;
        border-style: solid;

        .ant-upload>div {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }

      .ant-upload-text {
        width: @width;
      }
    }
  }
}
