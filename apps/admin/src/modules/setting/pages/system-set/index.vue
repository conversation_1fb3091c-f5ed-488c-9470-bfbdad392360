<template>
  <uc-layout-form :is-cancel="false" @submit="handleSubmit">
    <a-space direction="vertical" :size="20">
      <a-card title="每日数据邮件">
        <a-form-item label="邮箱账号" class="required">
          <div v-for="item, index in formState.statistics_emails" :key="index">
            <a-input v-model:value="formState.statistics_emails[index]" class="m-b-10" placeholder="请添加邮箱账号" /> 
            <a-button v-if="index === 0" class="m-l-10" type="primary" @click="add">
              添加
            </a-button>
            <a-button v-else class="m-l-10" danger @click="sub(index)">
              移除
            </a-button>
          </div>
        </a-form-item>

        <a-form-item label="数据字段" class="required">
          <a-spin v-if="formState.statistics_fields.length === 0" />
          <div v-else>
            <a-checkbox v-for="item, index in formState.statistics_fields" :key="index" v-model:checked="item.checked">{{ item.label }}</a-checkbox>
          </div>
        </a-form-item>
      </a-card>
    </a-space>
  </uc-layout-form>
</template>

<script setup>
import { nextTick } from "vue";
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useFormState } from "@/composables/useFormState";
import { message } from "ant-design-vue";
import { systemSettingApi, systemSettingUpdateApi } from '../../api'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  statistics_emails: [''],
  statistics_fields: []
})

let dataConfig = {}
const loadData = () => {
  systemSettingApi.get({}).then((data) => {
    dataConfig = data
    setFormState(data.value)
  })
}

setFormRules({
  statistics_emails: {validator(_,value){
    var emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/
    for(let index in value) {
      if (!value[index]) {
        return Promise.reject('请输入邮箱地址')
      }
      if (!emailRegex.test(value[index])) {
        return Promise.reject('邮箱地址格式错误')
      }
    }
    return Promise.resolve();
  }},
  statistics_fields: {validator(_,value){
      if (value.length < 1) {
        return Promise.reject('请选择数据字段')
      }
      return Promise.resolve();
  }}
})

const add = () => {
  formState.value.statistics_emails.push("")
}

const sub = (index) => {
  formState.value.statistics_emails.splice(index, 1)
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

const handleSubmit = async () => {
  if (!(await validateForm())) return
  useLoadingMessage(systemSettingUpdateApi.post({...dataConfig,value:formState.value})).then(() => {
      message.success('操作成功：系统设置')
      loadData()
  })
}

</script>

<style lang="less" scoped>

</style>