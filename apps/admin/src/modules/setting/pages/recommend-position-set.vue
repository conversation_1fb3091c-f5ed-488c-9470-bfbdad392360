<template>
  <div class="recommend-position">
    <uc-layout-form :is-cancel="false" @submit="handleSubmit">
      <uc-row>
        <uc-col v-for="(item, index) in formState.value" :key="index">
          <a-card :title="recommendPositionType.filterValue(item.position).label">
            <a-form-item label="推荐方式" class="required">
              <a-select
                v-model:value="item.recommend_way"
                placeholder="请选择推荐方式"
                :options="recommendModeOptions"
              />
            </a-form-item>
            <a-form-item v-if="item.recommend_way == recommendPositionMode.rule" label="推荐规则" class="required">
              <a-select
                v-model:value="item.recommend_rule"
                placeholder="请选择推荐规则"
                :options="recommendRuleOptions"
              />
            </a-form-item>
            <a-form-item v-if="item.recommend_way == recommendPositionMode.custom" label="推荐规则" class="required">
              <a-input v-model:value="item.recommendApi" placeholder="请输入推荐规则API" />
            </a-form-item>
            <a-form-item v-if="item.recommend_way == recommendPositionMode.appoint" label="推荐商品" class="required">
              <a-textarea
                v-model:value="item.recommend_skus"
                placeholder="请输入推荐商品SKU，一行一个"
                style="max-width: 700px"
                :rows="4"
              />
            </a-form-item>
          </a-card>
        </uc-col>
      </uc-row>
    </uc-layout-form>
  </div>
</template>
<script setup>
import { nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { recommendPositionApi, recommendPositionUpdateApi } from '../api'
import { recommendPositionType, recommendPositionMode, recommendRule } from '../enums'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useRecommendData } from '../useRecommend'

const recommendPositionTypeOptions = recommendPositionType.options()
const recommendRuleOptions = recommendRule.options()
const recommendModeOptions = recommendPositionMode.options()
const { dataToShow, showToData } = useRecommendData()

const document = ref({})
nextTick(() => {
  useLoadingMessage(
    recommendPositionApi.get('', {}).then(data => {
      document.value = data
      setFormState(dataToShow(data))
    }),
    {
      loadingText: '正在加载数据...'
    }
  )
})

// 组装formState、formRules
const formStateBasic = {}
const formRulesBasic = {}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  value: []
})

const validatorValue = (_, value) => {
  let tip
  value.some(({ recommend_way, recommend_rule, recommendApi, recommend_skus }) => {
    switch (recommend_way) {
      case recommendPositionMode.rule:
        !recommend_rule && (tip = '请选择推荐规则')
        break
      case recommendPositionMode.appoint:
        if (!recommend_skus) {
          tip = '请输入推荐商品'
        } else if (recommend_skus.split('\n').length > 50) {
          tip = '推荐商品最多50条'
        }
        break
      case recommendPositionMode.custom:
        !recommendApi && (tip = '请输入推荐规则API')
        break
    }
    return tip
  })
  return tip ? Promise.reject(tip) : Promise.resolve()
}

setFormRules({
  value: { validator: validatorValue }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return
  document.value.value = showToData(formState.value)

  await recommendPositionUpdateApi.post(document.value)
  message.success('操作成功')
}
</script>
