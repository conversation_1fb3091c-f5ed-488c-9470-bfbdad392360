<template>
  <uc-layout-list title="海报设置" class="flex">
    <template #list>
      <a-table :data-source="data" row-key="id" :loading="loading" :pagination="false">
        <a-table-column title="海报封面">
          <template #default="{ record }">
            <uc-upload :list="[record?.value?.photo_url || fallbackErr]" :max-length="1" disabled :size="[50, 50]" />
          </template>
        </a-table-column>
        <a-table-column title="海报位置" data-index="key">
          <template #default="{ record }">
            {{ posterSetTypes.filter(record.key).label }}
          </template>
        </a-table-column>
        <a-table-column title="更新时间" data-index="updated_at" />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-button type="link" class="link">
              更新
              <uc-upload
                :max-length="1"
                class="absolute opacity-0 top-0 left-0"
                :size="[50, 20]"
                @update:list="onUpdate($event, record)"
              />
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { fallbackErr } from '@/utils/imgs'
import { posterSetApi, posterSetUpdateApi } from '../../api'
import { posterSetTypes } from '../../enums'

const { data, getData, loading } = useApiRequest(posterSetApi.get)
const onUpdate = ([photo_url], { key }) => {
  posterSetUpdateApi.post({ key, value: { photo_url } }).then(() => {
    message.success('操作成功')
    getData()
  })
}
</script>
