<template>
  <uc-layout-form :is-save="!isAllDisabled" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="资料设置" class="h-fill">
          <a-form-item label="基本信息" class="required">
            <template v-for="(item, index) in dataList" :key="index">
              <a-checkbox v-if="item.type !== 'custom'" v-model:checked="item.checked" :disabled="item.is_base">
                {{ item.title }}
              </a-checkbox>
            </template>
            <a-checkbox v-model:checked="checked1" @change="eventChecked"> 个性标签 </a-checkbox>
          </a-form-item>
          <a-form-item v-show="isShow" label="个性标签" class="label">
            <template v-for="(label, labelIndex) in dataList" :key="labelIndex">
              <div v-if="label.type == 'custom'">
                <a-space direction="vertical" :size="10">
                  <div>
                    <a-input v-model:value="label.title" class="w-500" placeholder="请输入标签名称，不超过6个字" />
                    <a-button
                      shape="circle"
                      size="small"
                      class="delete-btn"
                      type="link"
                      :disabled="dataList.length == 7 || isAllDisabled"
                    >
                      <template #icon>
                        <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveSend(labelIndex)" />
                      </template>
                    </a-button>
                  </div>
                  <a-textarea
                    v-model:value="label.content"
                    class="w-500"
                    :rows="10"
                    placeholder="请输入标签，一行一个，单个标签不超过20字"
                    style="margin-bottom: 10px"
                  />
                </a-space>
              </div>
            </template>
          </a-form-item>
          <a-form-item v-show="isShow" label=" " :colon="false">
            <a-button type="link" class="p-0" :disabled="isAllDisabled" @click="onAddRule"> 添加个性标签 </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="拉黑设置">
          <a-form-item label="拉黑原因" name="black_setting" class="required">
            <a-textarea
              v-model:value="formState.black_setting"
              placeholder="请输入拉黑原因，一行一个，单个理由不超过20字"
              :auto-size="{ minRows: 6, maxRows: 6 }"
              :disabled="isAllDisabled"
              style="max-width: 700px !important; width: 500px"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { nextTick } from 'vue'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { telePhoneVerify } from '@/utils/index'
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { userSettingApi, userSettingUpdateApi } from '../api'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { cloneDeep } from 'lodash'
import { set } from 'nprogress'

const { id } = useRoute().params
const isAllDisabled = ref(false)
isAllDisabled.value = !!id

const checked1 = ref(false)
const isShow = ref(false)
const eventChecked = () => {
  // console.log(checked1.value);
  if (checked1.value == false) {
    isShow.value = false
  } else {
    isShow.value = true
  }
}

// 默认个性标签
const defaultSendStamps = { title: '', type: 'custom', content: '' }
const router = useRouter()
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  black_setting: undefined,
  data_setting: cloneDeep(defaultSendStamps)
})

let dataConfig = {}
let dataList = ref([])
const loadData = () => {
  userSettingApi.get({}).then(data => {
    dataConfig = data
    dataList.value = data.value.data_setting
    setFormState(data.value)
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

// 自定义个性标签验证
const rulesValidate = (_, value) => {
  if (checked1.value == true) {
    for (const item of dataList.value) {
      if (item.type == 'custom') {
        if (!item.title) return Promise.reject('请输入个性标签名称')
        if (!item.content) return Promise.reject('请输入个性标签')
        if (item.title.length > 6) return Promise.reject('个性标签名称最长为6')
        item.content.split('\n').map(value => {
          if (value.length > 20) return Promise.reject('个性标签最长为20')
        })
      }
    }
    return Promise.resolve()
  } else {
    for (const item of dataList.value) {
      if (item.type == 'custom') {
        if (item.title.length > 6) return Promise.reject('个性标签名称最长为6')
        item.content.split('\n').map(value => {
          if (value.length > 20) return Promise.reject('个性标签最长为20')
        })
      }
    }
    return Promise.resolve()
  }
}

// 自定义拉黑原因
const SendUserValidate = (_, value) => {
  if (!value) return Promise.reject('请输入拉黑原因')
  value.split('\n').map(item => {
    if (item.length > 20) return Promise.reject('拉黑原因不能超过20字')
  })
  const arr = value.split('\n').map(value => value.trim())
  const resArr = [...new Set(arr)]
  if (arr.length !== resArr.length) return Promise.reject('拉黑原因不可重复')
  return Promise.resolve()
}

setFormRules({
  black_setting: { validator: SendUserValidate, trigger: 'blur' },
  data_setting: { validator: rulesValidate, trigger: 'blur' }
})

const isChecked = e => {
  console.log(e)
}

// 删除
const onRemoveSend = index => {
  dataList.value.splice(index, 1)
}

// 添加
const onAddRule = () => {
  if (dataList.value.length == 11) return message.info('最多可添加5个个性标签')
  dataList.value.push(cloneDeep(defaultSendStamps))
}

// 提交
const handleSubmit = async () => {
  if (!(await validateForm())) return
  await userSettingUpdateApi
    .post({ ...dataConfig, value: { data_setting: dataList.value, black_setting: formState.value.black_setting } })
    .then(() => {
      message.success('用户设置保存成功')
      loadData()
    })
}
</script>
<style scoped lang="less">
.delete-btn {
  margin-left: 10px;
}

.label {
  position: relative;
}

.p-0 {
  position: absolute;
  top: -25px;
  left: 0;
}
</style>
