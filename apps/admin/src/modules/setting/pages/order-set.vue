<template>
  <uc-layout-form :is-cancel="false" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="实物发货" class="h-fill">
          <a-form-item label="实物发货" class="required">
            <a-radio-group v-model:value="formState.order_for_non_mainland">
              <a-radio value="1"> 支持港澳台及海外地区 </a-radio>
              <a-radio value="0"> 不支持港澳台及海外地区 </a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="自动取消订单" class="required">
            <a-input-number
              v-model:value="formState.cancel_timeout"
              :min="1"
              placeholder="用户下单在设置的时间段内未支付时，则自动取消订单，按分钟计算"
            />
          </a-form-item>
          <a-form-item label="自动确认收货" class="required">
            <a-input-number
              v-model:value="formState.confirm_timeout"
              :min="1"
              placeholder="发货后超过设置的时间，则自动确认签收，按天计算"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="退货地址">
          <a-form-item label="联系人" class="required">
            <a-input v-model:value.trim="formState.name" maxlength="20" placeholder="请输入接受退货申请的联系人" />
          </a-form-item>
          <a-form-item label="联系号码" class="required">
            <a-input v-model:value.trim="formState.phone_number" placeholder="请输入接受退货申请的联系人手机号码" />
          </a-form-item>
          <a-form-item label="所在城市" class="required">
            <a-cascader
              v-model:value="formState.address"
              placeholder="请选择退货地址所在城市"
              :options="citys.map_options"
              :field-names="{ label: 'label', value: 'label', children: 'children' }"
            />
          </a-form-item>
          <a-form-item label="详细地址" class="required">
            <a-textarea
              v-model:value.trim="formState.detail"
              maxlength="100"
              style="max-width: 700px !important"
              placeholder="请输入详细退货地址，不超过100字"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <a-card title="售后设置">
      <a-form-item label="退货期限" class="required">
        <a-input-number
          v-model:value="formState.return_timeout"
          :min="1"
          placeholder="请输入确认签收后在设置的时间段内可以申请退货，超过退货期限后则无法进行退货"
        />
      </a-form-item>
      <a-form-item label="已自提退款期限" class="required">
        <a-input-number
          v-model:value="formState.pick_up_return_timeout"
          :min="1"
          placeholder="请输入已自提订单可操作退款的天数，超过退款期限则无法退款"
        />
      </a-form-item>
      <a-form-item label="退款理由" class="required">
        <a-textarea
          v-model:value="formState.refund_reason"
          :rows="10"
          style="max-width: 700px !important"
          placeholder="请输入退款理由，一行一个，单个退款理由不超过20字"
        />
      </a-form-item>
      <a-form-item label="退货理由" class="required">
        <a-textarea
          v-model:value="formState.return_reason"
          :rows="10"
          style="max-width: 700px !important"
          placeholder="请输入退货理由，一行一个，单个退货理由不超过20字"
        />
      </a-form-item>
      <a-form-item label="退货须知" class="required">
        <a-textarea
          v-model:value="formState.return_instructions"
          :rows="10"
          :maxlength="100"
          style="max-width: 700px !important"
          placeholder="请输入退货须知，不超过100字"
        />
      </a-form-item>
    </a-card>
  </uc-layout-form>
</template>
<script setup>
import { nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { message } from 'ant-design-vue'
import { telePhoneVerify } from '@/utils/index'
import { orderSettingApi, orderSettingUpdateApi } from '../api'
import citys from '@/utils/citys'

const { formState, setFormState, setFormRules, validateForm } = useFormState({})

let dataConfig = {}
const loadData = () => {
  orderSettingApi.get({}).then(data => {
    dataConfig = data
    setFormState(data.value)
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

setFormRules({
  cancel_timeout: { required: true, message: '请输入自动取消订单时间' },
  confirm_timeout: { required: true, message: '请输入自动确认发货时间' },
  name: { required: true, message: '请输入联系人' },
  phone_number: {
    validator(_, value) {
      if (telePhoneVerify(value)) {
        return Promise.reject('请输入正确格式手机号码')
      }
      return Promise.resolve()
    }
  },
  address: { required: true, message: '请选择所在城市' },
  detail: { required: true, message: '请输入详细地址' },
  return_timeout: { required: true, message: '请输入退款期限' },
  pick_up_return_timeout: { required: true, message: '请输入自提退款期限' },
  refund_reason: {
    validator(_, value) {
      if (!value) {
        return Promise.reject('请输入退款理由')
      }

      if(value.indexOf('\n\n') >= 0) {
        return Promise.reject('退款理由存在空行，请检查修改')
      }

      const reasonArr = value.split('\n')
      const reasonErrorArr = reasonArr.filter(reason => reason.length > 20)
      if (reasonErrorArr.length) {
        return Promise.reject('单个退款理由不超过20字')
      } else {
        return Promise.resolve()
      }
    }
  },
  return_reason: {
    validator(_, value) {
      if (!value) {
        return Promise.reject('请输入退货理由')
      }
      if(value.indexOf('\n\n') >= 0) {
        return Promise.reject('退款理由存在空行，请检查修改')
      }
      const reasonArr = value.split('\n')
      const reasonErrorArr = reasonArr.filter(reason => reason.length > 20)
      if (reasonErrorArr.length) {
        return Promise.reject('单个退货理由不超过20字')
      } else {
        return Promise.resolve()
      }
    }
  },
  return_instructions: { required: true, message: '请输入退货须知' }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return
  useLoadingMessage(orderSettingUpdateApi.post({ ...dataConfig, value: formState.value })).then(() => {
    message.success('操作成功：订单设置')
    loadData()
  })
}
</script>
<style scoped lang="less"></style>
