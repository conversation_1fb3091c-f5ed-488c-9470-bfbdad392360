<template>
  <div class="agreement flex flex-dc">
    <div class="top">
      <a-form :label-col="layoutLabelCol" :wrapper-col="layoutWrapperCol">
        <a-space direction="vertical" :size="20">
          <a-form-item label="试用海报" class="required poster">
            <uc-upload
              :list="formState.value.poster ? [formState.value.poster] : []"
              :max-length="1"
              @update:list="data => (formState.value.poster = data[0])"
            />
          </a-form-item>
        </a-space>
      </a-form>
    </div>
    <uc-rich-text v-model="formState.value.desc" placeholder="请输入协议内容" :height="600" />
    <div class="footer-box">
      <a-button type="primary" @click="onSubmit">
        保存
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { apiTrial, trialGuideUpdateAction } from '../api'

import { message } from 'ant-design-vue'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  value: {
    poster: '',
    desc: ''
  }
})

const loadData = () => {
  apiTrial.get().then(res => {
    setFormState(res)
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

setFormRules({
  value: {
    validator(_, value) {
      const { poster, desc } = value
      if (!poster) return Promise.reject('请上传封面')
      if (!desc) return Promise.reject('请输入内容')
      return Promise.resolve()
    }
  }
})

const onSubmit = async () => {
  if (!(await validateForm())) return
  useLoadingMessage(trialGuideUpdateAction.post(formState.value)).then(() => {
    message.success('保存成功')
  })
}
</script>
<style scoped lang="less">
.agreement {
  height: 100%;
  padding-bottom: 50px;
  .top {
    background: #fff;
    padding-top: 30px;
    margin-bottom: 20px;
    :deep(.ant-space) {
      width: 100%;
      display: flex;
    }
    :deep(.submit-bar-place) {
      display: none;
    }
    :deep(.poster .ant-col) {
      flex: 0 0 150px;
      max-width: 150px;
    }
  }
  .footer-box {
    width: 100%;
    padding: 10px 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9;
    box-shadow: 0 0 8px #f0f1f2;
  }
  :deep(.m-rich-text) {
    padding-bottom: 80px;
  }
}
</style>
