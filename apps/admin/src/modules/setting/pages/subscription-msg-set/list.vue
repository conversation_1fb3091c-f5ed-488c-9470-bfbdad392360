<template>
  <div class="subscription-msg">
    <a-space direction="vertical" :size="20" class="flex">
      <a-card title="订阅消息">
        <a-table :data-source="collection" :pagination="false" row-key="id" :loading="loading">
          <a-table-column title="模板名称/ID" data-index="title" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis">
                {{ record.title }}
              </div>
              <div class="text-ellipsis">
                {{ record.template_id }}
              </div>
            </template>
          </a-table-column>

          <a-table-column title="关键词" data-index="config" ellipsis>
            <template #default="{ record }">
              <div v-if="record.config.length">
                {{
                  record.config
                    .filter(i => i.type !== 'page')
                    .map(item => item.label)
                    .join()
                }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="配置状态" width="120px">
            <template #default="{ record }">
              <a-badge
                :status="statusFilter(record.config_status).colorType"
                :text="statusFilter(record.config_status).label"
              />
            </template>
          </a-table-column>

          <a-table-column title="操作" width="70px">
            <template #default="{ record }">
              <a-button type="link" @click="toEdit(record)">
                配置
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </a-card>
    </a-space>
  </div>
</template>
<script setup>
import { nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { statusFilter } from '@/composables/subscriptionMsgStatus'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { subscriptionMsgApi } from '../../api'

const collection = ref([])

const loadData = async () => {
  const [costTmplRes] = await Promise.all([subscriptionMsgApi.list()])
  collection.value = costTmplRes
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据…'
  })
})

const router = useRouter()
const toEdit = ({ id }) => router.push({ name: 'subscription-msg-set-edit', params: { id } })
</script>
