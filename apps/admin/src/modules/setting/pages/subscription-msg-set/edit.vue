<template>
  <a-card class="top-title" style="margin-bottom: 20px">
    <div style="font-weight: 500; font-style: normal; font-size: 20px">
      {{ formState.title }}
    </div>
    <div style="color: rgba(0, 0, 0, 0.***************)">
      {{ formState.desc }}
    </div>
  </a-card>
  <div class="layout-form">
    <a-form ref="formRef" :model="formState">
      <a-space direction="vertical" :size="20">
        <div>
          <a-space direction="vertical" :size="20" class="flex">
            <a-card title="模版配置">
              <div>
                <div style="float: left">
                  <span style="display: inline-block; margin-right: 4px; color: #ff4d4f; font-size: 14px">&nbsp;&nbsp;&nbsp;&nbsp;*</span><span>模版ID：</span>
                </div>
                <a-form-item
                  class="required"
                  name="template_id"
                  :rules="{
                    required: true,
                    message: '请输入模版ID',
                    trigger: 'change'
                  }"
                >
                  <a-input
                    v-model:value="formState.template_id"
                    style="width: 690px; !important"
                    placeholder="请输入模版ID"
                  />
                </a-form-item>
              </div>
              <div style="clear: both"></div>
              <div style="float: left">
                <span style="display: inline-block; margin-right: 4px; color: #ff4d4f; font-size: 14px">*</span><span>模版内容：</span>
              </div>
              <div style="width: 290px; float: left">
                <div v-for="(domain, index) in formState.config" v-show="domain.type !== 'page'" :key="domain.key">
                  <a-input
                    v-model:value="domain.label"
                    :disabled="true"
                    style="width: 100px; display: inline-block; margin-right: 20px"
                  />
                  <a-form-item
                    style="display: inline-block"
                    :name="['config', index, 'type']"
                    :rules="
                      domain.type == 'page'
                        ? {}
                        : {
                          required: true,
                          message: '请输入参数类型',
                          trigger: 'change'
                        }
                    "
                  >
                    <a-input
                      v-model:value="domain.type"
                      :disabled="domain.type == 'page'"
                      placeholder="参数类型"
                      style="width: 150px"
                    />
                  </a-form-item>
                </div>
              </div>
              <div style="float: left">
                <a-form-item
                  v-for="(domain, index) in formState.config"
                  v-show="domain.type !== 'page'"
                  :key="domain.key"
                  :name="['config', index, 'value']"
                  :rules="
                    domain.is_default || domain.type == 'page'
                      ? {}
                      : {
                        required: true,
                        message: '请输入参数内容',
                        trigger: 'change'
                      }
                  "
                >
                  <a-input
                    v-model:value="domain.value"
                    :disabled="domain.is_default || domain.type == 'page'"
                    placeholder="参数内容"
                    style="width: 400px"
                  />
                </a-form-item>
              </div>
            </a-card>
          </a-space>
        </div>
      </a-space>
    </a-form>
    <div class="submit-bar">
      <a-space :size="10">
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button type="primary" :loading="loading" @click="onSave">
          保存
        </a-button>
      </a-space>
    </div>
  </div>
</template>
<script setup>
import { watch, nextTick, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { subscriptionMsgApi } from '../../api'
import { cloneDeep } from 'lodash'
import { useLoading } from '@/composables/useToggles'

const router = useRouter()
const { loading, setLoading } = useLoading()
const formRef = ref()
const { id } = useRoute().params
const loadData = () => {
  subscriptionMsgApi.get(id, { filters: {} }).then(res => {
    setFormState(res)
  })
}
if (id) {
  nextTick(() => {
    loadData()
  })
}
const { formState, setFormState } = useFormState({
  title: undefined,
  desc: undefined,
  rules: []
})

const onCancel = () => router.back()

const onSave = async () => {
  setLoading(true)
  try {
    await handleSubmit()
  } finally {
    setLoading(false)
  }
}

const handleSubmit = async () => {
  formRef.value
    .validate()
    .then(async () => {
      let formParams = cloneDeep(formState.value)
      if (id) {
        await subscriptionMsgApi.update(id, formParams)
        message.success('编辑完成')
      }
      router.back()
    })
    .catch(error => {
      // console.log('error', error)
    })
}
</script>
<style lang="less" scoped>
.top-title {
  :deep(.ant-card-body) {
    padding: 20px !important;
  }
}

.layout-form {
  position: relative;

  :deep(.ant-space) {
    width: 100%;
    display: flex;
  }

  :deep(.ant-input-number) {
    width: 100%;
    max-width: 500px;
  }

  :deep(.ant-form .ant-form-item) {
    .ant-select,
    .ant-input,
    .ant-cascader-picker,
    .ant-input-wrapper {
      max-width: 690px !important;
    }
  }
}

.submit-bar {
  &,
  &-place {
    height: 60px;
  }

  position: fixed;
  bottom: 6px;
  left: 0;
  z-index: 9;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  width: 100%;
  background: #fff;
  box-shadow: 0 0px 8px #f0f1f2;

  :deep(.ant-space) {
    padding-right: 10px;
    justify-content: flex-end;
  }
}
</style>
