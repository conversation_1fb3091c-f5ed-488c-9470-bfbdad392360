<template>
  <uc-layout-list title="核销员配置">
    <template #filter>
      <a-form-item name="operation">
        <a-input-group compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            placeholder="请选择"
            :options="writeOff.options()"
            @change="changeConditionKey()"
          />
          <a-input v-model:value.trim="formState.conditionValue" class="w-420" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="setModalVisible(true)">
        新增核销员
      </a-button>
    </template>
    <template #list>
      <a-table
        row-key="id"
        :pagination="stdPagination(data)"
        :data-source="data.items"
        :loading="loading"
        @change="setPage"
      >
        <a-table-column title="用户昵称" data-index="nickname" ellipsis />
        <a-table-column title="手机号码" data-index="phone_number" ellipsis>
          <template #default="{ record }">
            <span>{{ $formatters.numberEncryption(record.phone_number) }}</span>
          </template>
        </a-table-column>
        <a-table-column width="70px" title="操作">
          <template #default="{ record }">
            <a-popconfirm
              placement="left"
              :title="`你确定要删除 ${record.nickname} 么？`"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal :visible="modalVisible" title="新增核销员" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item class="required" name="phones">
        <a-textarea
          v-model:value="addFormState.phones"
          placeholder="请输入手机号, 一行一个"
          :auto-size="{ minRows: 5, maxRows: 10 }"
          :maxlength="200"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onCancel">
        取消
      </a-button>
      <a-button type="primary" :loading="modelLoading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { userRoles, getWriteOffList, batchAddUser, deleteUser } from '../api'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { useFormState } from '@/composables/useFormState'
import { writeOff } from '../enums'
import { ref } from 'vue'
import { message, notification } from 'ant-design-vue'

// 查询表单
const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: writeOff.phone_number,
  conditionValue: undefined
})

onRestFormState(() => {
  setPage()
})

// 弹框控制变量
const { modalVisible, setModalVisible } = useModalVisible()

// loading
const { loading: modelLoading, setLoading: setModelLoading } = useLoading()

// 用户角色
const roleId = ref(0)

// 新增表单
const {
  formState: addFormState,
  resetFormState: resetAddFromState,
  setFormRules: setAddFormRules,
  validateForm: setAddValidateForm
} = useFormState({
  phones: ''
})

// 自定义手机号验证
const SendUserValidate = (_, value) => {
  if (!value) return Promise.reject('请填写手机号')
  const arr = value.split('\n').map(value => value.trim())
  const resArr = [...new Set(arr)]
  if (arr.length !== resArr.length) return Promise.reject('手机号码不可重复')
  return Promise.resolve()
}
setAddFormRules({
  phones: { validator: SendUserValidate, trigger: 'blur' }
})

// 处理查询表单参数
const filterParams = () => {
  if (formState.value.conditionKey && formState.value.conditionValue) {
    if (formState.value.conditionKey == writeOff.nickname) {
      return { [formState.value.conditionKey]: '%' + formState.value.conditionValue + '%' }
    } else {
      return { [formState.value.conditionKey]: formState.value.conditionValue }
    }
  }
  return {}
}

// 表格请求
const { data, setPage, loading, refresh } = usePaginatorApiRequest(async ({ offset, limit }) => {
  if (!roleId.value) {
    const roleArr = await userRoles.list()
    roleId.value = roleArr[0].id
  }

  return getWriteOffList.paginator({
    filters: useTransformQuery({ ...filterParams(), role_id: roleId.value }, {}),
    offset,
    limit,
    sorts: ['-role_updated_at']
  })
})

// 改变就清空
const changeConditionKey = () => {
  formState.value.conditionValue = undefined
}

// 关闭新增弹框
const onCancel = () => {
  setModalVisible(false)
  resetAddFromState()
}

// 新增核销员
const handleCreate = async () => {
  setModelLoading(true)

  if (!(await setAddValidateForm())) {
    setModelLoading(false)
    return
  }

  const phones = addFormState.value.phones
    .split('\n')
    .map(value => value.trim())
    .join(',')

  batchAddUser
    .post({ phones, role_id: roleId.value })
    .then(res => {
      let description = ''
      const keys = Object.keys(res.invalid_phones)

      if (keys.length > 0) {
        keys.forEach(key => {
          description += `列表第 ${+key + 1} 个手机号为 ${res.invalid_phones[key]} 新增失败，请重试`
        })

        notification.open({
          message: '添加失败',
          description,
          duration: 0
        })
      } else {
        message.success('插入成功')
      }

      setPage()
    })
    .finally(() => {
      resetAddFromState()
      setModelLoading(false)
      setModalVisible(false)
    })
}

// 删除核销员
const handleDelete = ({ id }) => {
  deleteUser.post({ user_id: id }).then(() => {
    message.success('删除完成')
    setPage()
  })
}
</script>
