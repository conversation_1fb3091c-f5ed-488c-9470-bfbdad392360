<template>
  <uc-layout-list title="热搜词设置">
    <template #filter>
      <div v-if="topList" class="p-b-16">
        <a-tag v-for="(item,index) in topList" :key="index">
          {{ item.keyword }}（{{ item.total_hits }}）
        </a-tag>
      </div>
      <p v-else class="w-fill color-grey t-center">
        暂无热搜词数据
      </p>
    </template>
    <template #extra>
      <a-button type="primary" @click="setModalVisible(true)">
        新增热搜词
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="groupedHotSearchKeys"
        row-key="id"
        :loading="loading"
        :pagination="false"
        :default-expand-all-rows="false"
        :scroll="{ x: 1200 }"
        @change="setPage"
      >
        <a-table-column title="词条名称" data-index="keyword" ellipsis />
        <a-table-column title="词条排序">
          <template #default="{ record }">
            <a-input-number
              v-if="record.sort"
              v-model:value="record.sort"
              :max="9999"
              class="sort"
              style="width:80px;text-align:center;"
              @blur="handleBlurSort(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <template v-if="!record.children">
              <a-popconfirm
                placement="left"
                title="你确定要删除该数据么？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" class="danger">
                  删除
                </a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal :visible="modalVisible" title="新增热搜词" @cancel="onCancel">
    <a-form :model="formState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item required label="词条名称" class="required">
        <a-input v-model:value="formState.keyword" maxlength="50" placeholder="请输入词条名称，不超过50字" />
      </a-form-item>
      <a-form-item required label="词条排序" class="required">
        <a-input-number
          v-model:value="formState.sort"
          :min="0"
          :max="9999"
          placeholder="请输入排序值（排序值越大排名越靠前）"
        />
      </a-form-item>
      <a-form-item label="分组名称">
        <a-select
          v-model:value="formState.group"
          :options="groupOptions"
          placeholder="请输入分组名称，不超过50字"
          show-search
          @search="onSelectSearch"
          @blur="handleBlur"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onCancel">
        取消
      </a-button>
      <a-button type="primary" :loading="submitting" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import {nextTick} from 'vue'
import {message} from "ant-design-vue";
import {useFormState} from '@/composables/useFormState'
import {useLoadingMessage} from '@/composables/useLoadingMessage'
import {useApiRequest} from '@/composables/useApiRequest'
import {hotSearchApi, hotSearchTopApi} from '../api'
import {useModalVisible} from '@/composables/useToggles'
import { generateRandom } from '@/utils/functions'

const {data, getData: setPage, loading} = useApiRequest(() =>
  hotSearchApi.list()
)
const submitting = ref(false)

const topList = ref()
const loadTopData = () => {
  hotSearchTopApi.get().then((res) => {
    topList.value = res
  })
}

nextTick(() => {
  useLoadingMessage(loadTopData(), {
    loadingText: '正在加载数据…'
  })
})

const handleDelete = async ({id}) => {
  await hotSearchApi.delete(id)
  message.success('删除完成')
  setPage()
}

const handleBlurSort = async ({id, sort}) => {
  if (sort == undefined) return
  if (sort < 0) return message.success('请选择0以上的数字')

  await hotSearchApi.update(id, {sort})
  message.success('修改成功')
  setPage()
}

// 新增热搜词

const {modalVisible, setModalVisible} = useModalVisible()

const {formState, resetFormState, setFormRules, validateForm} = useFormState({
  keyword: undefined,
  sort: undefined,
  group: undefined,
})

setFormRules({
  keyword: {required: true, message: '请输入词条名称'},
  sort: {required: true, message: '请输入词条排序'},
})

const showAdd = () => {
  setModalVisible(true)
}

const handleSubmit = async () => {
  if (!(await validateForm())) {
    return
  }
  if(submitting.value) {
    return
  }
  submitting.value = true


  try {
    await hotSearchApi.create(formState.value)
    message.success('创建完成')
  } finally {
    onCancel()
    setPage()
    submitting.value = false
  }
}

const onCancel = () => {
  setModalVisible(false)
  resetFormState()
}

const groupOptions = computed(() => {
  const set = new Set()
  data.value.map(item => {
    if (item.group) set.add(item.group)
  })
  let arr = [...set]
  return arr.map(a => {
    return {
      value: a,
      label: a
    }
  })
})

const groupedHotSearchKeys = computed(() => {
  if(!data.value) return []
  const dateItems = toRaw(data.value)

  /* 分组处理 */
  const set = new Set()
  dateItems.map(item => {
    if (item.group) set.add(item.group)
  })
  let arr = [...set]
  arr = arr.map(a => {
    return {
      id: generateRandom(),
      keyword: a,
      group: null,
      children: []
    }
  })

  const groupData = []
  const noGroupData = dateItems.filter(item => !item.group)
  const hasGroupData = dateItems.filter(item => item.group)
  groupData.push(...noGroupData)
  arr.map(a => {
    hasGroupData.map(c => {
      if (c.group == a.keyword) {
        a.children.push(c)
      }
    })
  })

  groupData.push(...arr)

  return groupData
})

let inputGroup
const onSelectSearch = input => {
  if (input) inputGroup = input
}
const handleBlur = () => {
  if (inputGroup) {
    formState.value.group = inputGroup
    inputGroup = ''
  }
}
</script>
<style scoped lang="less">
.sort {
  :deep(.ant-input-number-input) {
    text-align: center !important;
  }
}
</style>
