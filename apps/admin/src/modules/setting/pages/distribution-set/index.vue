<template>
  <uc-layout-form :is-save="false" :is-cancel="false" @submit="handleSubmit">
    <a-card title="分销设置">
      <a-form-item label="结算周期" class="required">
        <a-input v-model:value="formState.complete_timeout" addon-after="天" placeholder="用户签收后多少天结算佣金" />
      </a-form-item>
      <a-form-item label="最小提现金额" class="required">
        <a-input v-model:value="min" addon-after="元" placeholder="只能是大于0的整数，默认是1" />
      </a-form-item>
      <a-form-item label="风险控制" class="required risk-form-item">
        <a-input v-model:value="formState.risk.day" addon-after="天" placeholder="在？天内" />
        <a-input v-model:value="formState.risk.number" addon-after="件" placeholder="推荐总数" />
      </a-form-item>
    </a-card>
  </uc-layout-form>
</template>

<script setup>
import { computed, nextTick } from "vue";
import { useFormState } from "@/composables/useFormState";
import { distributionSettingApi } from "../../api";
import { useLoadingMessage } from "@/composables/useLoadingMessage";

const { formState, setFormState } = useFormState({
  withdrawal: {},
  risk: {}
})
let dataConfig = {}
const min = computed(() => {
  return formState.value.withdrawal.min / 100 || 0
})
const loadData = () => {
  distributionSettingApi.get().then(data => {
    dataConfig = data
    setFormState(data.value)
  })
}

const handleSubmit = async () =>{
  if (!(await validateForm())) return
  useLoadingMessage(distributeSettingApi.patch({...dataConfig,value:formState.value})).then(() => {
      message.success('操作成功：分销设置')
      loadData()
  })
}
nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

</script>

<style scoped lang="less">
:deep(.ant-form .ant-form-item) {
  .ant-input-wrapper,
  .ant-input-group {
    max-width: 590px;
  }
}

.risk-form-item {
  .ant-form-item-control-input-content {
    display: flex;
  }
  .ant-input-group-wrapper {
    width: 295px;
  }
}
</style>
