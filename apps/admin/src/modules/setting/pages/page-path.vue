<template>
  <uc-layout-list title="页面路经">
    <template #filter>
      <a-form-item name="name">
        <a-input v-model:value.trim="formState.name" placeholder="页面名称" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="页面名称" data-index="name" width="200px" ellipsis />
        <a-table-column title="页面路径" data-index="page" ellipsis />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-button type="link" @click="copyLink(record.page)">
              链接
            </a-button>
            <!-- <a-button type="link" @click="copyLink(record.page)">
              小程序链接
            </a-button> -->
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { pagePathApi } from '../api'

const linkPath = Object.freeze('/invitation/pages/standard/page-path?id=')

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  pagePathApi.paginator({
    filters: useTransformQuery(formState, { name: 'like' }),
    offset,
    limit
  })
)
const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined
})

onRestFormState(() => setPage())
</script>
