<template>
  <uc-layout-form @submit="handleSubmit">
    <div class="transport-cost">
      <a-space direction="vertical" :size="20" class="flex">
        <a-card title="标准配送">
          <a-form-item label="模板名称" class="required">
            <a-input v-model:value="formState.title" maxlength="30" placeholder="请输入运费模版名称，不超过30字" />
          </a-form-item>
          <a-form-item label="运费说明" class="required">
            <a-textarea v-model:value="formState.desc" maxlength="200" placeholder="请输入运费配送说明，不超过200字" :rows="4" />
          </a-form-item>
        </a-card>
        <a-card title="配送运费">
          <template #extra>
            <a-button type="primary" @click="setModalVisible(true)">
              添加运费
            </a-button>
          </template>
          <a-table :data-source="formState.rules" row-key="address_str" :loading="loading" @change="setPage">
            <a-table-column title="配送地区" data-index="address_str" ellipsis />
            <a-table-column title="运费" data-index="freight" width="150px" />
            <a-table-column title="操作" width="70px">
              <template #default="{ index }">
                <a-button type="link" class="danger" @click="onDelete(index)">
                  删除
                </a-button>
              </template>
            </a-table-column>
          </a-table>
        </a-card>
      </a-space>
      <a-modal :visible="modalVisible" title="添加运费" @cancel="onCancel" @ok="onSubmitAddCost">
        <a-form :model="formStateCost" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
          <a-form-item required label="配送地区" class="required">
            <a-select v-model:value="formStateCost.address" placeholder="请选择配送地区" mode="multiple" @change="changeSelectedAreaAll">
              <a-select-option v-for="(item,index) in map_options_final" :key="index" :value="item.label" :disabled="item.disabled || allAreaDisabled">
                {{ item.label }}
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item required label="配送运费" class="required">
            <a-input-number v-model:value="formStateCost.freight" :min="0" placeholder="请输入配送运费" />
          </a-form-item>
        </a-form>
      </a-modal>
    </div>
  </uc-layout-form>
</template>
<script setup>
import { watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useModalVisible } from '@/composables/useToggles'
import { freightApi } from '../../api'
import citys from '@/utils/citys'
import { cloneDeep } from 'lodash'
import formatters from '@/utils/formatters'

let { map_options, map_options_all, map_option_type_all } = citys

let map_options_final = ref([cloneDeep(map_option_type_all), ...map_options])

const router = useRouter()
const { id } = useRoute().params

const AREA_TYPE_ALL = Object.freeze('所有地区')

const loadData = () => {
  freightApi.get(id, { relations: ['rules'] }).then(res => {
    const { rules } = res

    //长度为34的则为所有地区
    rules.forEach(rule => {
      if (rule.address.length == 34) {
        rule.address = [AREA_TYPE_ALL]
        rule.address_str = AREA_TYPE_ALL
      } else {
        rule.address_str = rule.address.join('、')
      }

      // 处理价格
      rule.freight = formatters.thousandSeparator(rule.freight)
    })
    setFormState(res)
  })
}

if (id) {
  nextTick(() => {
    loadData()
  })
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  desc: undefined,
  rules: []
})

// 列表已有地区选项禁用逻辑
watch(
  () => formState.value,
  () => {
    const { rules } = formState.value
    // 列表有数据
    if (rules.length) {
      // 重置禁用
      map_options_final.value.forEach(op => (op.disabled = false))

      // 处理地区选项
      if (rules.length == 1 && rules[0].address.includes(AREA_TYPE_ALL)) {
        // 选中所有地区 禁用逻辑
        allAreaDisabled.value = true
        map_options_final.value.forEach(op => (op.disabled = true))
      } else {
        // 未选中所有地区则去掉所有地区选项
        if (map_options_final.value[0].label == AREA_TYPE_ALL) {
          map_options_final.value.splice(0, 1)
        }
      }

      // 禁用地区选项
      rules.forEach(rule => {
        // 已选地区禁用
        rule.address.forEach(ad => {
          let matchObj = map_options_final.value.find(({ label }) => label == ad)
          if (matchObj) matchObj.disabled = true
        })
      })
    } else {
      // 加上所有地区选项
      if (map_options_final.value[0].label != AREA_TYPE_ALL) {
        map_options_final.value = [cloneDeep(map_option_type_all), ...map_options_final.value]
      }

      // 去除所有禁用
      map_options_final.value.forEach(op => (op.disabled = false))
      allAreaDisabled.value = false
    }
  },
  { deep: true }
)

const onDelete = index => formState.value.rules.splice(index, 1)

setFormRules({
  title: { required: true, message: '请输入模板名称' },
  desc: { required: true, message: '请输入运费说明' },
  rules: {
    validator(_, value) {
      if (value.length == 0) {
        return Promise.reject('请先添加运费')
      }
      return Promise.resolve()
    }
  }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return

  let formParams = cloneDeep(formState.value)
  let { rules } = formParams

  //配送地区为所有地区时加入所有地区省份
  if (rules.length == 1 && rules[0]?.address_str.includes(AREA_TYPE_ALL)) {
    rules[0].address = []
    map_options.forEach(province => {
      rules[0].address.push(province.label)
    })
  }

  //价格处理
  rules.forEach(rule => {
    rule.freight = formatters.priceToInteger(rule.freight)
  })

  if (id) {
    await freightApi.update(id, formParams)
    message.success('编辑完成')
  } else {
    await freightApi.create(formParams)
    message.success('创建完成')
  }
  router.back()
}

// 新增运费modal

const { modalVisible, setModalVisible } = useModalVisible()

const {
  formState: formStateCost,
  resetFormState: resetFormStateCost,
  setFormRules: setFormRulesCost,
  validateForm: validateFormCost
} = useFormState({
  address: undefined,
  freight: undefined
})

setFormRulesCost({
  address: { required: true, message: '请选择地区' },
  freight: { required: true, message: '请输入配送运费' }
})

const onCancel = () => {
  setModalVisible(false)
  resetFormStateCost()
  changeSelectedAreaAll()
}

const onSubmitAddCost = async () => {
  if (!(await validateFormCost())) return

  let rawRecord = {}
  const { address } = formStateCost.value
  Object.assign(rawRecord, {
    ...formStateCost.value,
    address_str: address.join('、')
  })
  formState.value.rules.push(rawRecord)
  onCancel()
}

// 禁用所有地区选项逻辑 监听不到所以使用change事件
let allAreaDisabled = ref(false)
const changeSelectedAreaAll = () => {
  let { address } = formStateCost.value
  if (address === undefined) {
    if (allAreaDisabled.value) allAreaDisabled.value = false
  } else {
    if (address.includes(AREA_TYPE_ALL)) {
      formStateCost.value.address = [AREA_TYPE_ALL]
      allAreaDisabled.value = true
    } else {
      allAreaDisabled.value = false
    }
  }
}
</script>
