<template>
  <div class="transport-cost">
    <a-space direction="vertical" :size="20" class="flex">
      <a-card title="计费规则">
        <template #extra>
          <a-button type="primary" @click="handleSubmit">
            保存
          </a-button>
        </template>
        <a-form-item label="结算方式" class="required">
          <a-radio-group v-model:value="constRule.value.rule" style="height: 32px;line-height: 32px;">
            <a-radio value="min" class="m-b-16" title="按最低运费结算（订单中的商品有多个运费模板时，取订单中运费最少的商品的运费计为订单总运费）">
              按最低运费结算
            </a-radio>
            <a-radio value="max" title="按最高运费结算（订单中的商品有多个运费模板时，取订单中运费最多的商品的运费计为订单总运费）">
              按最高运费结算
            </a-radio>
          </a-radio-group>
        </a-form-item>
        <!-- <a-form-item label="包邮条件" class="required">
          <a-input-group compact>
            <a-select
              v-model:value="constRule.value.freight_free_type"
              class="m-r-10"
              style="width:120px;"
              placeholder="请选择"
              :options="conditionOptions"
              @change="constRule.value.freight_free_condition = undefined"
            />
            <a-input-number v-if="constRule.value.freight_free_type == freightCondition.pay_amount" v-model:value.trim="constRule.value.freight_free_condition" style="width:300px;" :min="0" placeholder="请输入商品实付款金额" />
            <a-input-number v-if="constRule.value.freight_free_type == freightCondition.total_amount" v-model:value.trim="constRule.value.freight_free_condition" style="width:300px;" :min="0" placeholder="请输入商品总金额" />
            <a-input-number v-if="constRule.value.freight_free_type == freightCondition.number" v-model:value.trim="constRule.value.freight_free_condition" style="width:300px;" :min="1" placeholder="请输入商品总件数" />
          </a-input-group>
        </a-form-item> -->
      </a-card>
      <a-card title="运费模板">
        <template #extra>
          <a-button type="primary" @click="toAdd">
            新增运费模板
          </a-button>
        </template>
        <a-table :data-source="collection" row-key="id" :loading="loading" @change="setPage">
          <a-table-column title="模板名称" data-index="title" ellipsis />
          <a-table-column title="默认模板" width="110px">
            <template #default="{ record }">
              <a-radio v-model:checked="record.is_default" @change="handleSetDefaultTmpl(record)" />
            </template>
          </a-table-column>
          <a-table-column title="操作" width="100px">
            <template #default="{ record }">
              <a-button type="link" :disabled="record.id == 1" @click="toEdit(record)">
                编辑
              </a-button>
              <a-popconfirm
                placement="left"
                title="你确定要删除该数据么？"
                :disabled="!record.can_delete"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" class="danger" :disabled="!record.can_delete">
                  删除
                </a-button>
              </a-popconfirm>
            </template>
          </a-table-column>
        </a-table>
      </a-card>
    </a-space>
  </div>
</template>
<script setup>
import { nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { freightApi, freightSettingApi, freightSettingUpdateApi } from '../../api'
import { freightCondition } from '../../enums'
import formatters from '@/utils/formatters'
import { cloneDeep } from 'lodash'

const collection = ref([])
const constRule = ref({ value: {} })

const conditionOptions = freightCondition.options()

const loadData = async () => {
  const [costTmplRes, constRuleRes] = await Promise.all([freightApi.list(), freightSettingApi.get()])
  // 默认参数
  if (!constRuleRes.value.freight_free_type) constRuleRes.value.freight_free_type = freightCondition.pay_amount

  let { freight_free_type, freight_free_condition } = constRuleRes.value
  if (freight_free_condition && freight_free_type !== freightCondition.number) {
    constRuleRes.value.freight_free_condition = formatters.thousandSeparator(freight_free_condition, false)
  }

  collection.value = costTmplRes
  constRule.value = constRuleRes
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据…'
  })
})

const router = useRouter()
const toAdd = () => router.push({ name: 'transport-cost-set-add' })
const toEdit = ({ id }) => router.push({ name: 'transport-cost-set-edit', params: { id } })

const handleDelete = async ({ id }) => {
  await freightApi.delete(id)
  message.success('删除完成')
  loadData()
}

const handleSetDefaultTmpl = async ({ id }) => {
  await freightApi.update(id, { is_default: 1 })
  message.success('设置成功')
  loadData()
}

const handleSubmit = async () => {
  let params = cloneDeep(constRule.value)
  const { freight_free_type, freight_free_condition } = params.value
  if (freight_free_type !== freightCondition.number) {
    params.value.freight_free_condition = formatters.priceToInteger(freight_free_condition)
  }

  await freightSettingUpdateApi.post(params)
  message.success('设置成功')
}
</script>
