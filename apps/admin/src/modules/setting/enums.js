/**
 * 推荐位类型
 */
export const recommendPositionType = Object.freeze({
  /**
   * 搜索推荐位
   */
  search: 'search',

  /**
   * 分类推荐位
   */
  category: 'category',

  /**
   * 支付推荐位
   */
  payment: 'payment',

  /**
   * 购物车推荐位
   */
  shopCart: 'shop_cart',

  /**
   * 商品详情推荐位
   */
  productDetails: 'product_detail',

  /**
   * 我的推荐位
   */
  mine: 'mine',

  options() {
    return [
      { label: '搜索推荐位', value: this.search },
      { label: '分类推荐位', value: this.category },
      { label: '支付推荐位', value: this.payment },
      { label: '购物车推荐位', value: this.shopCart },
      { label: '商详推荐位', value: this.productDetails },
      { label: '我的推荐位', value: this.mine },
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 推荐位方式
 */
export const recommendPositionMode = Object.freeze({
  /**
   * 规则推荐
   */
  rule: 'rule',
  /**
   * 指定推荐
   */
  appoint: 'appoint',
  /**
   * 自定义推荐
   */
  custom: 'custom',

  options() {
    return [
      { label: '规则推荐', value: this.rule },
      { label: '指定推荐', value: this.appoint },
      { label: '自定义推荐', value: this.custom }
    ]
  }
})

/**
 * 推荐规则类型
 */
export const recommendRule = Object.freeze({
  /**
   * 商品浏览降序推荐
   */
  view: '-pv',

  /**
   * 商品销量降序推荐
   */
  saleNum: '-sale_num',

  /**
   * 商品上架降序推荐
   */
  onSaleTime: '-on_sale_time',

  /**
   * 商品评分降序推荐
   */
  commentScore: '-comment_score',

  /**
   * 商品价格升序推荐
   */
  defaultPrice: 'default_price',

  options() {
    return [
      { label: '商品浏览降序推荐', value: this.view },
      { label: '商品销量降序推荐', value: this.saleNum },
      { label: '商品上架降序推荐', value: this.onSaleTime },
      { label: '商品评分降序推荐', value: this.commentScore },
      { label: '商品价格升序推荐', value: this.defaultPrice }
    ]
  }
})

/**
 * 运费模板免减条件
 */
export const freightCondition = Object.freeze({
  /**
   * 商品实付款
   */
  pay_amount: 'pay_amount',

  /**
   * 商品总金额
   */
  total_amount: 'total_amount',

  /**
   * 商品总件数
   */
  number: 'number',

  options() {
    return [
      { label: '商品实付款', value: this.pay_amount },
      { label: '商品总金额', value: this.total_amount },
      { label: '商品总件数', value: this.number }
    ]
  }
})

/**
 * 海报设置类型
 */
export const posterSetTypes = Object.freeze({
  /**
   * 新品试用
   */
  trial: 'trial',

  /**
   * 积分商城
   */
  credit_goods: 'credit_goods',

  options() {
    return [
      { label: '新品试用', value: this.trial },
      { label: '积分商城', value: this.credit_goods }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 渠道设置
 */
export const channel = Object.freeze({
  /**
   * 微信
   */
  wechat: 'wechat',
  /**
   * 京东
   */
  jd: 'jd',
  /**
   * 天猫
   */
  tmall: 'tmall',
  /**
   * 抖音
   */
  tiktok: 'tiktok',
  /**
   * 微博
   */
  blog: 'blog',
  /**
   * 小红书
   */
  red_book: 'red_book',

  options() {
    return [
      { label: '微信', value: this.wechat },
      { label: '京东', value: this.jd },
      { label: '天猫', value: this.tmall },
      { label: '抖音', value: this.tiktok },
      { label: '微博', value: this.blog },
      { label: '小红书', value: this.red_book }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 用户昵称 or 手机号
 */
export const writeOff = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',

  /**
   * 手机号
   */
  phone_number: 'phone_number',

  options() {
    return [
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phone_number }
    ]
  }
})
