export default {
  path: 'setting',
  meta: {
    title: '设置',
    antIcon: 'SettingOutlined'
  },
  children: [
    {
      path: 'system-set',
      name: 'system-set',
      meta: {
        title: '系统设置'
      },
      component: () => import('./pages/system-set/index')
    },
    {
      path: 'order-set',
      name: 'order-set',
      meta: {
        title: '订单设置'
      },
      component: () => import('./pages/order-set')
    },
    {
      path: 'user-set',
      name: 'user-set',
      meta: {
        title: '用户设置'
      },
      component: () => import('./pages/user-set')
    },
    {
      path: 'transport-cost-set',
      name: 'transport-cost-set',
      meta: {
        title: '运费设置',
        keepAlive: true
      },
      component: () => import('./pages/transport-cost-set/list')
    },
    {
      path: 'transport-cost-set-edit/:id',
      name: 'transport-cost-set-edit',
      meta: {
        title: '编辑运费模板'
      },
      hidden: true,
      component: () => import('./pages/transport-cost-set/edit')
    },
    {
      path: 'transport-cost-set-add',
      name: 'transport-cost-set-add',
      meta: {
        title: '新增运费模板'
      },
      hidden: true,
      component: () => import('./pages/transport-cost-set/edit')
    },
    {
      path: 'poster-set',
      name: 'poster-set',
      meta: {
        title: '海报设置'
      },
      component: () => import('./pages/poster/list')
    },
    {
      path: 'subscription-msg-set',
      name: 'subscription-msg-set',
      meta: {
        title: '订阅消息'
      },
      component: () => import('./pages/subscription-msg-set/list')
    },
    {
      path: 'subscription-msg-set-edit/:id',
      name: 'subscription-msg-set-edit',
      meta: {
        title: '编辑订阅消息'
      },
      hidden: true,
      component: () => import('./pages/subscription-msg-set/edit')
    },
    {
      path: 'page-path',
      name: 'page-path',
      meta: {
        title: '页面路径'
      },
      component: () => import('./pages/page-path')
    },
    {
      path: 'write-off',
      name: 'write-off',
      meta: {
        title: '核销员配置'
      },
      component: () => import('./pages/write-off')
    },
    // {
    //   path: 'handbook',
    //   name: 'handbook',
    //   meta: {
    //     title: '试用指南'
    //   },
    //   component: () => import('./pages/handbook')
    // },
    {
      path: 'hot-search-set',
      name: 'hot-search-set',
      meta: {
        title: '热搜词设置'
      },
      component: () => import('./pages/hot-search-set')
    },
    {
      path: 'recommend-position-set',
      name: 'recommend-position-set',
      meta: {
        title: '推荐位设置'
      },
      component: () => import('./pages/recommend-position-set')
    },
    {
      path: 'distribution-set',
      name: 'distribution-set',
      meta: {
        title: '分销设置'
      },
      component: () => import('./pages/distribution-set/index.vue')
    },
  ]
}
