import { apiFactory } from '@/api'

export const settingsActions = apiFactory.command('/setting/settings/register-agreement')
export const settingsActionsUpdateApi = apiFactory.command('/setting/settings/register-agreement/update')

export const permissionsApi = apiFactory.command(`/admin/roles/permissions`) // 权限

export const freightSettingApi = apiFactory.command('/setting/settings/freight') // 计费规则
export const freightSettingUpdateApi = apiFactory.command('/setting/settings/freight/update') // 计费规则

export const freightApi = apiFactory.restful('/setting/freights') // 运费模板

export const subscriptionMsgApi = apiFactory.restful('/template-message/subscribe-message-templates') // 订阅消息

export const orderSettingApi = apiFactory.command('/setting/settings/order') // 订单设置
export const orderSettingUpdateApi = apiFactory.command('/setting/settings/order/update') // 订单设置
export const userSettingApi = apiFactory.command('/setting/settings/user-config') // 用户设置
export const userSettingUpdateApi = apiFactory.command('/setting/settings/user-config/update') // 用户设置

export const systemSettingApi = apiFactory.command('/setting/settings/system') // 系统设置
export const systemSettingUpdateApi = apiFactory.command('/setting/settings/system/update') // 修改系统设置

export const hotSearchApi = apiFactory.restful('/setting/hot-search-keywords') // 热搜词列表

export const hotSearchTopApi = apiFactory.command('/setting/hot-search-keywords/ranking') // 热搜词排名

export const recommendPositionApi = apiFactory.command('/setting/settings/recommend')
export const recommendPositionUpdateApi = apiFactory.command('/setting/settings/recommend/update')
export const pagePathApi = apiFactory.restful('/setting/page-paths') // 页面路径
export const channelApi = apiFactory.restful('/setting/channels') // 渠道名称

// mock data api

export const codeRewardApi = apiFactory.restful('/product-register/awards')

export const apiTrial = apiFactory.command('/setting/settings/trial') // 试用指南
export const trialGuideUpdateAction = apiFactory.command('/setting/settings/trial/update') // 试用指南
export const posterSetApi = apiFactory.command('/setting/settings/poster') // 海报设置
export const posterSetUpdateApi = apiFactory.command('/setting/settings/poster/update') // 海报更新

export const userRoles = apiFactory.restful('/user/roles') // 用户角色列表
export const getWriteOffList = apiFactory.restful(`/user/users`) // 根据角色获取用户列表
export const batchAddUser = apiFactory.command(`/user/users/set-role`) // 角色批量添加用户
export const deleteUser = apiFactory.command(`/user/users/cancel-role`) // 角色删除用户

export const privacyAgreementActions = apiFactory.command('/setting/settings/privacy-agreement')
export const privacyAgreementActionsUpdateApi = apiFactory.command('/setting/settings/privacy-agreement/update')

export const memberRuleAgreementActions = apiFactory.command('/setting/settings/member-rule-agreement')
export const memberRuleAgreementActionsUpdateApi = apiFactory.command('/setting/settings/member-rule-agreement/update')

export const settingKeyApi = key => apiFactory.command(`/setting/settings/${key}`)
export const settingApi = apiFactory.command(`/setting/settings`)
export const settingKeyUpdateApi = key => apiFactory.command(`/setting/settings/${key}/update`)

export const distributionSettingApi = apiFactory.command('/setting/settings/distribution') // 分销设置
