import { cloneDeep } from 'lodash'
import { recommendPositionMode } from './enums'

export const useRecommendData = () => {
  const dataToShow = obj => {
    const data = cloneDeep(obj)
    // data.value.forEach(item => {
    //   item.recommendApi = ''
    //   if (item.recommend_way == recommendPositionMode.custom) {
    //     item.recommendApi = item.recommend_rule
    //     item.recommend_rule = ''
    //   }
    // })
    return data
  }

  const showToData = obj => {
    const data = cloneDeep(obj)
    let list = []
    data.value.forEach(item => {
      const { position, recommend_rule, recommend_way, recommend_skus, recommendApi } = item
      switch (item.recommend_way) {
        case recommendPositionMode.custom:
          list.push({ position, recommendApi, recommend_way })
          break
        case recommendPositionMode.appoint:
          list.push({ position, recommend_skus, recommend_way })
          break
        case recommendPositionMode.rule:
          list.push({ position, recommend_rule, recommend_way })
          break
      }
    })
    return list
  }

  return { dataToShow, showToData }
}
