const baseURL = 'https://yuqianji-mp-1308375884.cos.ap-shanghai.myqcloud.com/' // 前缀

const assets = {
  previewaWardQuestionnaireImg: 'user-center/admin/interaction/award_questionnaire/award_questionnaire_preview.png?v=1',
  return: 'user-center/admin/renovation/login/icon/return.svg',
  invitePreviewUrl: 'user-center/admin/interaction/invite_activity/invite-skin.png',
  sudokuPreviewUrl: 'user-center/admin/interaction/draw_activity/sudoku-preview.jpg',
  signPreviewUrl: 'user-center/admin/interaction/sign_activity/sign-skin.png',
  luckyPreviewUrl: 'user-center/admin/interaction/draw_activity/preview.jpg',
  jigsawPreviewUrl: 'user-center/admin/interaction/jigsaw_activity/preview.png'
}
Object.entries(assets).forEach(([key, value]) => {
  assets[key] = baseURL + value
})
export default assets
