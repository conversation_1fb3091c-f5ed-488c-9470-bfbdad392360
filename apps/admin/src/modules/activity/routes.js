export default {
  path: 'activity',
  meta: {
    title: '互动',
    antIcon: 'AntDesignOutlined'
  },
  children: [
    {
      path: 'register-activities-list',
      name: 'register-activities-list',
      meta: {
        title: '入会有礼',
        keepAlive: true
      },
      component: () => import('./pages/register-activities/list')
    },
    {
      path: 'register-activities-add',
      name: 'register-activities-add',
      hidden: true,
      meta: {
        title: '新增入会有礼'
      },
      component: () => import('./pages/register-activities/edit')
    },
    {
      path: 'register-activities-edit/:id',
      name: 'register-activities-edit',
      hidden: true,
      meta: {
        title: '编辑入会有礼'
      },
      component: () => import('./pages/register-activities/edit')
    },
    {
      path: 'register-activities-details/:id',
      name: 'register-activities-details',
      hidden: true,
      meta: {
        title: '查看入会有礼'
      },
      component: () => import('./pages/register-activities/edit')
    },
    {
      path: 'register-activities-data/:id',
      name: 'register-activities-data',
      hidden: true,
      meta: {
        title: '入会有礼数据统计'
      },
      component: () => import('./pages/register-activities/data')
    },
    {
      path: 'polite/invitation',
      name: 'invite-activities',
      meta: {
        title: '邀请有礼',
        keepAlive: true
      },
      component: () => import('./pages/invite-activities/list')
    },
    {
      path: 'invitation/data/:id',
      name: 'invitation-data',
      hidden: true,
      meta: {
        title: '邀请有礼数据'
      },
      component: () => import('./pages/invite-activities/data')
    },
    {
      path: 'look/invitation/:id',
      name: 'look-invitation',
      meta: {
        title: '查看邀请有礼'
      },
      hidden: true,
      component: () => import('./pages/invite-activities/edit')
    },
    {
      path: 'edit/invitation/:id',
      name: 'edit-invitation',
      meta: {
        title: '编辑邀请有礼'
      },
      hidden: true,
      component: () => import('./pages/invite-activities/edit')
    },
    {
      path: 'add/invitation/',
      name: 'add-invitation',
      meta: {
        title: '新增邀请有礼'
      },
      hidden: true,
      component: () => import('./pages/invite-activities/edit')
    },
    {
      path: 'register-activities-signin-gift/list',
      name: 'register-activities-sign-gift-list',
      meta: {
        title: '签到有礼',
        keepAlive: true
      },
      component: () => import('./pages/signin-gift/list')
    },
    {
      path: 'signin-gift-add',
      name: 'signin-gift-add',
      hidden: true,
      meta: {
        title: '新增签到有礼'
      },
      component: () => import('./pages/signin-gift/edit')
    },
    {
      path: 'signin-gift-edit/:id/:status',
      name: 'signin-gift-edit',
      hidden: true,
      meta: {
        title: '编辑签到有礼'
      },
      component: () => import('./pages/signin-gift/edit')
    },
    {
      path: 'signin-gift-details/:id',
      name: 'signin-gift-details',
      hidden: true,
      meta: {
        title: '查看签到有礼'
      },
      component: () => import('./pages/signin-gift/edit')
    },
    {
      path: 'experience-add',
      name: 'experience-add',
      hidden: true,
      meta: {
        title: '新增新品试用'
      },
      component: () => import('./pages/experience/add-experiencer')
    },
    {
      path: 'experience-edit/:id',
      name: 'experience-edit',
      hidden: true,
      meta: {
        title: '编辑新品试用'
      },
      component: () => import('./pages/experience/add-experiencer')
    },
    {
      path: 'experience-data',
      name: 'experience-data',
      hidden: true,
      meta: {
        title: '新品试用数据'
      },
      component: () => import('./pages/experience/data-list')
    },
    {
      path: 'experience-list',
      name: 'experience-list',
      meta: {
        title: '新品试用',
        keepAlive: true
      },
      component: () => import('./pages/experience/index-list')
    },
    {
      path: 'signin-gift-data/:id',
      name: 'signin-gift-data',
      hidden: true,
      meta: {
        title: '入会有礼数据统计'
      },
      component: () => import('./pages/signin-gift/data')
    },
    {
      path: 'award-questionnaire',
      name: 'award-questionnaire',
      meta: {
        title: '有奖调查',
        keepAlive: true
      },
      component: () => import('./pages/award-questionnaire/list')
    },
    {
      path: 'award-questionnaire/data/:id',
      name: 'award-questionnaire-data',
      hidden: true,
      meta: {
        title: '有奖调查数据'
      },
      component: () => import('./pages/award-questionnaire/data')
    },
    {
      path: 'look/award-questionnaire/:id',
      name: 'award-questionnaire-look',
      meta: {
        title: '查看有奖调查'
      },
      hidden: true,
      component: () => import('./pages/award-questionnaire/edit')
    },
    {
      path: 'award-questionnaire-add',
      name: 'award-questionnaire-add',
      hidden: true,
      meta: {
        title: '新增有奖调查'
      },
      component: () => import('./pages/award-questionnaire/edit')
    },
    {
      path: 'questionnaire-list',
      name: 'questionnaire-list',
      meta: {
        title: '调查问卷',
        keepAlive: true
      },
      component: () => import('./pages/questionnaire/list')
    },
    {
      path: 'questionnaire-add',
      name: 'questionnaire-add',
      meta: {
        title: '新增调查问卷',
        useLayout: false
      },
      hidden: true,
      component: () => import('./pages/questionnaire/edit')
    },
    {
      path: 'questionnaire-edit/:id',
      name: 'questionnaire-edit',
      meta: {
        title: '编辑调查问卷',
        useLayout: false
      },
      component: () => import('./pages/questionnaire/edit'),
      hidden: true
    },
    {
      path: 'questionnaire-data/:id',
      name: 'questionnaire-data',
      hidden: true,
      meta: {
        title: '问卷统计'
      },
      component: () => import('./pages/questionnaire/data')
    },
    {
      path: 'first-screen-popup',
      name: 'first-screen-popup',
      meta: {
        title: '首屏弹窗',
        keepAlive: true
      },
      component: () => import('./pages/first-screen-popup/list')
    },
    {
      path: 'first-screen-popup-add',
      name: 'first-screen-popup-add',
      hidden: true,
      meta: {
        title: '新增首屏弹窗'
      },
      component: () => import('./pages/first-screen-popup/edit')
    },
    {
      path: 'first-screen-popup-edit/:id',
      name: 'first-screen-popup-edit',
      hidden: true,
      meta: {
        title: '编辑首屏弹窗'
      },
      component: () => import('./pages/first-screen-popup/edit')
    },
    {
      path: 'jigsaw-list',
      name: 'jigsaw-list',
      meta: {
        title: '拼图有礼',
        keepAlive: true
      },
      component: () => import('./pages/jigsaw/list')
    },
    {
      path: 'jigsaw-add',
      name: 'jigsaw-add',
      hidden: true,
      meta: {
        title: '新增拼图有礼'
      },
      component: () => import('./pages/jigsaw/edit')
    },
    {
      path: 'jigsaw-edit/:id',
      name: 'jigsaw-edit',
      hidden: true,
      meta: {
        title: '编辑拼图有礼'
      },
      component: () => import('./pages/jigsaw/edit')
    }, {
      path: 'jigsaw-data/:id',
      name: 'jigsaw-data',
      hidden: true,
      meta: {
        title: '拼图有礼数据'
      },
      component: () => import('./pages/jigsaw/data')
    },
    {
      path: 'award-questionnaire-edit/:id',
      name: 'award-questionnaire-edit',
      hidden: true,
      meta: {
        title: '编辑有奖调查'
      },
      component: () => import('./pages/award-questionnaire/edit')
    },
    {
      path: 'offline-reservation-list',
      name: 'offline-reservation-list',
      meta: {
        title: '线下预约',
        keepAlive: true
      },
      component: () => import('./pages/offline-reservation/list'),
    },
    {
      path: 'reservation-add',
      name: 'reservation-add',
      meta: {
        title: '新增线下预约'
      },
      component: () => import('./pages/offline-reservation/edit'),
      hidden: true
    },
    {
      path: 'reservation-edit/:id/:no/:status',
      name: 'reservation-edit',
      meta: {
        title: '编辑线下预约'
      },
      component: () => import('./pages/offline-reservation/edit'),
      hidden: true
    },
    {
      path: 'reservation-data/:id',
      name: 'reservation-data',
      meta: {
        title: '线下预约数据'
      },
      component: () => import('./pages/offline-reservation/data'),
      hidden: true
    },
    {
      path: 'lucky-turntable',
      name: 'lucky-turntable',
      meta: {
        title: '幸运大转盘',
        keepAlive: true
      },
      component: () => import('./pages/lucky-turntable/list')
    },
    {
      path: 'lucky-turntable-add',
      name: 'lucky-turntable-add',
      hidden: true,
      meta: {
        title: '新增大转盘'
      },
      component: () => import('./pages/lucky-turntable/edit')
    },
    {
      path: 'lucky-turntable-edit/:id',
      name: 'lucky-turntable-edit',
      hidden: true,
      meta: {
        title: '编辑大转盘'
      },
      component: () => import('./pages/lucky-turntable/edit')
    },
    {
      path: 'lucky-turntable-data/:id',
      name: 'lucky-turntable-data',
      hidden: true,
      meta: {
        title: '大转盘数据'
      },
      component: () => import('./pages/lucky-turntable/data')
    },
    {
      path: 'sudoku-list',
      name: 'sudoku-list',
      meta: {
        title: '惊爆九宫格',
        keepAlive: true
      },
      component: () => import('./pages/sudoku/list')
    },
    {
      path: 'sudoku-add',
      name: 'sudoku-add',
      hidden: true,
      meta: {
        title: '新增九宫格'
      },
      component: () => import('./pages/sudoku/edit')
    },
    {
      path: 'sudoku-edit/:id',
      name: 'sudoku-edit',
      hidden: true,
      meta: {
        title: '编辑九宫格'
      },
      component: () => import('./pages/sudoku/edit')
    },
    {
      path: 'sudoku-data',
      name: 'sudoku-data',
      hidden: true,
      meta: {
        title: '九宫格数据'
      },
      component: () => import('./pages/sudoku/data')
    },
    {
      path: 'scene-activities-list',
      name: 'scene-activities-list',
      meta: {
        title: '场景营销',
        keepAlive: true
      },
      component: () => import('./pages/scene-activities/list')
    },
    {
      path: 'scene-activities-add',
      name: 'scene-activities-add',
      hidden: true,
      meta: {
        title: '新增场景营销'
      },
      component: () => import('./pages/scene-activities/edit')
    },
    {
      path: 'scene-activities-edit/:id',
      name: 'scene-activities-edit',
      hidden: true,
      meta: {
        title: '编辑场景营销'
      },
      component: () => import('./pages/scene-activities/edit')
    },
    {
      path: 'scene-activities-details/:id',
      name: 'scene-activities-details',
      hidden: true,
      meta: {
        title: '查看场景营销'
      },
      component: () => import('./pages/scene-activities/edit')
    },
  ]
}
