export function getAddressQQ(options) {
    var oScrc = document.createElement('script');
    var fn = 'function1';
    window[fn] = options.cb;
    oScrc.src = "https://apis.map.qq.com/ws/geocoder/v1/?callback="+fn+"&output=jsonp&address="+options.adr+"&key=LDVBZ-UEL3J-2T6FR-X5D4I-6U5ZK-ZSBPR";
    document.body.appendChild(oScrc); 
    oScrc.onload = function () {
        this.remove();
    }
}
export function getLocationQQ(options) {
    var oScrc = document.createElement('script');
    var fn = 'function1';
    window[fn] = options.cb;
    oScrc.src = "https://apis.map.qq.com/ws/geocoder/v1/?callback="+fn+"&output=jsonp&location="+options.location+"&key=LDVBZ-UEL3J-2T6FR-X5D4I-6U5ZK-ZSB<PERSON>";
    document.body.appendChild(oScrc); 
    oScrc.onload = function () {
        this.remove();
    }
}



