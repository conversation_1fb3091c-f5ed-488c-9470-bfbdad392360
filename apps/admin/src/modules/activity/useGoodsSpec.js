import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { goodsSpecsApi } from './api'

export function useSpecs(callback = null, params = {}) {
  const { data: specs } = useApiRequest(() => {
    return goodsSpecsApi.list(params).then(res => {
      res.forEach(i => (i.title = `${i.sku}-${i.attrs[0].value}`))
      return callback ? callback(res) : res
    })
  })

  return {
    specs
  }
}

export function useSpecOptions(params) {
  const { specs: specOptions } = useSpecs(res => useTransformOptions(res, 'title', 'id'), params)
  return {
    specOptions
  }
}
