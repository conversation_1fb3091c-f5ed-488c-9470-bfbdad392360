const STATUS_CANCELLED = 'cancelled'
const STATUS_NORMAL = 'waitingnormal'
const STATUS_FAILED = 'failedfailed'
const STATUS_SUCCESSFUL = 'successfulnormal'
const STATUS_COMPLETED = 'successfulcompleted'
const statusList = Object.freeze([
    { label: '取消预约', value: STATUS_CANCELLED, colorType: 'default' },
    { label: '预约成功', value: STATUS_SUCCESSFUL, colorType: 'success' },
    { label: '预约成功', value: STATUS_COMPLETED, colorType: 'success' },
    { label: '等待审核', value: STATUS_NORMAL, colorType: 'processing' },
    { label: '预约失败', value: STATUS_FAILED, colorType: 'error' }
])
const statusFilter = list => {
    if(list[1]===STATUS_CANCELLED)return statusList[0]
    console.log(list[0] + list[1])
    switch (list[0] + list[1]) {
        case STATUS_COMPLETED:
            return statusList[1]
        case STATUS_SUCCESSFUL:
            return statusList[1]

        case STATUS_FAILED:
            return statusList[4]

        case STATUS_NORMAL:
            return statusList[3]
    }
}
export const useStatusFilter = () => {
    return { statusFilter }
}