import { ref } from 'vue'
import { cloneDeep } from 'lodash'
import { useBatchTransformMedia, useTransformMedia } from '@/composables/useTransformFormat'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { questionnaireList<PERSON>pi } from './api'
import { questionInfo } from './enums'
import { useTransformPrize } from './usePrizeAward'
/**
 * 转换问卷编辑数据
 */
export const useTransformQuestion = () => {
  const fields = ['bg_url', 'poster', 'pop_url']
  const personInfoOption = questionInfo.personInfoOption()
  const transformMedia = (data, type = 'array') => {
    fields.forEach(key => {
      data[key] = useTransformMedia(data[key], type)
    })
  }

  const transformShow = data => {
    const res = cloneDeep(data)
    transformMedia(res.page_setting)
    const personInfo = []
    res.subjects = res.subjects.filter(item => {
      item.is_required = !!item.is_required
      item.options.forEach(it => {
        it.url = useTransformMedia(it.url, 'array')
        it.jump_subject_code = +(it.jump_subject_code ?? -1)
      })
      const isPersonInfo = questionInfo.filterPersonInfo(item.option_type)
      isPersonInfo && personInfo.push(item.option_type)
      return !isPersonInfo
    })

    return { data: res, personInfo }
  }

  const transformRequest = (data, personInfo) => {
    const form = cloneDeep(data)
    transformMedia(form.page_setting, 'string')
    form.subjects.forEach(item => {
      item.id = undefined
      item.is_required = +item.is_required
      item.options.forEach(it => {
        it.id = it.subject_id = undefined
        it.url = useTransformMedia(it.url, 'string')
        it.option_type = item.option_type
      })
    })
    personInfo.forEach(({ value: option_type, state }) => {
      if (state) {
        const item = { option_type, title: questionInfo.filter(option_type).label, is_required: 1, code: '-100' }
        if (option_type === questionInfo.city) {
          item.options = [
            {
              content: '其它',
              is_freestyle: 1,
              option_type
            }
          ]
        } else {
          item.options = personInfoOption[option_type].map(content => ({
            content,
            is_freestyle: 0,
            option_type
          }))
        }
        form.subjects.push(item)
      }
    })
    return form
  }

  return { transformShow, transformRequest }
}

/**
 * 转换问卷统计数据
 */
export const useTransformQuesStatis = filters => {
  const useQues = useTransformQuestion()
  const transformShow = responseData => {
    const [statis, question] = cloneDeep(responseData)
    const { data } = useQues.transformShow(question)
    data.subjects.forEach(item => {
      item.options = item.options.map(it => {
        const result = statis.find(el => el.option_id === it.id) || { count: 0, percent: 0 }
        return { ...result, ...it }
      })
      switch (item.option_type) {
        case questionInfo.question_answer:
          item.table = usePaginatorApiRequest(({ offset, limit }) =>
            questionnaireListApi.paginator({
              filters: filters,
              relations: ['user'],
              offset,
              limit
            })
          )
          item.table.setPage()
          break
        case questionInfo.score:
          item.options.reverse()
          break
      }
    })
    return data
  }

  return { transformShow }
}

/**
 * 转换有奖调查编辑数据
 */
export const useTransformAwardQuestion = () => {
  const { transformPrizesRequest } = useTransformPrize()
  const pageConfig = ref([
    { label: '活动背景', value: [], field: 'activity_bg' },
    { label: '活动文案', value: [], field: 'activity_text' }
  ])
  // const modalConfig = ref([
  //   { label: '未开始', value: [], field: 'prepare' },
  //   { label: '已结束', value: [], field: 'end' }
  // ])

  const transformMedia = (list, data, field, type = 'array') => {
    const fields = field.split('.')
    let fieldVal = data
    fields.forEach(key => {
      fieldVal = fieldVal[key]
    })
    list.value.forEach(item => {
      if (type === 'array') {
        item.value = useTransformMedia(fieldVal[item.field], type)
      } else {
        fieldVal[item.field] = useTransformMedia(item.value, type)
      }
    })
  }

  const transformShow = data => {
    const res = cloneDeep(data)
    transformMedia(pageConfig, res, 'page_setting.page')
    // transformMedia(modalConfig, res, 'page_setting.popup')
    return res
  }

  const transformRequest = data => {
    const form = cloneDeep(data)
    form.page_setting.page = {}
    form.page_setting.popup = {}
    transformMedia(pageConfig, form, 'page_setting.page', 'string')
    // transformMedia(modalConfig, form, 'page_setting.popup', 'string')

    form.rules.forEach(rule => {
      transformPrizesRequest(rule)
    })
    return form
  }

  return { pageConfig, transformShow, transformRequest }
}
