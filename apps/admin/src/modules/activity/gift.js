import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useApiRequest } from '@/composables/useApiRequest'
import { awardOptionsApi ,userLevelsApi} from './api'
export const giftUpdata = () => {
    /**
* 处理奖项
*/
    const handleAward = (item, filters = {}) =>
        useAward(
            data => {
                item.options = useTransformOptions(data.items, 'title', 'id', ['type', 'photo_url'])
            },
            20,
            filters
        )
    /**
     * 更新礼品下拉列表
     */
    const handleShowGifts = record => {
        handleAward(record, useTransformQuery({ type: record.type }))
    }

    /**
     * 搜索
     */
    const handleSearchGifts = (title, record) => {
        handleAward(
            record,
            useTransformQuery(
                {
                    type: record.type,
                    title
                },
                {
                    title: 'like'
                }
            )
        )
    }
    const useUserLevelOptions=(filters) =>{
        const { levels: levelOptions } = useUserLevel(cates => useTransformOptions(cates, 'level_name', 'id'), filters)
        return {
          levelOptions
        }
      }
    return { handleShowGifts, handleSearchGifts,useUserLevelOptions }
}
async function useAward(callback = null, limit, filters) {
    const awards = await awardOptionsApi.paginator({
        filters: { ...filters },
        offset: 1,
        limit,
        status: 'normal'
    })
    return callback(awards)
}
function useUserLevel(callback = null, filters = {}) {
    const { data: levels } = useApiRequest(() => {
      return userLevelsApi.list({ filters }).then(cates => (callback ? callback(cates) : cates))
    })
  
    return {
      levels
    }
  }