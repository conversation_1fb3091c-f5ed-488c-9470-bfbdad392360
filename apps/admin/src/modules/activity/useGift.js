import { useTransformOptions } from '@/composables/useTransformOptions'
import { giftApi } from './api'
import { giftType } from './enums'
import { cloneDeep } from 'lodash'

export async function useGift(callback = null, limit) {
  const cates = await giftApi.paginator({
    offset: 1,
    limit
  })
  return callback(cates)
}

export async function useGiftFilter(callback = null, filters) {
  const cates = await giftApi.list({
    filters
  })
  return callback(cates)
}

export async function useGiftOptions(query) {
  const giftOptions = await useGift(cates => useTransformOptions(cates.items, 'title', 'id'), query)
  return { giftOptions }
}

export async function useGiftOptionsFilter(query) {
  const giftOptionsFilter = await useGiftFilter(
    cates => useTransformOptions(cates, 'title', 'id', ['photo_urls']),
    query
  )
  return { giftOptionsFilter }
}

export async function useAllGiftOptions() {
  let allGiftOptions = cloneDeep(giftType)
  let getGiftOptions = []
  allGiftOptions.options = allGiftOptions.options()

  Object.keys(giftType).forEach(type => {
    if (type != 'options') {
      getGiftOptions.push(useGiftFilter(cates => useTransformOptions(cates, 'title', 'id', ['photo_urls']), { type }))
    }
  })

  await Promise.all(getGiftOptions).then(result => {
    allGiftOptions.options.forEach((type, index) => {
      type.options = result[index]
    })
  })
  return { allGiftOptions }
}
