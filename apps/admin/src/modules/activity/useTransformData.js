import { cloneDeep, sortBy } from 'lodash'
import { useTransformImg } from '@/composables/useTransformFormat'
import formatters from '@/utils/formatters'
import { usePrizeAwardUpdate, useTransformPrize } from './usePrizeAward'
import { activityRuleList, jigsawPrizes } from './enums'
import { useAwardOptionsFilter, useAwardFilter, useAward } from './useAward'
/**
 * 转换签到有礼编辑数据
 */
export const useSigninGiftEdit = () => {
  const { batchTransformImg } = useTransformImg()
  const { handleAward } = usePrizeAwardUpdate()
  const { transformPrizesRequest } = useTransformPrize()

  const imgKeys = {
    rule: ['photo_url'],
    share: ['friend_poster', 'friend_circle_poster']
  }
  /**
   * 转换成显示数据
   */
  const transformToShow = async (data, isActivityStatusNormal) => {
    const cloneData = cloneDeep(data)
    // batchTransformImg(cloneData.page_setting.popup)
    batchTransformImg(cloneData.page_setting.page)
    batchTransformImg(cloneData.share_setting, 'array', imgKeys.share)
    // 规则数据转换
    const showPrizePrm = []
    cloneData.rules.forEach(rule => {
      batchTransformImg(rule, 'array', imgKeys.rule) // 处理图片格式
      rule.award_settings.forEach(prize => {
        // 活动进行中，禁用编辑已有奖项层级，活动数量只能增加
        if (isActivityStatusNormal.value) prize.disabled = true
        showPrizePrm.push(handleAward(prize, { id: prize.option_id }))
      })
    })
    await Promise.all(showPrizePrm)

    cloneData.rules_first = cloneData.rules.filter(({ type }) => type == activityRuleList.first)
    cloneData.rules_continuity = cloneData.rules.filter(({ type }) => type == activityRuleList.continuity)
    cloneData.rules_accumulate = sortBy(
      cloneData.rules.filter(({ type }) => type == activityRuleList.accumulate),
      ['condition']
    )
    cloneData.rules_continuous = sortBy(
      cloneData.rules.filter(({ type }) => type == activityRuleList.continuous),
      ['condition']
    )
    return cloneData
  }
  /**
   * 转换成请求数据
   */
  const transformToRequest = data => {
    const cloneData = cloneDeep(data)
    const { rules_accumulate, rules_continuity, rules_first, rules_continuous } = cloneData

    cloneData.rules = rules_continuity.concat(rules_accumulate, rules_first, rules_continuous)
    cloneData.rules.forEach(rule => {
      transformPrizesRequest(rule)
      batchTransformImg(rule, 'string', imgKeys.rule)
    })
    // batchTransformImg(cloneData.page_setting.popup, 'string')
    batchTransformImg(cloneData.page_setting.page, 'string')
    batchTransformImg(cloneData.share_setting, 'string', imgKeys.share)

    return cloneData
  }

  return { transformToShow, transformToRequest }
}

/**
 * 转换拼图有礼编辑数据
 */
export const useJigsawEdit = () => {
  const { transformPrizesRequest } = useTransformPrize()
  const { batchTransformImg } = useTransformImg()
  const { handleAward } = usePrizeAwardUpdate()

  const imgKeys = {
    rule: ['prize_url', 'pop_url'],
    jigsaw: ['photo_url', 'pop_url'],
    shareSetting: ['session', 'local']
  }

  // 转换成显示数据
  const transformToShow = async data => {
    const cloneData = cloneDeep(data)
    cloneData.settings.forEach(item => {
      batchTransformImg(item, 'array', imgKeys.jigsaw)
      item.probability /= 100
    })
    const task = []
    cloneData.rules.forEach(rule => {
      batchTransformImg(rule, 'array', imgKeys.rule)
      rule.award_settings.forEach(item => {
        item.type === 'none' && (item.stock = item.quantity = item.type = undefined)
        task.push(handleAward(item, { id: item.option_id }))
      })

      rule.probability /= 100
      rule.isZero = rule.grade === jigsawPrizes.zero
    })

    batchTransformImg(cloneData.page_setting.page)
    batchTransformImg(cloneData.page_setting.popup)
    batchTransformImg(cloneData.share_setting, 'array', imgKeys.shareSetting)
    await Promise.all(task)
    return cloneData
  }
  // 转换成请求数据
  const transformToRequest = data => {
    const cloneData = cloneDeep(data)
    cloneData.settings.forEach((item, i, arr) => {
      batchTransformImg(item, 'string', imgKeys.jigsaw)
      item.probability = formatters.priceToInteger(item.probability)
      if (arr.length - 1 === i) {
        item.sort = 0
      } else {
        item.sort = i + 1
        item.stock = 0
      }
    })
    cloneData.rules.forEach(rule => {
      transformPrizesRequest(rule)
      batchTransformImg(rule, 'string', imgKeys.rule)
      rule.award_settings.forEach(item => {
        if (!item.type) {
          item.type = 'none'
          item.option_id = 0
          item.quantity = 0
          item.stock = 0
        }
      })
      rule.probability = formatters.priceToInteger(rule.probability)
    })

    batchTransformImg(cloneData.page_setting.page, 'string')
    batchTransformImg(cloneData.page_setting.popup, 'string')
    batchTransformImg(cloneData.share_setting, 'string', imgKeys.shareSetting)

    return cloneData
  }

  return { transformToShow, transformToRequest }
}
