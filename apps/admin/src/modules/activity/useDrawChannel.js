import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { turnplateChannelApi } from './api'
import { ref } from 'vue'

export function useDrawChannelOptions(filters = {}) {
  const options = ref()
  const { loading, setLoading } = useLoading()

  const search = (val = '') => {
    let searchFilters = {}
    if(val) {
      searchFilters = { filters: {channel: `%${val}%`} }
    } else {
      searchFilters = filters
    }
    const { getData } = useApiRequest(() => {
      options.value = []
      setLoading(true)
      return turnplateChannelApi.list({
        ...searchFilters
      })
        .then((cates) => {
          options.value = useTransformOptions(cates, 'channel', 'channel')
        }).finally(() => setLoading(false))
    }, false)
    getData()
  }

  search()

  return {
    search,
    options,
    loading
  }
}
