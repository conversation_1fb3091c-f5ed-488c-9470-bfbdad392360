/**
 * 活动状态
 */
export const activityStatus = Object.freeze({
  options() {
    return [
      { label: '未开始', value: "not_start" },
      { label: '进行中', value: "normal" },
      { label: '已结束', value: "ended" },
    ]
  }
})
/**
 * 报名资格
 */
export const qualificationOptions = Object.freeze({
  options() {
    return [
      { label: '不限资格', value: 0 },
    ]
  }
})
/**
 * 报名方式
 */
export const reservationType = Object.freeze({
  /**
   * 完善个人信息
   */
  complete_info:"complete_info",
  /**
   * 填写问卷
   */
  questionnaire:"questionnaire",
  options() {
    return [
      { label: '完善个人信息报名', value: "complete_info" },
      { label: '填写调查问卷报名', value: "questionnaire" },
    ]
  }
})
/**
 * 预约时段是否开启
 */
export const switchOptions = Object.freeze({
    /**
   * 开启
   */
    true:true,
    /**
     * 关闭
     */
    false:false,
  options() {
    return [
      { label: '开启', value: this.true },
      { label: '关闭', value: this.false },
    ]
  }
})
/**
 * 审核类型
 */
export const auditType = Object.freeze({
  /**
   * 自动确认
   */
  automatic: "automatic",
  /**
   * 人工审核
   */
  manual: "manual",
  options() {
    return [
      { label: '自动确认', value: this.automatic },
      { label: '人工审核', value: this.manual },
    ]
  }
})
/**
 * 签到方式
 */
export const signType = Object.freeze({
  /**
   * 用户扫码
   */
  user_scanning:"user_scanning",
  /**
   * 管理员扫码
   */
  admin_scanning:"admin_scanning",
  /**
   * 用户到场
   */
  user_present:"user_present",
  options() {
    return [
      { label: '现场工作人员出示签到码，用户扫码即完成签到', value: this.user_scanning },
      { label: '用户出示核销码，现场工作人员扫码即完成签到', value: this.admin_scanning },
      // { label: '用户到场即可，无需签到核销', value: this.user_present },
    ]
  }
})
/**
 * 发放方式
 */
export const provideOptions = Object.freeze({
  //线上
  online: "online",
  //线下
  offline: "offline",
  options() {
    return [
      { label: '报名成功后，用户在线上领取礼品', value: this.online },
      { label: '报名成功后，用户在线下领取礼品', value: this.offline },
    ]
  }
})
export const reservationStatus = Object.freeze({
  //等待审核
  waiting: "waitingnormal",
  //取消预约
  cancelled: "cancelled",
  //预约失败
  failed: "failedfailed",
  //预约成功
  successful: "successfulnormal",
  options() {
    return [
      { label: '等待审核', value: this.waiting },
      { label: '取消预约', value: this.cancelled },
      { label: '预约失败', value: this.failed },
      { label: '预约成功', value: this.successful },
    ]
  }
})
/**
 * 礼品类型
 */
export const giftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',

  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 抽奖资格
   */
  draw_qualification: 'draw_qualification',

  /**
   * 积分
   */
  credit: 'credit',

  /**
   * 成长值
   */
  growthValue: 'growth_value',

  options() {
    return [
      { label: '实物礼品', value: this.entity, biz_type: 'gift', placeholder: '请选择礼品' },
      { label: '虚拟卡券', value: this.invented, biz_type: 'gift', placeholder: '请选择礼品' },
      { label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' },
      { label: '会员积分', value: this.credit, biz_type: 'credit', placeholder: '请输入积分值' }
      // { label: '抽奖资格', value: this.draw_qualification, biz_type: 'draw', placeholder: '请输入抽奖次数' }
    ]
  },
  registerOptions() {
    return [
      { label: '实物礼品', value: this.entity, biz_type: 'gift', placeholder: '请选择礼品' },
      { label: '虚拟卡券', value: this.invented, biz_type: 'gift', placeholder: '请选择礼品' },
      { label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' },
      { label: '会员积分', value: this.credit, biz_type: 'credit', placeholder: '请输入积分值' },
      { label: '抽奖资格', value: this.draw_qualification, biz_type: 'draw', placeholder: '请输入抽奖次数' }
    ]
  },
  turntableOptions() {
    return [
      { label: '实物礼品', value: this.entity },
      { label: '虚拟卡券', value: this.invented },
      { label: '抽奖次数', value: this.draw_times },
      { label: '会员积分', value: this.credit }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  },
  getPlaceholder(value) {
    if (!value) return '请选择礼品'
    return this.options().find(item => item.value == value)?.placeholder || '请选择礼品'
  }
})

/**
 * 活动状态
 */
export const writeOffFilter = Object.freeze({
  options() {
    return [
      { label: '未开始', value: "not_start" },
      { label: '进行中', value: "normal" },
      { label: '已结束', value: "ended" },
    ]
  }
})