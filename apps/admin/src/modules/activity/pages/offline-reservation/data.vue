<template>
  <div class="signin-gift-statistics">
    <a-row :gutter="16" class="stats-data">
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            访问(人/次)
          </div>
          <div class="stats-data-number">
            {{ total.uv }}/ {{ total.pv }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            参与/取消
          </div>
          <div class="stats-data-number">
            {{ total.participate_number }} / {{ total.cancel_number }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            预约/签到
          </div>
          <div class="stats-data-number">
            {{ total.reservation_number }} / {{ total.sign_in_number }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            新/老用户
          </div>
          <div class="stats-data-number">
            {{ total.new_user_number }} / {{ total.old_user_number }}
          </div>
        </div>
      </a-col>
    </a-row>
  </div>

  <uc-layout-list title="预约名单">
    <template #extra>
      <div style="display: flex">
        <div>
          <a-input-group compact>
            <a-select
              v-model:value="formState.type"
              class="w-120"
              :options="writeOff.options()"
              @change="reservationStatusOptionChange"
            />
            <a-input
              v-model:value.trim="formState.keyValue"
              placeholder="请输入关键词"
              class="m-r-10"
              @input="keyValueChange"
            />
            <a-select
              v-model:value="formState.reservationValue"
              placeholder="预约状态"
              class="w-120"
              :options="reservationStatus.options()"
              @change="reservationStatusChange"
            />
          </a-input-group>
        </div>

        <div style="margin-left: 10px">
          <a-button type="primary" @click="batchAudit">
            批量审核
          </a-button>
        </div>
      </div>
      <!-- </a-form-item>
      </a-form> -->
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="用户昵称" ellipsis>
          <template #default="{ record }">
            {{ record.user?.nickname }}
          </template>
        </a-table-column>
        <a-table-column title="手机号码" width="110px">
          <template #default="{ record }">
            {{ $formatters.numberEncryption(record.user?.phone_number) }}
          </template>
        </a-table-column>
        <a-table-column title="会员等级" width="110px">
          <template #default="{ record }">
            {{ record.user?.level.level_name }}
          </template>
        </a-table-column>
        <a-table-column title="预约时间" width="220px">
          <template #default="{ record }">
            {{
              record.reservation_date
                ? record.reservation_date + ' ' + record.time_range
                : record.activity.start_time + ' ~ ' + record.activity.end_time.substring(11, 16)
            }}
          </template>
        </a-table-column>
        <a-table-column title="预约提交时间" width="220px">
          <template #default="{ record }">
            {{ record.created_at }}
          </template>
        </a-table-column>
        <a-table-column title="预约状态" width="120px">
          <template #default="{ record }">
            <a-badge
              :status="statusFilter([record.audit_status, record.status]).colorType"
              :text="statusFilter([record.audit_status, record.status]).label"
            />
          </template>
        </a-table-column>
        <a-table-column width="150px" title="操作">
          <template #default="{ record }">
            <a-button
              :disabled="record.status !== 'normal' || !record.activity.enable_reservation_period"
              type="link"
              @click="handleChange(record)"
            >
              改期
            </a-button>
            <!-- <a-button type="link" @click="handleChange(record)"> 改期 </a-button> -->
            <a-button
              type="link"
              :disabled="record.audit_status !== 'waiting'"
              @click="handleSingle(record, 'successful')"
            >
              同意
            </a-button>
            <a-button
              type="link"
              class="danger"
              :disabled="record.audit_status !== 'waiting'"
              @click="handleSingle(record, 'failed')"
            >
              拒绝
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <div>
    <uc-layout-list title="签到名单">
      <template #extra>
        <div>
          <a-input-group compact>
            <a-select
              v-model:value="formState.type2"
              class="w-120"
              :options="writeOff.options()"
              @change="reservationStatusOptionChange2"
            />
            <a-input v-model:value.trim="formState.keyValue2" placeholder="请输入关键词" @input="keyValueChangeSec" />
          </a-input-group>
        </div>
      </template>
      <template #list>
        <a-table
          :data-source="data2.items"
          row-key="id"
          :loading="loading2"
          :pagination="stdPagination(data2)"
          @change="setPage"
        >
          <a-table-column title="用户昵称" ellipsis>
            <template #default="{ record }">
              {{ record.user?.nickname }}
            </template>
          </a-table-column>
          <a-table-column title="手机号码" width="120px">
            <template #default="{ record }">
              {{ $formatters.numberEncryption(record.user?.phone_number) }}
            </template>
          </a-table-column>
          <a-table-column title="会员等级" width="130px">
            <template #default="{ record }">
              {{ record.user?.level.level_name }}
            </template>
          </a-table-column>
          <a-table-column title="预约时间" width="220px">
            <template #default="{ record }">
              {{
                record.reservation_date
                  ? record.reservation_date + ' ' + record.time_range
                  : record.activity.start_time + ' ~ ' + record.activity.end_time.substring(11, 16)
              }}
            </template>
          </a-table-column>
          <a-table-column title="核销员" width="150px" ellipsis>
            <template #default="{ record }">
              {{ record.verifier?.nickname }}
            </template>
          </a-table-column>
          <a-table-column title="签到时间" width="220px">
            <template #default="{ record }">
              {{ record.sign_time }}
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
  </div>

  <a-modal :visible="visible" title="批量审核" @cancel="onClose">
    <a-textarea
      v-model:value="batchAuditText"
      :rows="10"
      style="max-width: 700px !important"
      placeholder="请输入审核用户手机号码，一行一个"
    />
    <template #footer>
      <a-button key="back" @click="onClose">
        取消
      </a-button>
      <a-button key="reject" type="primary" danger :loading="loading" @click="handleBatch(id, 'failed')">
        拒绝
      </a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="handleBatch(id, 'successful')">
        同意
      </a-button>
    </template>
  </a-modal>
  <a-modal :visible="changeVisible" title="预约改期" @cancel="onClose">
    <a-form>
      <a-form-item label="用户信息" class="required">
        <a-input v-model:value="formState.userinfo" style="width: 380px" disabled maxlength="20" />
      </a-form-item>
      <a-form-item label="预约时间" class="required">
        <a-input v-model:value="formState.start_time" style="width: 380px" show-time disabled placeholder="预约时间" />
      </a-form-item>
      <a-form-item label="预约改期" class="required">
        <a-select
          v-model:value="formState.reservation_date"
          style="width: 185px"
          placeholder="预约时段"
          :options="reservationDateOptions"
          :disabled="isRead"
          @change="reservationDateOptionsChange"
        />
        <span class="m-r-10"></span>
        <a-select
          v-model:value="formState.time_range"
          placeholder="预约时段"
          style="width: 185px"
          :options="timeRangeOptions"
          :disabled="isRead"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button key="back" @click="onClose">
        取消
      </a-button>
      <a-button key="submit" type="primary" :loading="loading" @click="handleChangeComfirm()">
        同意
      </a-button>
    </template>
  </a-modal>
</template>
    
    <script setup>
import moment from 'moment'
import { useRoute } from 'vue-router'
import { SearchOutlined } from '@ant-design/icons-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { writeOff } from '../../enums.js'
import { debounce } from 'lodash'
import { reservationStatus } from '../../reservationEnums.js'
import {
  reservationChartApi,
  reservationAuditApi,
  reservationDataApi,
  reservationHourApi,
  changeApi
} from '../../api.js'
import { useStatusFilter } from '../../useStatusFilter.js'
import { ref } from 'vue'
import { message } from 'ant-design-vue'
const { statusFilter } = useStatusFilter()
const { formState, resetFormState } = useFormState({
  type: 'phone_number',
  type2: 'phone_number',
  record_id: 0,
  reservation_date: '',
  time_range: ''
}) // 查询表单

defineComponent({
  SearchOutlined
})
// 表格请求
const { id } = useRoute().params // 参数
const dataApi = reservationDataApi
let reservationDateOptions = ref([])
let timeRangeOptions = ref([])
const reservationDateOptionsChange = value => {
  reservationDateOptions.value.forEach(e => {
    if (value === e.value) {
      timeRangeOptions.value = e.child
    }
  })
}
const disabledList = []
const relations = ['verifier', 'activity']
let filters = { activity_id: id }
let relation_filters = {}
let relation_filters2 = {}
let relation_filters_user_type = ''
let relation_filters_user_type2 = ''
const arr = [
  { status: 'normal', audit_status: 'waiting ', relations: ['verifier', 'activity'], activity_id: id },
  { status: 'normal', audit_status: ['successful', 'completed'], relations: ['verifier', 'activity'], activity_id: id },
  { status: 'failed', audit_status: 'failed ', relations: ['verifier', 'activity'], activity_id: id },
  { status: 'cancelled', relations: ['verifier', 'activity'], activity_id: id }
]
const reservationStatusOptionChange = e => {
  relation_filters_user_type = e
  formState.value.keyValue = ''
}
const reservationStatusOptionChange2 = e => {
  relation_filters_user_type2 = e
  formState.value.keyValue2 = ''
}
const keyValueChange = debounce(e => {
  setPage()
}, 500)
const keyValueChangeSec = debounce(e => {
  setPageSec()
}, 500)
const reservationStatusChange = e => {
  switch (e) {
    case 'waitingnormal':
      filters = arr[0]
      break
    case 'cancelled':
      filters = arr[3]
      break
    case 'failedfailed':
      filters = arr[2]
      break
    default:
      'successfulnormal'
      filters = arr[1]
      break
  }
  setPage()
}

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  if (relation_filters_user_type === 'nickname') {
    relation_filters = { user: { nickname: formState.value.keyValue } }
  } else {
    relation_filters = { user: { phone_number: formState.value.keyValue } }
  }
  return dataApi.paginator({
    filters,
    relations,
    relation_filters,
    offset,
    limit
  })
})
const disabledStartTime = e => {
  if (disabledList.length > 1 && disabledList.length !== 0) {
    return (
      moment(e).format('X') < moment(disabledList[1]).format('X') ||
      moment(e).format('X') > moment(disabledList[0]).format('X')
    )
  } else {
    return moment(e).format('X') !== moment(disabledList[0]).format('X')
  }
}
const obj = usePaginatorApiRequest(({ offset, limit }) => {
  if (relation_filters_user_type2 === 'nickname') {
    relation_filters2 = { user: { nickname: formState.value.keyValue2 } }
  } else {
    relation_filters2 = { user: { phone_number: formState.value.keyValue2 } }
  }

  return dataApi.paginator({
    filters: { status: ['completed'], activity_id: id },
    relations: ['verifier', 'activity'],
    relation_filters: relation_filters2,
    offset,
    limit
  })
})

const data2 = obj.data
const setPageSec = obj.setPage
const loading2 = obj.loading
const visible = ref(false)
const changeVisible = ref(false)
const giftType = [{}]
const total = ref({
  pv: 0,
  uv: 0,
  participate_number: 0,
  cancel_number: 0,
  reservation_number: 0,
  sign_in_number: 0,
  new_user_number: 0,
  old_user_number: 0
})
let batchAuditParmas = [
  {
    phone_number: 0,
    audit_status: 'successful'
  }
]
const batchAuditText = ref('')
reservationChartApi.get({ activity_id: id }).then(res => {
  total.value = res
})

const batchAudit = () => {
  batchAuditText.value = ''
  visible.value = true
  // batchAuditApi.post(batchAuditParmas)
}
const handleBatchAuditText = status => {
  if (!status) return
  return batchAuditText.value.trim().replaceAll('\n', ',').split(',')
}

const handleChange = record => {
  changeVisible.value = true
  if (!record.user) {
    record.user = {}
  } else {
    formState.value.userinfo = record.user.nickname + '(' + record.user?.phone_number + ')'
    formState.value.record_id = record.id
    formState.value.start_time = record.reservation_date + ' ' + record.time_range.replace('-', ' ~ ')
  }

  reservationHourApi
    .get({
      activity_id: id,
      start_date: record.activity.start_time.slice(0, 10),
      end_date: record.activity.end_time.slice(0, 10),
      limit: 99999
    })
    .then(res => {
      let arr = []
      for (const item of res) {
        let child = []
        if (!item.is_full) {
          item.time_ranges.forEach(e => {
            if (!e.is_full) {
              child.push({ label: e.time_range, value: e.time_range })
            }
          })
          arr.push({ label: item.date, value: item.date, child })
        }
      }
      reservationDateOptions.value = arr
    })
}
const handleSingle = (record, status) => {
  reservationAuditApi
    .post({
      activity_id: record.activity_id,
      phones: record.user.phone_number,
      audit_status: status
    })
    .finally(() => {
      visible.value = false
      setPage()
      setPageSec()
    })
}
const handleBatch = (activity_id, status) => {
  const phones = handleBatchAuditText(status)
  reservationAuditApi
    .post({
      activity_id: activity_id,
      phones,
      audit_status: status
    })
    .then(res => {
      let str = ''
      if (res.invalid_phone) {
        let a = res.invalid_phone['1']
        let b = res.invalid_phone['2'] ?","+res.invalid_phone['2'] :""
        str = '审核失败：' + a + b
        if(b){
          str+="..."
        }
        message.warning(str)
      }
    })
    .finally(() => {
      visible.value = false
      setPage()
    })
}
const handleChangeComfirm = () => {
  changeApi.post(formState.value).then(() => {
    changeVisible.value = false
    setPage()
    message.success('操作成功')
  })
}
const handleDelete = () => {}
const onClose = () => {
  visible.value = false
  changeVisible.value = false
}
</script>
<style scoped lang="less">
.title {
  color: #00000073 !important;
}
.number {
  font-size: 20px;
}

.list {
  background-color: #fff;
  margin-bottom: 20px;
  padding: 10px;
}
.list_title {
  background-color: #fff;
  padding: 16px 20px;
  margin-bottom: 1px;
}
.select-width {
  width: 100px;
  :deep(.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
    width: 100px;
  }
}
:deep(.ant-input) {
  width: 300px;
}
.stats-data {
  &-wrap {
    padding: 24px;
    background: #fff;
  }
  &-title {
    color: #999;
    line-height: 1;
  }
  &-number {
    line-height: 1;
    font-size: 22px;
    color: #333;
    padding-top: 12px;
  }
}
</style>