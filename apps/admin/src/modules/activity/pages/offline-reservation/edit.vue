<template>
  <!--  -->
  <uc-layout-form @submit="onSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动封面" name="cover_url" class="required">
            <uc-upload
              :list="formState.cover_url ? [formState.cover_url] : []"
              upload-text=""
              :show-label="true"
              label-text="活动封面"
              :max-length="1"
              :disabled="isallRead"
              @update:list="data => (formState.cover_url = data[0])"
            />
          </a-form-item>
          <a-form-item label="活动时间" name="start_time" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isallRead || isRead"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              placeholder="活动开始时间"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isallRead || formState.status === 'ended'"
              :disabled-date="disabledEndTime"
              placeholder="活动结束时间"
            />
          </a-form-item>
          <a-form-item label="活动名称" name="title" class="required">
            <a-input
              v-model:value="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isallRead"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value="formState.tag"
              maxlength="10"
              placeholder="请输入活动标签，单个标签不超过10字"
              :disabled="isallRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="预约设置" class="h-fill">
          <!-- <a-form-item label="报名资格" name="user_level_id" class="required">
        <a-select
          v-model:value="formState.user_level_id"
          placeholder="请选择报名资格"
          style="width: 100%"
          :options="levelOptions"
          :disabled="isRead ||isallRead"
          @change="handleQualificationChange"
        />
      </a-form-item> -->
          <a-form-item label="报名方式" name="reservation_type" class="required">
            <a-select
              v-model:value="formState.reservation_type"
              placeholder="请选择报名方式"
              style="width: 100%"
              :disabled="isallRead || isRead"
              :options="reservationType.options()"
              @change="handleMethodChange"
            />
            <div v-if="formState.reservation_type === 'questionnaire'" style="margin-top: 24px">
              <a-select
                v-model:value="formState.questionnaire.questionnaire_id"
                :options="questionList"
                allow-clear
                placeholder="请选择调查问卷"
                :disabled="isallRead || isRead"
              />
            </div>
          </a-form-item>
          <a-form-item label="报名消耗" name="cost" class="required">
            <a-input-number
              v-model:value="formState.cost"
              :precision="0"
              :min="0"
              placeholder="请输入报名消耗积分值"
              :disabled="isallRead"
            />
          </a-form-item>
          <a-form-item label="预约名额" name="audit_type" class="required">
            <a-select
              v-model:value="formState.audit_type"
              placeholder="请选择审核类型"
              class="w-160 m-r-10"
              :options="auditType.options()"
              :disabled="isallRead || isRead"
              @change="handleExamineChange"
            />
            <a-input-number
              v-model:value="formState.limit_value"
              class="w-160 m-r-10"
              :precision="0"
              :min="0"
              placeholder="预约名额，0表示不限"
              :disabled="isallRead"
            />
            <a-input-number
              v-model:value="formState.hour_max_limit_value"
              class="w-160"
              :precision="0"
              :min="1"
              placeholder="每小时最大可预约人数"
              :disabled="isallRead"
            />
          </a-form-item>
          <a-form-item label="预约时段" name="enable_reservation_period" class="required">
            <a-space :size="10" wrap>
              <a-select
                v-model:value="formState.enable_reservation_period"
                class="w-160"
                placeholder="是否开启预约时段"
                style="width: 100%"
                :options="switchOptions.options()"
                :disabled="isallRead || isRead"
              />
              <a-time-picker
                v-model:value="formState.reservation_start_time_am"
                class="w-160"
                format="HH:mm"
                value-format="HH:mm"
                hide-disabled-options
                :disabled-hours="deleleHoursAm"
                :disabled-minutes="deleleMinutesAm"
                :minute-step="30"
                placeholder="选择上午开始时间"
                :disabled="isallRead || isRead || !formState.enable_reservation_period"
              />
              <a-time-picker
                v-model:value="formState.reservation_end_time_am"
                class="w-160"
                format="HH:mm"
                value-format="HH:mm"
                hide-disabled-options
                :disabled-hours="deleleHoursAm"
                :disabled-minutes="deleleMinutesAm"
                :minute-step="30"
                placeholder="选择上午结束时间"
                :disabled="isallRead || isRead || !formState.enable_reservation_period"
              />
              <a-time-picker
                v-model:value="formState.reservation_start_time_pm"
                class="w-160"
                format="HH:mm"
                value-format="HH:mm"
                hide-disabled-options
                :disabled-hours="deleleHoursPm"
                :disabled-minutes="deleleMinutesPm"
                :minute-step="30"
                placeholder="选择下午开始时间"
                :disabled="isallRead || isRead || !formState.enable_reservation_period"
              />
              <a-time-picker
                v-model:value="formState.reservation_end_time_pm"
                class="w-160"
                format="HH:mm"
                value-format="HH:mm"
                hide-disabled-options
                :disabled-hours="deleleHoursPm"
                :disabled-minutes="deleleMinutesPm"
                :minute-step="30"
                placeholder="选择下午结束时间"
                :disabled="isallRead || isRead || !formState.enable_reservation_period"
              />
            </a-space>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="签到核销">
          <a-form-item label="签到方式" name="sign_type" class="required">
            <a-select
              v-model:value="formState.sign_type"
              placeholder="请选择签到方式"
              style="width: 100%"
              :options="signType.options()"
              :disabled="isallRead || isRead"
              @change="handleSignChange"
            />
          </a-form-item>
          <a-form-item label="奖品发放" name="award_dispatch_type" class="required">
            <a-select
              v-model:value="formState.award_dispatch_type"
              placeholder="请选择奖品发放方式"
              style="width: 100%"
              :options="provideOptions.options()"
              :disabled="isallRead || isRead"
            />
          </a-form-item>
          <a-form-item v-if="formState.award_dispatch_type === 'online'" label="兑奖期限" name="limit_day" class="required">
            <a-input-number
              v-model:value="formState.limit_day"
              :precision="0"
              :min="1"
              placeholder="请输入活动结束后领奖截止天数"
              :disabled="isallRead"
            />
          </a-form-item>
          <a-form-item
            v-if="formState.award_dispatch_type === 'online'"
            label="核销奖励"
            name="rule.award_settings"
            class="required"
          >
            <div v-for="(item, index) in formState.rule.award_settings" :key="index" class="addGift">
              <a-select
                v-model:value="item.type"
                class="w-160 m-r-10"
                placeholder="礼品类型"
                style="width: 100%"
                :options="giftType.options()"
                :disabled="isallRead || isRead"
                @change="handleGiftTypeChange"
              />
              <a-input-number
                v-if="item.type === giftType.credit"
                v-model:value="item.quantity"
                class="w-300 m-r-10"
                show-search
                :min="1"
                :placeholder="giftType.getPlaceholder(item.type)"
                :disabled="isallRead || isRead"
              />
              <a-select
                v-else
                v-model:value="item.option_id"
                class="w-300 m-r-10"
                :placeholder="giftType.getPlaceholder(item.type)"
                :options="item.options"
                show-search
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                :disabled="isallRead || isRead"
                @focus="handleShowGifts(item)"
                @search="handleSearchGifts($event, item)"
              />
              <a-select
                v-model:value="item.user_level"
                class="w-200 m-r-10"
                placeholder="会员等级"
                style="width: 100%"
                :options="levelOptions"
                :disabled="isallRead || isRead"
              />
              <a-input-number
                v-model:value="item.stock"
                class="w-100 m-r-10"
                :precision="0"
                :min="1"
                placeholder="活动数量"
                :disabled="isallRead"
              />
              <a-button
                shape="circle"
                size="small"
                class="delete-btn"
                type="link"
                :disabled="formState.rule.length == 1 || isallRead || isRead"
              >
                <template #icon>
                  <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveGift(index)" />
                </template>
              </a-button>
            </div>
            <div v-if="!isRead && !isallRead" class="addGift">
              <a href="javascript:void(0)" @click="onAddGift">添加奖励层级</a>
            </div>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动地点">
          <a-form-item label="活动城市" name="city" class="required">
            <a-cascader
              v-model:value="formState.city"
              placeholder="请选择城市"
              :options="citys.map_options"
              :field-names="{ label: 'label', value: 'label', children: 'children' }"
              :disabled="isallRead || isRead"
              @change="handleCityChange"
              @blur="onBlur"
            />
          </a-form-item>
          <a-form-item label="详细地址" name="address.detail" class="required">
            <a-input
              v-model:value="formState.address.detail"
              maxlength="50"
              placeholder="请输入详细地址，不超过50字"
              :disabled="isallRead || isRead"
              @blur="onBlur"
            />
          </a-form-item>
          <a-form-item label="自动定位" name="type" class="required">
            <!-- <a-input
          v-model:value="search.keyword"
          maxlength="50"
          placeholder="请输入详细地位地址，不超过50字"
          :disabled="isRead || isallRead"
          @change="
            () => {
              formState.address.location_detail = search.keyword
            }
          "
        /> -->
            <div style="position: relative; z-index: 3">
              <lMap
                :location-detail="formState.address.location_detail"
                :disabled="isallRead || isRead"
                placeholder="请输入详细地位地址，不超过50字"
                @expose-keyword="getKeyword"
              />
            </div>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.desc" :height="300" :is-validator="true" placeholder="请输入活动详情" />
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.note" :height="300" :index="1" :is-validator="true" placeholder="预约须知" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { cloneDeep } from 'lodash'
import { defineComponent, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import lMap from './components/map.vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePrizeAwardUpdate } from '../../usePrizeAward'
import { giftUpdata } from '../../gift.js'
import {
  qualificationOptions,
  auditType,
  signType,
  switchOptions,
  provideOptions,
  reservationType,
  giftType
} from '../../reservationEnums'
import citys from '@/utils/citys'
import { reservationEditApi, addreservationApi, questionnaireApi } from '../../api'
import { add } from 'lodash-es'
import { start } from 'nprogress'
const { handleShowGifts, handleSearchGifts, useUserLevelOptions } = giftUpdata()
const { handleAward } = usePrizeAwardUpdate()
const { levelOptions } = useUserLevelOptions({ is_enable: 1, is_edit: true })
defineComponent({
  lMap
})
const router = useRouter()
const { id, no, status } = useRoute().params // 参数
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  cover_url: '',
  start_time: '',
  end_time: '',
  title: '',
  tag: '',
  // user_level_id: 1,
  reservation_type: 'complete_info',
  cost: undefined,
  enable_reservation_period: true,
  reservation_start_time_am: '',
  reservation_end_time_am: '',
  reservation_start_time_pm: '',
  reservation_end_time_pm: '',
  audit_type: 'automatic',
  limit_value: undefined,
  hour_max_limit_value: undefined,
  sign_type: 'user_scanning',
  award_dispatch_type: 'offline',
  limit_day: undefined,
  rule: {
    award_settings: [
      {
        option_id: undefined,
        type: 'entity',
        quantity: undefined,
        stock: undefined,
        user_level: 1,
        user_dispatch_setting_id: null
      }
    ]
  },
  address: {
    province: '',
    city: '',
    region: '',
    detail: '',
    latitude: -90,
    longitude: -180,
    location_detail: ''
  },
  questionnaire: {
    questionnaire_id: undefined
  },
  city: undefined,
  detail: '',
  desc: '',
  note: `<p>&nbsp;①  请您遵守活动参与规则急现场工作人员引导，如违反相关规则将可能影响到您的账户状态</p><p>&nbsp;②  请于预约时间段开始前15分钟完成签到，若未完成则视为自动放弃体验，主办方将不承担任何责任</p><p>&nbsp;③  活动预约需要填写真实有效个人信息，如姓名、联系电话</p><p>&nbsp;④  本页面预约活动不收取任何费用，且主办方不负责承担活动参加人任何其他费用，包括但不限于机票、酒店、食宿等</p><p>&nbsp;⑤  如因天气、疫情等因素取消或改活动，我们将第一时间通知活动参加人，请尽量保持手机畅通&nbsp;</p><p>&nbsp;⑥  如因个人信息填写错误等非主办方原因导致活动参加人未收到活动信息通知而未能参加活动的，主办方概不负责&nbsp;</p><p>&nbsp;⑦  如果您是未成年人，则必须由父母或法定监护人为您报名参加本次活动体验</p>`
})
const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)
let isallRead = ref(false)
let isRead = ref(false)

if (id) {
  if (no === 'no') {
    isallRead.value = true
  } else if (status !== 'not_start') {
    isRead.value = true
  }
  reservationEditApi.get(id, { relations: ['rule', 'questionnaire'] }).then(async res => {
    const showPrizePrm = []
    if (res.rule) {
      res.rule.award_settings.forEach(item => {
        item.type === 'none' && (item.stock = item.quantity = item.type = undefined)
        showPrizePrm.push(handleAward(item, { id: item.option_id }))
      })
      await Promise.all(showPrizePrm)
    }

    setFormState(res)
    if (!res.rule) {
      formState.value.rule = {
        award_settings: [
          {
            option_id: undefined,
            type: 'entity',
            quantity: undefined,
            stock: undefined,
            user_level: 1,
            user_dispatch_setting_id: null
          }
        ]
      }
    }
    search.value.keyword = res.address.location_detail
    formState.value.city = [res.address.province, res.address.city, res.address.region]
  })
}
const questionList = ref([])
const search = ref({ keyword: '' })
const getKeyword = e => {
  formState.value.address.location_detail = e.adr
  formState.value.address.latitude = e.location.lat
  formState.value.address.longitude = e.location.lng
}

const initList = () => {
  questionnaireApi.paginator({ page: 1, limit: 20, all: false }).then(({ items }) => {
    questionList.value = useTransformOptions(items, 'title', 'id')
  })
}
initList()
//活动时间验证
const timeValidator = (_, value) => {
  if (!formState.value.start_time) {
    return Promise.reject('请选择开始时间')
  }
  if (!formState.value.end_time) {
    return Promise.reject('请选择结束时间')
  }
  if (
    !formState.value.enable_reservation_period &&
    formState.value.start_time.substring(0, 10) !== formState.value.end_time.substring(0, 10)
  ) {
    return Promise.reject('活动仅一天时支持关闭预约时段')
  }
  return Promise.resolve()
}
//预约时段验证
const switchValidator = (_, value) => {
  if (!formState.value.enable_reservation_period) return Promise.resolve()
  if (!formState.value.reservation_start_time_am) {
    return Promise.reject('请选择上午开始时间')
  }
  if (!formState.value.reservation_end_time_am) {
    return Promise.reject('请选择上午结束时间')
  }
  if (!formState.value.reservation_start_time_pm) {
    return Promise.reject('请选择下午开始时间')
  }
  if (!formState.value.reservation_end_time_pm) {
    return Promise.reject('请选择下午结束时间')
  }
  return Promise.resolve()
}
//预约名额验证
const examineValidator = (_, value) => {
  if (!formState.value.hour_max_limit_value && formState.value.hour_max_limit_value !== 0) {
    return Promise.reject('请输入预约名额')
  }
  if (!formState.value.limit_value && formState.value.limit_value !== 0) {
    return Promise.reject('请输入每小时最大可预约人数')
  }
  return Promise.resolve()
}
//核销奖励验证
const limitValidator = (_, value) => {
  for (const item of formState.value.rule.award_settings) {
    if (formState.value.award_dispatch_type === 'offline') {
      return Promise.resolve()
    }
    if (!formState.value.limit_day && item.limit_day !== 0) {
      return Promise.reject('请输入兑奖期限')
    }
  }
  return Promise.resolve()
}
const giftValidator = (_, value) => {
  for (const item of formState.value.rule.award_settings) {
    if (formState.value.award_dispatch_type === 'offline') {
      return Promise.resolve()
    }
    if (item.type === 'entity' && !item.option_id && item.option_id !== 0) {
      return Promise.reject('礼品名称不能为空')
    }
    if (!item.user_level && item.user_level !== 0) {
      return Promise.reject('会员等级不能为空')
    }
    if (!item.stock && item.stock !== 0) {
      return Promise.reject('活动数量不能为空')
    }
  }
  return Promise.resolve()
}
setFormRules({
  cover_url: { required: true, message: '请上传活动封面' },
  start_time: { validator: timeValidator },
  end_time: { required: true, message: '请选择活动时间' },
  title: { required: true, message: '请选择活动名称' },
  tag: { required: true, message: '请选择活动标签' },
  selected_items: { required: true, message: '请选择报名资格', type: 'array' },
  reservation_type: { required: true, message: '请选择报名方式' },
  cost: { required: true, message: '请输入报名消耗积分值' },
  enable_reservation_period: { validator: switchValidator },
  audit_type: { validator: examineValidator },
  // sign_type: { required: true, message: '请选择签到方式' },
  // award_dispatch_type: { required: true, message: '请选择奖品发放方式' },
  limit_day: { validator: limitValidator, message: '请输入兑奖期限' },
  'rule.award_settings': { validator: giftValidator },
  city: { required: true, message: '请输入活动城市' },
  'address.detail': { required: true, message: '请输入详细地址' }
})

// 点击提交按钮
const onSubmit = async () => {
  if (no === 'no') {
    router.replace({ name: 'offline-reservation-list' })
    return
  }
  if (!(await validateForm())) return
  if (formState.start_time) {
  }
  const params = cloneDeep(formState.value)
  if (params.award_dispatch_type === 'offline') {
    delete params.rule
  }
  if (params.reservation_type !== 'questionnaire') {
    delete params.questionnaire
  }
  if (id) {
    //更新
    reservationEditApi.update(id, params).then(() => {
      message.success('操作完成')
      router.replace({ name: 'offline-reservation-list' })
    })
  } else {
    //新增
    addreservationApi.post(params).then(() => {
      message.success('操作完成')
      router.replace({ name: 'offline-reservation-list' })
    })
  }
}
const deleleHoursAm = () => {
  let hoursData = []
  for (let hour = 0; hour < 24; hour++) {
    if (!(hour >= 7 && hour <= 12)) {
      hoursData.push(hour)
    }
  }
  return hoursData
}
const deleleHoursPm = () => {
  let hoursData = []
  for (let hour = 0; hour < 24; hour++) {
    if (!(hour >= 12 && hour <= 20)) {
      hoursData.push(hour)
    }
  }
  return hoursData
}
const deleleMinutesAm = hour => {
  if (hour === 12) {
    return [30]
  }
}
const deleleMinutesPm = hour => {
  if (hour === 20) {
    return [30]
  }
}
const selected_items = ref([0])
const handleSignChange = e => {
  if (e === 'user_present') {
    formState.value.award_dispatch_type = 'offline'
  }
}
const handleCityChange = e => {
  formState.value.address.province = e[0]
  formState.value.address.city = e[1]
  formState.value.address.region = e[2]
}
//添加奖励层级
const onAddGift = e => {
  if (formState.value.rule.award_settings.length == 10) return
  formState.value.rule.award_settings.push({
    option_id: undefined,
    type: 'entity',
    quantity: undefined,
    stock: undefined,
    user_level: 1,
    user_dispatch_setting_id: null
  })
}
//移除奖励层级
const onRemoveGift = index => {
  if (formState.value.rule.award_settings.length == 1) return
  formState.value.rule.award_settings.splice(index, 1)
}
const onBlur = () => {
  const {
    city,
    address: { detail }
  } = formState.value

  if (city && detail) {
    const cityStr = Object.values(city).reduce((prev, item) => prev + item, '')
    formState.value.address.location_detail = cityStr + detail
  }
}
</script>
<style scoped lang="less">
.addGift {
  margin-bottom: 10px;
}
</style>
