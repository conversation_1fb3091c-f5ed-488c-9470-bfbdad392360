<template>
  <!--  -->
  <uc-layout-list title="线下预约">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
          <span style="margin-left:15px"></span>
          <a-select
            v-model:value="formState.status"
            placeholder="活动状态"
            class="w-120"
            :options="activityStatus.options()"
            @change="changeConditionKey"
          />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称" ellipsis>
          <template #default="{ record }">
            {{ record.title }}
          </template>
        </a-table-column>
        <a-table-column width="150px" title="PV/UV" ellipsis align="right">
          <template #default="{ record }">
            {{ record.pv + '/' + record.uv }}
          </template>
        </a-table-column>
        <a-table-column title="活动时间" width="360px">
          <template #default="{ record }">
            {{ record.start_time }} ～ {{ $formatters.transformActivityEndTime(record.end_time) }}
          </template>
        </a-table-column>
        <a-table-column title="活动状态" width="120px" ellipsis>
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column width="180px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="copyLink(linkPath + record.id)">
              链接
            </a-button>

            <a-button type="link" @click="onView(record)">
              数据
            </a-button>

            <a-button v-if="record.status !== 'ended'" type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-button v-if="record.status === 'ended'" type="link" @click="onEdit(record, 'no')">
              查看
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增线下预约
      </a-button>
    </template>
  </uc-layout-list>
</template>
  
  <script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { reservationListApi } from '../../api'
import { activityStatus } from '../../reservationEnums'
import { useTimeStatus } from '@/composables/useTimeStatus'
const linkPath = Object.freeze(`/reservation/pages/standard/details/index?id=`)
const { statusFilter } = useTimeStatus()
const { formState,onRestFormState, resetFormState } = useFormState({
  title:undefined,
  status:undefined
}) // 查询表单
const router = useRouter()
onRestFormState(() => setPage())
// 表格请求
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  reservationListApi.paginator({
    filters: useTransformQuery(formState, { title: 'like' }),
    offset,
    limit
  })
)

const onAdd = () => {
  router.push({
    name: 'reservation-add'
  })
}
const onEdit = ({ id ,status}, no) => {
  no=no??"yes"
  router.push({
    name: 'reservation-edit',
    params: { id, no ,status}
  })
}
const onView = ({ id }) => {
  router.push({
    name: 'reservation-data',
    params: { id }
  })
}
const handleDelete = ({ id }) => {
  reservationListApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}
</script>
  
  <style scoped lang="less">
</style>