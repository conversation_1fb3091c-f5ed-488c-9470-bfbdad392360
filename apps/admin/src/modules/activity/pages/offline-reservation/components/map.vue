<template>
  <!--  -->
  <div>
    <div style="width: 100%; height: 400px">
      <a-input
        v-model:value.trim="keyword"
        :disabled="props.disabled"
        maxlength="50"
        :placeholder="props.placeholder"
        @blur="inputAdr"
      />
      <div style="margin-top: 10px"></div>
      <tmap-map
        map-key="LDVBZ-UEL3J-2T6FR-X5D4I-6U5ZK-ZSBPR"
        :events="events"
        :center="center"
        :zoom="zoom"
        :double-click-zoom="doubleClickZoom"
      >
        <tmap-multi-marker ref="markers" :styles="markerStyles" :geometries="markerGeometries" />
      </tmap-map>
    </div>
  </div>
</template>
<script>
import { getAddressQQ, getLocationQQ } from '../../../qqjsonp.js'
export default {
  name: 'LMap'
}
</script>
<script setup>
import { nextTick, onMounted, ref, watch } from 'vue'
import { debounce } from 'lodash'
import { message } from 'ant-design-vue'

const props = defineProps({
  // 标题
  locationDetail: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})
const markers = ref()
const paths = [
  { lat: 30.291552, lng: 120.075245 },
  { lat: 28.2092, lng: 112.8903 }
]
const stop = ref(false)
const move = latLng => {
  center.value = latLng
  // markerGeometries.value.position = latLng
}
const keyword = ref('')
const print = e => {
  paths[0] = paths[1]
  paths[1] = { lat: Number(e.latLng.lat), lng: Number(e.latLng.lng) }
  moveAlong()
  getLocationQQ({
    location: e.latLng.lat + ',' + e.latLng.lng,
    cb: res => {
      const result = res.result
      const location = result.location
      const adr =
        result.address_component.province + result.address_component.city + result.formatted_addresses.recommend
      const addressComponent = result.address_component
      const obj = { location, adr, addressComponent }
      emit('exposeKeyword', obj)
      stop.value = true
      keyword.value = adr
      move(location)
      inputAdr()
    }
  })
}
const markerGeometries = ref([
  {
    id: 'marker', //点标记唯一标识，后续如果有删除、修改位置等操作，都需要此id
    position: { lat: 30.291552, lng: 120.075245 } //点标记坐标位置
  }
])
const moveAlong = () => {
  markers.value.moveAlong(
    {
      marker: {
        path: paths.map(p => {
          return new window.TMap.LatLng(p.lat, p.lng)
        }),
        speed: 100000000
      }
    },
    {
      autoRotation: true
    }
  )
}
const markerStyles = {}
const events = {
  click: print
}
const control = {
  scale: {},
  zoom: {}
}
const zoom = 17
const center = ref({ lat: 30.290756, lng: 120.074387 })
const latLng = {}
const emit = defineEmits(['exposeKeyword'])
const inputAdr = debounce(e => {
  if (!keyword.value) {
    return
  }
  if (stop.value) {
    stop.value = false
    return
  }
  getAddressQQ({
    adr: keyword.value,
    cb: res => {
      if (res.status === 347) {
        message.error(res.message)
        return
      }
      move(res.result.location)
      getLocationQQ({
        location: res.result.location.lat + ',' + res.result.location.lng,
        cb: res => {
          const result = res.result
          const location = result.location
          paths[1] = result.location
          markerGeometries.value.position = result.location
          console.log(result.location, paths)
          moveAlong()
          const adr =
            result.address_component.province + result.address_component.city + result.formatted_addresses.recommend
          const addressComponent = result.address_component
          const obj = { location, adr, addressComponent }
          emit('exposeKeyword', obj)
        }
      })
    }
  })
}, 500)
watch(
  () => props.locationDetail,
  (newVal, oldVal) => {
    keyword.value = newVal
    inputAdr()
  }
)
</script>
<style scoped lang="less">
.bmap {
  margin-top: 10px;
  width: 1000px;
  height: 0px;
}
</style>
