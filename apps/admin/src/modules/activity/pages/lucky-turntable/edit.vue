<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="活动开始时间"
              :disabled="isNormal || isRead"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="活动结束时间"
              :disabled="isRead"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              class="input-width"
              placeholder="请输入活动标签，单个标签不超过10字"
              maxlength="10"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="是否允许分享" name="is_share">
            <a-radio-group v-model:value="formState.is_share">
              <a-radio :value="1">允许</a-radio>
              <a-radio :value="0">不允许</a-radio>
            </a-radio-group>
          </a-form-item>
          <a-form-item label="活动渠道">
            <div class="flex">
              <a-select
                v-model:value="formState.channel"
                mode="tags"
                show-search
                placeholder="请选择活动渠道"
                :options="channelsOptions"
                class="w-300"
                :max-tag-count="1"
                @search="channelsSearch"
                @select="channelsSelect"
              />
              <a-input-number
                v-model:value.trim="formState.channel_limit"
                class="m-l-2 w-200"
                placeholder="请输入活动渠道限制次数"
                min="0"
                :disabled="isRead"
              />
            </div>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="抽奖次数" class="required">
            <a-input-number
              v-model:value="formState.draw_limit"
              :min="0"
              :disabled="isRead"
              placeholder="请输入本次活动免费抽奖次数上限（不含奖励的抽奖次数，0表示不限制）"
            />
          </a-form-item>
          <a-form-item label="单日抽奖" class="required">
            <a-input-number
              v-model:value="formState.daily_draw_limit"
              :min="0"
              :disabled="isRead"
              placeholder="请输入单日免费抽奖次数上限（不含奖励的抽奖次数）"
            />
          </a-form-item>
          <a-form-item label="单日中奖" class="required">
            <a-input-number
              v-model:value="formState.daily_winner_limit"
              :min="0"
              :disabled="isRead"
              placeholder="请输入单日中奖次数上限（0表示不限制）"
            />
          </a-form-item>
          <a-form-item label="每人中奖" class="required">
            <a-input-number
              v-model:value="formState.everyone_winner_limit"
              :min="0"
              :disabled="isRead"
              placeholder="请输入每人总中奖次数上限（0表示不限制）"
            />
          </a-form-item>
          <a-form-item label="抽奖消耗" class="required">
            <a-input-number
              v-model:value="formState.cost"
              :min="0"
              :disabled="isRead"
              placeholder="请输入单次抽奖消耗值（0表示不消耗）"
            />
          </a-form-item>
          <a-form-item label="兑奖期限" class="required">
            <a-input-number
              v-model:value="formState.exchange_date_limit"
              :min="0"
              :disabled="isRead"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
            />
          </a-form-item>
          <a-form-item label="参与资格" class="required">
            <a-select
              v-model:value="formState.user_level_id"
              placeholder="请选择参与资格"
              :disabled="isRead"
              :options="levelOptions"
            />
            <a-input
              v-model:value="formState.requirement"
              class="flex m-t-14"
              :disabled="isRead"
              placeholder="请输入参与资格校验API（非必填）"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="奖项设置" class="h-fill">
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            :label="`奖项${ruleIndex + 1}`"
            class="required form-items-margin-bottom"
          >
            <template v-for="(item, index) in rule.award_settings" :key="index">
              <a-space class="m-b-10" wrap>
                <a-select
                  v-model:value="rule.grade"
                  class="w-100"
                  placeholder="请选择"
                  :options="turntablePrizes.options()"
                  :disabled="isRead"
                  @change="onChangeGiftGrade(rule, item)"
                />
                <a-select
                  v-model:value="item.type"
                  class="w-100"
                  :placeholder="rule.isZero ? '谢谢参与' : '奖品类型'"
                  :options="turntableOptions"
                  :disabled="rule.isZero || isEdit || isRead"
                  @change="onChangeGiftType(item)"
                />
                <a-input-number
                  v-if="item.type === giftType.credit"
                  v-model:value="item.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(item.type)"
                  :disabled="rule.isZero || isEdit || isRead"
                />
                <a-input
                  v-else-if="rule.isZero"
                  class="w-300"
                  placeholder="谢谢参与"
                  :disabled="rule.isZero || isEdit || isRead"
                />
                <a-select
                  v-else
                  v-model:value="item.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(item.type)"
                  :options="item.options"
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="rule.isZero || isEdit || isRead"
                  @focus="handleShowGifts(item)"
                  @search="handleSearchGifts($event, item)"
                />
                <a-input-number
                  v-model:value="item.stock"
                  min="0"
                  class="w-100"
                  placeholder="活动数量"
                  :disabled="rule.isZero || isEdit || isRead"
                />
                <a-input-number
                  v-model:value="rule.probability"
                  class="w-100"
                  :min="0"
                  placeholder="中奖概率"
                  :disabled="isRead"
                />
                <uc-upload
                  v-model:list="rule.prize_url"
                  class="upload-small-cover"
                  size="small"
                  title="奖品图片"
                  :max-length="1"
                  upload-text=" "
                  :disabled="isRead"
                />
                <uc-upload
                  v-model:list="rule.pop_url"
                  class="upload-small-cover"
                  size="small"
                  title="中奖弹窗图片"
                  :max-length="1"
                  upload-text=" "
                  :disabled="isRead"
                />
              </a-space>
            </template>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="页面配置" class="h-fill">
          <a-form-item label="皮肤模式" class="required">
            <a-select
              v-model:value="formState.page_setting.skin_mode"
              class="m-r-10 w-310"
              :disabled="isRead"
              placeholder="请选择皮肤"
              allow-clear
              :options="skinMode.option()"
            />
            <a-button class="p-lr-0 relative" type="link">
              预览效果图
              <div class="w-fill h-fill hide absolute top-0">
                <a-image class="opacity-0" :src="assets.luckyPreviewUrl" />
              </div>
            </a-button>
          </a-form-item>
          <a-form-item label="颜色配置" class="required">
            <a-space :size="10" wrap>
              <a-input
                v-model:value.trim="formState.page_setting.color.subject"
                class="w-160"
                placeholder="主题色，如#000000"
                :disabled="isRead"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.sub"
                class="w-160"
                placeholder="辅助色，如#000000"
                :disabled="isRead"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.bg"
                class="w-160"
                placeholder="背景色，如#000000"
                :disabled="isRead"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.title"
                class="w-160"
                placeholder="标题色，如#000000"
                :disabled="isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" name="title" class="required">
            <a-space wrap>
              <uc-upload
                v-model:list="formState.page_setting.page.activity_bg"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动背景"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.activity_text"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动文案"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.turnplate_bottom"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="转盘底座"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.turnplate_bg"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="转盘背景"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.available_button"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="抽奖可用"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.unavailable_button"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="抽奖禁用"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.drawing_button"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="按钮底图"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.share_button"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="邀请好友按钮"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.prize_button"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="我的奖品按钮"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.rule_button"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动规则按钮"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.more_bg"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="更多活动"
                :disabled="isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="弹窗配置" name="title" class="required">
            <a-space wrap>
              <uc-upload
                v-model:list="formState.page_setting.popup.cost_deficiency"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="抵扣不足"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.popup.requirement"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="资格不符"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.popup.daily_draw_limit"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="次数受限"
                :disabled="isRead"
              />
              <!-- <uc-upload
            v-model:list="formState.page_setting.popup.prepare"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="未开始"
            :disabled="isRead"
          />
          <uc-upload
            v-model:list="formState.page_setting.popup.end"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="已结束"
            :disabled="isRead"
          /> -->
            </a-space>
          </a-form-item>
          <a-form-item label="更多活动跳转">
            <uc-jump v-model:value="formState.page_setting.jumps.more_jump" ignore-jumps="['contact', 'share']" />
          </a-form-item>
          <a-form-item label="抵扣不足跳转">
            <uc-jump
              v-model:value="formState.page_setting.jumps.cost_deficiency_jump"
              ignore-jumps="['contact', 'share']"
            />
          </a-form-item>
          <a-form-item label="资格不符跳转">
            <uc-jump
              v-model:value="formState.page_setting.jumps.requirement_jump"
              ignore-jumps="['contact', 'share']"
            />
          </a-form-item>
          <a-form-item label="次数受限跳转">
            <uc-jump
              v-model:value="formState.page_setting.jumps.daily_draw_limit_jump"
              ignore-jumps="['contact', 'share']"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="分享设置" class="h-fill">
          <a-form-item label="分享海报" name="title" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.share_setting.poster.session"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="会话海报"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.share_setting.poster.local"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="本地海报"
                :disabled="isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="分享文案" class="required">
            <a-input
              v-model:value.trim="formState.share_setting.content"
              class="input-width"
              placeholder="请输入分享文案，不超过20字"
              :maxlength="20"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.desc" placeholder="请输入活动说明（不超过500字）" :disabled="isRead" :height="300" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useTransformImg } from '@/composables/useTransformFormat'
import { colorVerify } from '@/utils/index'
import { turnplateApi } from '../../api'
import { skinMode, turntablePrizes, giftType } from '../../enums'
import { jumpType } from '@/enums/jump'
import { useUserLevelOptions } from '../../useUserLevel'
import { usePrizeAwardUpdate, useTransformPrize } from '../../usePrizeAward'
import { useDisabledDate } from '@/composables/useDisabledDate'
import assets from '../../assets.config'
import { useDrawChannelOptions } from '../../useDrawChannel'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
const { transformPrizesRequest } = useTransformPrize()
const { batchTransformImg } = useTransformImg()
const { levelOptions } = useUserLevelOptions({ is_enable: 1 })
const { TIME_STATUS_NOSTART, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()
const turntableOptions = giftType.turntableOptions()

const { options: channelsOptions, search: channelsSearch } = useDrawChannelOptions()
const channelsSelect = (value) => {
  formState.value.channel = [value]
}

const router = useRouter()
const { id } = useRoute().params

let isNostart = ref(false)
let isNormal = ref(false)
let isEdit = ref(false)
let isRead = ref(false)

const imgKeys = {
  rule: ['prize_url', 'pop_url']
}

nextTick(() => {
  if (id) {
    const hideLoading = message.loading('正在加载数据...')
    isEdit.value = true

    turnplateApi
      .get(id, { relations: ['rules'] })
      .then(async res => {
        switch (res.status) {
          case TIME_STATUS_NOSTART:
            isNostart.value = true
            break
          case TIME_STATUS_NORMAL:
            isNormal.value = true
            break
          case TIME_STATUS_ENDED:
            isRead.value = true
            break
        }
        const showPrizePrm = []
        res.rules.forEach(rule => {
          batchTransformImg(rule, 'array', imgKeys.rule)
          rule.award_settings.forEach(item => {
            item.type === 'none' && (item.stock = item.quantity = item.type = undefined)
            showPrizePrm.push(handleAward(item, { id: item.option_id }))
          })

          rule.probability /= 100
          rule.isZero = rule.grade === turntablePrizes.zero
        })
        await Promise.all(showPrizePrm)

        batchTransformImg(res.page_setting.page)
        batchTransformImg(res.page_setting.popup)
        batchTransformImg(res.share_setting.poster)

        setDefaultJump(res)

        res.channel = res.channel ? [res.channel] : []

        setFormState(res)
      })
      .finally(hideLoading)
  }
})

const defaultJump = () => {
  return {
    jump_type: jumpType.no,
    jump_link: undefined,
    appid: undefined
  }
}
const setDefaultJump = (row) => {
  for (const key in formState.value.page_setting.jumps) {
    if (!row.page_setting.jumps) {
      row.page_setting.jumps = {}
    }
    if (!row.page_setting.jumps[key]) {
      row.page_setting.jumps[key] = defaultJump()
    }
  }
}

const giftDefault = {
  award_settings: [onPrizeDefault()],
  probability: undefined,
  grade: turntablePrizes.zero,
  isZero: true,
  prize_url: [],
  pop_url: []
}

const ruleDefault = Array.from({ length: 8 }, () => cloneDeep(giftDefault))

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  type: 'turnplate',
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  is_share: 1,
  draw_limit: undefined,
  daily_draw_limit: undefined,
  daily_winner_limit: undefined,
  everyone_winner_limit: undefined,
  cost: undefined,
  exchange_date_limit: undefined,
  user_level_id: 1, // 默认普通会员
  requirement: undefined,
  rules: ruleDefault,
  page_setting: {
    skin_mode: skinMode.default,
    color: {
      subject: undefined,
      sub: undefined,
      bg: undefined,
      title: undefined
    },
    page: {
      activity_bg: [],
      activity_text: [],
      turnplate_bottom: [],
      turnplate_bg: [],
      available_button: [],
      unavailable_button: [],
      drawing_button: [],
      share_button: [],
      prize_button: [],
      rule_button: [],
      more_bg: []
    },
    popup: {
      // prepare: [],
      // end: [],
      cost_deficiency: [],
      requirement: [],
      daily_draw_limit: []
    },
    jumps: {
      more_jump: defaultJump(),
      cost_deficiency_jump: defaultJump(),
      requirement_jump: defaultJump(),
      daily_draw_limit_jump: defaultJump(),
    }
  },
  share_setting: {
    poster: {
      session: [],
      local: []
    },
    content: undefined
  },
  desc: undefined,
  channel: [],
  channel_limit: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  rules: {
    validator(_, value) {
      let tip
      let probability_sum = 0
      for (const { grade, probability, prize_url, pop_url, award_settings } of value) {
        for (const { type, option_id, quantity, stock } of award_settings) {
          if (grade != turntablePrizes.zero) {
            if (!type) {
              tip = '请选择奖项礼品类型'
              break
            }
            if (type === giftType.credit) {
              if (!quantity) {
                tip = '请输入积分值'
                break
              }
            } else {
              if (!option_id) {
                tip = '请选择奖项礼品'
                break
              }
            }
            if (!stock && stock !== 0) {
              tip = '请输入奖项活动数量'
              break
            }
          }
        }
        probability_sum += probability
        if (!probability && probability !== 0) {
          tip = '请输入中奖概率'
          break
        }
        if (!prize_url.length) {
          tip = '请选择奖项奖品图片'
          break
        }
        if (!pop_url.length) {
          tip = '请选择奖项弹窗图片'
          break
        }
        if (tip) break
      }

      if (tip) return Promise.reject(tip)
      if (probability_sum < 100) return Promise.reject('所有奖项中奖概率未达到100%')
      if (probability_sum > 100) return Promise.reject('所有奖项中奖概率超过100%')
      return Promise.resolve()
    }
  },
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  draw_limit: { required: true, message: '请输入本次活动免费抽奖次数上限' },
  daily_draw_limit: { required: true, message: '请输入单日免费抽奖次数上限' },
  daily_winner_limit: { required: true, message: '请输入单日总中奖次数上限' },
  everyone_winner_limit: { required: true, message: '请输入每人总中奖次数上限' },
  cost: { required: true, message: '请输入单次抽奖消耗值' },
  exchange_date_limit: { required: true, message: '请输入活动结束后领奖截止天数' },
  user_level_id: { required: true, message: '请选择参与资格' },

  page_setting: {
    validator(_, value) {
      const { skin_mode, page, color, popup } = value
      if (!skin_mode) return Promise.reject('请选择皮肤模式')
      if (!colorVerify(color.subject)) return Promise.reject('请输入主题色，以#开头+6位数字字母组合')
      if (!colorVerify(color.sub)) return Promise.reject('请输入辅助色，以#开头+6位数字字母组合')
      if (!colorVerify(color.bg)) return Promise.reject('请输入背景色，以#开头+6位数字字母组合')
      if (!colorVerify(color.title)) return Promise.reject('请输入标题色，以#开头+6位数字字母组合')
      // if (!page.activity_bg.length) return Promise.reject('请选择活动背景图片')
      // if (!page.activity_text.length) return Promise.reject('请选择活动文案图片')
      // if (!page.turnplate_bottom.length) return Promise.reject('请选择转盘底座图片')
      // if (!page.share_button.length) return Promise.reject('请选择邀请好友按钮图片')
      // if (!page.prize_button.length) return Promise.reject('请选择我的奖品按钮图片')
      // if (!page.rule_button?.length) return Promise.reject('请选择活动规则按钮图片')
      // if (!page.turnplate_bg.length) return Promise.reject('请选择转盘背景图片')
      // if (!page.available_button.length) return Promise.reject('请选择抽奖可用图片')
      // if (!page.unavailable_button.length) return Promise.reject('请选择抽奖不可用图片')
      // if (!page.drawing_button.length) return Promise.reject('请选择底图照片')
      // if (!popup.prepare.length) return Promise.reject('请选择未开始图片')
      // if (!popup.end.length) return Promise.reject('请选择已结束图片')
      if (!popup.cost_deficiency.length) return Promise.reject('请选择抵扣不足图片')
      if (!popup.requirement.length) return Promise.reject('请选择资格不符弹窗图片')
      if (!popup.daily_draw_limit.length) return Promise.reject('请选择次数受限图片')
      return Promise.resolve()
    }
  },
  share_setting: {
    validator(_, value) {
      const { poster, content } = value
      if (!poster.session.length) return Promise.reject('请选择会话海报')
      // if (!poster.local.length) return Promise.reject('请选择本地海报')
      if (!content) return Promise.reject('请输入分享文案')
      return Promise.resolve()
    }
  },
  desc: { required: true, message: '请输入活动说明' }
})

const onChangeGiftGrade = async (record, item) => {
  const { grade, type } = record

  if (grade === turntablePrizes.zero) {
    Object.assign(record, {
      isZero: true,
      probability: undefined,
      award_settings: [
        onPrizeDefault({
          type: undefined,
          setting: { prize_url: [], pop_url: [] }
        })
      ]
    })
  } else {
    record.isZero = false
    item.type = type || giftType.coupon
    onChangeGiftType(item)
  }
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)

  // handle params
  params.rules.forEach(rule => {
    transformPrizesRequest(rule)
    batchTransformImg(rule, 'string', imgKeys.rule)
    rule.award_settings.forEach(item => {
      if (!item.type) {
        item.type = 'none'
        item.option_id = 0
        item.quantity = 0
        item.stock = 0
      }
    })

    rule.probability *= 100
  })

  batchTransformImg(params.page_setting.page, 'string')
  batchTransformImg(params.page_setting.popup, 'string')
  batchTransformImg(params.share_setting.poster, 'string')
  params.channel = params.channel[0]
  id ? await turnplateApi.update(id, params) : await turnplateApi.create(params)
  message.success('操作完成')
  router.back()
}
</script>
<style scoped lang="less">
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}

.upload-small-cover {
  :deep(.ant-upload-picture-card-wrapper) {
    width: 32px;
    height: 32px;
  }

  :deep(.ant-upload) {
    height: 32px !important;
    margin: 0 !important;
  }
}

.form-items-margin-bottom {
  margin-bottom: 0 !important;
}
</style>
