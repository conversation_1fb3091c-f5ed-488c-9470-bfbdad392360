<template>
  <uc-layout-list title="首屏弹窗">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入弹窗名称" />
      </a-form-item>
      <a-form-item name="productType">
        <a-select v-model:value="formState.status" placeholder="弹窗状态" allow-clear :options="statusList" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="toAdd">
        新增首屏弹窗
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="弹窗名称/投放用户及频次" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.poster_url"
              :title="record.title"
              :subtit="launchCrowd.filterValue(record?.setting?.user.type).label"
            />
          </template>
        </a-table-column>
        <a-table-column title="点击UV/PV" width="150px" ellipsis align="right">
          <template #default="{ record }">
            {{ record.uv }}/{{ record.pv }}
          </template>
        </a-table-column>
        <a-table-column title="弹窗排序" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="input-center"
              style="width: 80px"
              :min="0"
              @blur="handleChangeSort(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="投放时间" width="200px">
          <template #default="{ record }">
            {{ record.start_time }}
            <div>至 {{ $formatters.transformActivityEndTime(record.end_time) }}</div>
          </template>
        </a-table-column>
        <a-table-column title="弹窗状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button v-if="record.status == TIME_STATUS_ENDED" type="link" class="link" @click="toEdit(record)">
              查看
            </a-button>
            <a-button
              v-if="record.status == TIME_STATUS_NORMAL || record.status == TIME_STATUS_NOSTART"
              type="link"
              @click="toEdit(record)"
            >
              编辑
            </a-button>
            <a-popconfirm
              :disabled="!record.can_delete"
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { popupApi } from '../../api'
import { launchCrowd } from '../../enums'

const { statusFilter, statusList, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  popupApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  status: undefined
})

onRestFormState(() => setPage())

const router = useRouter()

const toAdd = () => router.push({ name: 'first-screen-popup-add' })
const toEdit = ({ id }) => router.push({ name: 'first-screen-popup-edit', params: { id } })

const handleDelete = async ({ id }) => {
  await popupApi.delete(id)
  message.success('删除完成')
  setPage()
}

const handleChangeSort = async ({ id, sort }) => {
  popupApi
    .update(id, { sort })
    .then(() => message.success('操作成功'))
    .finally(setPage)
}
</script>
