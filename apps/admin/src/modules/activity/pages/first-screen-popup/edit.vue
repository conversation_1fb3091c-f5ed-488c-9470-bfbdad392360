<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="弹窗海报" class="required">
            <uc-upload v-model:list="formState.poster_url" upload-text=" " :max-length="1" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="投放时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isRead"
              placeholder="活动开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isRead"
              placeholder="活动结束时间"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="弹窗名称" class="required">
            <a-input v-model:value="formState.title" maxlength="30" placeholder="请输入弹窗广告名称，不超过30字" :disabled="isRead" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="弹窗配置" class="h-fill">
          <a-form-item label="投放频率" class="required">
            <a-select
              v-model:value="formState.frequency"
              placeholder="请选择投放频率"
              :options="launchHz.options()"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="弹窗排序" class="required">
            <a-input-number v-model:value="formState.sort" :min="0" placeholder="排序值越大排名越靠前" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="投放人群" class="required">
            <a-select
              v-model:value="formState.setting.user.type"
              placeholder="请选择投放人群"
              :options="launchCrowd.options()"
              :disabled="isRead"
            />
            <div>
              <a-textarea
                v-if="formState.setting.user.type == launchCrowd.appoint"
                v-model:value="formState.setting.user.range"
                style="width: 630px; max-width: 630px !important"
                class="m-t-20"
                placeholder="请输入指定用户手机号码，一行一个"
                :rows="4"
                :disabled="isRead"
              />
              <a-select
                v-if="formState.setting.user.type == launchCrowd.tags"
                v-model:value="formState.setting.user.user_tags_range"
                mode="multiple"
                show-search
                :filter-option="userTagFilterOption"
                allow-clear
                placeholder="请选择用户标签"
                class="m-t-20"
                style="width: 500px; max-width: 500px !important"
                :options="userTagOptions"
                :disabled="isRead"
              />
            </div>
          </a-form-item>
          <a-form-item label="弹窗跳转" class="required">
            <uc-jump v-model:value="formState.setting.jump" ignore-jumps="['contact', 'share']" />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, debounce } from 'lodash'
import { popupApi } from '../../api'
import { launchHz, launchCrowd } from '../../enums'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useUserTag } from '@/modules/member/useUserTag'
import { jumpType } from '@/enums/jump' 

const { userTagOptions, userTagFilterOption } = useUserTag()

const { statusList, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()

const { id, status } = useRoute().params

const isEdit = ref(false)

let isRead = ref(false)

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  popupApi
    .get(id, { relations: ['prizes'] })
    .then(async res => {
      res.poster_url = [res.poster_url]
      isRead.value = res.status == TIME_STATUS_ENDED
      isEdit.value = res.status === TIME_STATUS_NORMAL
      setFormState(res)
    })
    .finally(hideLoading)
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  poster_url: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  frequency: 'day_once',
  sort: undefined,
  setting: {
    user: {
      type: launchCrowd.all,
      range: ''
    },
    jump: {
      jump_type: jumpType.page,
      jump_link: undefined,
      appid: undefined
    }
  }
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  poster_url: { required: true, message: '请选择弹窗海报' },
  start_time: { required: true, message: '请选择投放开始时间' },
  title: { required: true, message: '请输入弹窗名称' },
  frequency: { required: true, message: '请选择投放频率' },
  sort: { required: true, message: '请输入弹窗排序' },
  setting: {
    validator(_, value) {
      const { jump, user } = value
      let tip

      // check settinf jump
      switch (jump.jump_type) {
        case jumpType.webview:
          if (!jump.jump_link) tip = '请输入网页URL'
          break
        case jumpType.page:
          if (!jump.jump_link) tip = '请输入链接URL'
          break
        case jumpType.miniprogram:
          if (!jump.appid) {
            tip = '请输入小程序appid'
            break
          }
          if (!jump.jump_link) {
            tip = '请输入链接URL'
            break
          }
      }
      if (tip) return Promise.reject(tip)

      // check setting user
      if (!user.type) return Promise.reject('请选择投放人群')
      if (user.type == launchCrowd.appoint && !user.range) return Promise.reject('请输入投放人群范围')

      return Promise.resolve()
    }
  }
})

const router = useRouter()

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) return

  // handle params
  const params = cloneDeep(formState.value)
  params.poster_url = params.poster_url[0]

  if (id) {
    await popupApi.update(id, params)
    message.success('编辑完成')
  } else {
    await popupApi.create(params)
    message.success('创建完成')
  }
  router.back()
}, 500)
</script>
<style scoped lang="less">
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}
</style>
