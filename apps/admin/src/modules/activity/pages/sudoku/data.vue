<template>
  <div class="signin-gift-statistics">
    <a-row :gutter="16" class="stats-data">
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            访问(人/次)
          </div>
          <div class="stats-data-number">
            {{ stats.uv }} / {{ $formatters.thousandSeparator(stats.pv, false, false) }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            邀请(人/次)
          </div>
          <div class="stats-data-number">
            {{ stats.invite }} / {{ $formatters.thousandSeparator(stats.ps, false, false) }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            抽奖(人/次)
          </div>
          <div class="stats-data-number">
            {{ stats.ud }} / {{ $formatters.thousandSeparator(stats.pd, false, false) }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="stats-data-wrap">
          <div class="stats-data-title">
            中奖(人/次)
          </div>
          <div class="stats-data-number">
            {{ stats.uw }} / {{ $formatters.thousandSeparator(stats.pw, false, false) }}
          </div>
        </div>
      </a-col>
    </a-row>
    <a-card class="m-t-14">
      <div id="container"></div>
    </a-card>
    <uc-layout-list title="抽奖统计">
      <template #extra>
        <div class="flex">
          <a-input-group compact>
            <a-select
              v-model:value="conditionKey"
              class="select"
              :options="userModeListOptions"
              @change="onConditionKeyChange"
            />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键词" @blur="handleSearch" />
          </a-input-group>
        </div>
      </template>
      <template #list>
        <a-table
          :data-source="data.items"
          row-key="user_id"
          :loading="loading"
          :pagination="stdPagination(data)"
          @change="setPage"
        >
          <a-table-column title="用户昵称" data-index="title" ellipsis width="200px">
            <template #default="{ record }">
              {{ record?.user?.nickname }}
            </template>
          </a-table-column>
          <a-table-column title="手机号码" data-index="title" width="150px" ellipsis>
            <template #default="{ record }">
              {{ $formatters.numberEncryption(record?.user?.phone_number) }}
            </template>
          </a-table-column>
          <a-table-column title="抽奖" data-index="draw" width="100px" />
          <a-table-column title="中奖" data-index="winner_count" width="100px" />
          <a-table-column title="奖项分布" data-index="distribution" />
          <a-table-column title="操作" width="70px">
            <template #default="{ record }">
              <a-button type="link" @click="showRecord(record)">
                记录
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>

    <a-modal
      v-model:visible="modalVisible"
      width="650px"
      :title="`${userInfo?.nickname} - 抽奖记录`"
      :footer="null"
      @cancel="handleCancel"
    >
      <a-table
        :data-source="dataRecord.items"
        row-key="id"
        :loading="loadingRecord"
        :pagination="stdPaginationSimple(dataRecord)"
        @change="setPageRecord"
      >
        <a-table-column title="抽奖时间" data-index="created_at" width="200px" />
        <a-table-column title="中奖内容" data-index="desc" ellipsis />
        <a-table-column title="兑奖状态" data-index="title" width="120px" ellipsis>
          <template #default="{ record }">
            <a-badge
              v-if="!record.prize"
              :status="prizeExchangeStatus.filterValue(prizeExchangeStatus.complete).colorType"
              :text="prizeExchangeStatus.filterValue(prizeExchangeStatus.complete).label"
            />
            <a-badge
              v-else
              :status="prizeExchangeStatus.filterValue(record?.prize?.status).colorType"
              :text="prizeExchangeStatus.filterValue(record?.prize?.status).label"
            />
          </template>
        </a-table-column>
      </a-table>
    </a-modal>
  </div>
</template>
<script setup>
import { nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { sudokuStatsRecordApi, sudokuStatsChartApi, sudokuStatsListApi } from '../../api'
import { UserModeList, prizeExchangeStatus } from '../../enums'
import { cloneDeep, uniqBy } from 'lodash'
import { Chart } from '@antv/g2'
import { DataView } from '@antv/data-set'

const userModeListOptions = UserModeList.options()
const router = useRouter()
const { id } = useRoute().params
if (!id) router.back()

const hideLoading = message.loading('正在加载数据...')
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  sudokuStatsListApi(id)
    .get({
      offset,
      limit,
      ...formState.value
    })
    .finally(hideLoading)
)

const setChart = () => {
  sudokuStatsChartApi(id)
    .get()
    .then(res => {
      stats.value = res.other

      res.char_data.forEach((a, aindex) => {
        res.char_data.forEach((b, bindex) => {
          if (aindex !== bindex && b.name === a.name) {
            a.value += b.value
          }
        })
      })
      let charList = uniqBy(res.char_data, 'name')
      var chartData = {
        name: '',
        children: charList.filter(({ name }) => name !== '谢谢参与')
      }

      const dv = new DataView()
      dv.source(chartData, { type: 'hierarchy' })
      dv.transform({
        field: 'value',
        type: 'hierarchy.treemap',
        tile: 'treemapResquarify',
        as: ['x', 'y']
      })
      var nodes = dv.getAllNodes()

      nodes.map(function (node) {
        node.name = node.data.name
        node.value = node.data.value
        return node
      })

      chart.source(nodes)
      chart.scale({
        value: {
          nice: false
        }
      })
      chart.axis(false)
      chart.legend(false)
      chart.tooltip({
        showTitle: false,
        itemTpl:
          '<li class="p-b-14" data-index={index}>' +
          '<span style="background-color:{color};" class="g2-tooltip-marker b-b"></span>' +
          '{name}<br/>' +
          '<br/><span style="padding-left: 16px">剩余数量：{count}</span><br/>' +
          '</li>'
      })
      chart
        .polygon()
        .position('x*y')
        .color('name')
        .tooltip('name*value', function (name, count) {
          return {
            name: name,
            count: count
          }
        })
        .style({
          lineWidth: 1,
          stroke: '#fff'
        })
        .label('name', {
          offset: 0,
          style: {
            textBaseline: 'middle',
            fill: '#fff',
            shadowBlur: 1,
            shadowColor: '#ccc',
          },
          formatter: function formatter(val) {
            if (val !== 'root') {
              return val
            }
          }
        })
      chart.render()
    })
}

let stats = ref({})
let chart = null

nextTick(() => {
  chart = new Chart({
    container: 'container',
    autoFit: true,
    forceFit: true,
    height: 360,
    padding: 0,
    animate: false
  })
  setPage()
  setChart()
})

const conditionBasic = Object.freeze({ nickname: undefined, phone_number: undefined })
const conditionKey = ref(userModeListOptions[0].value)
const conditionValue = ref()
const onConditionKeyChange = () => (conditionValue.value = undefined)
const { formState, onRestFormState, resetFormState } = useFormState(cloneDeep(conditionBasic))

const handleSearch = () => {
  Object.assign(formState.value, conditionBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

onRestFormState(() => setPage())

let userInfo = ref()
const {
  data: dataRecord,
  setPage: setPageRecord,
  loading: loadingRecord,
  refresh: refreshRecord
} = usePaginatorApiRequest(
  ({ offset, limit }) =>
    sudokuStatsRecordApi(userInfo.value.id, id).paginator({
      offset,
      limit,
      relations: ['prize']
    }),
  undefined,
  false
)

const { modalVisible, setModalVisible } = useModalVisible()

const showRecord = ({ user }) => {
  if (!user) return message.info('暂无该用户记录')
  userInfo.value = user
  setModalVisible(true)
  setPageRecord()
}

const handleCancel = () => {
  setModalVisible(false)
}
</script>
<style lang="less" scoped>
.select-width {
  width: 100px;
  :deep(.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
    width: 100px;
  }
}
:deep(.ant-input) {
  width: 300px;
}
.stats-data {
  &-wrap {
    padding: 24px;
    background: #fff;
  }
  &-title {
    color: #999;
    line-height: 1;
  }
  &-number {
    line-height: 1;
    font-size: 22px;
    color: #333;
    padding-top: 12px;
  }
}
</style>
