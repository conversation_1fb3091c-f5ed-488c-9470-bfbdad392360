<template>
  <div class="register-activities-statistics">
    <a-card title="活动趋势">
      <div id="container"></div>
    </a-card>
    <uc-layout-list title="入会有礼">
      <template #extra>
        <div class="flex">
          <a-input-group compact>
            <a-select v-model:value="conditionKey" class="select" :options="userModeListOptions" @change="onConditionKeyChange" />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键词" @blur="handleSearch" />
          </a-input-group>
        </div>
      </template>
      <template #list>
        <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
          <a-table-column title="用户昵称" data-index="title" ellipsis>
            <template #default="{ record }">
              {{ record?.user?.nickname }}
            </template>
          </a-table-column>
          <a-table-column title="手机号码" data-index="title" ellipsis>
            <template #default="{ record }">
              {{ $formatters.numberEncryption(record?.user?.phone_number) }}
            </template>
          </a-table-column>
          <a-table-column title="注册来源" data-index="aaaa" ellipsis />
          <a-table-column title="注册时间" data-index="created_at">
            <template #default="{ record }">
              {{ record.created_at || '0000-00-00 00:00:00' }}
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
  </div>
</template>
<script setup>
import { ref,nextTick, watch ,onUnmounted} from 'vue'
import { message } from 'ant-design-vue'
import { useRoute,useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from "@/composables/useTransformQuery";
import { registerActivitiesChart,registerActivitiesStats } from '../../api'
import { Chart } from '@antv/g2'
import { UserModeList } from '../../enums'
import { cloneDeep } from 'lodash'

const userModeListOptions = UserModeList.options()
const router = useRouter()
const { id } = useRoute().params
if(!id) router.back()

onUnmounted(()=>{
 if(chart) chart.destroy()
})

const hideLoading = message.loading('正在加载数据...')
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  registerActivitiesStats(id).get({
    relations:['user'],
    relation_filters:{
      user:useTransformQuery({
        nickname:formState.value.nickname,
        phone_number:formState.value.phone_number,
      }, {
        nickname: 'like',
        phone_number: '=',
      }),
    },
    offset,
    limit,
  }).finally(hideLoading)
)

const setChart = () =>{
  registerActivitiesChart(id).get()
  .then(res=>{
     chart.data(res)

    chart.scale({
      date: {
        range: [0, 1]
      },
      num: {
        min: 0,
        nice: true
      }
    })

    chart.tooltip({
      title: 'date',
      shared: true
    })
    chart.scale('num', {
      alias: '入会数量'
    })
    chart.line().position('date*num').shape('smooth')
    chart.point().position('date*num')
    chart.render()
  })
}

nextTick(() => {
  chart = new Chart({
    container: 'container',
    autoFit: true,
    height: 360
  })
  setPage()
  setChart()
})

let chart = null;

const conditionBasic = Object.freeze({nickname:undefined,phone_number:undefined})
const conditionKey = ref(userModeListOptions[0].value)
const conditionValue = ref()
const { formState, onRestFormState, resetFormState } = useFormState(cloneDeep(conditionBasic))

const onConditionKeyChange = () => conditionValue.value = undefined

const handleSearch = () =>{
  Object.assign(formState.value,conditionBasic,{
    [conditionKey.value]:conditionValue.value,
  })
  setPage()
}

onRestFormState(() => setPage())

</script>
<style lang="less" scoped>
.select-width {
  width: 100px;
  :deep(.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
    width: 100px;
  }
}
:deep(.ant-input) {
  width: 300px;
}
</style>
