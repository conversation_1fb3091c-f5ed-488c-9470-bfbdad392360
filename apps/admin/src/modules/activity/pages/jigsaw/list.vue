<template>
  <uc-layout-list title="拼图有礼">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="活动状态" allow-clear :options="statusList" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增拼图有礼
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称" data-index="title" ellipsis />
        <a-table-column title="UV/PV" width="150px" align="right">
          <template #default="{ record }">
            {{ record.uv }}/{{ record.pv }}
          </template>
        </a-table-column>
        <a-table-column title="活动时间" width="360px">
          <template #default="{ record }">
            {{ record.start_time }} ~ {{ $formatters.transformActivityEndTime(record.end_time) }}
          </template>
        </a-table-column>
        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" class="link" @click="copyLink(linkPath + record.id)">
              链接
            </a-button>
            <a-button
              v-if="record.status == TIME_STATUS_NORMAL || record.status == TIME_STATUS_ENDED"
              type="link"
              class="link"
              @click="onData(record)"
            >
              数据
            </a-button>
            <a-button
              v-if="record.status == TIME_STATUS_NORMAL || record.status == TIME_STATUS_NOSTART"
              type="link"
              @click="onEdit(record)"
            >
              编辑
            </a-button>
            <a-popconfirm
              v-if="record.status == TIME_STATUS_NOSTART"
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
            <a-button v-if="record.status == TIME_STATUS_ENDED" type="link" class="link" @click="onEdit(record)">
              查看
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { jigsawApi } from '../../api'

const linkPath = Object.freeze(`/jigsaw/pages/standard/activity/index?id=`)

const { statusFilter, statusList, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  jigsawApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  status: undefined
})

onRestFormState(() => setPage())

const router = useRouter()

const onAdd = () => router.push({ name: 'jigsaw-add' })
const onEdit = ({ id }) => router.push({ name: 'jigsaw-edit', params: { id } })
const onData = ({ id }) => router.push({ name: 'jigsaw-data', params: { id } })

const handleDelete = async ({ id }) => {
  await jigsawApi.delete(id)
  message.success('删除完成')
  setPage()
}
</script>
