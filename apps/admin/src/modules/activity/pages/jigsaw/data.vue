<template>
  <a-row :gutter="18">
    <a-col :span="6">
      <div class="bgc-white p-24">
        <div class="color-45 m-b-6">
          访问(人/次)
        </div>
        <div class="color-85 fs-22">
          {{ stats.uv }} / {{ $formatters.thousandSeparator(stats.pv, false, false) }}
        </div>
      </div>
    </a-col>
    <a-col :span="6">
      <div class="bgc-white p-24">
        <div class="color-45 m-b-6">
          邀请(人/次)
        </div>
        <div class="color-85 fs-22">
          {{ stats.ui }} / {{ $formatters.thousandSeparator(stats.pi, false, false) }}
        </div>
      </div>
    </a-col>
    <a-col :span="6">
      <div class="bgc-white p-24">
        <div class="color-45 m-b-6">
          参与(新/老)
        </div>
        <div class="color-85 fs-22">
          {{ stats.jn }} / {{ $formatters.thousandSeparator(stats.jo, false, false) }}
        </div>
      </div>
    </a-col>
    <a-col :span="6">
      <div class="bgc-white p-24">
        <div class="color-45 m-b-6">
          中奖(人/次)
        </div>
        <div class="color-85 fs-22">
          {{ stats.uw }} / {{ $formatters.thousandSeparator(stats.pw, false, false) }}
        </div>
      </div>
    </a-col>
  </a-row>
  <a-card class="m-t-14">
    <div id="container"></div>
  </a-card>
  <uc-layout-list title="抽奖统计">
    <template #extra>
      <div class="flex">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" class="select" :options="userModeListOptions" @change="onConditionKeyChange" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键词" class="w-300" @blur="handleSearch" />
        </a-input-group>
      </div>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="user_id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="用户昵称" data-index="title" ellipsis width="200px">
          <template #default="{ record }">
            {{ record?.user?.nickname }}
          </template>
        </a-table-column>
        <a-table-column title="手机号码" data-index="title" width="150px" ellipsis>
          <template #default="{ record }">
            {{ $formatters.numberEncryption(record?.user?.phone_number) }}
          </template>
        </a-table-column>
        <a-table-column title="抽奖" data-index="draw_count" width="100px" />
        <a-table-column title="中奖" data-index="winner_count" width="100px" />
        <a-table-column title="奖项分布" data-index="distribution" />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-button type="link" @click="showRecord(record)">
              记录
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal v-model:visible="modalVisible" width="650px" :title="`${userInfo?.nickname} - 抽奖记录`" :footer="null" @cancel="handleCancel">
    <a-table :data-source="dataRecord.items" row-key="id" :loading="loadingRecord" :pagination="stdPaginationSimple(dataRecord)" @change="setPageRecord">
      <a-table-column title="抽奖时间" data-index="created_at" width="200px" />
      <a-table-column title="中奖内容" data-index="desc" ellipsis />
      <a-table-column title="兑奖状态" data-index="title" width="120px" ellipsis>
        <template #default="{ record }">
          <a-badge v-if="!record.prize" :status="prizeExchangeStatus.filterValue(prizeExchangeStatus.complete).colorType" :text="prizeExchangeStatus.filterValue(prizeExchangeStatus.complete).label" />
          <a-badge v-else :status="prizeExchangeStatus.filterValue(record?.prize?.status).colorType" :text="prizeExchangeStatus.filterValue(record?.prize?.status).label" />
        </template>
      </a-table-column>
    </a-table>
  </a-modal>
</template>
<script setup>
import { nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { jigsawStatisticsApi, jigsawRecordsApi, jigsawUserRecordsApi } from '../../api'
import { UserModeList, prizeExchangeStatus } from '../../enums'
import { cloneDeep, uniqBy } from 'lodash'
import { Chart } from '@antv/g2'
import { DataView } from '@antv/data-set'
import { useTransformQuery } from '@/composables/useTransformQuery'

const userModeListOptions = UserModeList.options()
const router = useRouter()
const { id } = useRoute().params
if (!id) router.back()

const hideLoading = message.loading('正在加载数据...')
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  jigsawRecordsApi(id)
    .paginator({
      offset,
      limit,
      nickname: formState.value.nickname ? useTransformQuery({ nickname: formState.value.nickname }, { nickname: 'like' }).nickname : '',
      phone_number: formState.value.phone_number,
    })
    .finally(hideLoading)
)
const setChart = () => {
  jigsawStatisticsApi(id)
    .list()
    .then(res => {
      stats.value = res.other

      res.char_data.forEach((a, aindex) => {
        res.char_data.forEach((b, bindex) => {
          if (aindex !== bindex && b.name === a.name) {
            a.value += b.value
          }
        })
      })
      const charList = uniqBy(res.char_data, 'name')
      const chartData = {
        name: '',
        children: charList.filter(({ name }) => name !== '谢谢参与')
      }

      const dv = new DataView()
      dv.source(chartData, { type: 'hierarchy' })
      dv.transform({
        field: 'value',
        type: 'hierarchy.treemap',
        tile: 'treemapResquarify',
        as: ['x', 'y']
      })
      var nodes = dv.getAllNodes()

      nodes.map(function (node) {
        node.name = node.data.name
        node.value = node.data.value
        return node
      })

      chart.source(nodes)
      chart.scale({
        value: {
          nice: false
        }
      })
      chart.axis(false)
      chart.legend(false)
      chart.tooltip({
        showTitle: false,
        itemTpl:
          '<li class="p-b-14" data-index={index}>' +
          '<span style="background-color:{color};" class="g2-tooltip-marker b-b"></span>' +
          '{name}<br/>' +
          '<br/><span style="padding-left: 16px">剩余数量：{count}</span><br/>' +
          '</li>'
      })
      chart
        .polygon()
        .position('x*y')
        .color('name')
        .tooltip('name*value', function (name, count) {
          return {
            name: name,
            count: count
          }
        })
        .style({
          lineWidth: 1,
          stroke: '#fff'
        })
        .label('name', {
          offset: 0,
          style: {
            textBaseline: 'middle',
            fill: '#fff',
            shadowBlur: 1,
            shadowColor: '#ccc',
          },
          formatter: function formatter(val) {
            if (val !== 'root') {
              return val
            }
          }
        })
      chart.render()
    })
}

let stats = ref({})
let chart = null

nextTick(() => {
  chart = new Chart({
    container: 'container',
    autoFit: true,
    forceFit: true,
    height: 360,
    padding: 0,
    animate: false
  })
  setPage()
  setChart()
})

const conditionBasic = Object.freeze({ nickname: undefined, phone_number: undefined })
const conditionKey = ref(userModeListOptions[0].value)
const conditionValue = ref()
const onConditionKeyChange = () => (conditionValue.value = undefined)
const { formState, onRestFormState, resetFormState } = useFormState(cloneDeep(conditionBasic))

const handleSearch = () => {
  Object.assign(formState.value, conditionBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

onRestFormState(() => setPage())

let userInfo = ref({})
const {
  data: dataRecord,
  setPage: setPageRecord,
  loading: loadingRecord,
  refresh: refreshRecord
} = usePaginatorApiRequest(
  ({ offset, limit }) => {
    return jigsawUserRecordsApi({ userId: userInfo.value.id, id }).paginator({
      offset,
      limit,
      relations: ['prize']
    })
  },
  undefined,
  false
)

const { modalVisible, setModalVisible } = useModalVisible()

const showRecord = ({ user }) => {
  if (!user) return message.info('暂无该用户记录')
  userInfo.value = user
  setModalVisible(true)
  setPageRecord()
}

const handleCancel = () => {
  setModalVisible(false)
}
</script>
<style lang="less" scoped>
.stats-data {
  &-wrap {
    padding: 24px;
    background: #fff;
  }
  &-title {
    color: #999;
    line-height: 1;
  }
  &-number {
    line-height: 1;
    font-size: 22px;
    color: #333;
    padding-top: 12px;
  }
}
</style>
