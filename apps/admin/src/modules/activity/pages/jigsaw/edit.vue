<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="活动开始时间"
              :disabled="isNormal || isRead"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="活动结束时间"
              :disabled="isRead"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              placeholder="请输入活动标签，单个标签不超过10字"
              maxlength="10"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="碎片数量" class="required">
            <a-select
              v-model:value="formState.debris_count"
              :disabled="isRead"
              :options="fragmentList.options()"
              @change="getFragment"
            />
          </a-form-item>
          <a-form-item label="助力资格" class="required">
            <a-space :size="10">
              <a-select
                v-model:value="formState.user_type"
                :disabled="isRead || isNormal"
                :options="helpList.options()"
                class="w-150"
                @change="onChangeHelpType"
              />
              <a-input-number
                v-model:value="formState.help_limit_value"
                placeholder="请输入助力次数"
                :min="1"
                class="w-340"
                :formatter="$formatters.number"
                :disabled="formState.user_type === helpList.new_user || isNormal || isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="拼图碎片" class="required">
            <template v-for="(item, index) in formState.settings" :key="index">
              <a-space class="activity-rule-item m-b-10">
                <a-input :value="jigsawFragment.filter(item.sort).label" class="w-100" disabled />
                <a-input-number
                  v-if="formState.settings.length - 1 === index"
                  v-model:value="item.stock"
                  class="w-390"
                  :min="0"
                  placeholder="发放数量"
                  :disabled="isRead"
                />
                <a-input-number
                  v-else
                  v-model:value="item.probability"
                  class="w-390"
                  :min="0"
                  placeholder="点亮概率"
                  :disabled="isRead"
                />
                <a-space>
                  <uc-upload
                    v-model:list="item.photo_url"
                    class="upload-small-cover"
                    size="small"
                    title="碎片图片"
                    :max-length="1"
                    upload-text=" "
                    :disabled="isRead"
                  />
                  <uc-upload
                    v-model:list="item.pop_url"
                    class="upload-small-cover"
                    size="small"
                    title="碎片弹窗图片"
                    :max-length="1"
                    upload-text=" "
                    :disabled="isRead"
                  />
                </a-space>
              </a-space>
            </template>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="拼图奖励">
          <a-form-item label="兑奖期限" class="required">
            <a-input-number
              v-model:value="formState.exchange_date_limit"
              :min="0"
              :formatter="$formatters.naturalNumber"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            class="form-items-margin-bottom"
            :label="ruleIndex ? ' ' : '奖品设置'"
            :class="{ required: !ruleIndex }"
            :colon="!ruleIndex"
          >
            <template v-for="(item, index) in rule.award_settings" :key="index">
              <a-space class="activity-rule-item m-b-10" wrap>
                <a-select
                  v-model:value="rule.grade"
                  class="w-100"
                  placeholder="请选择"
                  :options="jigsawPrizes.options()"
                  :disabled="isRead"
                  @change="onChangeGiftGrade(rule, item)"
                />
                <a-select
                  v-model:value="item.type"
                  class="w-100"
                  :placeholder="rule.isZero ? '谢谢参与' : '奖品类型'"
                  :options="turntableOptions"
                  :disabled="rule.isZero || isEdit || isRead"
                  @change="onChangeGiftType(item)"
                />
                <a-input-number
                  v-if="item.type === giftType.credit"
                  v-model:value="item.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(item.type)"
                  :disabled="rule.isZero || isEdit || isRead"
                />
                <a-input
                  v-else-if="rule.isZero"
                  class="w-300"
                  placeholder="谢谢参与"
                  :disabled="rule.isZero || isEdit || isRead"
                />
                <a-select
                  v-else
                  v-model:value="item.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(item.type)"
                  :options="item.options"
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="rule.isZero || isEdit || isRead"
                  @focus="handleShowGifts(item)"
                  @search="handleSearchGifts($event, item)"
                />
                <a-input-number
                  v-model:value="item.stock"
                  min="0"
                  class="w-100"
                  placeholder="活动数量"
                  :disabled="rule.isZero || isRead"
                />
                <a-input-number
                  v-model:value="rule.probability"
                  class="w-100"
                  :min="0"
                  placeholder="中奖概率"
                  :disabled="isRead"
                />
                <a-space>
                  <uc-upload
                    v-model:list="rule.prize_url"
                    class="upload-small-cover"
                    size="small"
                    title="奖品图片"
                    :max-length="1"
                    upload-text=" "
                    :disabled="isRead"
                  />
                  <uc-upload
                    v-model:list="rule.pop_url"
                    class="upload-small-cover"
                    size="small"
                    title="中奖弹窗图片"
                    :max-length="1"
                    upload-text=" "
                    :disabled="isRead"
                  />
                </a-space>
              </a-space>
            </template>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="页面配置" class="h-fill">
          <a-form-item label="皮肤模式" class="required">
            <a-select
              v-model:value="formState.page_setting.skin_mode"
              class="m-r-10 w-310"
              placeholder="请选择皮肤"
              allow-clear
              :disabled="isRead"
              :options="skinMode.option()"
            />
            <a-button class="p-lr-0 relative" type="link">
              预览效果图
              <div class="w-fill h-fill hide absolute top-0">
                <a-image class="opacity-0" :src="assets.jigsawPreviewUrl" />
              </div>
            </a-button>
          </a-form-item>
          <a-form-item label="颜色配置" class="required">
            <a-space :size="10" wrap>
              <a-input
                v-model:value.trim="formState.page_setting.color.subject"
                class="w-160"
                placeholder="主题色，如#000000"
                :disabled="isRead"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.sub"
                class="w-160"
                placeholder="辅助色，如#000000"
                :disabled="isRead"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.bg"
                class="w-160"
                placeholder="背景色，如#000000"
                :disabled="isRead"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.title"
                class="w-160"
                placeholder="标题色，如#000000"
                :disabled="isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.page_setting.page.activity_bg"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动背景"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.activity_text"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动文案"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.my_jigsaw"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="我的拼图"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.friend_jigsaw"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="好友拼图"
                :disabled="isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="弹窗配置" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.page_setting.popup.help_limit_err"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="助力超限"
                :disabled="isRead"
              />
              <!--  <uc-upload
            v-model:list="formState.page_setting.popup.prepare"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="未开始"
            :disabled="isRead"
          />
          <uc-upload
            v-model:list="formState.page_setting.popup.end"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="已结束"
            :disabled="isRead"
          /> -->
            </a-space>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="分享设置" class="h-fill">
          <a-form-item label="分享海报" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.share_setting.session"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="会话海报"
                :disabled="isRead"
              />
              <uc-upload
                v-model:list="formState.share_setting.local"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="本地海报"
                :disabled="isRead"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="分享文案" class="required">
            <a-input
              v-model:value.trim="formState.share_setting.share_text"
              class="input-width"
              placeholder="请输入分享文案，不超过20字"
              :maxlength="20"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text
          v-model="formState.desc"
          placeholder="请输入活动说明（不超过500字）"
          :disabled="isRead"
          :height="300"
          :max-length="5"
        />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { colorVerify } from '@/utils/index'
import { jigsawApi } from '../../api'
import { skinMode, jigsawPrizes, giftType, awardBizType, helpList, fragmentList, jigsawFragment } from '../../enums'
import { useUserLevelOptions } from '../../useUserLevel'
import { usePrizeAwardUpdate } from '../../usePrizeAward'
import { useJigsawEdit } from '../../useTransformData'
import assets from '../../assets.config'

const { transformToShow, transformToRequest } = useJigsawEdit()
const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()

const { levelOptions } = useUserLevelOptions({ is_enable: 1 })
const { statusList, TIME_STATUS_NOSTART, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()
const turntableOptions = giftType.options()
const fragmentOptions = jigsawFragment.options()

const router = useRouter()
const { id } = useRoute().params

let isNostart = ref(false)
let isNormal = ref(false)
let isEdit = ref(false)
let isRead = ref(false)

const initFragmentItem = sort => ({ probability: '', sort, photo_url: [], pop_url: [], stock: '' })
const getFragment = num => {
  if (!num) return
  const list = Array.from({ length: num }, (v, i) => initFragmentItem(fragmentOptions[i].value))
  list.push(initFragmentItem(fragmentOptions[fragmentOptions.length - 1].value))
  formState.value.settings = list
}

nextTick(() => {
  if (id) {
    const hideLoading = message.loading('正在加载数据...')
    isEdit.value = true

    jigsawApi
      .get(id, { relations: ['rules', 'settings'] })
      .then(async res => {
        switch (res.status) {
          case TIME_STATUS_NOSTART:
            isNostart.value = true
            break
          case TIME_STATUS_NORMAL:
            isNormal.value = true
            break
          case TIME_STATUS_ENDED:
            isRead.value = true
            break
        }
        getFragment(res.debris_count)
        const form = await transformToShow(res)
        setFormState(form)
      })
      .finally(hideLoading)
  } else {
    getFragment(fragmentList.four)
  }
})

const giftDefault = {
  award_settings: [onPrizeDefault()],
  isZero: true,
  probability: undefined,
  grade: jigsawPrizes.zero,
  prize_url: [],
  pop_url: []
}

const ruleDefault = Array.from({ length: 8 }, () => cloneDeep(giftDefault))

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  debris_count: fragmentList.four,
  user_type: helpList.new_user,
  help_limit_value: 1,
  settings: [],
  exchange_date_limit: '',
  rules: ruleDefault,
  page_setting: {
    skin_mode: skinMode.default,
    color: {
      subject: '',
      sub: '',
      bg: '',
      title: ''
    },
    page: {
      activity_bg: [],
      activity_text: [],
      my_jigsaw: [],
      friend_jigsaw: []
    },
    popup: {
      help_limit_err: []
      // prepare: [],
      // end: []
    }
  },
  share_setting: {
    session: [],
    local: [],
    share_text: ''
  },
  desc: ''
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  help_limit_value: {
    validator(_, value) {
      if (formState.value.user_type === helpList.all && !value) return Promise.reject('请输入助力次数')
      return Promise.resolve(true)
    }
  },
  settings: {
    validator(_, value) {
      let tip = ''
      let sum = 0
      for (const i in value) {
        const { probability, sort, photo_url, pop_url, stock } = value[+i]
        const { label } = jigsawFragment.filter(sort)
        if (!photo_url.length) {
          tip = `请上传${label}的碎片图`
        } else if (!pop_url.length) {
          tip = `请上传${label}的弹窗图`
        }
        if (value.length - 1 === +i) {
          if (!stock) {
            tip = `请输入${label}的发放数量`
          }
        } else {
          sum += +probability
          if (!probability) {
            tip = `请输入${label}的点亮概率`
          }
        }

        if (tip) {
          return Promise.reject(tip)
        }
      }
      if (sum !== 100) return Promise.reject('所以碎片概率未等于100')
      return Promise.resolve(true)
    }
  },
  exchange_date_limit: { required: true, message: '请输入活动结束后领奖截止天数' },
  rules: {
    validator(_, value) {
      let tip
      let probability_sum = 0
      for (const { grade, probability, prize_url, pop_url, award_settings } of value) {
        for (const { type, option_id, quantity, stock } of award_settings) {
          if (grade != jigsawPrizes.zero) {
            if (!type) {
              tip = '请选择奖项礼品类型'
              break
            }
            if (type === giftType.credit) {
              if (!quantity) {
                tip = '请输入积分值'
                break
              }
            } else {
              if (!option_id) {
                tip = '请选择奖项礼品'
                break
              }
            }
            if (!stock && stock !== 0) {
              tip = '请输入奖项活动数量'
              break
            }
          }
        }
        probability_sum += probability
        if (!probability && probability !== 0) {
          tip = '请输入中奖概率'
          break
        }
        if (!prize_url.length) {
          tip = '请选择奖项奖品图片'
          break
        }
        if (!pop_url.length) {
          tip = '请选择奖项弹窗图片'
          break
        }
        if (tip) break
      }

      if (tip) return Promise.reject(tip)
      if (probability_sum < 100) return Promise.reject('所有奖项中奖概率未达到100%')
      if (probability_sum > 100) return Promise.reject('所有奖项中奖概率超过100%')
      return Promise.resolve()
    }
  },
  page_setting: {
    validator(_, value) {
      const { skin_mode, page, color, popup } = value
      if (!skin_mode) return Promise.reject('请选择皮肤模式')
      if (!colorVerify(color.subject)) return Promise.reject('请输入主题色，以#开头+6位数字字母组合')
      if (!colorVerify(color.sub)) return Promise.reject('请输入辅助色，以#开头+6位数字字母组合')
      if (!colorVerify(color.bg)) return Promise.reject('请输入背景色，以#开头+6位数字字母组合')
      if (!colorVerify(color.title)) return Promise.reject('请输入标题色，以#开头+6位数字字母组合')
      if (!page.activity_bg.length) return Promise.reject('请上传活动背景图片')
      if (!page.activity_text.length) return Promise.reject('请上传活动文案图片')
      if (!page.my_jigsaw.length) return Promise.reject('请上传我的拼图图片')
      if (!page.friend_jigsaw.length) return Promise.reject('请上传好友拼图图片')
      if (!popup.help_limit_err.length) return Promise.reject('请上传助力超限图片')
      // if (!popup.prepare.length) return Promise.reject('请上传未开始图片')
      // if (!popup.end.length) return Promise.reject('请上传已结束图片')
      return Promise.resolve()
    }
  },
  share_setting: {
    validator(_, value) {
      const { session, local, share_text } = value
      if (!session.length) return Promise.reject('请选择会话海报')
      if (!local.length) return Promise.reject('请选择本地海报')
      if (!share_text) return Promise.reject('请输入分享文案')
      return Promise.resolve()
    }
  },
  desc: { required: true, message: '请输入活动说明' }
})

const onChangeHelpType = () => {
  formState.value.help_limit_value = formState.value.user_type === helpList.new_user ? 1 : ''
}
const onChangeGiftGrade = async (record, item) => {
  const { grade, type } = record

  if (grade === jigsawPrizes.zero) {
    Object.assign(record, {
      isZero: true,
      probability: undefined,
      award_settings: [
        onPrizeDefault({
          type: undefined,
          setting: { prize_url: [], pop_url: [] }
        })
      ]
    })
  } else {
    record.isZero = false
    item.type = type || giftType.coupon
    onChangeGiftType(item)
  }
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = transformToRequest(formState.value)
  id ? await jigsawApi.replace(id, params) : await jigsawApi.create(params)
  message.success('操作完成')
  router.back()
}
</script>

<style scoped lang="less">
.upload-small-cover {
  :deep(.ant-upload-picture-card-wrapper) {
    width: 32px !important;
    height: 32px !important;
  }

  :deep(.ant-upload) {
    height: 32px !important;
    margin: 0 !important;
  }
}

.form-items-margin-bottom {
  margin-bottom: 0 !important;
}
</style>
