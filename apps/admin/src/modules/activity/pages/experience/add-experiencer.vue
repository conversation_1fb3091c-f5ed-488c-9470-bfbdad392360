<template>
  <div class="index">
    <c-load v-model:show="load" />
    <uc-layout-form :is-save="!submitDisabled" @submit="confirmCommit">
      <uc-row>
        <uc-col>
          <a-card title="基本信息" class="h-fill">
            <a-form-item label="活动封面" name="cover_url">
              <div class="img-wrap">
                <uc-upload v-model:list="formState.imgList" upload-text=" " :disabled="disabled" :max-length="1" />
              </div>
            </a-form-item>
            <a-form-item label="活动时间">
              <a-date-picker
                v-model:value="disaDate.start_time"
                :disabled="disabled2"
                format="YYYY-MM-DD HH:mm:ss"
                class="w-240"
                show-time
                placeholder="活动开始时间"
                :disabled-date="disabledStartTime"
                :disabled-time="disabledStartTime"
                :size="size"
              />
              <span class="separator">~</span>
              <a-date-picker
                v-model:value="disaDate.end_time"
                :disabled="disabled"
                class="w-240"
                format="YYYY-MM-DD HH:mm:ss"
                show-time
                placeholder="活动结束时间"
                :disabled-date="disabledEndTime"
                :size="size"
              />
            </a-form-item>

            <a-form-item label="活动名称" name="reportName">
              <a-input
                v-model:value="formState.reportName"
                :disabled="disabled"
                :size="size"
                autocomplete="off"
                placeholder="请输入活动名称，不超过100字"
                :maxlength="100"
              />
            </a-form-item>

            <a-form-item label="活动标签" class="non-required">
              <a-input
                v-model:value="formState.reportTag"
                :disabled="disabled"
                :size="size"
                :maxlength="10"
                placeholder="请输入活动标签，不超过10字"
              />
            </a-form-item>
          </a-card>
        </uc-col>
        <uc-col>
          <a-card title="试用规则">
            <a-form-item label="试用问卷" name="questionnaire">
              <a-select
                v-model:value="formState.questionnaire"
                :disabled="disabled2"
                :size="size"
                show-search
                placeholder="请选择试用问卷"
                :options="questionnaire"
                :filter-option="filterOption"
                @search="searchQuestionnaires"
              >
                <template v-if="loadQuestionnaires" #notFoundContent>
                  <div class="load-wrap">
                    <a-spin size="small" />
                  </div>
                </template>
              </a-select>
            </a-form-item>

            <a-form-item label="试用产品" name="sku">
              <a-select
                v-model:value="formState.goods[0].sku"
                :disabled="disabled2"
                show-search
                placeholder="请输入试用产品SKU"
                :size="size"
                :options="goodsList"
                @search="searchGoods"
              >
                <template v-if="loadGoods" #notFoundContent>
                  <div class="load-wrap">
                    <a-spin size="small" />
                  </div>
                </template>
              </a-select>
            </a-form-item>

            <a-form-item label="试用数量" name="setting_stock">
              <a-input-number
                v-model:value="formState.goods[0].setting_stock"
                :disabled="disabled2"
                style="width: 100%"
                :size="size"
                placeholder="请输入试用数量"
                :min="0"
                precision="0"
              />
            </a-form-item>

            <a-form-item label="申请消耗" name="consume">
              <a-input-number
                v-model:value="formState.consume"
                :disabled="disabled2"
                style="width: 100%"
                :size="size"
                :min="0"
                placeholder="请输入申请试用消耗积分值"
              />
            </a-form-item>

            <a-form-item label="开奖时间" name="publish_time">
              <a-date-picker
                v-model:value="formState.publish_time"
                :disabled="disabled"
                format="YYYY-MM-DD HH:mm:ss"
                show-time
                :size="size"
                :disabled-date="disabledStartTime2"
                class="w-500"
                placeholder="请选择试用名单公布时间"
              />
            </a-form-item>

            <a-form-item label="试用期限" name="day_limit">
              <a-input-number
                v-model:value="formState.day_limit"
                :disabled="disabled"
                style="width: 100%"
                :size="size"
                :min="1"
                placeholder="请输入名单公布后领取试用截止天数"
              />
            </a-form-item>
          </a-card>
        </uc-col>
      </uc-row>
      <!-- <a-card title="试用规则"> -->
      <!-- 富文本 -->
      <div class="wrap wrap3">
        <!-- <p v-if="disabled" class="p" v-html="formState.desc"></p> -->
        <uc-rich-text
          v-model="formState.desc"
          :disabled="disabled"
          :height="300"
          :is-validator="true"
          placeholder="请输入活动说明~"
        />
      </div>
      <!-- </a-card> -->
    </uc-layout-form>
  </div>
</template>

<script setup>
import cLoad from '@/components/uc-loading.vue'
import { defineComponent, ref, reactive, watch } from 'vue'
import { apiQuestionnaires, apiGoods, trialsApi } from '../../api'
import { formatDate } from '@/utils/date'
import { message, Col, Row } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { useTransformQuery } from '@/composables/useTransformQuery'

import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'

const router = useRouter()
const { id, status, typePage } = useRoute().params
defineComponent({
  cLoad,
  'a-col': Col
})
//素材参数
const submitDisabled = ref(false)
const imgList = ref([])
const disabled = ref(false)
//活动开始时间 试用问卷 试用产品 试用数量 申请消耗 开始后不可修改
const disabled2 = ref(false)
const formRef = ref()
const formRef2 = ref()
const questionnaire = ref([])
const cover_url_err = ref(false)
const goodsList = ref([])
const load = ref(false)
const loadGoods = ref(false) //商品加载
const loadQuestionnaires = ref(false) //问卷加载
const formState = reactive({
  startDate: null,
  endDate: null,
  reportName: null,
  reportTag: null,
  sku: null,
  publish_time: null,
  desc: null,
  tag: null,
  imgList: [],
  stock: null,
  consume: null,
  questionnaire: null,
  goods: [{ sku: undefined, setting_stock: undefined, goods_id: undefined }]
})
const size = ref('middle')

// 活动开始时间-结束限制
const { formState: disaDate } = useFormState({
  start_time: undefined,
  end_time: undefined
})

// 同步双方数据
watch(
  () => [disaDate.value.start_time, disaDate.value.end_time],
  val => {
    formState.startDate = val[0]
    formState.endDate = val[1]

    //校验开始时间数据
    if (formState.publish_time) {
      const endTime = new Date(val[1].toString()).getTime()
      const publishTime = new Date(formState.publish_time.toString()).getTime()
      if (publishTime < endTime) {
        formState.publish_time = null
      }
    }
  }
)
const { disabledStartTime, disabledEndTime } = useDisabledDate(disaDate)

// 活动开奖时间限制
const { formState: publicDate } = useFormState({
  start_time: undefined,
  end_time: undefined
})
const disabledStartTime2 = cur => {
  const curDate = new Date(cur.toString())

  if (formState.endDate) {
    const date = new Date(formState.endDate.toString())
    return date > curDate
  }
  return false
}

// 查看时任何参数不能更改
if (typePage == 'query') {
  disabled.value = true
  disabled2.value = true
}

// 只有进行中和未开始有编辑按钮，开始后指定参数不能更改
if (status == 'normal') disabled2.value = true

if (id) {
  load.value = true
  trialsApi
    .get(id, { filters: { id }, relations: ['questionnaire', 'goods'] })
    .then(res => {
      if (res.status === 'ended') {
        submitDisabled.value = true
        disabled.value = true
        disabled2.value = true
      }
      const data = res
      formState.imgList = [data.cover_url]
      disaDate.value.start_time = res.start_time
      disaDate.value.end_time = formState.endDate = data.end_time
      formState.reportName = data.title
      formState.reportTag = data.tag
      formState.goods = data.goods //.goods[0]
      formState.publish_time = data.publish_time
      formState.desc = data.desc
      formState.day_limit = data.day_limit
      formState.consume = data.consume
      formState.questionnaire = data.questionnaire?.questionnaire_id
      getGoods()
      getQuestionnaires()
    })
    .finally(() => (load.value = false))
}

//获取问卷
const getQuestionnaires = name => {
  const filters = useTransformQuery(
    {
      stock: '>0',
      title: name
    },
    {
      title: 'like'
    }
  )
  loadQuestionnaires.value = true
  apiQuestionnaires
    .get('', { filters, limit: 20 })
    .then(res => {
      if (res.items) {
        questionnaire.value = res.items.map(m => {
          m.value = m.id
          m.label = m.title
          return m
        })
      }
    })
    .finally(() => {
      loadQuestionnaires.value = false
    })
}
getQuestionnaires()

//获取试用产品
const getGoods = sku => {
  loadGoods.value = true
  const filters = useTransformQuery(
    {
      stock: '>0',
      sku
    },
    {
      sku: 'like'
    }
  )
  apiGoods
    .get('', { relations: ['goods'], relation_filters: { goods: { on_sale: 1 } }, filters, limit: 20 })
    .then(res => {
      if (res.items) {
        // console.log('res.items', res.items)
        goodsList.value = res.items.map(m => {
          m.label = m.sku + '-' + m.goods.title
          m.value = m.sku
          return m
        })
      }
    })
    .finally(() => {
      loadGoods.value = false
    })
}
getGoods()

//搜索商品
let trime2
const searchGoods = sku => {
  if (trime2) {
    clearTimeout(trime2)
  }
  trime2 = setTimeout(() => {
    getGoods(sku)
  }, 300)
}

//搜索问卷
let trime1
const searchQuestionnaires = name => {
  if (trime1) {
    clearTimeout(trime1)
  }
  trime1 = setTimeout(() => {
    getQuestionnaires(name)
  }, 300)
}
//问卷过滤
const filterOption = (input, option) => {
  return option.title.toLowerCase().indexOf(input.toLowerCase()) >= 0
}

const confirmCommit = async () => {
  if (checkParam()) {
    return
  }

  let currentProduct = goodsList.value.find(i => i.sku === formState.goods[0].sku) || {}
  const param = {
    cover_url: formState.imgList[0], //封面图片
    start_time: formatDate(formState.startDate), //活动开始时间
    end_time: formatDate(formState.endDate), //活动结束时间
    title: formState.reportName, //活动名称
    tag: formState.reportTag, //活动标签
    goods: [
      {
        ...formState.goods[0],
        goods_id: currentProduct.goods.id
      }
    ],
    consume: formState.consume, //申请消耗
    desc: formState.desc, //富文本描述
    questionnaire: {
      biz_type: 'trial',
      questionnaire_id: formState.questionnaire
    }, //调查问卷
    day_limit: formState.day_limit, //试用期限
    publish_time: formatDate(formState.publish_time) //开奖时间
  }

  load.value = true
  let p
  if (id) {
    p = trialsApi.update(id, param)
  } else {
    p = trialsApi.create(param)
  }
  p.then(res => {
    message.success('提交成功')
    router.back()
  }).finally(() => (load.value = false))
}

// 校验输入
const checkParam = () => {
  if (!formState.imgList.length) {
    return message.error('请先上传封面图片')
  }
  if (!formState.startDate) {
    return message.error('请选择活动开始时间')
  }
  if (!formState.endDate) {
    return message.error('请选择活动结束时间')
  }
  if (!formState.reportName) {
    return message.error('请填写活动名称')
  }
  if (!formState.questionnaire) {
    return message.error('请填写试用问卷')
  }

  if (!formState.goods[0].sku) {
    return message.error('请填写试用产品sku')
  }
  if (!formState.goods[0].setting_stock) {
    return message.error('请填写试用数量')
  }
  if (!formState.consume && formState.consume != 0) {
    return message.error('请输入申请试用消耗积分值')
  }
  if (!formState.publish_time) {
    return message.error('请选择试用名单公布时间')
  }
  if (!formState.day_limit) {
    return message.error('请输入名单公布后领取试用截止天数')
  }
  if (!formState.desc) {
    return message.error('请填写活动描述')
  }
}
</script>

<style lang="less" scoped>
.index {
  :deep(.ant-form-item-label) {
    label::before {
      content: '*';
      color: red;
      margin-right: 2px;
    }
  }

  .non-required {
    :deep(.ant-form-item-label) {
      label::before {
        content: '';
      }
    }
  }
}

.load-wrap {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100px;
}
</style>
