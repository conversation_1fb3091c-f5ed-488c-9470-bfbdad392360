<template>
  <div class="index">
    <div class="data-list">
      <uc-layout-list title="试用名单">
        <template #extra>
          <div class="search">
            <div></div>
            <div v-if="!list.length" class="r">
              <a-button class="but" type="primary" @click="openAlert">
                中奖名单
              </a-button>
            </div>
          </div>
        </template>
        <!-- 用户list -->
        <template #list>
          <a-config-provider>
            <template #renderEmpty>
              <empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                description="尚未抽取试用名单，点击右上角「中奖名单」抽取中奖用户名单"
              />
            </template>
            <a-table
              row-key="id"
              :loading="loading"
              :data-source="list"
              :pagination="stdPagination(data)"
              @change="setPage"
            >
              <a-table-column title="试用用户" ellipsis width="200px">
                <template #default="{ record }">
                  <div class="text-ellipsis">
                    {{ record.user?.nickname || '-' }}
                  </div>
                  <div>{{ $formatters.numberEncryption(record.user?.phone_number) }}</div>
                </template>
              </a-table-column>

              <a-table-column title="收货信息" ellipsis>
                <template #default="{ record }">
                  <template v-if="record.consignee_info">
                    <div class="text-ellipsis">
                      {{ record.consignee_info?.consignee }} -
                      {{ $formatters.numberEncryption(record.consignee_info?.consignee_phone_number) }}
                    </div>
                    <div class="text-ellipsis">
                      {{ record?.consignee_info?.address }}
                    </div>
                  </template>
                  <div v-else>
                    -
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="试用状态" width="120px" ellipsis>
                <template #default="{ record }">
                  <div v-if="record.order_id == 0" class="text-ellipsis status">
                    <span class="s1"></span>
                    <div>未下单</div>
                  </div>
                  <div v-if="record.order_id > 0" class="text-ellipsis status">
                    <span class="s2"></span>
                    <div>已下单</div>
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="下单时间" width="120px">
                <template #default="{ record }">
                  {{ record?.order?.created_at || '-' }}
                </template>
              </a-table-column>
            </a-table>
          </a-config-provider>
        </template>
      </uc-layout-list>
    </div>

    <!-- 抽取适用者名单 -->
    <div class="get-periencer">
      <a-modal v-model:visible="modalStatus" title="抽取试用名单" @ok="handleOk">
        <a-textarea
          v-model:value="phones"
          placeholder="请输入试用名单手机号码，一行一个"
          :auto-size="{ minRows: 5, maxRows: 10 }"
        />
      </a-modal>
    </div>
  </div>
</template>

<script setup>
import { defineComponent, ref, reactive } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, isEqual, uniqWith, debounce } from 'lodash'
import { apiTrials, apiQuestionnaires, apiGoods, apiTrialRecords, apiWinner } from '../../../api'
import { formatDate } from '@/utils/date'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { Empty } from 'ant-design-vue'
const { statusList, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()

const props = defineProps({
  trialId: {
    type: [String, Number],
    default: null
  },
  status: {
    type: [String, Number],
    default: null
  }
})

// 搜索关键字
const wH = ref(window.innerHeight)
const load = ref(false)
const modalStatus = ref(false)
const phones = ref(null)
const searchKey = reactive({ type: 'phone', str: '手机号' })
const list = ref([])

//打开抽取中奖
const openAlert = () => {
  phones.value = ''
  modalStatus.value = true
}

//提交抽取中奖
const handleOk = () => {
  if (!phones.value) {
    return message.warning('请至少填写一个号码')
  }

  let nameList = phones.value.split('\n')
  let nameListArr = uniqWith(nameList, isEqual).join('\n')

  apiWinner
    .create({
      trial_id: props.trialId,
      phone_number: nameListArr
    })
    .then(() => {
      message.success('添加成功')
      modalStatus.value = false
      refresh()
    })
}

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  //创建参数
  return apiTrialRecords.paginator({
    filters: { status: 'success', trial_id: props.trialId },
    relation_filters: {
      // user: useTransformQuery(param[0], param[1])
    },
    offset,
    limit,
    relations: ['user', 'order']
  })
})
watch(data, val => {
  list.value = val.items.map(m => {
    return m
  })
  // console.log(list.value[0])
})
</script>

<style lang="less" scoped>
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 50px;
  // padding: 0 20px;
  background: #fff;
  width: 100%;
  .r {
    display: flex;
    align-items: center;
    .but {
      margin-left: 10px;
    }
  }
}
.data-list {
  .wrap {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .noraml {
    width: 100%;
  }
  .status {
    display: flex;
    align-items: center;
    span {
      width: 4px;
      height: 4px;
      border-radius: 100%;
      margin-right: 6px;
    }
    .s1 {
      background: rgb(24, 144, 255);
    }
    .s2 {
      background: yellowgreen;
    }
  }
}
</style>
