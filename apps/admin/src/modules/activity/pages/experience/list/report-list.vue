<template>
  <div class="index">
    <c-load v-model:show="load" />

    <div class="data-list">
      <uc-layout-list title="试用报告">
        <template #extra>
          <div class="search">
            <div class="r">
              <a-button type="primary" @click="modalStatus = true">
                上传报告
              </a-button>
            </div>
          </div>
        </template>

        <template #list>
          <a-config-provider>
            <template #renderEmpty>
              <empty
                :image="Empty.PRESENTED_IMAGE_SIMPLE"
                description="尚未提交试用报告，点击右上角「上传报告」上传用户试用心得"
              />
            </template>
            <a-table
              row-key="id"
              :loading="loading"
              :data-source="list"
              :pagination="stdPagination(data)"
              @change="setPage"
            >
              <a-table-column title="报告名称" ellipsis>
                <template #default="{ record }">
                  <div class="text-ellipsis">
                    {{ record.title }}
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="用户昵称" width="200px" ellipsis>
                <template #default="{ record }">
                  {{ record?.user?.nickname }}
                </template>
              </a-table-column>

              <a-table-column title="手机号码" width="150px" ellipsis>
                <template #default="{ record }">
                  <div class="text-ellipsis">
                    {{ $formatters.numberEncryption(record.user?.phone_number) }}
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="提交时间" width="200px" ellipsis>
                <template #default="{ record }">
                  <div class="text-ellipsis">
                    {{ record.created_at }}
                  </div>
                </template>
              </a-table-column>

              <a-table-column title="操作" width="110px" ellipsis>
                <template #default="{ record }">
                  <div class="buts">
                    <div class="but but1" @click="onPreview(record)">
                      预览
                    </div>
                    <div class="but but2" @click="onDel(record.id)">
                      删除
                    </div>
                  </div>
                </template>
              </a-table-column>
            </a-table>
          </a-config-provider>
        </template>
      </uc-layout-list>
    </div>

    <!-- 上传适用报告 -->
    <div class="get-periencer">
      <a-modal v-model:visible="modalStatus" title="上传试用报告" @ok="commitReport" @cancel="resetData">
        <div class="body">
          <a-form ref="formStateRef" :model="formState" name="basic" autocomplete="off">
            <a-form-item label="试用活动" name="activity" class="required">
              <a-select
                v-model:value="formState.activity"
                :size="size"
                placeholder="请选择试用活动"
                :options="trialsList"
                disabled
              />
            </a-form-item>

            <a-form-item label="试用用户" name="user" class="required">
              <a-select
                v-model:value="formState.user"
                :size="size"
                placeholder="请选择试用用户"
                :options="recordsList"
              />
            </a-form-item>

            <a-form-item label="报告名称" name="reportName" class="required">
              <a-input
                v-model:value="formState.reportName"
                :maxlength="20"
                placeholder="请输入试用报告名称，不超过20个字"
              />
            </a-form-item>
            <a-form-item label="试用心得" name="list" class="diy-upload required">
              <uc-upload v-model:list="formState.list" :max-length="50" show-label label-text="封面" />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </div>

    <!-- 预览适用报告 -->
    <div class="get-preview">
      <!-- <a-modal v-model:visible="modalPreview" width="400px" :footer="null" @ok="handleOk">
        <template #title>
          <div class="text-ellipsis" :title="`试用报告-${previewData.nickname}`">
            试用报告-{{ previewData.nickname }}
          </div>
        </template>
        <div class="get-preview-body">
          <div class="head">
            <img alt="" :src="previewData.head.url" />
          </div>
          <div class="content">
            <div v-for="(i, idx) of previewData.data" :key="idx" class="img-wrap">
              <img :src="i.url" />
            </div>
          </div>
        </div>
      </a-modal> -->
      <a-drawer v-model:visible="modalPreview" :closable="false" placement="right" width="400px">
        <template #title>
          <div class="text-ellipsis" :title="`试用报告-${previewData.nickname}`">
            试用报告-{{ previewData.nickname }}
          </div>
        </template>
        <div class="get-preview-body">
          <div class="head">
            <img alt="" :src="previewData.head.url" />
          </div>
          <div class="content">
            <div v-for="(i, idx) of previewData.data" :key="idx" class="img-wrap">
              <img :src="i.url" />
            </div>
          </div>
        </div>
      </a-drawer>
    </div>
  </div>
</template>

<script setup>
import cLoad from '@/components/uc-loading.vue'
import { Modal, Empty } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { defineComponent, ref, reactive, createVNode } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, isEqual, uniqWith, debounce } from 'lodash'
import { formatDate } from '@/utils/date'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import {
  apiTrials,
  apiQuestionnaires,
  apiGoods,
  apiTrialRecords,
  apiTrialReports,
  apiDelTrialReports
} from '../../../api'
import { useTransformQuery } from '@/composables/useTransformQuery'
const { statusList, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()

const props = defineProps({
  trialId: {
    type: [String, Number],
    default: null
  },
  trialTitle: {
    type: [String, Number],
    default: null
  },
  stock: {
    type: [String, Number],
    default: 0
  }
})

defineComponent({
  cLoad
})
// 搜索关键字
const wH = ref(window.innerHeight)
const load = ref(false)
const butLoad = ref(false)
const modalStatus = ref(false)
const modalPreview = ref(false)
const perinecerList = ref(null)
const formStateRef = ref()
const total = ref(0)
//试用活动列表
const trialsList = ref([])
//试用用户列表
const recordsList = ref([])
const list = ref([])
const previewData = ref({ head: '', data: [], nickname: '' })

const formState = reactive({
  activity: null,
  user: null,
  reportName: null,
  list: []
})

//  预览报告
const onPreview = data => {
  // console.log(data)
  apiDelTrialReports.get(data.id, {}).then(res => {
    previewData.value = {
      head: res.content[0],
      data: res.content.slice(1),
      nickname: data.user?.nickname
    }
  })
  modalPreview.value = true
}

//删除报告
const onDel = async id => {
  await apiDelTrialReports.delete(id)
  message.success('删除成功')
  refresh()
}

//获取select数据
const getData = async () => {
  const records = await apiTrialRecords.get('', {
    relations: ['user'],
    filters: { status: 'success', trial_id: props.trialId }
  })

  trialsList.value = [{ label: props.trialTitle, value: props.trialId }]
  formState.activity = props.trialId

  recordsList.value = records.items.map(m => {
    m.label = m.user.nickname
    m.value = m.user_id
    return m
  })
  console.log('recordsList', recordsList.value)
}
getData()

//提交报告
const commitReport = async () => {
  try {
    const data = formState
    if (!data.user) {
      return message.warn('请选择试用用户')
    }

    if (!data.reportName) {
      return message.warn('请填写报告名称')
    }
    if (!data.list.length) {
      return message.warn('请上传试用心得')
    }

    load.value = true
    const res = await apiTrialReports.create({
      trial_id: props.trialId,
      user_id: data.user,
      title: data.reportName,
      content: data.list.map(url => ({
        url
      }))
    })
    load.value = false
    if (res) {
      message.success('上传成功')
      modalStatus.value = false
      refresh()
      resetData()
    }
  } catch (e) {
    load.value = false
    return
  }
}

//清空数据
const resetData = () => {
  formState.user = null
  formState.reportName = null
  formState.list = []
}

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  //创建参数
  return apiTrialReports.paginator({
    filters: { trial_id: props.trialId },
    offset,
    limit,
    relations: ['user']
  })
})
watch(data, val => {
  total.value = val.total
  list.value = val.items.map(m => {
    return m
  })
})
</script>

<style lang="less" scoped>
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  // height: 50px;
  // padding: 0 20px;
  background: #fff;
  width: 100%;
  .r {
    display: flex;
    align-items: center;
    .but {
      margin-left: 10px;
    }
  }
}
.data-list {
  .buts {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    .but {
      padding: 0 6px;
      cursor: pointer;
      color: #1890ff;
    }
    .but2 {
      color: #f5222d;
    }
  }
}
.get-preview-body {
  .head {
    width: 347px;
    background: rgba(0, 0, 0, 0.1);
    margin: 0 auto;
    img {
      width: 100%;
    }
  }
  .content {
    margin-top: 10px;
    width: 347px;
    background: rgba(0, 0, 0, 0.1);
    margin: 10px auto 0 auto;
    .img-wrap {
      width: 100%;
      img {
        width: 100%;
      }
    }
  }
}

.diy-upload {
  :deep(.ant-upload-picture-card-wrapper) {
    display: inline-block !important;
  }
}
</style>
