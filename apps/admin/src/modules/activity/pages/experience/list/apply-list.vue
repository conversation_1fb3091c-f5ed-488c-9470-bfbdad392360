<template>
  <div class="index">
    <div class="data-list">
      <uc-layout-list title="申请名单">
        <template #extra>
          <div class="search">
            <div></div>
            <div class="r">
              <a-input-group compact>
                <a-select v-model:value="keyType" style="width: 110px" width="200px">
                  <a-select-option value="phone">
                    手机号码
                  </a-select-option>
                  <a-select-option value="name">
                    用户昵称
                  </a-select-option>
                </a-select>
                <a-input v-model:value="keyword" style="width: 200px" placeholder="请输入关键词" @blur="queryExec" />
              </a-input-group>
              <a-button class="but" @click="onDown">
                导出试用问卷
              </a-button>
            </div>
          </div>
        </template>
        <!-- 用户list -->
        <template #list>
          <a-table
            row-key="id"
            :loading="loading"
            :data-source="list"
            :pagination="stdPagination(data)"
            @change="setPage"
          >
            <a-table-column title="用户昵称" ellipsis>
              <template #default="{ record }">
                <div class="text-ellipsis">
                  {{ record.user?.nickname }}
                </div>
              </template>
            </a-table-column>

            <a-table-column title="手机号码" width="200px" ellipsis>
              <template #default="{ record }">
                <div class="text-ellipsis">
                  {{ $formatters.numberEncryption(record?.user?.phone_number) }}
                </div>
              </template>
            </a-table-column>

            <a-table-column title="申请次数" width="200px" ellipsis>
              <template #default="{ record }">
                <div class="text-ellipsis">
                  第{{ record.apply_count }}次
                </div>
              </template>
            </a-table-column>

            <a-table-column title="申请时间" width="200px" ellipsis>
              <template #default="{ record }">
                <div class="text-ellipsis">
                  {{ record.created_at }}
                </div>
              </template>
            </a-table-column>
          </a-table>
        </template>
      </uc-layout-list>
    </div>
  </div>
</template>

<script setup>
import { defineComponent, ref, reactive, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, isEqual, uniqWith, debounce } from 'lodash'
import { apiTrials, apiQuestionnaires, apiGoods, apiTrialRecords, apiDownload, exportUrl } from '../../../api'
import { formatDate } from '@/utils/date'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useStore } from '@/store/auth'

const { statusList, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()
const props = defineProps({
  trialId: {
    type: [String, Number],
    default: null
  },
  questionnaireId: {
    type: [String, Number],
    default: null
  }
})
// 参数值
const wH = ref(window.innerHeight)
const list = ref([])
const keyType = ref('phone')
const keyword = ref('')

const queryExec = () => {
  refresh()
}

const createParams = () => {
  //创建参数
  let param = {}
  if (keyType.value == 'phone') {
    param = [
      {
        phone_number: keyword.value
      },
      { phone_number: '=' }
    ]
  }
  if (keyType.value == 'name') {
    param = [
      {
        nickname: keyword.value
      },
      { nickname: 'like' }
    ]
  }
  return param
}
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  const param = createParams()
  return apiTrialRecords.paginator({
    relation_filters: {
      user: useTransformQuery(param[0], param[1])
    },
    filters: { trial_id: props.trialId },
    offset,
    limit,
    relations: ['user']
  })
})

const formatQuery = obj => {
  return encodeURIComponent(JSON.stringify(obj))
}
// 导出
const onDown = () => {
  const { state } = useStore()
  const param = createParams()
  const filters =
    'filters=' + formatQuery({ questionnaire_id: props.questionnaireId, biz_id: props.trialId, biz_type: 'trial' })
  const relations = 'relations=user'
  const relation_filters = 'relation_filters=' + formatQuery(useTransformQuery(param[0], param[1]))
  const linkUrl = exportUrl + '?' + filters + '&' + relations + '&' + relation_filters+ '&token=' + state.token
  const a = document.createElement('a')
  a.href = linkUrl
  a.download = '申请名单' + new Date().getTime() + '.xlsx'
  a.click()
}
watch(data, val => {
  list.value = val.items.map(m => {
    return m
  })
})
</script>

<style lang="less" scoped>
.data-list {
  :deep(.layout-list) {
    display: flex;
  }
}
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  // height: 50px;
  // padding: 0 20px;
  background: #fff;
  .r {
    display: flex;
    align-items: center;
    .but {
      margin-left: 10px;
    }
  }
}
</style>
