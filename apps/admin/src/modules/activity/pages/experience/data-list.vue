<template>
  <div class="index">
    <apply-list :trial-id="trialId" :questionnaire-id="questionnaireId" />
    <experiencer-list v-if="status == 'ended'" :trial-id="trialId" />
    <report-list
      v-if="status == 'ended' && data.items.length"
      :trial-id="trialId"
      :stock="stock"
      :trial-title="trialTitle"
    />
  </div>
</template>

<script setup>
import { defineComponent, ref } from '@vue/runtime-core'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useRoute } from 'vue-router'
import applyList from './list/apply-list.vue'
import experiencerList from './list/experiencer-list'
import reportList from './list/report-list.vue'
import { apiTrialRecords } from '../../api'

defineComponent({
  applyList,
  experiencerList,
  reportList
})

const trialId = ref(null)
const trialTitle = ref(null)
const status = ref(false)
const questionnaireId = ref(null)
const stock = ref(false)
const route = useRoute()
trialId.value = route.query.id
status.value = route.query.status
stock.value = route.query.stock
trialTitle.value = route.query.title
questionnaireId.value = route.query.questionnaire_id

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  //创建参数
  return apiTrialRecords.paginator({
    filters: { status: 'success', trial_id: trialId.value },
    offset: 1,
    limit: 1,
    relations: ['user', 'order']
  })
})
</script>
