<template>
  <div class="index">
    <div class="data-list">
      <uc-layout-list title="新品试用">
        <template #filter>
          <a-form-item>
            <a-input v-model:value="keyword" style="width: 320px" placeholder="活动名称" />
          </a-form-item>

          <a-form-item>
            <a-select v-model:value="keyType" style="width: 120px" placeholder="活动状态" allow-clear>
              <a-select-option key="" value="not_start">
                未开始
              </a-select-option>
              <a-select-option key="" value="normal">
                进行中
              </a-select-option>
              <a-select-option key="" value="ended">
                已结束
              </a-select-option>
            </a-select>
          </a-form-item>

          <a-form-item>
            <a-button class="but" type="primary" @click="queryExec">
              查询
            </a-button>
          </a-form-item>
          <a-form-item>
            <a-button class="but" @click="resetData">
              重置
            </a-button>
          </a-form-item>
        </template>

        <template #extra>
          <a-button class="but" type="primary" @click="goAdd">
            新增新品试用
          </a-button>
        </template>
        <!-- 用户list -->
        <template #list>
          <a-table
            row-key="id"
            :loading="loading"
            :data-source="list"
            :pagination="stdPagination(data)"
            @change="setPage"
          >
            <a-table-column title="活动名称" ellipsis>
              <template #default="{ record }">
                <div class="text-ellipsis">
                  {{ record.title }}
                </div>
              </template>
            </a-table-column>

            <a-table-column title="UV/PV" width="150px" ellipsis align="right">
              <template #default="{ record }">
                <div class="text-ellipsis">
                  {{ record?.uv }}
                  /
                  {{ record?.pv }}
                </div>
              </template>
            </a-table-column>

            <a-table-column title="活动时间" ellipsis width="360px">
              <template #default="{ record }">
                <div class="text-ellipsis">
                  {{ record.start_time }} ~ {{ record.end_time }}
                </div>
              </template>
            </a-table-column>

            <a-table-column title="活动状态" width="120px" ellipsis>
              <template #default="{ record }">
                <div class="status-list">
                  <div v-if="record.status == 'not_start'" class="status">
                    <span class="status2"></span>
                    未开始
                  </div>
                  <div v-if="record.status == 'normal'" class="status">
                    <span class="status1"></span>
                    进行中
                  </div>
                  <div v-if="record.status == 'ended'" class="status">
                    <span class="status3"></span>
                    已结束
                  </div>
                </div>
              </template>
            </a-table-column>

            <a-table-column title="操作" width="150px">
              <template #default="{ record }">
                <a-button type="link" @click="copyLink(linkPath + record.id)">
                  链接
                </a-button>
                <a-button v-if="record.status != 'not_start'" type="link" @click="onData(record)">
                  数据
                </a-button>
                <a-button v-if="record.status != 'ended'" type="link" @click="onEdit(record)">
                  编辑
                </a-button>
                <a-popconfirm
                  v-if="record.status === 'not_start'"
                  placement="left"
                  title="你确定要删除该数据么？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" class="danger">
                    删除
                  </a-button>
                </a-popconfirm>

                <a-button v-if="record.status === 'ended'" type="link" @click="onQuery(record)">
                  查看
                </a-button>
              </template>
            </a-table-column>
          </a-table>
        </template>
      </uc-layout-list>
    </div>
  </div>
</template>

<script setup>
import cLoad from '@/components/uc-loading.vue'
import { Modal } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { defineComponent, ref, reactive, watch, createVNode } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, isEqual, uniqWith, debounce } from 'lodash'
import { apiTrials, apiQuestionnaires, apiGoods, apiTrialRecords, apiTrialsId } from '../../api'
import { formatDate } from '@/utils/date'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
const { statusList, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()
defineComponent({
  cLoad
})

// 参数值
const wH = ref(window.innerHeight)
const load = ref(false)
const list = ref([])
const keyType = ref(null)
const keyword = ref(null)
const router = useRouter()

//重置数据
const resetData = () => {
  keyType.value = null
  keyword.value = null
  setPage()
}
//前往新增
const goAdd = () => {
  router.push({
    name: 'experience-add'
  })
}

const queryExec = () => {
  refresh()
}

// 跳转小程序的页面地址
const linkPath = Object.freeze(`/trial/pages/standard/detail/index?id=`)

const onEdit = e => {
  const { id, status } = e
  router.push({
    name: 'experience-edit',
    params: {
      id,
      status
    }
  })
}
const handleDelete = async data => {
  await apiTrialsId.delete(data.id)
  message.success('删除成功')
  refresh()
}
const onData = data => {
  const { id, status, stock, title, questionnaire } = data
  if (!questionnaire) {
    return message.warn('问卷信息异常')
  }
  router.push({
    name: 'experience-data',
    query: {
      id,
      status,
      stock,
      title,
      questionnaire_id: questionnaire?.questionnaire_id
    }
  })
}

const onQuery = ({ id }) => {
  router.push({
    name: 'experience-edit',
    params: {
      id,
      typePage: 'query'
    }
  })
}

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  //创建参数
  let param = {
    status: keyType.value || undefined,
    title: keyword.value || undefined
  }
  return apiTrials.paginator({
    filters: {
      ...useTransformQuery(param, { title: 'like' })
    },
    offset,
    limit,
    relations: ['user', 'questionnaire']
  })
})
watch(data, val => {
  list.value = val.items.map(m => {
    return m
  })
})
</script>

<style lang="less" scoped>
.top-head {
  display: flex;
  align-items: center;
  background: #fff;
  // height: 50px;
  padding-bottom: 20px;
}
.search {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 50px;
  background: #fff;
  padding: 0 10px;
  .r {
    display: flex;
    align-items: center;
    .but {
      margin-left: 10px;
    }
  }
}
.data-list {
  // height: calc(100vh - 600px);
  // background: #fff;
  .buts {
    display: flex;
    align-items: center;
    justify-content: center;
    .but {
      color: #1890ff;
      margin: 0 5px;
      cursor: pointer;
    }
  }
  .status-list {
    .status {
      display: flex;
      align-items: center;
      span {
        width: 4px;
        height: 4px;
        border-radius: 100%;
        margin-right: 4px;
      }
      .status1 {
        background: #52c41a;
      }
      .status2 {
        background: #1890ff;
      }
      .status3 {
        background: rgba(0, 0, 0, 0.2);
      }
    }
  }
}
</style>
