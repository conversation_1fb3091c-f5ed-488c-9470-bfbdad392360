<template>
  <div class="signin-gift-statistics">
    <a-card title="活动趋势">
      <div id="container"></div>
    </a-card>
    <uc-layout-list title="签到统计">
      <template #extra>
        <div class="flex">
          <a-input-group compact>
            <a-select v-model:value="conditionKey" class="select" :options="userModeListOptions" @change="onConditionKeyChange" />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键词" @blur="handleSearch" />
          </a-input-group>
        </div>
      </template>
      <template #list>
        <a-table :data-source="data.items" row-key="user_id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
          <a-table-column title="用户昵称" data-index="title" ellipsis>
            <template #default="{ record }">
              {{ record?.user?.nickname }}
            </template>
          </a-table-column>
          <a-table-column title="手机号码" data-index="title" ellipsis>
            <template #default="{ record }">
              {{ $formatters.numberEncryption(record?.user?.phone_number) }}
            </template>
          </a-table-column>
          <a-table-column title="连签" data-index="continuity_days" width="100px" />
          <a-table-column title="累签" data-index="accumulate_days" width="100px" />
          <a-table-column title="首次签到时间" data-index="first_time" width="200px" />
          <a-table-column title="最后签到时间" data-index="last_time" width="200px" />
          <a-table-column title="操作" width="70px">
            <template #default="{ record }">
              <a-button type="link" @click="showRecord(record)">
                记录
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>

    <a-modal v-model:visible="modalVisible" width="650px" :title="`${userInfo?.nickname} - 签到记录`" :footer="null" @cancel="handleCancel">
      <a-table :data-source="dataRecord.items" row-key="id" :loading="loadingRecord" :pagination="stdPaginationSimple(dataRecord)" @change="setPageRecord">
        <a-table-column title="签到时间" data-index="created_at" width="190px" />
        <a-table-column title="签到奖励" data-index="desc" ellipsis />
      </a-table>
    </a-modal>
  </div>
</template>
<script setup>
import { nextTick,watch,onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute,useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { signActivityRecordApi,signActivitiesChart,signActivitiesStats } from '../../api'
import { Chart } from '@antv/g2'
import { UserModeList } from '../../enums'
import { cloneDeep } from 'lodash'

const userModeListOptions = UserModeList.options()
const router = useRouter()
const { id } = useRoute().params
if(!id) router.back()

onUnmounted(()=>{
 if(chart) chart.destroy()
})

const hideLoading = message.loading('正在加载数据...')
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  signActivitiesStats(id).get({
    offset,
    limit,
    ...formState.value
  }).finally(hideLoading)
)


const setChart = () =>{
  signActivitiesChart(id).get()
  .then(res=>{
    chart.data(res)

    chart.scale({
      date: {
        range: [0, 1]
      },
      num: {
        min: 0,
        nice: true
      }
    })

    chart.tooltip({
      title: 'date',
      shared: true
    })
    chart.scale('num', {
      alias: '签到人数'
    })
    chart.line().position('date*num').shape('smooth')
    chart.point().position('date*num')
    chart.render()
  })
}

let chart = null

nextTick(() => {
  chart = new Chart({
    container: 'container',
    autoFit: true,
    height: 360
  })
  setPage()
  setChart()
})

const conditionBasic = Object.freeze({nickname:undefined,phone_number:undefined})
const conditionKey = ref(userModeListOptions[0].value)
const conditionValue = ref()
const { formState, onRestFormState, resetFormState } = useFormState(cloneDeep(conditionBasic))

const onConditionKeyChange = () => conditionValue.value = undefined

const handleSearch = () =>{
  Object.assign(formState.value,conditionBasic,{
    [conditionKey.value]:conditionValue.value,
  })
  setPage()
}

onRestFormState(() => setPage())

let userInfo = ref();
const { data:dataRecord, setPage:setPageRecord, loading:loadingRecord, refresh:refreshRecord } = usePaginatorApiRequest(({ offset, limit }) =>
  signActivityRecordApi.paginator({
    filters: useTransformQuery({
      activity_id:id,
      user_id:userInfo.value.id,
    }, {
      title: 'like'
    }),
    offset,
    limit,
  }),undefined,false,
)

const { modalVisible,setModalVisible } = useModalVisible()

const showRecord = ({ user }) => {
  if(!user) return message.info('暂无该用户记录')
  userInfo.value = user
  setModalVisible(true)
  setPageRecord()
}

const handleCancel = () =>{
  setModalVisible(false)
}


</script>
<style lang="less" scoped>
.select-width {
  width: 100px;
  :deep(.ant-select-single:not(.ant-select-customize-input) .ant-select-selector) {
    width: 100px;
  }
}
:deep(.ant-input) {
  width: 300px;
}
</style>
