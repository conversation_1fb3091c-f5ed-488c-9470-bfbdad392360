<template>
  <uc-layout-form class="signin-gift" :is-save="!read" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              placeholder="请选择开始时间"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              class="w-240"
              :disabled="read"
              :disabled-date="isNormal ? disabledEndTimeBeforeNow : disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value.trim="formState.title"
              class="input-width"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="read"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              class="input-width"
              placeholder="请输入活动标签，不超过10字"
              :maxlength="10"
              :disabled="read"
            />
          </a-form-item>
          <a-form-item label="是否自动签到" name="is_auto_sign">
            <a-radio-group v-model:value="formState.is_auto_sign">
              <a-radio :value="0">手动</a-radio>
              <a-radio :value="1">自动</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="兑奖期限" class="required">
            <a-input-number
              v-model:value="formState.limit_day"
              min="0"
              class="input-width"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
              :disabled="read"
            />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules_continuity"
            :key="ruleIndex"
            class="rules_continuity"
            :label="ruleIndex ? ' ' : '循环签到奖励'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex, 'm-b-10': ruleIndex < formState.rules_continuity.length - 1 }"
          >
            <a-input-group v-for="(prize, index) in rule.award_settings" :key="index" compact>
              <a-space :size="10" wrap class="m-b-0">
                <a-input v-model:value="rule.condition" addon-after="天" class="w-80 t-center" disabled />
                <a-select
                  v-model:value="prize.type"
                  class="w-100"
                  placeholder="礼品类型"
                  allow-clear
                  :disabled="rule.disabled || read || isNormal"
                  :options="giftType.options()"
                  @change="onChangeGiftType(prize)"
                />
                <a-input-number
                  v-if="prize.type === giftType.credit"
                  v-model:value="prize.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :disabled="rule.disabled || read || isNormal"
                />
                <a-select
                  v-else
                  v-model:value="prize.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :options="prize.options"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="rule.disabled || read || isNormal"
                  @focus="handleShowGifts(prize)"
                  @search="handleSearchGifts($event, prize)"
                />
                <a-input-number
                  v-model:value.number="prize.stock"
                  style="width: 90px"
                  min="0"
                  placeholder="活动数量"
                  :disabled="read || (isNormal && prize.user_dispatch_setting_id)"
                />
                <uc-upload
                  v-model:list="rule.photo_url"
                  title="签到弹窗"
                  class="upload-position"
                  :max-length="1"
                  :disabled="read || isNormal"
                  :size="'small'"
                />
              </a-space>
            </a-input-group>
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules_first"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '首签奖励'"
            :colon="!ruleIndex"
            class="m-b-10"
          >
            <a-input-group v-for="(prize, index) in rule.award_settings" :key="index" compact>
              <a-space :size="10">
                <a-select
                  v-model:value="prize.type"
                  style="width: 190px"
                  placeholder="礼品类型"
                  allow-clear
                  :disabled="rule.disabled || read"
                  :options="giftType.options()"
                  @change="onChangeGiftType(prize)"
                />
                <a-input-number
                  v-if="prize.type === giftType.credit"
                  v-model:value="prize.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :disabled="rule.disabled || read"
                />
                <a-select
                  v-else
                  v-model:value="prize.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :options="prize.options"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="rule.disabled || read"
                  @focus="handleShowGifts(prize)"
                  @search="handleSearchGifts($event, prize)"
                />
                <a-input-number
                  v-model:value.number="prize.stock"
                  class="w-90"
                  min="0"
                  placeholder="活动数量"
                  :disabled="read || (isNormal && prize.user_dispatch_setting_id)"
                />
                <a-button shape="circle" size="small" class="delete-btn" type="link" :disabled="read || rule.disabled">
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveRule_first(ruleIndex)" />
                  </template>
                </a-button>
              </a-space>
            </a-input-group>
          </a-form-item>
          <a-form-item :label="formState.rules_first.length ? ' ' : '首签奖励'" :colon="!formState.rules_first.length">
            <a-button v-if="!read" type="link" class="p-0" @click="onAddRule_first"> 添加奖励层级 </a-button>
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules_accumulate"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '累签奖励'"
            :colon="!ruleIndex"
            class="m-b-10"
          >
            <a-input-group v-for="(prize, index) in rule.award_settings" :key="index" compact>
              <a-space :size="10">
                <a-input-number
                  v-model:value.number="rule.condition"
                  class="w-80"
                  placeholder="累签天数"
                  addon-after="天"
                  :disabled="rule.disabled || read"
                  min="1"
                />
                <a-select
                  v-model:value="prize.type"
                  class="w-100"
                  placeholder="礼品类型"
                  allow-clear
                  :disabled="rule.disabled || read"
                  :options="giftType.options()"
                  @change="onChangeGiftType(prize)"
                />
                <a-input-number
                  v-if="prize.type === giftType.credit"
                  v-model:value="prize.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :disabled="rule.disabled || read"
                />
                <a-select
                  v-else
                  v-model:value="prize.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :options="prize.options"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="rule.disabled || read"
                  @focus="handleShowGifts(prize)"
                  @search="handleSearchGifts($event, prize)"
                />
                <a-input-number
                  v-model:value.number="prize.stock"
                  class="w-90"
                  min="0"
                  placeholder="活动数量"
                  :disabled="read || (isNormal && prize.user_dispatch_setting_id)"
                />
                <a-button shape="circle" size="small" class="delete-btn" type="link" :disabled="read || rule.disabled">
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveRule(ruleIndex)" />
                  </template>
                </a-button>
              </a-space>
            </a-input-group>
          </a-form-item>
          <a-form-item
            :label="formState.rules_accumulate.length ? ' ' : '累签奖励'"
            :colon="!formState.rules_accumulate.length"
          >
            <a-button v-if="!read" type="link" class="p-0" @click="onAddRule"> 添加奖励层级 </a-button>
          </a-form-item>

          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules_continuous"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '连签奖励'"
            :colon="!ruleIndex"
            class="m-b-10"
          >
            <a-input-group v-for="(prize, index) in rule.award_settings" :key="index" compact>
              <a-space :size="10">
                <a-input-number
                  v-model:value.number="rule.condition"
                  class="w-80"
                  placeholder="连签天数"
                  addon-after="天"
                  :disabled="rule.disabled || read"
                  min="1"
                />
                <a-select
                  v-model:value="prize.type"
                  class="w-100"
                  placeholder="礼品类型"
                  allow-clear
                  :disabled="rule.disabled || read"
                  :options="giftType.options()"
                  @change="onChangeGiftType(prize)"
                />
                <a-input-number
                  v-if="prize.type === giftType.credit"
                  v-model:value="prize.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :disabled="rule.disabled || read"
                />
                <a-select
                  v-else
                  v-model:value="prize.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :options="prize.options"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="rule.disabled || read"
                  @focus="handleShowGifts(prize)"
                  @search="handleSearchGifts($event, prize)"
                />
                <a-input-number
                  v-model:value.number="prize.stock"
                  class="w-90"
                  min="0"
                  placeholder="活动数量"
                  :disabled="read || (isNormal && prize.user_dispatch_setting_id)"
                />
                <a-button shape="circle" size="small" class="delete-btn" type="link" :disabled="read || rule.disabled">
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveRuleContinuous(ruleIndex)" />
                  </template>
                </a-button>
              </a-space>
            </a-input-group>
          </a-form-item>
          <a-form-item
            :label="formState.rules_continuous.length ? ' ' : '连签奖励'"
            :colon="!formState.rules_continuous.length"
          >
            <a-button v-if="!read" type="link" class="p-0" @click="onAddRuleContinuous"> 添加奖励层级 </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="页面配置">
          <a-form-item label="皮肤模式" class="required">
            <a-select
              v-model:value="formState.page_setting.skin_mode"
              class="m-r-10 w-310"
              placeholder="请选择皮肤"
              allow-clear
              :options="skinMode.option()"
              :disabled="read"
            />
            <a-button class="p-lr-0 relative" type="link">
              预览效果图
              <div class="w-fill h-fill hide absolute top-0">
                <a-image class="opacity-0" :src="assets.signPreviewUrl" />
              </div>
            </a-button>
          </a-form-item>
          <a-form-item label="颜色配置" class="required">
            <a-space :size="10" wrap>
              <a-input
                v-model:value.trim="formState.page_setting.color.subject"
                class="w-160"
                placeholder="主题色，如#000000"
                :disabled="read"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.sub"
                class="w-160"
                placeholder="辅助色，如#000000"
                :disabled="read"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.bg"
                class="w-160"
                placeholder="背景色，如#000000"
                :disabled="read"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.title"
                class="w-160"
                placeholder="标题色，如#000000"
                :disabled="read"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" name="title" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.page_setting.page.activity_bg"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动背景"
                :disabled="read"
              />
              <uc-upload
                v-model:list="formState.page_setting.page.popup_url"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="签到弹窗配置"
                :disabled="read"
              />
              <!-- <uc-upload
                v-model:list="formState.page_setting.page.activity_text"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动文案"
                :disabled="read"
              /> -->
              <!-- <uc-upload
            v-model:list="formState.page_setting.page.received"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="已领取"
            :disabled="read"
          />
          <uc-upload
            v-model:list="formState.page_setting.page.no_stock"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="已领光"
            :disabled="read"
          /> -->
            </a-space>
          </a-form-item>
          <!--  <a-form-item label="弹窗配置" name="title" class="required">
        <a-space>
          <uc-upload
            v-model:list="formState.page_setting.popup.prepare"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="未开始"
            :disabled="read"
          />
          <uc-upload
            v-model:list="formState.page_setting.popup.end"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="已结束"
            :disabled="read"
          />
        </a-space>
      </a-form-item> -->
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="分享设置" class="h-fill">
          <a-form-item label="分享海报" name="title" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.share_setting.friend_poster"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="会话海报"
                :disabled="read"
              />
              <uc-upload
                v-model:list="formState.share_setting.friend_circle_poster"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="本地海报"
                :disabled="read"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="分享文案" class="required">
            <a-input
              v-model:value.trim="formState.share_setting.share_text"
              class="input-width"
              placeholder="请输入分享文案，不超过20字"
              :maxlength="20"
              :disabled="read"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <uc-rich-text v-model="formState.desc" v-model:isValidator="isValidator" :disabled="!!can_look || read" />
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { cloneDeep, debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useAward } from '../../useAward'
import { signActivityApi } from '../../api'
import { skinMode, giftType, activityRuleList, awardBizType } from '../../enums'
import { usePrizeAwardUpdate } from '../../usePrizeAward'
import { useSigninGiftEdit } from '../../useTransformData'
import assets from '../../assets.config'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
const { transformToShow, transformToRequest } = useSigninGiftEdit()
const { statusList, TIME_STATUS_NORMAL } = useTimeStatus()

const router = useRouter()
const { id, status, readOnly } = useRoute().params
const read = Boolean(readOnly)
let isNormal = ref(false)
let isEdit = ref(false)

if (id) {
  isEdit.value = true
  if (status == TIME_STATUS_NORMAL) isNormal.value = true

  const hideLoading = message.loading('正在加载数据...')
  signActivityApi
    .get(id, { relations: ['rules'] })
    .then(async data => {
      setFormState(await transformToShow(data, isNormal))
    })
    .finally(hideLoading)
}

const defaultSigninRuleBasic = Object.freeze({
  type: undefined,
  condition: undefined,
  photo_url: [],
  award_settings: [onPrizeDefault({ type: undefined })],
  disabled: false
})

const defaultSignInRuleContinuity = Object.freeze(
  Object.assign(cloneDeep(defaultSigninRuleBasic), {
    type: activityRuleList.continuity
  })
)

const defaultSignInRuleAccumulate = Object.freeze(
  Object.assign(cloneDeep(defaultSigninRuleBasic), {
    type: activityRuleList.accumulate
  })
)

const defaultSignInRuleContinuous = Object.freeze(
  Object.assign(cloneDeep(defaultSigninRuleBasic), {
    type: activityRuleList.continuous
  })
)

const defaultSignInRuleFirst = Object.freeze(
  Object.assign(cloneDeep(defaultSigninRuleBasic), {
    type: activityRuleList.first,
    condition: 0 //首次签到天数为0
  })
)

const defaultRulesContinuity = Array.from({ length: 7 }, (v, k) => ({
  ...cloneDeep(defaultSignInRuleContinuity),
  condition: k + 1
}))

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  is_auto_sign: 0,
  limit_day: undefined,
  rules: [],
  page_setting: {
    page: {
      activity_bg: undefined,
      popup_url: undefined
      // activity_text: undefined
      // received: undefined,
      // no_stock: undefined
    },
    color: {
      bg: undefined,
      sub: undefined,
      title: undefined,
      subject: undefined
    },
    // popup: {
    //   end: undefined,
    //   prepare: undefined
    // },
    skin_mode: 'default'
  },
  share_setting: {
    friend_poster: undefined,
    friend_circle_poster: undefined,
    share_text: undefined
  },
  desc: undefined,
  rules_continuity: defaultRulesContinuity,
  rules_accumulate: [],
  rules_continuous: [],
  rules_first: []
})

const { disabledStartTime, disabledEndTime, disabledEndTimeBeforeNow } = useDisabledDate(formState)

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  limit_day: { required: true, message: '请输入兑奖期限' },
  rules_continuity: {
    required: true,
    validator(_, value) {
      let tip
      for (const { photo_url, award_settings } of value) {
        for (const award_setting of award_settings) {
          if (!award_setting.type) {
            tip = '请选择循环签到奖励礼品类型'
            break
          }

          if (!award_setting.quantity) {
            award_setting.quantity = 0
          }

          if (award_setting.stock === undefined || award_setting.stock === null) {
            if (award_setting.quantity) {
              tip = '请输入已设置奖励的循环签到奖励活动数量'
              break
            }
            award_setting.stock = 0
          }
        }
        if (tip) break
      }

      if (tip) return Promise.reject(tip)
      return Promise.resolve()
    }
  },
  rules_accumulate: {
    validator(_, value) {
      let tip
      for (const { award_settings, condition } of value) {
        if (!condition) {
          tip = '请输入累签奖励的累签天数'
        } else {
          for (const { type, option_id, quantity, stock } of award_settings) {
            if (!type) {
              tip = '请选择累签奖励礼品类型'
              break
            }
            if (type === giftType.credit) {
              if (!quantity) {
                tip = '请输入积分值'
                break
              }
            } else {
              if (!option_id) {
                tip = '请选择累签奖励礼品'
                break
              }
            }
            if (stock === undefined || stock === null) {
              tip = '请输入累签奖励活动数量'
              break
            }
          }
        }
        if (tip) break
      }

      if (tip) return Promise.reject(tip)
      return Promise.resolve()
    }
  },
  rules_continuous: {
    validator(_, value) {
      let tip
      for (const { award_settings, condition } of value) {
        if (!condition) {
          tip = '请输入连签奖励的连签天数'
        } else {
          for (const { type, option_id, quantity, stock } of award_settings) {
            if (!type) {
              tip = '请选择连签奖励礼品类型'
              break
            }
            if (type === giftType.credit) {
              if (!quantity) {
                tip = '请输入积分值'
                break
              }
            } else {
              if (!option_id) {
                tip = '请选择连签奖励礼品'
                break
              }
            }
            if (stock === undefined || stock === null) {
              tip = '请输入连签奖励活动数量'
              break
            }
          }
        }
        if (tip) break
      }

      if (tip) return Promise.reject(tip)
      return Promise.resolve()
    }
  },
  page_setting: {
    validator(_, value) {
      const colorReg = Object.freeze(/^#[0-9a-zA-Z]{6}$/)
      const { page, color, popup, skin_mode } = value
      if (!skin_mode) return Promise.reject('请选择皮肤模式')
      if (!colorReg.test(color.subject)) return Promise.reject('请输入主题色，以#开头+6位数字字母组合')
      if (!colorReg.test(color.sub)) return Promise.reject('请输入辅助色，以#开头+6位数字字母组合')
      if (!colorReg.test(color.bg)) return Promise.reject('请输入背景色，以#开头+6位数字字母组合')
      if (!colorReg.test(color.title)) return Promise.reject('请输入标题色，以#开头+6位数字字母组合')
      if (!page.activity_bg) return Promise.reject('请选择活动背景图片')
      // if (!page.activity_text) return Promise.reject('请选择活动文案图片')
      // if (!page.received) return Promise.reject('请选择已领取图片')
      // if (!page.no_stock) return Promise.reject('请选择已领光图片')
      // if (!popup.prepare) return Promise.reject('请选择未开始图片')
      // if (!popup.end) return Promise.reject('请选择已结束图片')
      return Promise.resolve()
    }
  },
  share_setting: {
    validator(_, value) {
      const { friend_poster, friend_circle_poster, share_text } = value
      if (!friend_poster) return Promise.reject('请选择分享好友海报')
      if (!friend_circle_poster) return Promise.reject('请选择分享朋友圈海报')
      if (!share_text) return Promise.reject('请输入分享文案')
      return Promise.resolve()
    }
  }
})

const onAddRule = () => {
  const { rules_accumulate } = formState.value
  rules_accumulate.push(cloneDeep(defaultSignInRuleAccumulate))
}

const onAddRuleContinuous = () => {
  const { rules_continuous } = formState.value
  rules_continuous.push(cloneDeep(defaultSignInRuleContinuous))
}

const onAddRule_first = () => {
  const { rules_first } = formState.value
  rules_first.push(cloneDeep(defaultSignInRuleFirst))
}

const onRemoveRule = index => formState.value.rules_accumulate.splice(index, 1)

const onRemoveRuleContinuous = index => formState.value.rules_continuous.splice(index, 1)

const onRemoveRule_first = index => formState.value.rules_first.splice(index, 1)

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) return

  const params = transformToRequest(formState.value)

  id ? await signActivityApi.replace(id, params) : await signActivityApi.create(params)
  message.success('操作完成')
  router.back()
}, 500)
</script>
<style scoped lang="less">
.signin-gift {
  .separator {
    .inline-block();
    width: 20px;
    text-align: center;
  }

  .input-width {
    width: 500px !important;
  }

  .rules_continuity {
    :deep(.ant-upload) {
      height: 32px;
    }
  }

  .upload-position {
    :deep(.ant-upload-picture-card-wrapper) {
      width: 32px;
      height: 32px;
    }

    :deep(.ant-upload) {
      margin: 0 !important;
    }
  }

  .delete-btn {
    padding-top: 2px;
    border: none;

    :deep(.anticon) {
      font-size: 20px;
    }
  }

  .delete-btn:disabled {
    :deep(.anticon) {
      color: #d9d9d9 !important;
    }
  }
}
</style>
