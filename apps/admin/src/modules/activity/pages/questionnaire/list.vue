<template>
  <div class="list-container">
    <uc-layout-list title="调查问卷">
      <template #filter>
        <a-form-item name="title">
          <a-input v-model:value.trim="formState.title" placeholder="请输入问卷名称" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage">
            查询
          </a-button>
          <a-button @click="resetFormState">
            重置
          </a-button>
        </a-form-item>
      </template>
      <template #extra>
        <a-button type="primary" @click="onAdd">
          新增调查问卷
        </a-button>
      </template>
      <template #list>
        <a-table
          :loading="loading"
          :data-source="data.items"
          :pagination="stdPagination(data)"
          row-key="id"
          @change="setPage"
        >
          <a-table-column title="问卷名称" data-index="title" ellipsis />
          <a-table-column title="题目" width="150px">
            <template #default="{ record }">
              {{ record.subject_count }}题
            </template>
          </a-table-column>

          <a-table-column title="提交" width="150px" data-index="submit_number" align="right" />
          <a-table-column title="操作" width="150px">
            <template #default="{ record }">
              <a-button type="link" @click="onData(record)">
                数据
              </a-button>
              <a-button type="link" @click="onEdit(record)">
                编辑
              </a-button>
              <a-popconfirm
                placement="left"
                title="你确定要删除该数据么？"
                :disabled="!record.can_delete"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" class="danger" :disabled="!record.can_delete">
                  删除
                </a-button>
              </a-popconfirm>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
  </div>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { questionnaireApi } from '../../api'
import { message } from 'ant-design-vue'

const router = useRouter()
const { formState, resetFormState, onRestFormState } = useFormState({ title: '' })
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  questionnaireApi.paginator({
    filters: useTransformQuery(formState, { title: 'like' }),
    offset,
    limit
  })
)

onRestFormState(setPage)

const onAdd = () => {
  router.push({ name: 'questionnaire-add' })
}

const onEdit = ({ id }) => router.push({ name: 'questionnaire-edit', params: { id } })

const onData = ({ id }) => router.push({ name: 'questionnaire-data', params: { id } })

const handleDelete = async ({ id }) => {
  await questionnaireApi.delete(id)
  message.success('删除成功')
  refresh()
}
</script>
