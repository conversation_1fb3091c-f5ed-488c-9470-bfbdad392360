<template>
  <!-- 渲染题目 -->
  <a-card title="">
    <div class="flex flex-sb m-b-10">
      <div class="flex-1 fs-16 fw-bold">
        Q{{ index + 1 }}、<span class="color-primary m-r-10">{{ questionInfo.filter(item.option_type).label }}</span>
        {{ item.title }}（{{ item.is_required ? '' : '非' }}必填）
      </div>
      <div v-if="showBtn" class="cursor-pointer">
        <span
          v-if="questionInfo.filterSingleSelect(item.option_type)"
          class="m-l-10 color-primary"
          @click="onEmit('skip')"
        >跳题逻辑</span>
        <span class="m-l-10 color-primary" @click="onEmit('edit')">编辑</span>
        <span class="m-l-10 color-danger" @click="onEmit('delete')">删除</span>
      </div>
    </div>
    <template v-if="questionInfo.filterSelect(item.option_type)">
      <div v-for="(it, i) in item.options" :key="i" class="flex flex-cc color-65 m-b-10 fs-14">
        {{ String.fromCharCode(i + 65) }}、
        <div v-if="questionInfo.filterImg(item.option_type)" class="m-r-10 cursor-pointer">
          <img :src="it.url[0]" :width="50" :height="50" style="background: #f5f5f5; object-fit: contain" />
          <!-- <a-image :width="32" :height="32" :src="it.url[0]" /> -->
        </div>
        {{ it.content }}{{ it.is_freestyle ? '________' : '' }}
        <span v-if="it.jump_subject_code >= 0" class="color-danger m-l-10">跳转到Q{{ it.jump_subject_code + 1 }}</span>
      </div>
    </template>
    <a-rate v-else-if="item.option_type === questionInfo.score" :value="0" disabled />
  </a-card>
</template>
<script>
export default {
  name: 'RenderSubject'
}
</script>
<script setup>
import { cloneDeep } from 'lodash'
import { questionInfo } from '../../enums'

const emit = defineEmits(['edit', 'skip', 'delete'])
const props = defineProps({
  item: {
    type: Object,
    default: () => ({})
  },
  index: {
    type: Number,
    required: true
  },
  showBtn: {
    type: Boolean,
    default: false
  }
})

const onEmit = emitName => {
  emit(emitName)
}
</script>
