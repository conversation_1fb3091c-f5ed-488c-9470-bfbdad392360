<template>
  <!-- 专题配置 -->
  <div class="layout-config" @click="onEmit('click')">
    <div class="scroll-x">
      <!-- 头部 -->
      <div class="m-head">
        <div class="u-return" @click="router.back()">
          <img :src="assets.return" />返回控制台
        </div>
        <div class="m-btns">
          <a-button @click="router.back()">
            取消
          </a-button>
          <a-button type="primary" v-bind="{ disabled }" @click="onEmit('submit')">
            保存
          </a-button>
        </div>
      </div>
      <!-- 左侧菜单 -->
      <div class="m-sidebar">
        <div class="scroll">
          <slot name="left"></slot>
        </div>
      </div>
      <!-- 中间小程序 -->
      <div class="m-applets" @click="onClearSelectAll">
        <div class="scroll">
          <slot name="center"></slot>
        </div>
      </div>
      <!-- 右侧元素面板 -->
      <div class="m-element">
        <div class="scroll">
          <slot name="right"></slot>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LayoutConfig'
}
</script>
<script setup>
import { useRouter } from 'vue-router'
import assets from '../../assets.config'
import VueDraggable from 'vuedraggable'

const router = useRouter() // 路由操作

const emit = defineEmits(['click', 'submit'])
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  }
})

const onEmit = emitName => {
  emit(emitName)
}

</script>
<style scoped lang="less">
.layout-config {
  @primary: #1890ff;
  @padding: 20px;
  @margin-bottom: 20px;
  p {
    margin-bottom: 0;
  }
  .scroll() {
    .scroll {
      min-height: 600px;
    }
  }
  width: 100%;
  height: 100%;
  overflow-x: auto;
  .scroll-x {
    display: flex;
    min-width: 1280px;
    height: 100vh;
    padding-top: 60px;
    background: #f0f2f5;

    .m-head {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 60px;
      z-index: 9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 @padding;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      .u-return {
        display: flex;
        align-items: center;
        color: @primary;
        cursor: pointer;
        font-size: 16px;
        img {
          width: 16px;
          margin-right: 8px;
        }
      }
      .m-btns {
        :deep(.ant-btn) {
          margin-left: 10px;
        }
      }
    }
    .m-sidebar {
      .scroll();
      width: 200px;
      height: 100%;
      background: #fff;
      overflow-y: auto;
    }
    .m-applets {
      flex-grow: 1;
      width: 30%;
      height: 100%;
      overflow-y: auto;
      padding: 24px;
      background: #f0f2f5;
    }
    .m-element {
      .scroll();
      width: 360px;
      height: 100%;
      overflow-y: auto;
      background: #fff;
    }
  }
}
</style>
