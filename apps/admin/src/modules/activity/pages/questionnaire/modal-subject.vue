<template>
  <!-- 弹框表单 -->
  <a-modal :visible="visible" :title="modalTitle" width="600px" @cancel="onCancel">
    <a-space v-if="type === 'skip'" direction="vertical" class="w-fill m-b-20" :size="20">
      <a-textarea class="w-fill" disabled :value="formState.title" :rows="rows" />
      <div v-for="(item, index) in formState.options" :key="index" class="flex flex-sb">
        <a-input disabled :value="`${String.fromCharCode(index + 65)}、${item.content}`" class="w-200 m-r-10" />
        <a-select v-model:value="item.jump_subject_code" placeholder="不跳转，按顺序填写下一题" class="w-fill">
          <a-select-option :value="-1">
            不跳转，按顺序填写下一题
          </a-select-option>
          <template v-for="i in total">
            <a-select-option v-if="i - 1 > formIndex" :key="i" :value="i - 1">
              跳转到Q{{ i }}
            </a-select-option>
          </template>
        </a-select>
      </div>
    </a-space>
    <a-form v-else>
      <a-form-item label="题目" class="required">
        <div class="flex flex-cc">
          <a-textarea
            v-model:value="formState.title"
            placeholder="请输入题目名称，不超过100字"
            :maxlength="100"
            :rows="rows"
            style="min-height: 98px; max-height: 98px"
          />
          <a-checkbox v-model:checked="formState.is_required" class="m-l-10" />
        </div>
      </a-form-item>
      <template v-if="questionInfo.filterSelect(formState.option_type)">
        <a-form-item v-for="(item, index) in formState.options" :key="index" label="选项" class="required">
          <div class="flex flex-cc">
            <uc-upload
              v-if="questionInfo.filterImg(formState.option_type)"
              v-model:list="item.url"
              :max-length="1"
              size="small"
            />
            <a-input
              v-model:value="item.content"
              placeholder="请输入选项，不超过100字"
              :maxlength="100"
              :disabled="item.is_freestyle"
            />
            <a-button
              shape="circle"
              size="small"
              class="delete-btn"
              type="link"
              :disabled="formState.options.length <= maxLimit"
              @click="onDelete(index)"
            >
              <template #icon>
                <uc-ant-icon class="m-l-10" name="CloseCircleFilled" type="danger" />
              </template>
            </a-button>
          </div>
        </a-form-item>
      </template>
    </a-form>
    <template #footer>
      <div class="w-fill flex flex-sb flex-cc cursor-pointer">
        <div v-if="type !== 'skip' && questionInfo.filterSelect(formState.option_type)" class="w-fill flex">
          <span class="color-primary m-r-10" @click="onAdd">单个选项</span><span
            :class="formState.options.some(item => item.is_freestyle) ? 'color-45' : 'color-primary'"
            @click="onAdd('other')"
          >其他选项</span>
        </div>
        <div class="w-fill">
          <a-button @click="onCancel">
            取消
          </a-button>
          <a-button type="primary" @click="onOk">
            确定
          </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: 'ModalSubject'
}
</script>
<script setup>
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { questionInfo } from '../../enums'
import { computed } from '@vue/runtime-core'

const emit = defineEmits(['cancel', 'ok'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    required: true // add:新增,edit:编辑,skip:跳题逻辑
  },
  form: {
    type: Object,
    default: () => ({})
  },
  formIndex: {
    type: Number,
    default: 0
  },
  total: {
    type: Number,
    default: 0
  }
})

const rows = 4

const { formState, setFormState, setFormRules, validateForm } = useFormState({ title: '' })

const validatorOptions = (rule, value) => {
  const { option_type } = formState.value
  if (questionInfo.filterSelect(option_type)) {
    if (value.some(item => !item.content)) return Promise.reject('请输入选项')
    if (questionInfo.filterImg(option_type) && value.some(item => !item.url.length))
      return Promise.reject('请上传选项图')
  }
  return Promise.resolve()
}
setFormRules({
  title: { required: true, message: '请输入题目名称' },
  options: { validator: validatorOptions }
})

const modalTitle = computed(() => {
  switch (props.type) {
    case 'add':
    case 'edit':
      return questionInfo.filter(props.form.option_type)?.label
    case 'skip':
      return '跳转逻辑'
  }
})

const createOptions = type => {
  switch (type) {
    case questionInfo.score:
      return Array.from({ length: 5 }, (v, k) => ({ content: k + 1, is_freestyle: 0, jump_subject_code: -1, url: [] }))
    case questionInfo.question_answer:
      return [{ content: '其它', is_freestyle: 1, jump_subject_code: -1, url: [] }]
    default:
      return Array.from({ length: 2 }, () => ({ content: '', is_freestyle: 0, jump_subject_code: -1, url: [] }))
  }
}

const initData = () => {
  const form = cloneDeep(props.form)
  switch (props.type) {
    case 'add':
      Object.assign(form, {
        title: '',
        is_required: true,
        options: createOptions(form.option_type)
      })
      break
  }
  setFormState(form)
}

watch(
  () => props.visible,
  value => {
    value && initData()
  }
)

const onAdd = type => {
  const isOther = type === 'other'
  const item = {
    content: isOther ? '其它' : '',
    is_freestyle: +isOther,
    url: [],
    jump_subject_code: -1
  }
  if (formState.value.options.some(item => item.is_freestyle)) {
    if (isOther) return
    formState.value.options.splice(formState.value.options.length - 1, 0, item)
  } else {
    formState.value.options.push(item)
  }
}
const maxLimit = 2 // 最大限制数
const onDelete = i => {
  formState.value.options.length > maxLimit && formState.value.options.splice(i, 1)
}
// 确定
const onOk = async () => {
  if (!(await validateForm())) return
  emit('ok', cloneDeep(formState.value))
}
// 取消
const onCancel = () => {
  emit('cancel')
}
</script>
<style lang="less" scoped>
:deep(.ant-upload-picture-card-wrapper) {
  width: 100% !important;
  height: auto !important;
  .ant-upload-select-picture-card {
    margin-bottom: 0 !important;
  }
}
</style>
