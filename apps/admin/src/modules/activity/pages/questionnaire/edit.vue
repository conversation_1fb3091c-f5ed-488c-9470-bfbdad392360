<template>
  <layout-config @submit="onSubmit" @click="selectIndex = -1">
    <template #left>
      <a-space direction="vertical" :size="20" class="p-t-26 p-l-26">
        <a-descriptions v-for="(item, index) in sidebar" :key="index" :title="item.label" :column="2">
          <a-descriptions-item v-for="(it, i) in item.children" :key="i" class="cursor-pointer">
            <div class="color-65" @click="onClickSidebar(it, item)">
              <span :class="{ 'color-primary': item.value === 'personInfo' && it.state }">{{ it.label }}</span>
            </div>
          </a-descriptions-item>
        </a-descriptions>
      </a-space>
    </template>
    <template #center>
      <a-card v-if="infoShow" title="" class="m-b-20">
        <h3>个人信息</h3>
        <div class="flex color-45">
          <template v-for="(item, index) in personInfo" :key="index">
            <span v-if="item.state" class="m-r-20">{{ item.label }}</span>
          </template>
        </div>
      </a-card>
      <vue-draggable v-model="formState.subjects" item-key="id" @change="onClearSkip">
        <template #item="{ element, index }">
          <div
            :class="index === selectIndex ? 'cursor-move select' : 'cursor-pointer'"
            class="m-b-20 border-2"
            @click.stop="onClickItem(index)"
            @dragstart="onDragstart($event, index)"
          >
            <render-subject
              :item="element"
              :index="index"
              :show-btn="index === selectIndex"
              @skip="onEdit(element, 'skip', index)"
              @edit="onEdit(element, 'edit', index)"
              @delete="onDelete(index)"
            />
          </div>
        </template>
      </vue-draggable>
    </template>
    <template #right>
      <a-space direction="vertical" :size="20" class="w-fill p-14">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称，不超过100字" :maxlength="100" />
        <a-input
          v-model:value.trim="formState.page_setting.theme_color"
          addon-before="主题色"
          placeholder="如#ffffff"
          :maxlength="7"
        />
        <a-input
          v-model:value.trim="formState.page_setting.sub"
          addon-before="辅助色"
          placeholder="如#ffffff"
          :maxlength="7"
        />
        <a-input
          v-model:value.trim="formState.page_setting.bg_color"
          addon-before="背景色"
          placeholder="如#ffffff"
          :maxlength="7"
        />
        <a-input
          v-model:value.trim="formState.page_setting.title"
          addon-before="标题色"
          placeholder="如#ffffff"
          :maxlength="7"
        />
        <div class="flex">
          <uc-upload v-model:list="formState.page_setting.bg_url" :max-length="1" show-label label-text="问卷背景" />
          <uc-upload v-model:list="formState.page_setting.poster" :max-length="1" show-label label-text="问卷海报" />
          <uc-upload v-model:list="formState.page_setting.pop_url" :max-length="1" show-label label-text="提交成功" />
        </div>
      </a-space>
    </template>
  </layout-config>
  <modal-subject
    :visible="modalVisible"
    :type="modalHandleType"
    :form="modalForm"
    :form-index="modalFormIndex"
    :total="formState.subjects.length"
    @cancel="setModalVisible(false)"
    @ok="onOk"
  />
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { validateColor } from '@/utils/validates'
import VueDraggable from 'vuedraggable'
import { message } from 'ant-design-vue'
import { questionInfo } from '../../enums'
import { useTransformQuestion } from '../../useTransform'
import { questionnaireApi } from '../../api'
import LayoutConfig from './layout-config'
import ModalSubject from './modal-subject'
import renderSubject from './render-subject'
import { generateRandom } from '@/utils/functions'
import { computed } from '@vue/reactivity'

const { transformShow, transformRequest } = useTransformQuestion()

const sidebar = ref([
  {
    label: '个人信息',
    value: 'personInfo',
    children: questionInfo.groupPersonInfo()
  },
  {
    label: '选择题型',
    value: 'type',
    children: questionInfo.groupType()
  },
  {
    label: '其他题型',
    value: 'other',
    children: questionInfo.groupOther()
  }
])
const personInfo = computed(() => sidebar.value[0].children)

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: '',
  page_setting: {
    bg_color: '',
    theme_color: '',
    bg_url: [],
    poster: [],
    pop_url: []
  },
  subjects: []
})
const validatorPageSetting = (rule, value) => {
  if (!validateColor(value.bg_color)) {
    return Promise.reject('请输入正确背景色')
  } else if (!validateColor(value.sub)) {
    return Promise.reject('请输入正确辅助色')
  } else if (!validateColor(value.theme_color)) {
    return Promise.reject('请输入正确主题色')
  } else if (!validateColor(value.title)) {
    return Promise.reject('请输入正确标题色')
  } else if (!value.bg_url.length) {
    return Promise.reject('请上传问卷背景')
  } else if (!value.poster.length) {
    return Promise.reject('请上传问卷海报')
  } else if (!value.pop_url.length) {
    return Promise.reject('请上传提交成功')
  }
  return Promise.resolve()
}
const validatorSubjects = (rule, value) => {
  if (!value.length) {
    return Promise.reject('请选择题型')
  }
  return Promise.resolve()
}

setFormRules({
  title: { required: true, message: '请输入问卷名称' },
  page_setting: { validator: validatorPageSetting },
  subjects: { validator: validatorSubjects }
})

const { id } = useRoute().params
const router = useRouter()

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  questionnaireApi
    .get(id, {})
    .then(res => {
      const { data, personInfo: info } = transformShow(res)
      personInfo.value.forEach(item => {
        item.state = info.includes(item.value)
      })
      setFormState(data)
    })
    .finally(hideLoading)
}

const modalForm = ref({})
const modalVisible = ref(false)
const modalHandleType = ref('add') // add:新增,edit:编辑,skip:跳题逻辑
const modalFormIndex = ref(-1)

const setModalVisible = visible => {
  modalVisible.value = visible
}
// 编辑
const onEdit = (item, type, i = -1) => {
  modalForm.value = item
  modalHandleType.value = type
  modalFormIndex.value = i
  setModalVisible(true)
}
// 删除
const onDelete = index => {
  formState.value.subjects.splice(index, 1)
  onClearSkip()
}

const infoShow = computed(() => personInfo.value.some(i => i.state))

// 左侧菜单
const onClickSidebar = (it, item) => {
  switch (item.value) {
    case 'personInfo':
      it.state = !it.state
      break
    default:
      onEdit({ option_type: it.value, code: formState.value.subjects.length }, 'add')
      break
  }
}

// 模态框提交
const onOk = form => {
  if (formState.value.subjects.some((item, i) => (i === modalFormIndex.value ? false : item.title === form.title))) {
    message.error('题目名称不可重复')
    return
  }
  switch (modalHandleType.value) {
    case 'add':
      formState.value.subjects.push(form)
      break
    case 'edit':
    case 'skip':
      formState.value.subjects[modalFormIndex.value] = form
      break
  }
  setModalVisible(false)
}

const selectIndex = ref(-1)

const onClickItem = index => {
  if (selectIndex.value === index) return
  selectIndex.value = index
}
const onDragstart = (e, index) => {
  if (selectIndex.value === index) return
  e.stopPropagation()
  e.preventDefault()
}
const onClearSkip = () => {
  formState.value.subjects.forEach((item, i) => {
    item.code = i
    item.options.forEach(it => {
      it.jump_subject_code = -1
    })
  })
  message.error('拖拽清理所有跳转')
}

const onSubmit = async () => {
  if (!(await validateForm())) return
  const params = transformRequest(formState.value, personInfo.value)
  id ? await questionnaireApi.replace(id, params) : await questionnaireApi.create(params)

  message.success('操作成功')
  router.back()
}
</script>
<style lang="less" scoped>
.select {
  border-color: @color-primary !important;
}
</style>
