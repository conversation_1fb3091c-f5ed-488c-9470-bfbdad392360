<template>
  <!-- 问卷数据统计 -->
  <a-space direction="vertical" :size="20" class="w-fill">
    <a-card title="参与名单">
      <template #extra>
        <a-button type="primary" @click="exportReport">
          下载问卷报告
        </a-button>
      </template>
      <a-table
        :loading="loading"
        :data-source="data.items"
        :pagination="stdPagination(data)"
        row-key="id"
        @change="setPage"
      >
        <a-table-column title="用户昵称" ellipsis>
          <template #default="{ record }">
            {{ record.user?.nickname }}
          </template>
        </a-table-column>
        <a-table-column title="手机号码">
          <template #default="{ record }">
            {{ record.user?.phone_number ? $formatters.numberEncryption(record.user?.phone_number) : '' }}
          </template>
        </a-table-column>
        <a-table-column title="性别">
          <template #default="{ record }">
            {{ record.sex || '-' }}
          </template>
        </a-table-column>
        <a-table-column title="年龄">
          <template #default="{ record }">
            {{ record.age || '-' }}
          </template>
        </a-table-column>
        <a-table-column title="城市" ellipsis>
          <template #default="{ record }">
            {{ record.city || '-' }}
          </template>
        </a-table-column>
        <a-table-column title="文化">
          <template #default="{ record }">
            {{ record.culture || '-' }}
          </template>
        </a-table-column>
        <a-table-column title="提交时间" width="200px" data-index="created_at" />
      </a-table>
    </a-card>
    <a-card v-for="(item, index) in subjects" :key="index">
      <div class="flex-1 fs-16 fw-bold m-b-20">
        Q{{ index + 1 }}、<span class="color-primary m-r-10">{{ questionInfo.filter(item.option_type).label }}</span>
        {{ item.title }}（{{ item.is_required ? '' : '非' }}必填）
      </div>
      <a-table
        v-if="item.option_type !== questionInfo.question_answer"
        :data-source="item.options"
        :pagination="false"
        row-key="id"
      >
        <a-table-column title="选项" ellipsis>
          <template #default="{ record, index: i }">
            <template v-if="item.option_type !== questionInfo.score">
              <div class="flex flex-cc m-b-10 fs-14">
                {{ String.fromCharCode(i + 65) }}、
                <div v-if="questionInfo.filterImg(item.option_type)" class="m-r-10 cursor-pointer hide">
                  <a-image :width="32" :height="32" :src="record.url[0]" />
                </div>
                {{ record.content }}{{ record.is_freestyle ? '________' : '' }}
              </div>
            </template>
            <a-rate
              v-else-if="item.option_type === questionInfo.score"
              :value="record.content"
              disabled
              class="color-danger"
            />
          </template>
        </a-table-column>
        <a-table-column title="占比" width="300px">
          <template #default="{ record }">
            <a-progress class="w-200" :percent="record.percent" status="normal" />
          </template>
        </a-table-column>
        <a-table-column title="小计" width="120px" data-index="count" />
      </a-table>
      <a-table
        v-else
        :loading="item.table.loading"
        :data-source="item.table.data.items"
        :pagination="stdPagination(item.table.data)"
        row-key="id"
        @change="item.table.setPage"
      >
        <a-table-column title="用户昵称" width="200px" ellipsis>
          <template #default="{ record }">
            {{ record.user?.nickname }}
          </template>
        </a-table-column>
        <a-table-column title="手机号码" width="150px">
          <template #default="{ record }">
            {{ record.user?.phone_number ? $formatters.numberEncryption(record.user?.phone_number) : '' }}
          </template>
        </a-table-column>
        <a-table-column title="答题内容" ellipsis>
          <template #default="{ record }">
            <a-tooltip :title="filterSubjectCont(record, item)">
              <span class="hand">{{ filterSubjectCont(record, item) }}</span>
            </a-tooltip>
          </template>
        </a-table-column>
      </a-table>
    </a-card>
  </a-space>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { questionnaireApi, questionnaireListApi, questionnaireSubjectApi, questionnaireDownloadApi } from '../../api'
import { questionInfo } from '../../enums'
import { useTransformQuesStatis } from '../../useTransform'
import { message } from 'ant-design-vue'

const { id } = useRoute().params
const { transformShow } = useTransformQuesStatis({ questionnaire_id: id })

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  questionnaireListApi.paginator({
    filters: { questionnaire_id: id },
    sorts: ['-created_at'],
    relations: ['user'],
    offset,
    limit
  })
)
setPage()

const subjects = ref([]) // 页面显示题目列表
const subjectStatis = questionnaireSubjectApi.list({ filters: { questionnaire_id: id } }) // 选项统计
const allSubject = questionnaireApi.get(id, {}) // 该问券所有题目
Promise.all([subjectStatis, allSubject]).then(data => {
  subjects.value = transformShow(data).subjects
})

// 过滤内容
const filterSubjectCont = computed(() => (record, item) => record.param.find(el => el.subject_id === item.id)?.content)

const exportReport = () => {
  if (data.value.items.length === 0) {
    message.warning('该问卷尚未收集数据，请先发布问卷进行收集')
    return
  }
  window.open(questionnaireDownloadApi(id))
}
</script>
