<template>
  <uc-layout-form class="m-edit" :is-save="!isShowStatus" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动时间：" name="rangeTime" class="required range-time">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              :disabled="isShowStatus || ispartShow"
              class="w-240"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              :disabled-date="ispartShow ? disabledEndTimeBeforeNow : disabledEndTime"
              :disabled="isShowStatus"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
              class="w-240"
            />
          </a-form-item>
          <a-form-item label="活动名称" name="title" class="required">
            <a-input
              v-model:value.trim="formState.title"
              class="w-500"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isShowStatus"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              class="w-500"
              placeholder="请输入活动标签，不超过10字"
              :max-length="10"
              :disabled="isShowStatus"
            />
          </a-form-item>
          <a-form-item label="邀请机制" name="invite_mode" class="required">
            <a-select
              v-model:value.trim="formState.invite_mode"
              class="w-500"
              :disabled="isShowStatus || ispartShow"
              @change="onTypeChange"
            >
              <a-select-option value="loop"> 循环邀请 (每邀请N人时可获得奖励,可配置邀请上限) </a-select-option>
              <a-select-option value="ladder"> 阶梯邀请 (邀请人数达到某个阶段时,可获得该阶段的奖励) </a-select-option>
            </a-select>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <!-- 循环邀请 or 阶梯邀请-->
        <a-card :title="loopOrladder == 'ladder' ? '阶梯邀请' : '循环邀请'">
          <a-form-item label="兑奖期限" name="limit_day" class="required">
            <a-input-number
              v-model:value.trim="formState.limit_day"
              class="w-500"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
              :disabled="isShowStatus"
              :min="0"
            />
          </a-form-item>
          <!-- 循环邀请 -->
          <template v-if="formState.invite_mode == 'loop'">
            <template v-for="(item1, index1) in formState.rules" :key="index1">
              <template v-if="!index1">
                <a-form-item label="邀请上限" name="title" class="required">
                  <a-input-number
                    v-model:value.trim="item1.invite_limit"
                    class="w-500"
                    placeholder="请输入活动期间单个用户邀请上限，0表示不限制"
                    :disabled="isShowStatus"
                    :min="0"
                  />
                </a-form-item>
                <a-form-item label="邀请人数" name="title" class="required">
                  <a-input-number
                    v-model:value.trim="item1.invite_num"
                    class="w-500"
                    placeholder="请输入邀请人数, 每邀请达到该人数就会得到奖励"
                    :disabled="isShowStatus"
                    :min="1"
                  />
                </a-form-item>
              </template>
              <a-form-item label="邀请奖励" name="title" class="required">
                <div v-for="(item, index) in item1.award_settings" :key="index" class="m-b-10">
                  <a-space :size="10">
                    <a-select
                      v-model:value="item.type"
                      placeholder="礼品类型"
                      allow-clear
                      :options="giftType.options()"
                      class="w-100"
                      :disabled="isShowStatus || (ispartShow && item.disabled)"
                      @change="onChangeGiftType(item)"
                    />
                    <a-input-number
                      v-if="item.type === giftType.credit"
                      v-model:value="item.quantity"
                      class="w-300"
                      :placeholder="giftType.getPlaceholder(item.type)"
                      :disabled="isShowStatus || (ispartShow && item.disabled)"
                    />
                    <a-select
                      v-else
                      v-model:value="item.option_id"
                      class="w-300"
                      :placeholder="giftType.getPlaceholder(item.type)"
                      :options="item.options"
                      allow-clear
                      show-search
                      :show-arrow="false"
                      :filter-option="false"
                      :not-found-content="null"
                      :disabled="isShowStatus || (ispartShow && item.disabled)"
                      @focus="handleShowGifts(item)"
                      @search="handleSearchGifts($event, item)"
                    />
                    <a-input-number
                      v-model:value.trim="item.stock"
                      min="0"
                      placeholder="活动数量"
                      :disabled="isShowStatus"
                      class="w-90"
                    />
                    <a-button
                      shape="circle"
                      size="small"
                      class="delete-btn"
                      type="link"
                      :disabled="item.disabled || item1.award_settings.length == 1 || isShowStatus"
                    >
                      <template #icon>
                        <uc-ant-icon name="CloseCircleFilled" type="danger" @click="deleteLoopFloor(item1, index)" />
                      </template>
                    </a-button>
                  </a-space>
                </div>
                <a-button type="link" class="p-0" :disabled="isShowStatus" @click="addLoopFloor(item1)">
                  添加奖励层级
                </a-button>
              </a-form-item>
            </template>
          </template>
          <!-- 阶梯邀请 -->
          <template v-else>
            <a-form-item
              v-for="(item1, index1) in formState.rules"
              :key="index1"
              :label="`邀请奖励${index1 + 1}`"
              name="title"
              class="required"
            >
              <a-space :size="10" class="m-b-10">
                <a-input-number
                  v-model:value.trim="item1.invite_num"
                  class="w-500"
                  placeholder="请输入邀请人数, 每邀请达到该人数就会得到奖励"
                  :disabled="isShowStatus"
                  :min="1"
                />
                <a-button type="link" class="p-0" :disabled="isShowStatus" @click="addLadderReward"> 添加 </a-button>
                <a-button
                  type="text"
                  class="p-0"
                  danger
                  :disabled="formState.rules.length == 1 || isShowStatus"
                  @click="deleteLadderReward(index1)"
                >
                  删除
                </a-button>
              </a-space>
              <div v-for="(item, index) in item1.award_settings" :key="index" class="m-b-10">
                <a-space :size="10">
                  <a-select
                    v-model:value="item.type"
                    placeholder="礼品类型"
                    allow-clear
                    :options="giftType.options()"
                    class="w-100"
                    :disabled="isShowStatus || (ispartShow && item.disabled)"
                    @change="onChangeGiftType(item)"
                  />
                  <a-input-number
                    v-if="item.type === giftType.credit"
                    v-model:value="item.quantity"
                    class="w-300"
                    :placeholder="giftType.getPlaceholder(item.type)"
                    :disabled="isShowStatus || (ispartShow && item.disabled)"
                  />
                  <a-select
                    v-else
                    v-model:value="item.option_id"
                    class="w-300"
                    :placeholder="giftType.getPlaceholder(item.type)"
                    :options="item.options"
                    allow-clear
                    show-search
                    :show-arrow="false"
                    :filter-option="false"
                    :not-found-content="null"
                    :disabled="isShowStatus || (ispartShow && item.disabled)"
                    @focus="handleShowGifts(item)"
                    @search="handleSearchGifts($event, item)"
                  />
                  <a-input-number
                    v-model:value.trim="item.stock"
                    placeholder="活动数量"
                    :disabled="isShowStatus"
                    class="w-90"
                  />
                  <a-button
                    shape="circle"
                    size="small"
                    class="delete-btn"
                    type="link"
                    :disabled="item.disabled || formState.rules[index1].award_settings.length == 1 || isShowStatus"
                  >
                    <template #icon>
                      <uc-ant-icon name="CloseCircleFilled" type="danger" @click="deleteLoopFloor(item1, index)" />
                    </template>
                  </a-button>
                </a-space>
              </div>
              <a-button type="link" class="p-0" :disabled="isShowStatus" @click="addLoopFloor(item1)">
                添加奖励层级
              </a-button>
            </a-form-item>
          </template>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="页面配置" width="100%">
          <a-form-item label="皮肤模式" class="required">
            <a-select v-model:value="formState.page_setting.skin_mode" class="w-310 m-r-10" :disabled="isShowStatus">
              <a-select-option value="default"> 系统模版-默认皮肤 </a-select-option>
            </a-select>
            <a-button class="p-lr-0 relative" type="link">
              预览效果图
              <div class="w-fill h-fill hide absolute top-0">
                <a-image class="opacity-0" :src="assets.invitePreviewUrl" />
              </div>
            </a-button>
          </a-form-item>
          <a-form-item label="颜色配置" name="color-config" class="required">
            <a-space wrap>
              <a-input
                v-model:value="formState.page_setting.color.subject"
                class="w-160"
                placeholder="主题色，如#000000"
                :disabled="isShowStatus"
              />
              <a-input
                v-model:value="formState.page_setting.color.sub"
                class="w-160"
                placeholder="辅助色，如#000000"
                :disabled="isShowStatus"
              />
              <a-input
                v-model:value="formState.page_setting.color.bg"
                class="w-160"
                placeholder="背景色，如#000000"
                :disabled="isShowStatus"
              />
              <a-input
                v-model:value="formState.page_setting.color.title"
                class="w-160"
                placeholder="标题色，如#000000"
                :disabled="isShowStatus"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" name="title" class="required">
            <a-space>
              <uc-upload
                :list="formState.page_setting.page.activity_bg ? [formState.page_setting.page.activity_bg] : []"
                upload-text=" "
                :show-label="true"
                label-text="活动背景"
                :max-length="1"
                :disabled="isShowStatus"
                @update:list="data => (formState.page_setting.page.activity_bg = data[0])"
              />
              <uc-upload
                :list="formState.page_setting.page.activity_text ? [formState.page_setting.page.activity_text] : []"
                upload-text=" "
                :show-label="true"
                label-text="活动文案"
                :max-length="1"
                :disabled="isShowStatus"
                @update:list="data => (formState.page_setting.page.activity_text = data[0])"
              />
              <uc-upload
                :list="formState.page_setting.page.activity_step ? [formState.page_setting.page.activity_step] : []"
                upload-text=" "
                :show-label="true"
                label-text="邀请步骤"
                :max-length="1"
                :disabled="isShowStatus"
                @update:list="data => (formState.page_setting.page.activity_step = data[0])"
              />
            </a-space>
          </a-form-item>
          <!-- <a-form-item label="弹窗配置" name="title" class="required">
        <a-space>
          <uc-upload
            :list="formState.page_setting.popup.prepare ? [formState.page_setting.popup.prepare] : []"
            upload-text=" "
            :show-label="true"
            label-text="未开始"
            :max-length="1"
            :disabled="isShowStatus"
            @update:list="data => (formState.page_setting.popup.prepare = data[0])"
          />
          <uc-upload
            :list="formState.page_setting.popup.end ? [formState.page_setting.popup.end] : []"
            upload-text=" "
            :show-label="true"
            label-text="已结束"
            :max-length="1"
            :disabled="isShowStatus"
            @update:list="data => (formState.page_setting.popup.end = data[0])"
          />
        </a-space>
      </a-form-item> -->
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="分享配置" width="100%" class="h-fill">
          <a-form-item label="分享海报" class="required flex">
            <uc-upload
              :list="formState.share_setting.friend_poster ? [formState.share_setting.friend_poster] : []"
              :max-length="1"
              upload-text=" "
              show-label
              label-text="会话海报"
              :disabled="isShowStatus"
              @update:list="data => (formState.share_setting.friend_poster = data[0])"
            />
            <uc-upload
              :list="formState.share_setting.friend_circle_poster ? [formState.share_setting.friend_circle_poster] : []"
              :max-length="1"
              upload-text=" "
              show-label
              label-text="本地海报"
              :disabled="isShowStatus"
              @update:list="data => (formState.share_setting.friend_circle_poster = data[0])"
            />
          </a-form-item>
          <a-form-item label="分享方案" name="title" class="required">
            <a-input
              v-model:value.trim="formState.share_setting.share_text"
              class="w-500"
              placeholder="请输入分享方案，不超过20字"
              :max-length="20"
              :disabled="isShowStatus"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>

    <uc-rich-text v-model="formState.desc" v-model:isValidator="isValidator" :disabled="isShowStatus" />
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { colorVerify } from '@/utils/index'
import { cloneDeep, debounce } from 'lodash'
import { giftType } from '../../enums'
import { inviteActivitiesApi } from '../../api'
import { usePrizeAwardUpdate, useTransformPrize } from '../../usePrizeAward'
import assets from '../../assets.config'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
const { transformPrizesRequest } = useTransformPrize()

const isValidator = ref(false) // 校验活动内容
const router = useRouter()
const { id } = useRoute().params

// 部分禁用和全部禁用
const ispartShow = ref(false)
const isShowStatus = ref(false)
const forbidden = status => {
  switch (status) {
    case 'normal': // 进行中部分禁用
      ispartShow.value = true
      break
    case 'ended': // 结束全部禁用
      isShowStatus.value = true
      break
  }
}

// 初始化数据 --邀请
const rules = {
  invite_num: undefined,
  award_settings: [onPrizeDefault({ type: 'credit' })]
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: null,
  end_time: null,
  title: undefined,
  tag: undefined,
  invite_mode: 'loop',
  limit_day: undefined,
  rules: [cloneDeep({ ...rules, invite_limit: undefined })],
  page_setting: {
    skin_mode: 'default',
    color: {
      subject: undefined,
      sub: undefined,
      bg: undefined,
      title: undefined
    },
    page: {
      activity_bg: undefined,
      activity_text: undefined,
      activity_step: undefined
    }
    // popup: {
    //   prepare: undefined,
    //   end: undefined
    // }
  },
  share_setting: {
    friend_poster: undefined,
    friend_circle_poster: undefined,
    share_text: undefined
  }
})

const { disabledStartTime, disabledEndTime, disabledEndTimeBeforeNow } = useDisabledDate(formState)

// 编辑获取数据
if (id) {
  const hideLoading = message.loading('正在加载数据')
  inviteActivitiesApi
    .get(id, { relations: ['rules'] })
    .then(async res => {
      const showPrizePrm = []
      res.rules.forEach(rule => {
        rule.award_settings.forEach(prize => {
          showPrizePrm.push(handleAward(prize, { id: prize.option_id }))
        })
      })

      await Promise.all(showPrizePrm)

      // 禁用状态
      forbidden(res.status)
      addField(res.rules, [], res.status)
      setFormState(res)
    })
    .finally(hideLoading)
}

// 添加禁用-礼品-上次数量值
const addField = (rules, allGiftOptions, status) => {
  //数组-阶梯邀请
  rules.map(item => {
    item.award_settings.map(item2 => {
      if (status == 'normal') {
        // 活动进行中才禁用
        item2.disabled = true
      }
    })
  })
}

// 切换时清除数据
const onTypeChange = type => {
  switch (type) {
    case 'loop': // 清除阶梯有礼
      formState.value.rules = cloneDeep([{ ...rules, invite_limit: undefined }])
      break
    case 'ladder': // 清除邀请有礼
      formState.value.rules = cloneDeep([rules])
      break
  }
}

// 自定义页面校验
const validatorPagesetting = (_, value) => {
  const { color, page } = value
  if (!value.skin_mode) return Promise.reject('请选择皮肤模式')
  if (!colorVerify(color.subject)) return Promise.reject('请输入主题色(以#开头+6位数字字母组合)')
  if (!colorVerify(color.sub)) return Promise.reject('请输入辅助色(以#开头+6位数字字母组合)')
  if (!colorVerify(color.bg)) return Promise.reject('请输入背景色(以#开头+6位数字字母组合)')
  if (!colorVerify(color.title)) return Promise.reject('请输入标题色(以#开头+6位数字字母组合)')
  if (!page.activity_bg) return Promise.reject('请上传活动背景图册')
  if (!page.activity_text) return Promise.reject('请上传活动文案图册')
  if (!page.activity_step) return Promise.reject('请上传邀请步骤图册')
  // if (!popup.prepare) return Promise.reject('请上传未开始图册')
  // if (!popup.end) return Promise.reject('请上传已结束图册')
  return Promise.resolve()
}

const validatorShare = (_, value) => {
  if (!value.friend_poster) return Promise.reject('请上传会话海报图')
  if (!value.friend_circle_poster) return Promise.reject('请上传本地海报图')
  if (!value.share_text) return Promise.reject('请输入分享文案')
  return Promise.resolve()
}

const validatorRules = (_, value) => {
  // 阶梯邀请
  let tip
  for (let i = 0; i < value.length; i++) {
    const { invite_num, award_settings } = value[i]
    if (!invite_num && formState.value.invite_mode === 'loop') {
      tip = `邀请奖励${i + 1}：请输入邀请上限`
      break
    }

    for (let j = 0; j < award_settings.length; j++) {
      const { type, quantity, option_id, stock } = award_settings[j]
      let currentSort = j + 1
      if (!type) {
        tip = `邀请奖励${currentSort}：请选择礼品类型`
        break
      } else {
        if (type === giftType.credit) {
          if (!quantity) {
            tip = `邀请奖励${currentSort}：请输入积分值`
            break
          }
        } else {
          if (!option_id) {
            tip = `邀请奖励${currentSort}：请选择奖励礼品`
            break
          }
        }
        if (!stock) {
          tip = `邀请奖励${currentSort}：请输入活动数量`
          break
        }
      }
    }
  }

  if (tip) return Promise.reject(tip)
  return Promise.resolve()
}

setFormRules({
  start_time: { required: true, message: '请选择开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  invite_mode: { required: true, message: '请选择邀请机制' },
  limit_day: { required: true, message: '请输入活动结束后领奖截止天数' },
  rules: { validator: validatorRules, trigger: 'blur' },
  page_setting: { validator: validatorPagesetting, trigger: 'blur' },
  share_setting: { validator: validatorShare, trigger: 'blur' }
})

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) return

  let params = cloneDeep(formState.value)
  const { rules } = params
  // 阶梯邀请
  rules.forEach(item => {
    transformPrizesRequest(item)
  })
  id ? await inviteActivitiesApi.replace(id, params) : await inviteActivitiesApi.create(params)
  message.success('操作完成')
  router.back()
}, 500)

// 循环邀请-阶梯邀请-新增
const addLoopFloor = item => {
  if (item.award_settings.length >= 5) {
    message.error('操作失败：最多可添加5个奖励层级')
    return
  } else {
    item.award_settings.push(onPrizeDefault({ type: 'credit' }))
  }
}

// 循环邀请-阶梯邀请-删除
const deleteLoopFloor = (item, index) => item.award_settings.splice(index, 1)

// 阶梯邀请-删除
const deleteLadderReward = index => formState.value.rules.splice(index, 1)

// 阶梯邀请-新增
const addLadderReward = () => {
  if (formState.value.rules.length >= 10) {
    message.error('操作失败：最多可添加10个邀请阶梯')
    return
  } else {
    formState.value.rules.push({
      award_settings: [onPrizeDefault({ type: 'credit' })]
    })
  }
}
</script>

<style scoped lang="less">
.m-edit {
  .range-time {
    .separator {
      .inline-block();
      width: 20px;
      text-align: center;
    }
  }

  .flex {
    :deep(.ant-form-item-control-input-content) {
      display: flex;
    }
  }

  .cleanup {
    border: none;
    background: none;
    box-shadow: none;
  }

  .delete-btn {
    padding-top: 2px;
    border: none;

    :deep(.anticon) {
      font-size: 20px;
    }
  }

  .delete-btn:disabled {
    :deep(.anticon) {
      color: #d9d9d9 !important;
    }
  }
}
</style>
