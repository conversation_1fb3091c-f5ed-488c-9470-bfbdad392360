<template>
  <a-modal v-model:visible="visible" width="800px" :title="modelTitle + ' - 邀请记录'" :footer="null">
    <a-table :data-source="dataInfo.items" row-key="id" :loading="loadingInfo" :pagination="stdPaginationSimple(dataInfo)" @change="setPageInfo">
      <a-table-column title="好友昵称">
        <template #default="{ record }">
          {{ record?.invitee?.nickname }}
        </template>
      </a-table-column>
      <a-table-column title="好友手机号码">
        <template #default="{ record }">
          {{ $formatters.numberEncryption($formatters.transformPhone(record.invitee?.phone_number)) }}
        </template>
      </a-table-column>
      <a-table-column title="时间" data-index="created_at" width="200px" />
    </a-table>
  </a-modal>

  <a-card title="活动趋势">
    <div id="container"></div>
  </a-card>

  <uc-layout-list title="邀请记录">
    <template #extra>
      <a-input-group compact>
        <a-select v-model:value="conditionKey" class="w-120" :options="UserModeList.options()" />
        <a-input v-model:value.trim="conditionValue" placeholder="请输入关键词" class="w-420" @blur="handleSearch" />
      </a-input-group>
    </template>
    <template #list>
      <a-table :data-source="data.items ?? []" row-key="inviter_id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="用户名称">
          <template #default="{ record }">
            {{ record?.inviter?.nickname }}
          </template>
        </a-table-column>
        <a-table-column title="手机号码">
          <template #default="{ record }">
            {{ $formatters.numberEncryption($formatters.transformPhone(record?.inviter?.phone_number)) }}
          </template>
        </a-table-column>
        <a-table-column title="已邀请" data-index="invited" />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-button type="link" @click="onRecord(record)">
              记录
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref, onUnmounted, watch, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { Chart } from '@antv/g2'
import { debounce } from 'lodash-es'
import { inviteRecordsInfoApi,inviteActivitiesChart,inviteActivitiesStats } from '../../api'
import { UserModeList } from '../../enums'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { message } from 'ant-design-vue'

const router = useRouter()
const { id } = useRoute().params
if (!id) router.back()

onUnmounted(()=>{
 if(chart) chart.destroy()
})

// 表格查询--邀请记录
const hideLoading = message.loading('正在加载数据...')
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  inviteActivitiesStats(id).get({
    ...getValue(),
    offset,
    limit
  }).finally(hideLoading)
)

const setChart = () => {
  inviteActivitiesChart(id).get()
  .then(res=>{
      // 配置chart
      chart.data(res)
      chart.scale({
        year: {
          range: [0, 1]
        },
        num: {
          min: 0,
          nice: true
        }
      })

      //提示
      chart.tooltip({
        title: 'date',
        shared: true
      })
      chart.scale('num', {
        alias: '邀请数量'
      })
      chart.line().position('date*num').shape('smooth')
      chart.point().position('date*num')
      chart.render()
  })
}

let chart = null;

nextTick(() => {
  chart = new Chart({
    container: 'container',
    autoFit: true,
    height: 360
  })
  setPage()
  setChart()
})

const linkPath = Object.freeze(`/bargaining/bargainTopic?id=`) // 链接路径

// 弹窗状态-弹窗名称
const visible = ref(false)
let modelTitle = ref('')

// 用户昵称或手机号码
const conditionKey = ref('nickname');
const conditionValue = ref();
watch(conditionKey, () => conditionValue.value = undefined)

const getValue = () => conditionValue.value ? { [conditionKey.value]: conditionValue.value } : {}

//模糊查询
const handleSearch = () => {
  debounce(setPage, 300)()
}

// 详情信息
const { data: dataInfo, setPage: setPageInfo, loading: loadingInfo } = usePaginatorApiRequest(({ offset, limit }) =>
  inviteRecordsInfoApi(id).get({
    relations: "invitee",
    filters: { "inviter_id": inviter_id.value },
    offset,
    limit
  }), undefined,
  false
)

// 邀请id
const inviter_id = ref();

// 点击记录
const onRecord = async ({ inviter }) => {
  if (!inviter) return message.info('暂无邀请记录')
  // 打开弹窗 -设置标题 设置邀请id
  modelTitle.value = inviter.nickname;
  inviter_id.value = inviter.id;
  await setPageInfo();
  visible.value = true;
}

</script>
