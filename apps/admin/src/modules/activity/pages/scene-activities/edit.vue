<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              :disabled="isEdit && !isNostart"
              class="w-240"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              class="w-240"
              :disabled="isRead"
              :disabled-date="disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value.trim="formState.title"
              class="input-width"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="活动类型" class="required">
            <a-select
              v-model:value.trim="formState.type"
              class="input-width"
              :options="sceneActivityType.options()"
              placeholder="请选择活动类型"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则" class="h-fill">
          <a-form-item label="兑奖期限" class="required">
            <a-input-number
              v-model:value="formState.limit_day"
              min="0"
              class="input-width"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '活动奖励'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex }"
            class="m-b-10"
          >
            <a-input-group v-for="(prize, index) in rule.award_settings" :key="index" compact>
              <a-space :size="10">
                <a-select
                  v-model:value="prize.type"
                  class="w-100"
                  placeholder="礼品类型"
                  allow-clear
                  :disabled="prize.disabled || isRead"
                  :options="giftType.sceneOptions()"
                  @change="onChangeGiftType(prize, index)"
                />
                <a-input-number
                  v-if="prize.type === giftType.credit"
                  v-model:value="prize.quantity"
                  class="w-300"
                  show-search
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :disabled="prize.disabled || isRead"
                />
                <a-select
                  v-else
                  v-model:value="prize.option_id"
                  class="w-300"
                  :placeholder="giftType.getPlaceholder(prize.type)"
                  :options="prize.options"
                  allow-clear
                  show-search
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="prize.disabled || isRead"
                  @focus="handleShowGifts(prize)"
                  @search="handleSearchGifts($event, prize)"
                />
                <a-input-number
                  v-model:value.number="prize.stock"
                  min="0"
                  class="w-90"
                  placeholder="活动数量"
                  :disabled="isRead"
                />
                <a-button
                  shape="circle"
                  size="small"
                  class="delete-btn"
                  type="link"
                  :disabled="isRead || prize.disabled || formState.rules.length === 1"
                >
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemovePrize(ruleIndex)" />
                  </template>
                </a-button>
              </a-space>
            </a-input-group>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button v-if="!isRead" type="link" class="p-0" @click="onAddPrize"> 添加奖励层级 </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <uc-rich-text v-model="formState.desc" v-model:isValidator="isValidator" :disabled="isRead" />
  </uc-layout-form>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep, debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { decimalVerify } from '@/utils/index'
import { sceneActivitiesApi } from '../../api'
import { giftType } from '../../enums'
import { usePrizeAwardUpdate, useTransformPrize } from '../../usePrizeAward'
import { sceneActivityType } from '../../enums'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
const { transformPrizesRequest } = useTransformPrize()

const { statusList, TIME_STATUS_NOSTART, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()
const router = useRouter()
const { id } = useRoute().params

let isNostart = ref(false)
let isNormal = ref(false)
let isEdit = ref(false)
let isRead = ref(false)

if (id) {
  isEdit.value = true
  const hideLoading = message.loading('正在加载数据...')

  sceneActivitiesApi
    .get(id, { relations: ['rules'] })
    .then(async res => {
      switch (res.status) {
        case TIME_STATUS_NOSTART:
          isNostart.value = true
          break
        case TIME_STATUS_NORMAL:
          isNormal.value = true
          break
        case TIME_STATUS_ENDED:
          isRead.value = true
          break
      }

      const showPrizePrm = []
      res.rules.forEach(rule => {
        rule.award_settings.forEach(prize => {
          // 回显奖项
          showPrizePrm.push(handleAward(prize, { id: prize.option_id }))

          // 活动进行中，则禁用编辑已有奖项层级，活动数量只能增加
          prize.disabled = isNormal.value
        })
      })
      await Promise.all(showPrizePrm)
      setFormState(res)
    })
    .finally(hideLoading)
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  type: undefined,
  limit_day: undefined,
  rules: [
    {
      award_settings: [
        onPrizeDefault({
          type: 'coupon'
        })
      ]
    }
  ],
  desc: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

const rulesValidator = (_, value) => {
  let tip
  for (const { award_settings } of value) {
    for (const { type, quantity, option_id, stock } of award_settings) {
      if (!type) {
        tip = '请选择礼品类型'
      } else {
        if (type === giftType.credit) {
          if (!quantity) {
            tip = '请输入积分值'
            break
          }
          if (decimalVerify(quantity)) {
            tip = '奖励积分不可输入小数'
            break
          }
        } else {
          if (!option_id) {
            tip = '请选择奖励礼品'
            break
          }
        }

        if (stock === '') {
          tip = '请输入活动数量'
          break
        }
      }
    }
    if (tip) break
  }

  if (tip) return Promise.reject(tip)
  return Promise.resolve()
}

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  type: { required: true, message: '请选择活动类型' },
  limit_day: { required: true, message: '请输入兑奖期限' },
  rules: { validator: rulesValidator },
  desc: { required: true, message: '请输入活动说明' }
})

const onAddPrize = () => {
  const { rules } = formState.value
  if (rules.length === 5) return message.error('最多能添加5个入会奖励层级')
  rules.push({ award_settings: [onPrizeDefault()] })
}

const onRemovePrize = index => formState.value.rules.splice(index, 1)

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) return

  const params = cloneDeep(formState.value)
  params.rules.forEach(rule => {
    transformPrizesRequest(rule)
  })
  id ? await sceneActivitiesApi.replace(id, params) : await sceneActivitiesApi.create(params)
  message.success('操作成功')
  router.back()
}, 500)
</script>
<style scoped lang="less">
.delete-btn {
  padding-top: 2px;
  border: none;

  :deep(.anticon) {
    font-size: 20px;
  }
}

.delete-btn:disabled {
  :deep(.anticon) {
    color: #d9d9d9 !important;
  }
}
</style>
