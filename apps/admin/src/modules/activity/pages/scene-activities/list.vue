<template>
  <div class="scene-activities">
    <uc-layout-list title="场景营销">
      <template #filter>
        <a-form-item name="title">
          <a-input v-model:value.trim="formState.title" placeholder="活动名称" />
        </a-form-item>
        <a-form-item name="type">
          <a-select 
            v-model:value.trim="formState.type"
            :options="sceneActivityType.options()"
            placeholder="活动类型"
          />
        </a-form-item>
        <a-form-item name="productType">
          <a-select v-model:value="formState.status" placeholder="活动状态" allow-clear :options="statusList" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage">
            查询
          </a-button>
          <a-button @click="resetFormState">
            重置
          </a-button>
        </a-form-item>
      </template>
      <template #extra>
        <a-button type="primary">
          <router-link :to="{name:'scene-activities-add'}">
            新增场景营销
          </router-link>
        </a-button>
      </template>
      <template #list>
        <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
          <a-table-column title="活动名称" data-index="title" align="left" ellipsis />
          <a-table-column title="活动类型" data-index="type" width="160px" align="left" ellipsis>
            <template #default="{ record }">
              {{ sceneActivityType.filter(record.type).label }}
            </template>
          </a-table-column>
          <a-table-column title="活动时间" width="360px">
            <template #default="{ record }">
              {{ record.start_time }} ～ {{ $formatters.transformActivityEndTime(record.end_time) }}
            </template>
          </a-table-column>
          <a-table-column title="活动状态" width="150px">
            <template #default="{ record }">
              <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
            </template>
          </a-table-column>
          <a-table-column title="操作" width="150px">
            <template #default="{ record }">
              <a-button type="link" @click="copyLink(linkPath + record.id)">
                链接
              </a-button>
              <!-- <a-button v-if="record.status != TIME_STATUS_NOSTART" type="link">
                <router-link
                  :to="{
                    name: 'scene-activities-data',
                    params:{
                      id: record.id
                    }
                  }"
                >
                  数据
                </router-link>
              </a-button> -->
              <a-button v-if="record.status != TIME_STATUS_ENDED" type="link">
                <router-link
                  :to="{
                    name: 'scene-activities-edit',
                    params: {
                      id: record.id,
                      status: record.status
                    }
                  }"
                >
                  编辑
                </router-link>
              </a-button>
              <a-popconfirm
                v-if="record.can_delete"
                placement="left"
                title="你确定要删除该数据么？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" class="danger">
                  删除
                </a-button>
              </a-popconfirm>
              <a-button v-if="record.status == TIME_STATUS_ENDED" type="link">
                <router-link
                  :to="{
                    name: 'scene-activities-details',
                    params:{
                      id: record.id,
                      readOnly:true
                    }
                  }"
                >
                  查看
                </router-link>
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
  </div>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { sceneActivitiesApi } from '../../api'
import { sceneActivityType } from '../../enums'

const linkPath = Object.freeze(`/auth/pages/standard/scene/index?id=`)

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
sceneActivitiesApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like',
    }),
    offset,
    limit,
  })
)

const { statusList,statusFilter,TIME_STATUS_NOSTART,TIME_STATUS_NORMAL,TIME_STATUS_ENDED } = useTimeStatus()

const { formState, onRestFormState, resetFormState } = useFormState({
  title:undefined,
  type: undefined,
  status:undefined,
})

onRestFormState(() => setPage())

const router = useRouter()

const handleDelete = async ({ id }) => {
    await registerActivitiesApi.delete(id)
    message.success('删除完成')
    setPage()
}
</script>
