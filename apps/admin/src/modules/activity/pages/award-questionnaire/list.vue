<template>
  <uc-layout-list title="有奖调查">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item name="status">
        <a-select v-model:value="formState.status" placeholder="活动状态" allow-clear class="w-150">
          <a-select-option v-for="item in statusList" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增有奖调查
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称" data-index="title" ellipsis />

        <a-table-column title="UV/PV" width="150px" ellipsis align="right">
          <template #default="{ record }">
            {{ record.uv }}/{{ record.pv }}
          </template>
        </a-table-column>

        <a-table-column title="活动时间" width="360px">
          <template #default="{ record }">
            {{ record.start_time }} ～ {{ $formatters.transformActivityEndTime(record.end_time) }}
          </template>
        </a-table-column>

        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" @click="copyLink(linkPath + record.id)">
              链接
            </a-button>
            <a-button
              v-if="record.status === 'ended' || record.status === 'normal'"
              type="link"
              @click="onData(record)"
            >
              数据
            </a-button>
            <a-button
              v-if="record.status === 'not_start' || record.status === 'normal'"
              type="link"
              @click="onEdit(record)"
            >
              编辑
            </a-button>
            <a-button v-if="record.status === 'ended'" type="link" @click="onLook({ isShow: true }, record)">
              查看
            </a-button>
            <a-popconfirm
              v-if="record.can_delete"
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <template #icon>
                <uc-ant-icon name="QuestionCircleOutlined" type="danger" />
              </template>
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { awardQuestionnaireApi } from '../../api'

const { statusList, statusFilter } = useTimeStatus() // 时间状态
const { formState, resetFormState, onRestFormState } = useFormState() // 查询表单
const router = useRouter()
const linkPath = Object.freeze(`/award-questionnaire/pages/standard/award-questionnaire/index?id=`) // 链接路径

// 表格查询
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  awardQuestionnaireApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit,
    relations: ['questionnaire']
  })
)
onRestFormState(setPage)

// 新增
const onAdd = () => {
  router.push({
    name: 'award-questionnaire-add'
  })
}

// 点击编辑
const onEdit = ({ id, status }) => {
  router.push({
    name: 'award-questionnaire-edit',
    params: {
      id
    }
  })
}

//点击查看
const onLook = ({ isShow }, { id }) => {
  router.push({
    name: 'award-questionnaire-look',
    params: {
      id
    }
  })
}

//点击数据
const onData = record => {
  router.push({
    name: 'award-questionnaire-data',
    params: {
      id: record.questionnaire.questionnaire_id,
      award_id: record.id
    }
  })
}

// 点击删除
const handleDelete = ({ id, title }) => {
  awardQuestionnaireApi
    .delete(id)
    .then(() => {
      message.success(`${title}删除完成`)
    })
    .finally(setPage)
}
</script>
