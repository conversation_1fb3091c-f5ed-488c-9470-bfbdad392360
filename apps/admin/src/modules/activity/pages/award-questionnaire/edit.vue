<template>
  <!-- 有奖调查编辑 -->
  <uc-layout-form :is-save="!isReadonly" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              class="w-240"
              :disabled="isReadonly || isStarted"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
              class="w-240"
              :disabled="isReadonly"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value.trim="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isReadonly"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              placeholder="请输入活动标签，不超过10字"
              :maxlength="10"
              :disabled="isReadonly"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="调查问卷" class="required">
            <a-select
              v-model:value="formState.questionnaire.questionnaire_id"
              :options="questionList"
              show-search
              allow-clear
              placeholder="请选择调查问卷"
              :disabled="isReadonly || isStarted"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="兑奖期限" class="required m-b-24">
            <a-input-number
              v-model:value="formState.limit_day"
              precision="0"
              min="0"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
              :disabled="isReadonly || isStarted"
            />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '调查奖励'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex }"
            class="m-b-10"
          >
            <uc-level-list
              v-model="rule.award_settings"
              :min="formState.rules.length <= 1 ? 1 : 0"
              disabled-delete
              @delete="onRemoveRule(ruleIndex)"
            >
              <template #item="{ item }">
                <a-select
                  v-model:value="item.type"
                  placeholder="请选择礼品类型"
                  :options="giftType.options()"
                  class="w-100"
                  :disabled="item.disabled"
                  @change="onChangeGiftType(item)"
                />
                <a-input-number
                  v-if="item.type === giftType.credit"
                  v-model:value="item.quantity"
                  style="width: 300px"
                  show-search
                  placeholder="请输入积分值"
                  :disabled="item.disabled"
                />
                <a-select
                  v-else
                  v-model:value="item.option_id"
                  style="width: 300px"
                  placeholder="请选择奖励礼品"
                  :options="item.options || []"
                  option-filter-prop="label"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="item.disabled"
                  @focus="handleShowGifts(item)"
                  @search="handleSearchGifts($event, item)"
                />
                <a-input-number
                  v-model:value="item.stock"
                  :min="0"
                  precision="0"
                  placeholder="活动数量"
                  class="w-90"
                  :disabled="item.disabled"
                />
              </template>
            </uc-level-list>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button
              type="link"
              class="p-0"
              :disabled="formState.rules.length >= 5 || isReadonly || isStarted"
              @click="onAddRule('up')"
            >
              添加发放层级
            </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="页面设置" class="h-fill">
          <a-form-item label="皮肤模式" class="required">
            <a-space size="0">
              <a-select
                v-model:value="formState.page_setting.skin_mode"
                :options="skinMode.option()"
                :disabled="isStarted"
                class="w-310"
              />
              <a-button type="link" @click="imgRef.$el.nextSibling.click()"> 效果图预览 </a-button>
              <div v-show="false">
                <a-image ref="imgRef" :src="assets.previewaWardQuestionnaireImg" />
              </div>
            </a-space>
          </a-form-item>
          <a-form-item label="颜色配置" class="required">
            <a-space :size="10" wrap>
              <a-input
                v-model:value.trim="formState.page_setting.color.subject"
                placeholder="主题色，如#000000"
                class="w-160"
                :disabled="isReadonly"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.sub"
                placeholder="辅助色，如#000000"
                class="w-160"
                :disabled="isReadonly"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.bg"
                placeholder="背景色，如#000000"
                class="w-160"
                :disabled="isReadonly"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.title"
                placeholder="标题色，如#000000"
                class="w-160"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" class="required">
            <a-space :size="10">
              <uc-upload
                v-for="(item, index) in pageConfig"
                :key="index"
                v-model:list="item.value"
                upload-text=" "
                show-label
                :label-text="item.label"
                :max-length="1"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
          <!-- <a-form-item label="弹窗配置" class="required">
        <a-space :size="10">
          <uc-upload
            v-for="(item, index) in modalConfig"
            :key="index"
            v-model:list="item.value"
            upload-text=" "
            show-label
            :label-text="item.label"
            :max-length="1"
            :disabled="isReadonly"
          />
        </a-space>
      </a-form-item> -->
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.desc" :height="400" :disabled="isReadonly" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { decimalVerify } from '@/utils'
import { colorVerify } from '@/utils/index'
import { skinMode, giftType } from '../../enums'
import { usePrizeAwardUpdate } from '../../usePrizeAward'
import assets from '../../assets.config'
import { useTransformAwardQuestion } from '../../useTransform'
import { awardQuestionnaireApi, questionnaireApi } from '../../api'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
let handleSearch = value => {
  if (value) {
    questionnaireApi.all({ filters: useTransformQuery({ title: value }, { title: 'like' }) }).then(data => {
      classifyList.value = useTransformOptions(data, 'title', 'id')
    })
  } else {
    initList()
  }
}

handleSearch = debounce(handleSearch, 300)
// 初始化20条数据
const questionList = ref([])
const initList = () => {
  questionnaireApi.paginator({ page: 1, limit: 20, all: false }).then(({ items }) => {
    questionList.value = useTransformOptions(items, 'title', 'id')
  })
}
initList()

const { TIME_STATUS_NOSTART } = useTimeStatus()

const imgRef = ref(null)
const prizeDefault = () => ({ award_settings: [onPrizeDefault({ type: giftType.credit })] })
const { pageConfig, transformShow, transformRequest } = useTransformAwardQuestion()
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  questionnaire: {
    questionnaire_id: undefined,
    biz_type: 'award_questionnaire'
  },
  rules: [prizeDefault()],
  page_setting: {
    skin_mode: 'default',
    color: {
      subject: '',
      sub: '',
      bg: '',
      title: ''
    }
  },
  desc: ''
})

const router = useRouter()
const { id, readonly } = useRoute().params
const isReadonly = ref(Boolean(+readonly))
const isStarted = ref(false)

const loadData = async () => {
  const data = await awardQuestionnaireApi.get(id, { relations: ['rules', 'questionnaire'] })
  let { rules } = data
  let showPrizePrm = []
  rules.forEach(rule => {
    rule.award_settings.forEach(prize => {
      showPrizePrm.push(handleAward(prize, { id: prize.option_id }))
    })
  })
  await Promise.all(showPrizePrm)

  isStarted.value = data.status !== TIME_STATUS_NOSTART
  if (isStarted.value) {
    rules.forEach(rule => {
      rule.award_settings.forEach(award => {
        award.disabled = true
      })
    })
  }
  setFormState(transformShow(data))
}

if (id) {
  useLoadingMessage(loadData(), { loadingText: '正在加载数据...' })
}

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)
const validatorRule = (rule, value) => {
  if (!value.questionnaire_id) {
    return Promise.reject('请选择调查问卷')
  }

  return Promise.resolve(true)
}

const validatorPage = (rule, value) => {
  if (!colorVerify(value.color.subject)) {
    return Promise.reject('请输入正确的主题色')
  } else if (!colorVerify(value.color.sub)) {
    return Promise.reject('请输入正确的辅助色')
  } else if (!colorVerify(value.color.bg)) {
    return Promise.reject('请输入正确的背景色')
  } else if (!colorVerify(value.color.title)) {
    return Promise.reject('请输入正确的标题色')
  }
  // const pageItem = pageConfig.value.concat(modalConfig.value).find(item => !item.value.length)
  // if (pageItem) return Promise.reject(`请上传${pageItem.label}图`)
  return Promise.resolve(true)
}

// 校验赠送礼品
const validatorRules = (_, value) => {
  let tip
  for (const { award_settings } of value) {
    for (const { type, quantity, option_id, stock } of award_settings) {
      if (!type) {
        tip = '请选择礼品类型'
      } else {
        if (type === giftType.credit) {
          if (!quantity) {
            tip = '请输入积分值'
            break
          }
          if (decimalVerify(quantity)) {
            tip = '奖励积分不可输入小数'
            break
          }
        } else {
          if (!option_id) {
            tip = '请选择奖励礼品'
            break
          }
        }

        if (!stock) {
          tip = '请输入活动数量'
          break
        }
        if (stock === 0) {
          tip = '活动数量不可为0'
          break
        }
      }
    }
    if (tip) break
  }

  if (tip) return Promise.reject(tip)
  return Promise.resolve()
}

setFormRules({
  start_time: { required: true, message: '请选择开始时间' },
  end_time: { required: true, message: '请选择结束时间' },
  title: { required: true, message: '请输入活动名称' },
  questionnaire: { validator: validatorRule },
  rules: { validator: validatorRules },
  page_setting: { validator: validatorPage },
  desc: { required: true, message: '请输入活动说明' }
})

// 添加层级
const onAddRule = () => {
  formState.value.rules.push(prizeDefault())
}
// 删除
const onRemoveRule = index => {
  formState.value.rules.splice(index, 1)
}

const handleSubmit = async () => {
  if (!(await validateForm())) return

  const params = transformRequest(formState.value)

  id ? await awardQuestionnaireApi.update(id, params) : await awardQuestionnaireApi.create(params)
  message.success('操作成功')
  router.back()
}
</script>
