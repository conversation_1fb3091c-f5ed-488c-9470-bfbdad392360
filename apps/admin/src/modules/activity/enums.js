import config from '@/config'
const hideCredit = config.featureConfig.member.hideCredit
/**
 * 礼品类型
 */
export const giftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',

  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 抽奖资格
   */
  draw_qualification: 'draw_qualification',

  /**
   * 积分
   */
  credit: 'credit',

  /**
   * 成长值
   */
  growthValue: 'growth_value',

  options() {
    return hideCredit
      ? [{ label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' }]
      : [
          { label: '实物礼品', value: this.entity, biz_type: 'gift', placeholder: '请选择礼品' },
          { label: '虚拟卡券', value: this.invented, biz_type: 'gift', placeholder: '请选择礼品' },
          { label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' },
          { label: '会员积分', value: this.credit, biz_type: 'credit', placeholder: '请输入积分值' }
          // { label: '抽奖资格', value: this.draw_qualification, biz_type: 'draw', placeholder: '请输入抽奖次数' }
        ]
  },
  registerOptions() {
    return hideCredit
      ? [{ label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' }]
      : [
          { label: '实物礼品', value: this.entity, biz_type: 'gift', placeholder: '请选择礼品' },
          { label: '虚拟卡券', value: this.invented, biz_type: 'gift', placeholder: '请选择礼品' },
          { label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' },
          { label: '会员积分', value: this.credit, biz_type: 'credit', placeholder: '请输入积分值' },
          { label: '抽奖资格', value: this.draw_qualification, biz_type: 'draw', placeholder: '请输入抽奖次数' }
        ]
  },
  turntableOptions() {
    return hideCredit
      ? [
          { label: '购物卡券', value: this.coupon }
        ]
      : [
          { label: '实物礼品', value: this.entity },
          { label: '虚拟卡券', value: this.invented },
          { label: '购物卡券', value: this.coupon },
          { label: '会员积分', value: this.credit },
          { label: '抽奖次数', value: this.draw_times }
        ]
  },
  sceneOptions() {
    return hideCredit
      ? [{ label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' }]
      : [
          { label: '会员积分', value: this.credit, biz_type: 'credit', placeholder: '请输入积分值' },
          { label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' }
        ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  },
  getPlaceholder(value) {
    if (!value) return '请选择礼品'
    return this.options().find(item => item.value == value)?.placeholder || '请选择礼品'
  }
})
/**
 * 礼品类型
 */
export const awardGiftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',
  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 积分
   */
  credit: 'credit',

  options() {
    return [
      { label: '实物礼品', value: this.entity, biz_type: awardBizType.gift },
      { label: '虚拟卡券', value: this.invented, biz_type: awardBizType.gift },
      { label: '购物卡券', value: this.coupon, biz_type: awardBizType.coupon },
      { label: '会员积分', value: this.credit, biz_type: awardBizType.credit }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})
/**
 * 奖项业务类型
 *
 * 关联字段:
 * 积分      award_biz_type = 'credit'   award_biz_id = 0       award_credit = 输入值  prize_title = ''       prize_url = ''
 * 礼品      award_biz_type = 'gift'     award_biz_id = 礼品id  award_credit = 0       prize_title = 礼品标题  prize_url = 礼品图片
 * 谢谢参与  award_biz_type = 'none'     award_biz_id = 0       award_credit = 0       prize_title = ''       prize_url = ''
 */
export const awardBizType = Object.freeze({
  /**
   * 积分
   */
  credit: 'credit',
  /**
   * 礼品[实物|虚拟]
   */
  gift: 'gift',
  /**
   * 购物卡券
   */
  coupon: 'coupon',
  /**
   * 谢谢参与
   */
  none: 'none'
})

/**
 * 用户身份
 */
export const IdentityList = Object.freeze({
  /**
   * 游客
   */
  visitor: '0',
  /**
   * 粉丝
   */
  fans: 'fans',
  /**
   * 会员
   */
  vip: '1',

  options() {
    return [
      { label: '粉丝', value: this.fans, colorType: '#1890ff' },
      { label: '会员', value: this.vip, colorType: '#52c41a' }
    ]
  }
})

/**
 * 注册来源
 */
export const SourceList = Object.freeze({
  /**
   * 试用
   */
  trial: 'trial',
  /**
   * 盲盒
   */
  blindBox: 'blind_box',
  /**
   * 幸运转盘
   */
  luckyGame: 'lucky_game',

  options() {
    return [
      { label: '试用', value: this.trial, colorType: '#FA8C16' },
      { label: '盲盒', value: this.blindBox, colorType: '#1890ff' },
      { label: '幸运转盘', value: this.luckyGame, colorType: '#52c41a' }
    ]
  }
})

/**
 * 互动数据统计 用户信息筛选条件
 */
export const UserModeList = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 手机号码
   */
  phone_number: 'phone_number',

  options() {
    return [
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phone_number }
    ]
  }
})

/**
 * 系统模版
 */
export const skinMode = Object.freeze({
  /**
   * 系统模版-默认皮肤
   */
  default: 'default',

  option() {
    return [{ label: '系统模版-默认皮肤', value: this.default }]
  }
})

/**
 * 活动规则类型
 */
export const activityRuleList = Object.freeze({
  /**
   * 连续签到
   */
  continuity: 'continuity',
  /**
   * 累计签到
   */
  accumulate: 'accumulate',
  /**
   * 首次签到
   */
  first: 'first',

  /**
   * 连签奖励
   */
  continuous: 'continuous'
})

/**
 * 投放频率[弹窗]
 */
export const launchHz = Object.freeze({
  /**
   * 永久一次
   */
  once: 'once',
  /**
   * 每天一次
   */
  day_once: 'day_once',
  /**
   * 每次访问
   */
  every_once: 'every_once',

  options() {
    return [
      { label: '永久1次（投放时间内，首次访问页面时出现，之后不在推送）', value: this.once },
      { label: '每天1次（投放时间内，每天首次访问页面时出现，之后不再推送）', value: this.day_once },
      { label: '每次访问（投放时间内，每次访问页面时出现）', value: this.every_once }
    ]
  }
})

/**
 * 投放人群[弹窗]
 */
export const launchCrowd = Object.freeze({
  /**
   * 全部
   */
  all: 'all',
  /**
   * 生日用户
   */
  birth: 'birth',
  /**
   * 指定
   */
  appoint: 'appoint',

  /**
   * 指定标签
   */
  tags: 'tags',

  options() {
    return [
      { label: '全部用户', value: this.all },
      { label: '生日用户', value: this.birth },
      { label: '指定用户', value: this.appoint },
      { label: '指定标签', value: this.tags }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 转盘奖项
 */
export const turntablePrizes = Object.freeze({
  /**
   * 第一
   */
  first: 'first',
  /**
   * 第二
   */
  second: 'second',
  /**
   * 第三
   */
  third: 'third',
  /**
   * 第四
   */
  fourth: 'fourth',
  /**
   * 第五
   */
  fifth: 'fifth',
  /**
   * 第六
   */
  sixth: 'sixth',
  /**
   * 第七
   */
  seventh: 'seventh',
  /**
   * 第八
   */
  eighth: 'eighth',
  /**
   * 谢谢参与
   */
  zero: 'zero',

  options() {
    return [
      { label: '一等奖', value: this.first },
      { label: '二等奖', value: this.second },
      { label: '三等奖', value: this.third },
      { label: '四等奖', value: this.fourth },
      { label: '五等奖', value: this.fifth },
      { label: '六等奖', value: this.sixth },
      { label: '七等奖', value: this.seventh },
      { label: '八等奖', value: this.eighth },
      { label: '谢谢参与', value: this.zero }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 兑奖状态
 */
export const prizeExchangeStatus = Object.freeze({
  /**
   * 未兑奖
   */
  normal: 'normal',
  /**
   * 已兑奖
   */
  complete: 'complete',
  /**
   * 已过期
   */
  expired: 'expired',

  options() {
    return [
      { label: '未兑奖', value: this.normal, colorType: 'processing' },
      { label: '已兑奖', value: this.complete, colorType: 'success' },
      { label: '已过期', value: this.expired, colorType: 'default' }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 题型信息
 */
export const questionInfo = Object.freeze({
  /**
   * 你的性别
   */
  sex: 'sex',
  /**
   * 你的年龄
   */
  age: 'age',
  /**
   * 所在城市
   */
  city: 'city',
  /**
   * 文化水平
   */
  culture: 'culture',
  /**
   * 文字单选
   */
  text_single_select: 'text_single_select',
  /**
   * 文字多选
   */
  text_multiple_select: 'text_multiple_select',
  /**
   * 图片单选
   */
  photo_single_select: 'photo_single_select',
  /**
   * 图片多选
   */
  photo_multiple_select: 'photo_multiple_select',
  /**
   * 单项问答
   */
  question_answer: 'question_answer',
  /**
   * 星星评分
   */
  score: 'score',

  options() {
    return [
      { label: '你的性别', value: this.sex, group: 'personInfo' },
      { label: '你的年龄', value: this.age, group: 'personInfo' },
      { label: '所在城市', value: this.city, group: 'personInfo' },
      { label: '文化水平', value: this.culture, group: 'personInfo' },
      { label: '文字单选', value: this.text_single_select, group: 'type' },
      { label: '文字多选', value: this.text_multiple_select, group: 'type' },
      { label: '图片单选', value: this.photo_single_select, group: 'type' },
      { label: '图片多选', value: this.photo_multiple_select, group: 'type' },
      { label: '单项问答', value: this.question_answer, group: 'other' },
      { label: '星星评分', value: this.score, group: 'other' }
    ]
  },
  personInfoOption() {
    return {
      [this.sex]: ['男', '女'],
      [this.age]: ['不满18岁', '18～25岁', '26～30岁', '31～40岁', '41～50岁', '51～60岁', '60岁以上'],
      [this.culture]: ['小学', '初中', '高中', '中专', '大专', '本科', '硕士', '博士']
    }
  },
  groupPersonInfo() {
    return this.options().filter(({ group }) => group === 'personInfo')
  },
  groupType() {
    return this.options().filter(({ group }) => group === 'type')
  },
  groupOther() {
    return this.options().filter(({ group }) => group === 'other')
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  },
  filterSingleSelect(value) {
    return [this.text_single_select, this.photo_single_select].includes(value)
  },
  filterSelect(value) {
    return [
      this.text_single_select,
      this.text_multiple_select,
      this.photo_single_select,
      this.photo_multiple_select
    ].includes(value)
  },
  filterImg(value) {
    return [this.photo_single_select, this.photo_multiple_select].includes(value)
  },
  filterPersonInfo(value) {
    return this.groupPersonInfo().find(item => item.value === value)
  }
})

/**
 * 助力资格
 */
export const helpList = Object.freeze({
  /**
   * 仅新用户可助力
   */
  new_user: 'new_user',
  /**
   * 全部用户可助力
   */
  all: 'all',

  options() {
    return [
      { label: '仅新用户可助力', value: this.new_user },
      { label: '全部用户可助力', value: this.all }
    ]
  }
})

/**
 * 碎片数量
 */
export const fragmentList = Object.freeze({
  /**
   * 4
   */
  four: 4,

  /**
   * 6
   */
  six: 6,

  /**
   * 8
   */
  eight: 8,

  /**
   * 9
   */
  nine: 9,

  options() {
    return [
      {
        label: '2*2拼图',
        value: this.four
      },
      {
        label: '2*3拼图',
        value: this.six
      },
      {
        label: '2*4拼图',
        value: this.eight
      },
      {
        label: '3*3拼图',
        value: this.nine
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 拼图碎片
 */
export const jigsawFragment = Object.freeze({
  /**
   * 第一
   */
  first: 1,
  /**
   * 第二
   */
  second: 2,
  /**
   * 第三
   */
  third: 3,
  /**
   * 第四
   */
  fourth: 4,
  /**
   * 第五
   */
  fifth: 5,
  /**
   * 第六
   */
  sixth: 6,
  /**
   * 第七
   */
  seventh: 7,
  /**
   * 第八
   */
  eighth: 8,
  /**
   * 第九
   */
  ninth: 9,
  /**
   * 万能
   */
  universal: 0,

  options() {
    return [
      { label: '一号碎片', value: this.first },
      { label: '二号碎片', value: this.second },
      { label: '三号碎片', value: this.third },
      { label: '四号碎片', value: this.fourth },
      { label: '五号碎片', value: this.fifth },
      { label: '六号碎片', value: this.sixth },
      { label: '七号碎片', value: this.seventh },
      { label: '八号碎片', value: this.eighth },
      { label: '九号碎片', value: this.ninth },
      { label: '万能碎片', value: this.universal }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 转盘奖项
 */
export const jigsawPrizes = Object.freeze({
  /**
   * 第一
   */
  first: 1,
  /**
   * 第二
   */
  second: 2,
  /**
   * 第三
   */
  third: 3,
  /**
   * 第四
   */
  fourth: 4,
  /**
   * 第五
   */
  fifth: 5,
  /**
   * 第六
   */
  sixth: 6,
  /**
   * 第七
   */
  seventh: 7,
  /**
   * 谢谢参与
   */
  zero: 0,

  options() {
    return [
      { label: '一等奖', value: this.first },
      { label: '二等奖', value: this.second },
      { label: '三等奖', value: this.third },
      { label: '四等奖', value: this.fourth },
      { label: '五等奖', value: this.fifth },
      { label: '六等奖', value: this.sixth },
      { label: '七等奖', value: this.seventh },
      { label: '谢谢参与', value: this.zero }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 用户昵称 or 手机号
 */
export const writeOff = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',

  /**
   * 手机号
   */
  phone_number: 'phone_number',

  options() {
    return [
      { label: '手机号码', value: this.phone_number },
      { label: '用户昵称', value: this.nickname }
    ]
  }
})

/**
 * 场景营销活动类型
 */
export const sceneActivityType = Object.freeze({
  /**
   * 生日有礼
   */
  birthday: 'birthday',

  options() {
    return [{ label: '生日有礼', value: this.birthday }]
  },

  filter(value) {
    return this.options().find(item => item.value === value)
  }
})
