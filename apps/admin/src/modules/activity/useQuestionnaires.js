import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { questionnairesApi } from './api'

export function useQuestionnaires(callback = null, filters = {}) {
  const { data: questionnaires } = useApiRequest(() => {
    return questionnairesApi.list({ filters }).then(cates => (callback ? callback(cates) : cates))
  })

  return {
    questionnaires
  }
}

export function useQuestionnaireOptions(filters) {
  const { questionnaires: questionnaireOptions } = useQuestionnaires(
    cates => useTransformOptions(cates, 'title', 'id'),
    filters
  )
  return {
    questionnaireOptions
  }
}
