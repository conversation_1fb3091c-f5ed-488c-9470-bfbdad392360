import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
import config from '@/config'

const { state } = useStore()

export const signActivityApi = apiFactory.restful('/activity/sign-activities')
export const signActivityRecordApi = apiFactory.restful('/activity/sign-activity-records/list-by-user')
export const signActivityActions = id => apiFactory.command(`/activity/sign-activity-records/${id}/stats`)
export const signActivitiesChart = id => apiFactory.command(`/activity/sign-activity-records/${id}/chart`)
export const signActivitiesStats = id => apiFactory.command(`/activity/sign-activity-records/${id}/stats`)

export const registerActivitiesApi = apiFactory.restful('/activity/register-activities')
export const registerActivitiesActions = apiFactory.command('/activity/register-activities')
export const registerRecordsStatsActions = id => apiFactory.command(`/activity/register-records/${id}/stats`)
export const registerActivitiesChart = id => apiFactory.command(`/activity/register-records/${id}/chart`)
export const registerActivitiesStats = id => apiFactory.command(`/activity/register-records/${id}/stats`)

export const giftsApi = apiFactory.restful('/gift/gifts')
export const joinApi = apiFactory.restful('/activity/register-activities')

export const inviteActivitiesApi = apiFactory.restful('/activity/invite-activities') // 邀请有礼
export const inviteRecordsInfoApi = id => apiFactory.command(`/activity/invite-records/${id}/info`) // 邀请有礼详情
export const inviteActivitiesChart = id => apiFactory.command(`/activity/invite-records/${id}/chart`)
export const inviteActivitiesStats = id => apiFactory.command(`/activity/invite-records/${id}/stats`)

export const popupApi = apiFactory.restful('/popup/popups')

export const giftApi = apiFactory.restful('/gift/gifts')
export const giftCategoryApi = apiFactory.restful('/gift/categories')

// 幸运大转盘
export const turnplateApi = apiFactory.restful('/activity/turnplate-activities')

export const turnplateChannelApi = apiFactory.restful('/activity/turnplate-activities/channel')

export const turnplateStatsRecordApi = (user_id, id) =>
  apiFactory.restful(`/activity/draw-statistics/${id}/user/${user_id}`)
export const turnplateStatsListApi = id => apiFactory.command(`/activity/draw-statistics/${id}`)
export const turnplateStatsChartApi = id => apiFactory.command(`/activity/draw-statistics/${id}/static`)

// 惊爆九宫格
export const sudokuApi = apiFactory.restful('/activity/sudoku-activities')
export const sudokuStatsRecordApi = (user_id, id) =>
  apiFactory.restful(`/activity/draw-statistics/${id}/user/${user_id}`)
export const sudokuStatsListApi = id => apiFactory.command(`/activity/draw-statistics/${id}`)
export const sudokuStatsChartApi = id => apiFactory.command(`/activity/draw-statistics/${id}/static`)

export const userLevelsApi = apiFactory.restful('/user/user-levels') // 用户等级
export const awardOptionsApi = apiFactory.restful('/user-dispatch-prize/options') // 奖项选项
export const questionnaireApi = apiFactory.restful('/activity/questionnaires') // 问券列表
export const questionnaireListApi = apiFactory.restful('/activity/questionnaire-statistics') // 参与名单
export const questionnaireSubjectApi = apiFactory.restful('/activity/questionnaire-statistics/subject') // 选项统计列表
export const questionnaireDownloadApi = id =>
  `${import.meta.env.VITE_API_BASE_URL
  }/activity/questionnaire-statistics/download?filters={"questionnaire_id":${id}}&token=${state.token}` // 下载问卷报告

export const awarDquestionnaireDownloadApi = (id, biz_id) =>
  `${import.meta.env.VITE_API_BASE_URL
  }/activity/questionnaire-statistics/download?filters={"questionnaire_id":${id},"biz_id":${biz_id},"biz_type":"award_questionnaire"}&token=${state.token
  }` // 下载问卷报告

// 新品试用
export const trialsApi = apiFactory.restful('/activity/trials') // 获取试用列表|新增试用

// 问卷
export const questionnairesApi = apiFactory.restful('/activity/questionnaires') // 获取问卷
export const goodsSpecsApi = apiFactory.restful('/goods/goods-specs')

export const apiTrials = apiFactory.restful('/activity/trials') // 获取试用列表|新增试用
export const apiGoods = apiFactory.restful('/goods/goods-specs') // 试用产品
export const apiQuestionnaires = apiFactory.restful('/activity/questionnaires') // 获取问卷
export const apiTrialsId = apiFactory.restful('/activity/trials') // 根据id获取试用|修改试用|删除试用
export const apiTrialRecords = apiFactory.restful('/activity/trial/records') // 试用数据-申请/试用名单
export const apiWinner = apiFactory.restful('/activity/trial/records/winner') // 试用数据-添加中奖名单
export const apiTrialReports = apiFactory.restful('/activity/trial/reports') // 试用报告
export const apiDelTrialReports = apiFactory.restful('/activity/trial/reports') // 删除试用报告||预览
export const apiDownload = apiFactory.restful('/activity/questionnaire-statistics/download') // 删除试用报告||预览

export const exportUrl = config.api.baseURL + '/activity/questionnaire-statistics/download'

// 有奖调查
export const awardQuestionnaireApi = apiFactory.restful('/activity/award-questionnaires')

export const jigsawApi = apiFactory.restful('/activity/jigsaw-activities') // 拼图有礼
export const jigsawStatisticsApi = id => apiFactory.restful(`/activity/jigsaw-statistics/${id}/statistics`) // 拼图数据静态记录
export const jigsawRecordsApi = id => apiFactory.restful(`/activity/jigsaw-statistics/${id}/records`) // 拼图数据抽奖统计
export const jigsawUserRecordsApi = ({ id, userId }) =>
  apiFactory.restful(`/activity/jigsaw-statistics/${id}/user/${userId}/records`) // 拼图数据抽奖统计

export const reservationListApi = apiFactory.restful('/activity/reservation-activities') // 列表
export const addreservationApi = apiFactory.command(`/activity/reservation-activities`) // 详情
export const reservationEditApi = apiFactory.restful(`/activity/reservation-activities`) // 详情

export const reservationChartApi = apiFactory.command('/activity/reservation-records/chart') // 预定活动统计数据
// export const batchAuditApi = apiFactory.command('/activity/reservation-record/batch-audit') // 批量审核
export const reservationAuditApi = apiFactory.command('/activity/reservation-records/audit') // 审核
export const reservationDataApi = apiFactory.restful(`/activity/reservation-records`) // 数据列表
export const reservationHourApi = apiFactory.command('/activity/reservation-activities/reservation-status') // 预约时段
export const changeApi = apiFactory.command('/activity/reservation-records/change-date') //改期

export const sceneActivitiesApi = apiFactory.restful('/activity/scene-activities') // 场景营销