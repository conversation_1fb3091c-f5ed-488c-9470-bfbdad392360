export default {
  path: 'custom',
  meta: {
    title: '定制',
    antIcon: 'ReconciliationOutlined'
  },
  children: [
    {
      path: 'custom-goods',
      name: 'custom-goods',
      meta: {
        title: '定制商品',
        keepAlive: true
      },
      component: () => import('./pages/goods/list')
    },
    {
      path: 'custom-goods-add',
      name: 'custom-goods-add',
      meta: {
        title: '添加定制商品'
      },
      hidden: true,
      component: () => import('./pages/goods/edit')
    },
    {
      path: 'custom-goods-edit/:id',
      name: 'custom-goods-edit',
      meta: {
        title: '编辑定制商品'
      },
      hidden: true,
      component: () => import('./pages/goods/edit')
    },
    {
      path: 'custom-order',
      name: 'custom-order',
      meta: {
        title: '定制订单',
        keepAlive: true
      },
      component: () => import('./pages/order/list')
    },
    {
      path: 'custom-template',
      name: 'custom-template',
      meta: {
        title: '定制推荐'
      },
      component: () => import('./pages/template/index')
    },
    {
      path: 'custom-voice-setting',
      name: 'custom-voice-setting',
      meta: {
        title: '电子祝福'
      },
      component: () => import('./pages/voice/setting')
    }
  ]
}
