/**
 * 搜索条件
 */
export const searchCondition = Object.freeze({
  /**
   * 标题
   */
  title: 'title',

  options() {
    return [{ label: '商品名称', value: this.title }]
  }
})

/**
 * 商品上架状态
 */
export const commodityOnState = Object.freeze({
  /**
   * 上架
   */
  putaway: 1,

  /**
   * 下架
   */
  soldOut: 0,

  options() {
    return [
      { label: '上架', value: this.putaway },
      { label: '下架', value: this.soldOut }
    ]
  }
})

export const customGoodsType = Object.freeze({
  /**
   * 标签祝福
   */
  tag_greeting: 'tag_greeting',

  options() {
    return [{ label: '标签祝福', value: this.tag_greeting }]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 定制状态
 */
export const customStatus = Object.freeze({
  /**
   * 待审核
   */
  normal: 'normal',
  /**
   * 通过
   */
  agree: 'agree',
  /**
   * 拒绝
   */
  refuse: 'refuse',
  options() {
    return [
      { label: '待审核', value: this.normal, color: '#1890FF' },
      { label: '通过', value: this.agree, color: '#52C41A' },
      { label: '拒绝', value: this.refuse, color: '#F5222D' }
    ]
  },
  filter(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

export const customRecommendTextType = Object.freeze({
  /**
   * 选择
   */
   select: 'select',
})
