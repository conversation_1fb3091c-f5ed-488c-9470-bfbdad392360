<template>
  <uc-layout-list title="全部订单">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.order_sn" placeholder="请输入订单编号" />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.combine_status"
          placeholder="订单状态"
          :options="orderStatus.customOptions()"
          allow-clear
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.examined_status"
          placeholder="定制状态"
          :options="customStatus.options()"
          allow-clear
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.type"
          placeholder="定制类型"
          :options="customGoodsType.options()"
          allow-clear
        />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="formState.created_at" :placeholder="['下单开始时间', '下单结束时间']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        :scroll="{ x: '1600px' }"
        @change="setPage"
      >
        <a-table-column title="订单编号/下单时间" width="200px" ellipsis>
          <template #default="{ record }">
            <router-link
              :to="{
                name: 'shop-detail',
                params: { id: record.order_attribute.order_id }
              }"
            >
              <div class="text-ellipsis">
                {{ record.order_attribute.order.sn }}
              </div>
            </router-link>
            <div>{{ record?.created_at }}</div>
          </template>
        </a-table-column>
        <a-table-column title="商品信息" ellipsis>
          <template #default="{ record: { spec } }">
            <a-space>
              <uc-img-text :url="spec.photo_url" :title="spec.goods.title" :subtit="getSpecs(spec)" />
            </a-space>
          </template>
        </a-table-column>
        <a-table-column title="下单用户" width="120px" ellipsis>
          <template #default="{ record }">
            <uc-avatar :src="record.user?.avatar" :nickname="record.user?.nickname" tooltip />
          </template>
        </a-table-column>
        <a-table-column title="定制类型" width="120px">
          <template #default="{ record }">
            {{
              record.items
                .map(item => customGoodsType.filter(item.type).label)
                .filter(item => item)
                .join(',')
            }}
          </template>
        </a-table-column>
        <a-table-column title="定制图" width="150px">
          <template #default="{ record }">
            <a-image
              v-for="(item, index) in record.items"
              :key="index"
              :width="50"
              :height="50"
              :src="item.preview_url"
              class="cursor-pointer"
              style="margin-right: 4px"
            />
          </template>
        </a-table-column>
        <a-table-column title="定制内容" width="180px">
          <template #default="{ record }">
            <template v-for="(item, index) in record.items" :key="index">
              <div v-for="(custom, cIndex) in setContent(item.params.config)" :key="cIndex">{{ custom }}</div>
            </template>
          </template>
        </a-table-column>
        <a-table-column title="订单状态" width="120px">
          <template
            #default="{
              record: {
                order_attribute: { order }
              }
            }"
          >
            <a-badge
              :text="orderStatus.shopFilterValue(order.combine_status).label"
              :color="orderStatus.shopFilterValue(order.combine_status).color"
            />
          </template>
        </a-table-column>

        <a-table-column title="审核状态">
          <template #default="{ record }">
            <a-badge
              :text="customStatus.filter(record.examined_status).label"
              :color="customStatus.filter(record.examined_status).color"
            />
            <div v-if="record.examined_remark" class="color-red">操作说明：{{ record.examined_remark }}</div>
          </template>
        </a-table-column>
        <a-table-column title="操作" width="60px" fixed="right">
          <template #default="{ record }">
            <a-button
              :disabled="record.examined_status !== customStatus.normal"
              type="link"
              style="#F5222D"
              @click="handleExamine(record)"
            >
              审核
            </a-button>
          </template>
        </a-table-column>
      </a-table>
      <a-modal :visible="modalVisible" title="定制审核" @cancel="closeModal">
        <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
          <a-form-item label="操作说明" class="required">
            <a-textarea
              v-model:value="modalFormState.examined_remark"
              placeholder="请输入操作备注说明，不超过20字请确认此单无异议，拒绝后用户需要重新提交定制信息"
              :rows="5"
              :maxlength="20"
            />
          </a-form-item>
        </a-form>
        <template #footer>
          <a-button type="primary" danger @click="handleRefuse"> 拒绝 </a-button>
          <a-button type="primary" @click="handleAgree"> 通过 </a-button>
        </template>
      </a-modal>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { message } from 'ant-design-vue'
import { customOrdersApi, customOrdersExamineApi } from '../../api'
import { customGoodsType, customStatus } from '../../enums'
import { orderStatus } from '@/modules/order/enums'
import { useSpecAttr } from '@/modules/promotion/useTransform'
import { useModalVisible } from '@/composables/useToggles'

const { formState, resetFormState, onRestFormState } = useFormState({
  examined_status: undefined,
  created_at: undefined,
  order_pay_status: '!unpaid',
  order_combine_status: undefined,
  order_sn: undefined,
  type: undefined
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  return customOrdersApi.paginator({
    filters: useTransformQuery(formState.value, { order_sn: 'like', created_at: 'dateRange' }),
    offset,
    limit,
    relations: ['items', 'user', 'orderAttribute', 'spec', 'goods'],
    relation_filters: {
      items: {
        type: formState.value.type
      }
    }
  })
})

const setContent = (configs) => {
  const values = []
  configs.forEach(item => {
    let valueArr = item.value.split("\n")
    valueArr.forEach((subItem, index) => {
      values.push(item.title + (valueArr.length > 1 ? index + 1 : '') + '：' + subItem)
    })
  })
  return values
}

const getSpecs = computed(() => item => useSpecAttr(item.attrs))
onRestFormState(() => setPage())

const { modalVisible, setModalVisible } = useModalVisible()

const {
  formState: modalFormState,
  validateForm: modalValidateForm,
  setFormRules: modalSetFormRules,
  resetFormState: modalResetFormState
} = useFormState({ examined_remark: undefined, examined_status: undefined, id: undefined })

modalSetFormRules({
  examined_remark: { required: true, message: `请输入驳回原因` }
})

const closeModal = () => {
  setModalVisible(false)
}

const onSubmit = async () => {
  const { id, examined_remark, examined_status } = modalFormState.value
  await customOrdersExamineApi.update(id, { examined_remark, examined_status })
  message.success('操作成功')
  closeModal()
  resetFormState()
}
const handleAgree = () => {
  modalFormState.value.examined_status = customStatus.agree
  onSubmit()
}

const handleRefuse = async () => {
  if (!(await modalValidateForm())) {
    return
  }
  modalFormState.value.examined_status = customStatus.refuse
  onSubmit()
}

const handleExamine = params => {
  modalResetFormState()
  setModalVisible(true)
  modalFormState.value.id = params.id
}
</script>
<style scoped lang="less"></style>
