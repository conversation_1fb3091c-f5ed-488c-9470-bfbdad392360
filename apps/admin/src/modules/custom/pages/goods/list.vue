<template>
  <uc-layout-list title="全部商品">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>

      <a-form-item>
        <a-select v-model:value="formState.on_sale" placeholder="上架状态" :options="commodityOnState.options()" />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary">
        <router-link
          :to="{
            name: 'custom-goods-add'
          }"
        >
          新增
        </router-link>
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="商品名称/定制图片" ellipsis>
          <template #default="{ record }">
            <uc-img-text v-bind="record" :title="record.title" :url="record.items[0].config.bg" />
          </template>
        </a-table-column>

        <a-table-column title="商品SKU" ellipsis>
          <template #default="{ record }">
            {{ record.sku }}
          </template>
        </a-table-column>

        <a-table-column title="定制类型" ellipsis>
          <template #default="{ record }">
            {{ customGoodsType.filter(record.items[0].type).label }}
          </template>
        </a-table-column>

        <a-table-column title="上架状态" width="120px">
          <template #default="{ record }">
            <a-switch
              v-model:checked="record.on_sale"
              :un-checked-value="0"
              :checked-value="1"
              @change="handleUpdate(record.id, { on_sale: $event - 0 })"
            />
          </template>
        </a-table-column>
        <a-table-column width="120px" title="操作">
          <template #default="{ record }">
            <a-button type="link">
              <router-link
                :to="{
                  name: 'custom-goods-edit',
                  params: {
                    id: record.id
                  }
                }"
              >
                编辑
              </router-link>
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { message } from 'ant-design-vue'
import { customGoodsApi } from '../../api'
import { searchCondition, commodityOnState, customGoodsType } from '../../enums'

const conditionOptions = searchCondition.options()
const conditionKey = ref(searchCondition.title)
const conditionValue = ref()
watch(conditionKey, (_val, oldVal) => {
  formState.value[oldVal] = conditionValue.value = undefined
})
watch(conditionValue, () => (formState.value[conditionKey.value] = conditionValue.value))

const queryFormBasic = Object.freeze({
  [searchCondition.title]: undefined
})

const { formState, resetFormState, onRestFormState } = useFormState({
  ...cloneDeep(queryFormBasic),
  on_sale: undefined
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  return customGoodsApi.paginator({
    filters: useTransformQuery(
      {
        on_sale: formState.value.on_sale,
        title: formState.value.title
      },
      { title: 'like' }
    ),
    offset,
    limit,
    relations: ['items']
  })
})

onRestFormState(() => setPage())

//  处理上架状态更新
const handleUpdate = async (id, props) => {
  await customGoodsApi.update(id, props)
  message.success('修改成功')
}

const handleDelete = ({ id }) => {
  customGoodsApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}
</script>
<style scoped lang="less"></style>
