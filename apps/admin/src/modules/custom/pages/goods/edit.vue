<template>
  <uc-layout-form class="custom-goods-edit" @submit="handleSubmit">
    <a-card title="商品信息">
      <a-form-item label="商品sku" name="sku" class="required">
        <uc-img-text
          v-if="specFormState.photo_url"
          class="m-b-10"
          :url="specFormState.photo_url"
          :title="specFormState.goods_title"
          :subtit="specFormState.subtit"
        />
        <a-button :disabled="id" @click="setModalVisible(true)">选择sku</a-button>
      </a-form-item>
      <a-form-item label="定制类型" name="bg" class="required">
        <a-checkbox-group
          :value="formState.items.map(item => item.type)"
          name="checkboxgroup"
          :options="customGoodsType.options()"
          @change="onCustomTypeChange"
        />
      </a-form-item>
    </a-card>
    <template v-for="(_, index) in formState.items" :key="index">
      <custom-tag-greeting v-model:value="formState.items[index]" />
    </template>
    <select-sku-goods v-model:visible="modalVisible" :mode="goodsSelectType.single" @ok="onSpecChange" />
  </uc-layout-form>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useModalVisible } from '@/composables/useToggles'
import { goodsSelectType } from '@/modules/promotion/enums'
import { customGoodsType } from '@/modules/custom/enums'
import { customGoodsApi } from '@/modules/custom/api'
import CustomTagGreeting from '@/modules/custom/components/tag-greeting.vue'
import { message } from 'ant-design-vue'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  sku: '',
  items: []
})
setFormRules({
  sku: { required: true, message: '请选择商品sku' },
  items: {
    validator(_, items) {
      if (!items || !items.length) {
        return Promise.reject('请选择定制类型')
      }
      let err = ''
      for (let i = 0; i < items.length; i++) {
        const item = items[i]
        const { bg, layer_width, layer_height, x, y } = item.config
        const label = customGoodsType.filter(item.type).label
        if (!item.template_id) {
          err = `请选择${label}模版`
          break
        }
        if (!bg) {
          err = `请上传${label}商品图片`
          break
        }
        if (!layer_width) {
          err = `请输入${label}定制区域宽度`
          break
        }
        if (!layer_height) {
          err = `请输入${label}定制区域高度`
          break
        }
        if (!x && x != 0) {
          err = `请输入${label}定制区域左边距`
          break
        }
        if (!y && y != 0) {
          err = `请输入${label}定制区域上边距`
          break
        }
      }
      return err ? Promise.reject(err) : Promise.resolve()
    }
  }
})

const { modalVisible, setModalVisible } = useModalVisible()

const { formState: specFormState, setFormState: setSpecFormState } = useFormState({
  photo_url: '',
  goods_title: '',
  subtit: '',
  title: ''
})

const getSubtit = (sku, attrs) => {
  return `${sku}: ${
    Array.isArray(attrs) ? attrs.reduce((prev, item, i) => prev + (i ? '/' : '') + item.value, '') : attrs
  }`
}
const onSpecChange = ([spec]) => {
  const {
    photo_url,
    sku,
    goods: { title },
    attrs
  } = spec
  formState.value.sku = sku
  formState.value.title = title
  const subtit = getSubtit(sku, attrs)
  setSpecFormState({ photo_url, sku, goods_title: title, subtit })
}

const defaultCustomItem = Object.freeze({
  type: '',
  config: ''
})
const onCustomTypeChange = types => {
  const items = []
  types.forEach(type => {
    let item = formState.value.items.find(item => item.type === type) || { ...defaultCustomItem, type }
    items.push(item)
  })
  formState.value.items = items
}

const { id } = useRoute().params
const router = useRouter()
const handleSubmit = async e => {
  if (!(await validateForm())) {
    return
  }
  id ? await customGoodsApi.update(id, formState.value) : await customGoodsApi.create(formState.value)
  message.success(id ? `编辑定制商品成功` : `新增定制商品成功`)
  router.back()
}
if (id) {
  customGoodsApi.get(id, { relations: ['items', 'spec', 'goods'] }).then(res => {
    setFormState(res)
    const {
      spec: { photo_url, sku, attrs },
      goods: { title }
    } = res
    setSpecFormState({ photo_url, sku, goods_title: title, subtit: getSubtit(sku, attrs) })
  })
}
</script>
