<template>
  <uc-layout-list title="素材">
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增素材
      </a-button>
    </template>
    <template #filter>
      <a-form-item label="电子祝福">
        <a-switch
          v-model:checked="formState.is_support"
          checked-children="开"
          un-checked-children="关"
          @change="onChange"
        />
      </a-form-item>
      <a-form-item label="订单物料">
        <a-switch
          v-model:checked="formState.is_additional_sku"
          checked-children="开"
          un-checked-children="关"
          @change="onChange"
        />
      </a-form-item>
      <a-form-item label="链接">
        <a-button type="link" style="padding: 0" @click="copyLinkByRoute('customVoiceVerify')">
          查看祝福卡链接
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="materialsFormState?.config?.materials"
        row-key="id"
        :scroll="{ x: '1600px' }"
        :custom-row="customRow"
      >
        <a-table-column title="素材图" width="200px" ellipsis>
          <template #default="{ record }">
            <a-image
              :width="50"
              :height="50"
              :src="record.url"
              class="cursor-pointer"
            />
          </template>
        </a-table-column>
        <a-table-column title="物料信息" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-if="record.spec"
              :url="record.spec.photo_url"
              :title="record.spec.goods_title"
              :subtit="record.spec.subtit"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record , index }">
            <a-button type="link" @click="onEdit(record , index)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="onDelete(record,index)"
            >
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <select-sku-goods v-model:visible="modalVisible" :mode="goodsSelectType.single" @ok="onSpecChange" />
  <a-modal
    :visible="tableModalVisible"
    :title="tableModalVisible.id ? '编辑素材' : '新增素材'"
  >
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item label="素材图片" name="photo_url" class="required">
        <uc-upload
          upload-text=" "
          :list="tableFormState.url ? [tableFormState.url] : []"
          :max-length="1"
          multiple
          @update:list="data => (tableFormState.url = data[0])"
        />
      </a-form-item>
      <a-form-item label="物料信息" name="name">
        <uc-img-text
          v-if="tableFormState.spec.sku"
          class="m-b-10"
          :url="tableFormState.spec.photo_url"
          :title="tableFormState.spec.goods_title"
          :subtit="tableFormState.spec.subtit"
        />
        <a-button class="m-r-10" type="primary" @click="setModalVisible(true)">选择物料</a-button>
        <a-button v-if="tableFormState.spec.sku" @click="tableFormState.spec = {}">删除物料</a-button>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setTableModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" @click="onSpecConfirm">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { goodsSelectType } from '@/modules/promotion/enums'
import { useModalVisible } from '@/composables/useToggles'
import { settingApi, settingKeyUpdateApi } from '@/modules/setting/api'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { customPageConfigApi, customPageConfigUpdateApi } from '@/modules/page-config/api'

const path = 'electron-blessing'
const key = 'electron_blessing'

const { formState, setFormState } = useFormState({
  is_support: false,
  is_additional_sku: false,
  oldValue: {}
})

const { modalVisible, setModalVisible } = useModalVisible()

const getSubtit = (sku, attrs) =>
  `${sku}: ${Array.isArray(attrs) ? attrs.reduce((prev, item, i) => prev + (i ? '/' : '') + item.value, '') : attrs}`

const onSpecChange = ([spec]) => {
  const {
    photo_url,
    sku,
    goods: { title },
    attrs
  } = spec

  tableFormState.value.spec = {
    sku,
    photo_url,
    goods_title: title,
    subtit: getSubtit(sku, attrs)
  }
}

const { formState: materialsFormState, setFormState: setMaterialsFormState } = useFormState({ config: {} })

const onChange = () => {
  const value = { ...formState.value.oldValue , ...formState.value }
  delete value.oldValue
  settingKeyUpdateApi(key).post({
    key,
    value
  })
}

const onMaterialChange = () => {
  customPageConfigUpdateApi(path).post(materialsFormState.value)
}

const onEdit = (record, index) => {
  tableFormState.value = {
    index,
    url: record.url,
    spec: record.spec || {}
  }
  tableModalVisible.value = true
}

const onAdd = () => {
  resetTableFormState()
  tableModalVisible.value = true
}

const onDelete = (_, index) => {
  materialsFormState.value.config.materials.splice(index, 1)
  onMaterialChange()
}

const { formState: tableFormState, setFormRules: setTableFormRules, resetFormState: resetTableFormState , validateForm: validateTableForm } = useFormState({
  index: -1,
  url: '',
  spec: {},
})

setTableFormRules({
  url: {
    required: true,
    message: '请上传素材图片'
  }
})

const { modalVisible: tableModalVisible , setModalVisible: setTableModalVisible } = useModalVisible()

const onSpecConfirm = async () => {
  if (!(await validateTableForm())) {
    return
  }

  const index = tableFormState.value.index
  delete tableFormState.value.index

  if(index > -1) {
    materialsFormState.value.config.materials[index] = tableFormState.value
  } else {
    materialsFormState.value.config.materials.push(JSON.parse(JSON.stringify(tableFormState.value)))
  }

  tableModalVisible.value = false

  onMaterialChange()
}

const customRow = (record, index) => ({
  draggable: 'true',
  onDragstart: (event) => {
    event.dataTransfer.setData('dragIndex', index);
  },
  onDragover: (event) => {
    event.preventDefault();
  },
  onDrop: (event) => {
    const dragIndex = event.dataTransfer.getData('dragIndex');
    const dropIndex = index;
    const data = materialsFormState.value.config.materials
    const draggedItem = data[dragIndex];
    data.splice(dragIndex, 1);
    data.splice(dropIndex, 0, draggedItem);
    onMaterialChange()
  },
});

useLoadingMessage(
  Promise.all([settingApi.get({ filters: { key } }), customPageConfigApi(path).get()]).then(([setting, config]) => {
    const [{ value }] = setting
    setFormState({
      is_support: value.is_support,
      is_additional_sku: value.is_additional_sku,
      oldValue: value
    })
    setMaterialsFormState(config)
  }),
  {
    loadingText: '正在加载数据'
  }
)
</script>
<style scoped lang="less"></style>
