<template>
  <div class="custom-template">
    <a-space direction="vertical" :size="20" style="width: 100%">
      <a-card v-for="(item, index) in formState.config" :key="index" :title="item.title">
        <DragTags
          v-model="item.value"
          :index="index"
          :closable="true"
          :close-title="`你确定要删除该${item.title}么？`"
          :type="item.key"
          @ok="moveOk"
          @edit="onEdit"
          @close="onDelete"
        />
        <a-tag class="custom-template__tag cursor-pointer" @click="onAdd(index)">
          <uc-ant-icon name="PlusOutlined" />
          添加{{ item.title }}
        </a-tag>
      </a-card>
    </a-space>
    <a-modal :visible="modalVisible" :title="`${modalTitle}`" @cancel="closeModal">
      <a-form :label-col="4" :wrapper-col="modalWrapperCol">
        <a-form-item v-if="formState.config[modalFormState.index].key === customRecommendTextType.select" :label="modalTitle" class="required">
          <a-input
            v-model:value="modalFormState.value[0]"
            :placeholder="`请输入第一行${formState.config[modalFormState.index].title}`"
          />
          <a-input
            v-model:value="modalFormState.value[1]"
            style="margin-top: 10px"
            :placeholder="`请输入第二行${formState.config[modalFormState.index].title}`"
          />
        </a-form-item>
        <a-form-item v-else :label="modalTitle" class="required">
          <a-input
            v-model:value="modalFormState.value"
            :placeholder="`请输入${formState.config[modalFormState.index].title}`"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="closeModal"> 取消 </a-button>
        <a-button type="primary" :loading="formLoading" @click="handleSubmitTag"> 确定 </a-button>
      </template>
    </a-modal>
  </div>
</template>
<script setup>
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { customPageConfigApi, customPageConfigUpdateApi } from '@/modules/page-config/api'
import { useFormState } from '@/composables/useFormState'
import { useModalVisible } from '@/composables/useToggles'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import DragTags from '@/components/drag-tags/index.vue'
import { customRecommendTextType } from '@/modules/custom/enums'

const { formState, setFormState } = useFormState({
  config: undefined
})
const path = 'custom-recommend-text'
const modalTitle = ref('')

nextTick(() => {
  useLoadingMessage(customPageConfigApi(path).get().then(setFormState), {
    loadingText: '正在加载数据'
  })
})

const {
  formState: modalFormState,
  setFormState: modalSetFormState,
  resetFormState: modalResetFormState,
  validateForm: modalValidateForm,
  setFormRules: modalSetFormRules
} = useFormState({ value: undefined, index: 0 , tagIndex: 0 })
const { modalVisible, setModalVisible } = useModalVisible()
const closeModal = () => {
  setModalVisible(false)
}

const onDelete = e => {
  const { index, tagIndex } = e
  formState.value.config[index].value.splice(tagIndex, 1)
  onSubmit()
}

const isEdit = ref(false)

const onAdd = index => {
  modalResetFormState()

  const state = {
    value: undefined,
    index: 0 ,
    tagIndex: 0
  }
  const item = formState.value.config[index]
  state.index = index
  modalTitle.value = '添加' + item.title
  isEdit.value = false

  if(item.key === customRecommendTextType.select) {
    state.value = ['','']
    modalSetFormRules(
      {
        value: {
          validator(rule, [val, val1] = []) {
            if (!val) return Promise.reject(`请输入第一行${item.title}`)
            if (!val1) return Promise.reject(`请输入第二行${item.title}`)
            return Promise.resolve()
          },
        }
      },
      true
    )
  } else {
    modalSetFormRules(
      {
        value: { required: true, message: `请输入${item.title}` }
      },
      true
    )
  }

  modalSetFormState(state)
  setModalVisible(true)
}

const onEdit = ({index, tagIndex, tag}) => {
  modalResetFormState()
  modalFormState.value.index = index
  modalFormState.value.tagIndex = tagIndex
  modalFormState.value.value = tag
  modalTitle.value = '编辑' + formState.value.config[index].title
  isEdit.value = true
  modalSetFormRules(
    {
      value: { required: true, message: `请输入${formState.value.config[index].title}` }
    },
    true
  )
  setModalVisible(true)
}

const onSubmit = async () => {
  const form = cloneDeep(formState.value)
  setFormState(await customPageConfigUpdateApi(path).post(form))
  message.success('操作成功')
}

const formLoading = ref(false)
const handleSubmitTag = async () => {
  if (!(await modalValidateForm())) {
    return
  }
  formLoading.value = true
  const { value, index, tagIndex } = modalFormState.value

  if(isEdit.value) {
    formState.value.config[index].value[tagIndex] = value
  } else {
    formState.value.config[index].value.push(value)
  }

  onSubmit()
    .then(() => {
      setModalVisible(false)
    })
    .finally(() => (formLoading.value = false))
}

const moveOk = () => {
  formLoading.value = true
  onSubmit().finally(() => (formLoading.value = false))
}
</script>
<style scoped lang="less">
.custom-template {
  &__tag {
    margin-bottom: 10px;
  }
  :deep(.ant-card-body) {
    padding-bottom: 14px;
  }
}
</style>
