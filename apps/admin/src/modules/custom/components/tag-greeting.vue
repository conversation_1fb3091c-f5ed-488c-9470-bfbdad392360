<template>
  <a-card :title="customGoodsType.filter(customGoodsType.tag_greeting).label">
    <a-form-item label="选择模版" name="bg" class="required">
      <a-select v-model:value="formState.template_id" placeholder="请选择模版" :options="templates" />
    </a-form-item>
    <custom-box v-model:value="formState.config" />
  </a-card>
</template>
<script setup>
import { customGoodsType } from '@/modules/custom/enums'
import { useFormState } from '@/composables/useFormState'
import { customTemplatesApi } from '@/modules/custom/api'
import CustomBox from './custom-box.vue'

const props = defineProps({
  value: {
    type: Object,
    value: {}
  }
})
const emit = defineEmits(['update:value'])

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  config: {},
  template_id: undefined,
  type: customGoodsType.tag_greeting
})

const templates = ref([])

onMounted(async () => {
  const list = await customTemplatesApi.list({
    filters: { type: customGoodsType.tag_greeting }
  })
  templates.value = list.map(item => ({ label: item.title, value: item.id }))
})

watch(
  () => formState.value,
  () => {
    emit('update:value', formState.value)
  },
  { deep: true }
)

setFormState(props.value)
</script>
