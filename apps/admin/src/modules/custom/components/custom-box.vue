<template>
  <div class="custom-box">
    <a-form-item label="商品图片" name="bg" class="required">
      <div class="custom-box__photo">
        <drag-img :info="dragInfo" @update:info="onUpdateFormState" />
      </div>
      <uc-upload
        :list="[]"
        class="bg-upload"
        :max-length="1"
        :upload-text="formState.bg_url ? '更换背景图片' : '上传背景图片'"
        @update:list="onUpdatePhoto"
      />
    </a-form-item>
    <a-form-item label="图片宽度" name="width" class="required">
      <a-input-number v-model:value="formState.width" placeholder="请输入图片宽度（自动识别）" disabled />
    </a-form-item>
    <a-form-item label="图片高度" name="height" class="required">
      <a-input-number v-model:value="formState.height" placeholder="请输入图片高度（自动识别）" disabled />
    </a-form-item>
    <a-form-item label="定制区域宽度" name="layer_width" class="required">
      <a-input-number
        v-model:value="formState.layer_width"
        placeholder="请输入定制区域宽度"
        @change="onUpdateDragInfo"
      />
    </a-form-item>
    <a-form-item label="定制区域高度" name="layer_height" class="required">
      <a-input-number
        v-model:value="formState.layer_height"
        placeholder="请输入定制区域高度"
        @change="onUpdateDragInfo"
      />
    </a-form-item>
    <a-form-item label="定制区域上边距" name="y" class="required">
      <a-input-number v-model:value="formState.y" placeholder="请输入定制区域上边距" @change="onUpdateDragInfo" />
    </a-form-item>
    <a-form-item label="定制区域左边距" name="x" class="required">
      <a-input-number v-model:value="formState.x" placeholder="请输入定制区域左边距" @change="onUpdateDragInfo" />
    </a-form-item>
  </div>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import DragImg from '@/modules/page-config/pages/my-config/drag-img.vue'
import { generateRandom } from '@/utils/functions'
import { throttle } from 'lodash'
const relWidth = 375
const initHotSize = { width: 100, height: 100 } // 初始化热区大小
const { width: iw, height: ih } = initHotSize
let imageWidth = 0
const pxToRpx = px => {
  return px * (imageWidth / relWidth)
}
const rpxToPx = rpx => {
  return rpx / (imageWidth / relWidth)
}
const props = defineProps({
  value: {
    type: Object,
    value: {}
  }
})
const emit = defineEmits(['update:value'])
const { formState, setFormState } = useFormState({
  bg_url: '',
  width: '',
  height: '',
  layer_width: '',
  layer_height: '',
  x: '',
  y: ''
})

const dragInfo = ref({})

const getImgInfo = async src => {
  return await new Promise(resolve => {
    const img = new Image()
    img.src = src
    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height
      })
    }
  })
}
const id = generateRandom()
const onUpdatePhoto = async ([url]) => {
  const { width, height } = await getImgInfo(url)
  imageWidth = width
  const left = ~~((relWidth - iw) / 2)
  const top = ~~(((height * relWidth) / width - ih) / 2)
  const data = { ...initHotSize, id }
  formState.value = {
    ...formState.value,
    bg_url: url,
    width,
    height,
    x: pxToRpx(left),
    y: pxToRpx(top),
    layer_width: pxToRpx(data.width),
    layer_height: pxToRpx(data.height)
  }
  onUpdateDragInfo()
}
const onUpdateFormState = throttle(e => {
  const [item] = e.items
  const { width, height, left, top, state } = item
  formState.value = {
    ...formState.value,
    x: pxToRpx(left),
    y: pxToRpx(top),
    layer_width: pxToRpx(width),
    layer_height: pxToRpx(height),
    state
  }
}, 500)

const onUpdateDragInfo = () => {
  const { layer_width, layer_height, x, y, bg_url } = formState.value
  dragInfo.value = {
    bg_url,
    items: [
      {
        id,
        state: false,
        width: rpxToPx(layer_width),
        height: rpxToPx(layer_height),
        left: rpxToPx(x),
        top: rpxToPx(y)
      }
    ]
  }
}
watch(
  () => formState.value,
  () => {
    emit('update:value', { ...formState.value, bg: formState.value.bg_url })
  },
  { deep: true }
)
onMounted(() => {
  const { x, y, layer_width, layer_height, bg, width } = props.value
  if (Object.keys(props.value).length) {
    imageWidth = width
    setFormState({
      ...props.value,
      bg_url: bg,
      x: x,
      y: y,
      layer_width: layer_width,
      layer_height: layer_height
    })
    onUpdateDragInfo()
  }
})
</script>
<style scoped lang="less">
.custom-box {
  &__photo {
    width: 375px;
  }
  .bg-upload {
    margin-top: 20px;
    :deep(.ant-upload-picture-card-wrapper) {
      width: 100px;
      height: 30px;
      .anticon-plus {
        display: none;
      }
      .ant-upload-list-picture-card {
        display: none;
      }
      .ant-upload-select-picture-card {
        width: 100% !important;
        height: 100% !important;
        margin: 0;
        .ant-upload > div {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    :deep(.ant-upload-list) {
      width: 100%;
      height: 100%;
      .ant-upload-list-picture-card-container {
        width: 100%;
        height: 100%;
        .ant-upload-list-item {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  :deep(.u-index),
  :deep(.u-delete) {
    display: none;
  }
}
</style>
