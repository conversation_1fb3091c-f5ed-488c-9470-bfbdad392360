/**
 * 可筛选的值
 */
export const filterSelectVuale = Object.freeze({
  /**
   * 追踪编码
   */
  code: 'code',

  /**
   * 追踪名称
   */
  title: 'title',

  options() {
    return [
      { label: '追踪编码', value: this.code},
      { label: '追踪名称', value: this.title},
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})


export const rankingType = Object.freeze({
  payment: 'payment',
  payment_quantity: 'payment_quantity',
  pv: 'pv',
  add_cart: 'add_cart',
})