export default {
  path: 'data',
  meta: {
    title: '看板',
    antIcon: 'DashboardOutlined'
  },
  children: [
    {
      path: 'daily-data',
      name: 'daily-data',
      meta: {
        title: '每日数据看板'
      },
      component: () => import('./pages/daily-data/list')
    },
    {
      path: 'channel-track',
      name: 'channel-track',
      meta: {
        title: '渠道追踪码'
      },
      component: () => import('./pages/channel-track/list')
    },
    {
      path: 'goods-ranking',
      name: 'goods-ranking',
      meta: {
        title: '商品TOP10'
      },
      component: () => import('./pages/goods-ranking/index')
    },
    {
      path: 'goods-data',
      name: 'goods-data',
      meta: {
        title: '商品数据明细'
      },
      component: () => import('./pages/goods-data/list')
    }
  ]
}
