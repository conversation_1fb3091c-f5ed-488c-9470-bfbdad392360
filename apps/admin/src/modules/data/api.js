import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

export const channelTrackApi = apiFactory.restful('/data/channel-track')
export const channelTrackTreeApi = apiFactory.command('/data/channel-track/tree')
export const exportTrackCodeApi = type =>
  `${import.meta.env.VITE_API_BASE_URL}/data/channel-track/excel/export/${type}?token=${state.token}` //导出追踪码
export const trackCodeStatisticApi = apiFactory.restful('/data/channel-track-statistics') //追踪码统计
export const shopDateStatisticsApi = apiFactory.restful('/data/shop-date-statistics') //看板每日数据
export const shopDateStatisticsTodayApi = apiFactory.restful('/data/shop-date-statistics/today') //实时今日统计数据
export const exportShopDateStatisticApi = type =>
  `${import.meta.env.VITE_API_BASE_URL}/data/shop-date-statistics/export/${type}?token=${state.token}` //导出看板每日数据

export const goodsDataApi = apiFactory.restful('/data/shop-goods-date-statistics')
export const exportGoodsDataApi = type =>
  `${import.meta.env.VITE_API_BASE_URL}/data/shop-goods-date-statistics/excel/export/${type}?token=${state.token}` //导出追踪码

export const goodsRankingDataApi = type =>  apiFactory.restful(`/data/shop-goods-date-statistics/ranking/${type}`) // 排行榜
export const exportGoodsRankingDataApi = (rankingType, type) =>  `${import.meta.env.VITE_API_BASE_URL}/data/shop-goods-date-statistics/ranking/excel/export/${rankingType}/${type}?token=${state.token}` // 排行榜导出
