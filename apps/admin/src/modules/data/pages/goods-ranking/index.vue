<template>
  <!-- 列表页基础组件 -->
  <div class="layout-list">
    <a-space direction="vertical" :size="20">
      <a-card class="std-filter">
        <a-form layout="inline">
          <a-form-item name="date">
            <uc-date-picker ref="datePicker" v-model="filterState.date" :allow-clear="true" />
          </a-form-item>
          <a-form-item name="date">
            <a-range-picker v-model:value="filterState.date" @change="dateRangeChange" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="handleSearch"> 查询 </a-button>
            <a-button @click="resetFilterState"> 重置 </a-button>
          </a-form-item>
        </a-form>
      </a-card>
      <uc-row>
        <uc-col>
          <a-card :head-style="{'text-align': 'center'}" v-bind="{ loading: paymentQuantityRankingLoading }">
            <template #title>
              <FlagOutlined /> 商品支付数量top10
            </template>
            <template #extra>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(options) => exportExcel(rankingType.payment_quantity, options)">
                    <a-menu-item key="xlsx"> 导出Excel </a-menu-item>
                    <a-menu-item key="csv"> 导出Csv </a-menu-item>
                  </a-menu>
                </template>
                <DownloadOutlined /> 
              </a-dropdown>
            </template> 
            <a-table
              v-if="!paymentQuantityRankingLoading"
              class="ant-table-striped"
              :data-source="paymentQuantityRanking"
              row-key="id"
              :loading="paymentQuantityRankingLoading"
              :pagination="false"
              bordered
              :row-class-name="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            >
              <a-table-column title="排名" width="70px" align="center">
                <template #default="{ index }">
                  <span>{{ index+1 }}</span>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" ellipsis>
                <template #default="{ record }">
                  <a-tooltip placement="top" :title="record.goods_title">
                    <span>{{ record.goods_title }}</span>
                  </a-tooltip>
                </template>
              </a-table-column>
              <a-table-column title="sku编码" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.sku }}</span>
                </template>
              </a-table-column>
              <a-table-column title="规格" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.spec_standard_name }}</span>
                </template>
              </a-table-column>
              <a-table-column title="支付件数" width="100px" align="center">
                <template #default="{ record }">
                  <span>{{ record.paid_quantity }}</span>
                </template>
              </a-table-column>
            </a-table>
          </a-card>
        </uc-col>
        <uc-col>
          <a-card :head-style="{'text-align': 'center'}" v-bind="{ loading: paymentRankingLoading }">
            <template #title>
              <FlagOutlined /> 商品支付金额top10
            </template>
            <template #extra>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(options) => exportExcel(rankingType.payment, options)">
                    <a-menu-item key="xlsx"> 导出Excel </a-menu-item>
                    <a-menu-item key="csv"> 导出Csv </a-menu-item>
                  </a-menu>
                </template>
                <DownloadOutlined /> 
              </a-dropdown>
            </template> 
            <a-table
              v-if="!paymentRankingLoading"
              class="ant-table-striped"
              :data-source="paymentRanking"
              row-key="id"
              :loading="paymentRankingLoading"
              :pagination="false"
              bordered
              :row-class-name="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            >
              <a-table-column title="排名" width="70px" align="center">
                <template #default="{ index }">
                  <span>{{ index+1 }}</span>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" ellipsis>
                <template #default="{ record }">
                  <a-tooltip placement="top" :title="record.goods_title">
                    <span>{{ record.goods_title }}</span>
                  </a-tooltip>
                </template>
              </a-table-column>
              <a-table-column title="sku编码" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.sku }}</span>
                </template>
              </a-table-column>
              <a-table-column title="规格" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.spec_standard_name }}</span>
                </template>
              </a-table-column>
              <a-table-column title="支付金额" width="100px" align="center">
                <template #default="{ record }">
                  <span>{{ $formatters.thousandSeparator(record.paid_amount) }}</span>
                </template>
              </a-table-column>
            </a-table>
          </a-card>
        </uc-col>
        <uc-col>
          <a-card :head-style="{'text-align': 'center'}" v-bind="{ loading: pvRankingLoading }">
            <template #title>
              <FlagOutlined /> 商品访问top10
            </template>
            <template #extra>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(options) => exportExcel(rankingType.pv, options)">
                    <a-menu-item key="xlsx"> 导出Excel </a-menu-item>
                    <a-menu-item key="csv"> 导出Csv </a-menu-item>
                  </a-menu>
                </template>
                <DownloadOutlined /> 
              </a-dropdown>
            </template> 
            <a-table
              v-if="!pvRankingLoading"
              class="ant-table-striped"
              :data-source="pvRanking"
              row-key="id"
              :loading="pvRankingLoading"
              :pagination="false"
              bordered
              :row-class-name="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            >
              <a-table-column title="排名" width="70px" align="center">
                <template #default="{ index }">
                  <span>{{ index+1 }}</span>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" ellipsis>
                <template #default="{ record }">
                  <a-tooltip placement="top" :title="record.goods_title">
                    <span>{{ record.goods_title }}</span>
                  </a-tooltip>
                </template>
              </a-table-column>
              <a-table-column title="sku编码" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.sku }}</span>
                </template>
              </a-table-column>
              <a-table-column title="规格" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.spec_standard_name }}</span>
                </template>
              </a-table-column>
              <a-table-column title="PV/UV" width="100px" align="center">
                <template #default="{ record }">
                  <span>{{ record.pv }}/{{ record.uv }}</span>
                </template>
              </a-table-column>
            </a-table>
          </a-card>
        </uc-col>
        <uc-col>
          <a-card :head-style="{'text-align': 'center'}" v-bind="{ loading: addCartRankingLoading }">
            <template #title>
              <FlagOutlined /> 商品加购top10
            </template>
            <template #extra>
              <a-dropdown>
                <template #overlay>
                  <a-menu @click="(options) => exportExcel(rankingType.add_cart, options)">
                    <a-menu-item key="xlsx"> 导出Excel </a-menu-item>
                    <a-menu-item key="csv"> 导出Csv </a-menu-item>
                  </a-menu>
                </template>
                <DownloadOutlined /> 
              </a-dropdown>
            </template> 
            <a-table
              v-if="!addCartRankingLoading"
              class="ant-table-striped"
              :data-source="addCartRanking"
              row-key="id"
              :loading="addCartRankingLoading"
              :pagination="false"
              bordered
              :row-class-name="(record, index) => (index % 2 === 1 ? 'table-striped' : null)"
            >
              <a-table-column title="排名" width="70px" align="center">
                <template #default="{ index }">
                  <span>{{ index+1 }}</span>
                </template>
              </a-table-column>
              <a-table-column title="商品名称" ellipsis>
                <template #default="{ record }">
                  <a-tooltip placement="top" :title="record.goods_title">
                    <span>{{ record.goods_title }}</span>
                  </a-tooltip>
                </template>
              </a-table-column>
              <a-table-column title="sku编码" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.sku }}</span>
                </template>
              </a-table-column>
              <a-table-column title="规格" width="160px" ellipsis>
                <template #default="{ record }">
                  <span>{{ record.spec_standard_name }}</span>
                </template>
              </a-table-column>
              <a-table-column title="加购次数" width="100px" align="center">
                <template #default="{ record }">
                  <span>{{ record.add_cart_count }}</span>
                </template>
              </a-table-column>
            </a-table>
          </a-card>
        </uc-col>
      </uc-row>
    </a-space>
  </div>
</template>

<script setup>
import moment from 'moment'
import { useFormState } from '@/composables/useFormState'
import { useApiRequest } from '@/composables/useApiRequest'
import { goodsRankingDataApi, exportGoodsRankingDataApi } from '../../api'
import { rankingType } from '../../enums'
import { DownloadOutlined, FlagOutlined } from '@ant-design/icons-vue'


const defaultDate = moment().subtract(1, 'days').format('YYYY-MM-DD')
const datePicker = ref()

const {
  formState: filterState,
  resetFormState: resetFilterState,
  onRestFormState
} = useFormState({
  date: [defaultDate + ' 00:00:00', defaultDate + ' 23:59:59']
})

const handleSearch = () => {
  paymentRankingSetPage()
  paymentQuantityRankingSetPage()
  pvRankingSetPage()
  addCartRankingSetPage()
}

onRestFormState(() => {
  datePicker.value.reset()
  paymentRankingSetPage()
  paymentQuantityRankingSetPage()
  pvRankingSetPage()
  addCartRankingSetPage()
})

const { data: paymentRanking, getData: paymentRankingSetPage, loading: paymentRankingLoading} = useApiRequest(() =>
goodsRankingDataApi(rankingType.payment).list({
    filters: {
      date: filterState.value.date
    },
  })
)

const { data: paymentQuantityRanking, getData: paymentQuantityRankingSetPage, loading: paymentQuantityRankingLoading} = useApiRequest(() =>
goodsRankingDataApi(rankingType.payment_quantity).list({
    filters: {
      date: filterState.value.date
    },
  })
)

const { data: pvRanking, getData: pvRankingSetPage, loading: pvRankingLoading} = useApiRequest(() =>
goodsRankingDataApi(rankingType.pv).list({
    filters: {
      date: filterState.value.date
    },
  })
)

const { data: addCartRanking, getData: addCartRankingSetPage, loading: addCartRankingLoading} = useApiRequest(() =>
goodsRankingDataApi(rankingType.add_cart).list({
    filters: {
      date: filterState.value.date
    },
  })
)


const exportExcel = (rankingType, { key }) => {
  const filters = {
    date: filterState.value.date,
  }
  let excelExportUrlQuery = encodeURI(`${exportGoodsRankingDataApi(rankingType, key)}&filters=${JSON.stringify(filters)}`)
  window.open(excelExportUrlQuery)
}

</script>

<style lang="less" scoped>
.ant-table-striped :deep(.table-striped) td {
  background-color: #fafafa;
}
.layout-list {
  @margin-filter: 10px;
  > :deep(.ant-space) {
    width: 100%;
  }
  :deep(.ant-form-inline .ant-form-item) {
    margin-bottom: @margin-filter;
  }
  .std-filter {
    :deep(.ant-card-body) {
      padding-bottom: 8px;
      .ant-form-inline {
        .ant-form-item {
          margin-bottom: 16px;

          .ant-input {
            width: 300px;
          }

          .ant-input-group-wrapper {
            width: 420px;

            .ant-input-group-addon {
              .ant-select {
                width: 120px;
              }
            }

            .ant-input {
              width: 100%;
            }
          }

          .ant-select {
            width: 120px;
          }

          .ant-calendar-picker,
          .ant-cascader-picker {
            width: 250px;

            .ant-input {
              width: 100%;

              .ant-calendar-range-picker-input {
                display: inline-block;
              }
            }
          }
        }
      }
      // 按钮
      .ant-form-item:last-child .ant-form-item-control-input-content .ant-btn {
        margin-right: @margin-filter;
        &:last-child {
          margin-right: 0;
        }
      }
    }
  }
}
</style>