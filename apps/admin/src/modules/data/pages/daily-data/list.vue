<template>
  <div>
    <uc-layout-list title="实时数据展示（今日）">
      <template #list>
        <a-table
          :data-source="dayTableData"
          :columns="dayTableColumms"
          row-key="date"
          :loading="loading"
          :pagination="false"
        />
      </template>
    </uc-layout-list>
    <uc-layout-list title="数据概览">
      <template #extra>
        <div class="flex">
          <uc-date-picker ref="datePicker" v-model="formState.date" :step="5" @change="getOverviewData" />
          <a-dropdown class="m-l-10">
            <template #overlay>
              <a-menu @click="exportExcel">
                <a-menu-item key="xlsx">
                  导出Excel
                </a-menu-item>
                <a-menu-item key="csv">
                  导出Csv
                </a-menu-item>
              </a-menu>
            </template>
            <a-button>
              导出
            </a-button>
          </a-dropdown>
        </div>
      </template>
      <template #list>
        <a-table
          :data-source="overviewTableData"
          :columns="overviewColumms"
          row-key="key"
          :loading="overviewLoading"
          :pagination="false"
        />
      </template>
    </uc-layout-list>
  </div>
</template>
<script setup>
import { useApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { shopDateStatisticsApi, shopDateStatisticsTodayApi, exportShopDateStatisticApi } from '../../api'
import { dateSelectVuale } from '@/enums/date'
import moment from 'moment'

const datePicker = ref()
const { data, loading } = useApiRequest(() => shopDateStatisticsTodayApi.list())
const dayTableData = computed(() => {
  if (!data.value) {
    return []
  }
  const copy = toRaw(data.value)
  for (const key in data.value) {
    data.value[key] = amountDiv(key, copy[key])
  }
  return [copy]
})


const dayTableColumms = computed(() => columms.filter(c => !c.onlyOverview))

const { formState, setFormRules, validateForm } = useFormState({
  date: [moment().subtract(6, 'days').format('YYYY-MM-DD'), moment().subtract(1, 'days').format('YYYY-MM-DD')]
})

setFormRules({
  date: { required: true, message: '请选择时间范围' },
})

const {
  data: overviewData,
  getData: getOverviewData,
  loading: overviewLoading
} = useApiRequest(() =>
  shopDateStatisticsApi.list({
    filters: {
      ...useTransformQuery({ date: formState.value.date }, { date: 'dateRange' }),
      type: datePicker.value.dateType || dateSelectVuale.day
    },
    sorts: ['-date']
  })
)
const overviewTableData = ref([])

watch(
  () => overviewData.value,
  () => {
    let arr = overviewData.value
    if (!arr) return []

    overviewColumms.map((item, index) => {
      if (index) {
        item.title = arr[index - 1] ? arr[index - 1].date + ' 较上一周期' : '/'
      }
    })

    overviewTableData.value = columms.map(item => {
      return {
        title: item.title,
        key: item.key,
        data1: arr[0] ? arr[0][item.key] : '-',
        data2: arr[1] ? arr[1][item.key] : '-',
        data3: arr[2] ? arr[2][item.key] : '-',
        data4: arr[3] ? arr[3][item.key] : '-',
        data5: arr[4] ? arr[4][item.key] : '-',
        data6: arr[5] ? arr[5][item.key] : '-'
      }
    }).filter(item => !columms.find(c => c.key == item.key)?.onlyToday)
  }
)

const exportExcel = async ({ key }) => {
  if (!(await validateForm())) return
  const filters = {
    ...useTransformQuery({ date: formState.value.date }, { date: 'dateRange' }),
    type: datePicker.value.dateType || dateSelectVuale.day
  }
  let excelExportUrlQuery = encodeURI(`${exportShopDateStatisticApi(key)}&filters=${JSON.stringify(filters)}&sorts[]=-date`)
  window.open(excelExportUrlQuery)
}

const amountDiv = (key, value) => {

  const valueStr = ['sales_amount', 'paid_amount', 'refund_amount', 'paid_freight_amount', 'unpaid_amount'].indexOf(key) !== -1 ? (Number(value) / 100) : value
  return (Number(valueStr) || `0`).toLocaleString({
    style: 'currency',
  })
}

const customRender = ({ text, column, record }) => {
  const key = column.key
  const compKey = key.substring(0, key.length - 1) + (parseInt(key.substring(key.length - 1)) + 1)
  const compVal = Number(record[compKey])
  const originVal = Number(text)
  const iconStyle = { marginLeft: '10px' }
  let rate = ''
  if (originVal > compVal) {
    if (compVal == 0) {
      rate = '+∞'
    } else {
      rate = '+' + (((originVal - compVal) / compVal) * 100).toFixed(2) + '%'
    }
    iconStyle.color = 'red'
  } else if (originVal < compVal) {
    rate = '-' + (((compVal - originVal) / compVal) * 100).toFixed(2) + '%'
    iconStyle.color = 'green'
  } else {
    rate = '0.00%'
    iconStyle.color = 'black'
  }

  return h('div', { class: 'flex' }, [
    h('div', { class: 'flex-1 t-right' }, amountDiv(record.key, text)),
    h('div', { class: 'flex-1', style: iconStyle }, rate)
  ])
}

const customHeaderCell = () => ({ style: { textAlign: 'center' } })

// table column
const columms = [
  {
    title: '访客数',
    dataIndex: 'visitors',
    key: 'visitors',
    align: 'right'
  },
  {
    title: '新访客数',
    dataIndex: 'new_visitors',
    key: 'new_visitors',
    align: 'right'
  },
  {
    title: '销售额',
    dataIndex: 'sales_amount',
    key: 'sales_amount',
    align: 'right'
  },
  {
    title: '订单量',
    dataIndex: 'order_count',
    key: 'order_count',
    align: 'right'
  },
  {
    title: '下单人数',
    dataIndex: 'placed_order_users',
    key: 'placed_order_users',
    align: 'right'
  },
  {
    title: '下单次数',
    dataIndex: 'placed_order_times',
    key: 'placed_order_times',
    align: 'right'
  },
  {
    title: '支付人数',
    dataIndex: 'paid_users',
    key: 'paid_users',
    align: 'right'
  },
  {
    title: '支付订单数',
    dataIndex: 'paid_order_count',
    key: 'paid_order_count',
    align: 'right'
  },
  {
    title: '支付金额',
    dataIndex: 'paid_amount',
    key: 'paid_amount',
    align: 'right'
  },
  {
    title: '支付运费',
    dataIndex: 'paid_freight_amount',
    key: 'paid_freight_amount',
    align: 'right',
    onlyOverview: true,
  },
  {
    title: '退款人数',
    dataIndex: 'refund_users',
    key: 'refund_users',
    align: 'right'
  },
  {
    title: '退款单数',
    dataIndex: 'refund_order_count',
    key: 'refund_order_count',
    align: 'right'
  },
  {
    title: '退款金额',
    dataIndex: 'refund_amount',
    key: 'refund_amount',
    align: 'right'
  },
  {
    title: '待发货量',
    dataIndex: 'shipping_count',
    key: 'shipping_count',
    align: 'right',
    onlyToday: true,
  },
  {
    title: '待售后量',
    dataIndex: 'after_sale_count',
    key: 'after_sale_count',
    align: 'right',
    onlyToday: true,
  },
  {
    title: '未支付金额',
    dataIndex: 'unpaid_amount',
    key: 'unpaid_amount',
    align: 'right',
    onlyOverview: true,
  },
]

const overviewColumms = [
  {
    title: '指标名称',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: 'data1',
    dataIndex: 'data1',
    key: 'data1',
    customRender,
    customHeaderCell
  },
  {
    title: 'data2',
    dataIndex: 'data2',
    key: 'data2',
    customRender,
    customHeaderCell
  },
  {
    title: 'data3',
    dataIndex: 'data3',
    key: 'data3',
    customRender,
    customHeaderCell
  },
  {
    title: 'data4',
    dataIndex: 'data4',
    key: 'data4',
    customRender,
    customHeaderCell
  },
  {
    title: 'data5',
    dataIndex: 'data5',
    key: 'data5',
    customRender,
    customHeaderCell
  }
]
</script>
