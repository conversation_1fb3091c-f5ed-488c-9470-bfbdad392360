<template>
  <uc-layout-list title="渠道追踪码" class="flex">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select
            v-model:value="filterState.conditionKey"
            class="w-120"
            :options="filterSelectVuale.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="filterState.conditionValue" :placeholder=" filterState.conditionKey == 'code' ? '多个渠道码使用英文逗号分隔' : '请输入关键词'" class="w-320" />
        </a-input-group>
      </a-form-item>
      <a-form-item name="count_date">
        <uc-date-picker ref="datePicker" v-model="filterState.count_date" :allow-clear="true" />
      </a-form-item>
      <a-form-item name="count_date">
        <a-range-picker v-model:value="filterState.count_date" :disabled-date="disabledDate" value-format="YYYY-MM-DD" @calendar-change="panelChange" @change="dateRangeChange" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch"> 查询 </a-button>
        <a-button @click="resetFilterState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="showAdd"> 新增渠道追踪 </a-button>
        <a-dropdown>
          <template #overlay>
            <a-menu @click="exportExcel">
              <a-menu-item key="xlsx"> 导出Excel </a-menu-item>
              <a-menu-item key="csv"> 导出Csv </a-menu-item>
            </a-menu>
          </template>
          <a-button> 导出 </a-button>
        </a-dropdown>
      </a-space>
    </template>
    <template #list>
      <a-card v-if="loading" v-bind="{ loading }" />
      <a-table
        v-if="!loading"
        :data-source="data"
        row-key="id"
        :loading="loading"
        :pagination="false"
        :default-expand-all-rows="isExpand"
        :scroll="{ x: 1200 }"
        @change="setPage"
      >
        <a-table-column title="渠道追踪编码/名称">
          <template #default="{ record }">
            <span v-if="record.children">{{ record.title }}</span>
            <uc-img-text v-else v-bind="record" :title="record.title" :label="record.code" class="track-code-title" />
          </template>
        </a-table-column>
        <a-table-column width="130px" title="UV/PV" align="right">
          <template #default="{ record }"> {{ record.uv }} / {{ record.pv }} </template>
        </a-table-column>
        <a-table-column width="120px" title="新/老用户" align="right">
          <template #default="{ record }"> {{ record.new_user_count }} / {{ record.old_user_count }} </template>
        </a-table-column>
        <a-table-column width="150px" title="加购人/商品数" align="right">
          <template #default="{ record }">
            {{ record.add_shopping_user_count }} / {{ record.add_shopping_count }}
          </template>
        </a-table-column>
        <a-table-column width="100px" title="分享次数" align="right">
          <template #default="{ record }">
            {{ record.share_count }}
          </template>
        </a-table-column>
        <a-table-column width="100px" title="注册人数" align="right">
          <template #default="{ record }">
            {{ record.register_user_count }}
          </template>
        </a-table-column>
        <a-table-column width="150px" title="下单人/单数/金额" align="right">
          <template #default="{ record }">
            <a-button v-if="record.code" type="link" @click="toOrder(record)">
              {{ record.order_user_count }} / {{ record.order_count }} /
              {{ $formatters.thousandSeparator(record.order_amount) }}
            </a-button>
            <span v-else>
              {{ record.order_user_count }} / {{ record.order_count }} /
              {{ $formatters.thousandSeparator(record.order_amount) }}
            </span>
          </template>
        </a-table-column>
        <a-table-column width="150px" title="支付人/单数/金额" align="right">
          <template #default="{ record }">
            {{ record.paid_user_count }} / {{ record.paid_count }} /
            {{ $formatters.thousandSeparator(record.paid_amount) }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px" align="right">
          <template #default="{ record }">
            <template v-if="!record.children">
              <a-button type="link" @click="copyLink(record.path, { tc: record.code })"> 链接 </a-button>
              <a-button type="link" @click="onDetail(record.code)"> 明细 </a-button>
              <a-popconfirm
                placement="left"
                title="你确定要删除该数据么？"
                :disabled="!record.can_delete"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" class="danger" :disabled="!record.can_delete"> 删除 </a-button>
              </a-popconfirm>
            </template>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisible" title="新增渠道追踪码" @cancel="setModalVisible(false)">
    <a-form :model="formState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item required label="追踪名称" class="required">
        <a-input v-model:value="formState.title" placeholder="请输入追踪名称，不超过50字" maxlength="50" />
      </a-form-item>
      <a-form-item required label="链接页面" class="required">
        <a-input v-model:value="formState.path" placeholder="请输入链接页面URL" />
      </a-form-item>
      <a-form-item label="分组名称" class="required">
        <a-select
          v-model:value="formState.group"
          :options="groupOptions"
          placeholder="请输入分组名称，不超过50字"
          show-search
          @search="onSelectSearch"
          @blur="handleBlur"
        />
      </a-form-item>
      <a-form-item label="分类名称" class="required">
        <a-select
          v-model:value="formState.category"
          :options="categoryOptions"
          placeholder="请输入分类名称，不超过50字"
          show-search
          @search="onSelectCategorySearch"
          @blur="handleBlurCategory"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit"> 确定 </a-button>
    </template>
  </a-modal>

  <!-- 渠道码明细 -->
  <a-modal width="1200px" :visible="modalVisibleDetail" title="渠道码明细" :footer="null" @cancel="closeModelDetail">
    <a-form
      style="margin-bottom: 20px"
      layout="inline"
      :model="statisticFormState"
      :label-col="modalLabelCol"
      :wrapper-col="modalWrapperCol"
    >
      <a-form-item label="时间段" style="width: 300px">
        <a-range-picker
          v-model:value="statisticFormState.count_date"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          placeholder="时间段"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setStatisticPage">查询</a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="resetStatisticFormState">重置</a-button>
      </a-form-item>
    </a-form>
    <a-table :data-source="statisticData.items" row-key="id" :loading="statisticLoading" :pagination="stdPaginationSimple(statisticData)" @change="setStatisticPage">
      <a-table-column title="渠道追踪编码" ellipsis align="right">
        <template #default="{ record }">
          {{ record.code }}
        </template>
      </a-table-column>
      <a-table-column title="日期" ellipsis align="right">
        <template #default="{ record }">
          {{ record.count_date }}
        </template>
      </a-table-column>
      <a-table-column width="130px" title="UV/PV" align="right">
        <template #default="{ record }"> {{ record.uv }} / {{ record.pv }} </template>
      </a-table-column>
      <a-table-column width="120px" title="新/老用户" align="right">
        <template #default="{ record }"> {{ record.new_user_count }} / {{ record.old_user_count }} </template>
      </a-table-column>
      <a-table-column width="150px" title="加购人/商品数" align="right">
        <template #default="{ record }">
          {{ record.add_shopping_user_count }} / {{ record.add_shopping_count }}
        </template>
      </a-table-column>
      <a-table-column width="150px" title="下单人/单数/金额" align="right">
        <template #default="{ record }">
          {{ record.order_user_count }} / {{ record.order_count }} /
          {{ $formatters.thousandSeparator(record.order_amount) }}
        </template>
      </a-table-column>
      <a-table-column width="150px" title="支付人/单数/金额" align="right">
        <template #default="{ record }">
          {{ record.paid_user_count }} / {{ record.paid_count }} /
          {{ $formatters.thousandSeparator(record.paid_amount) }}
        </template>
      </a-table-column>
    </a-table>
  </a-modal>
</template>
<script setup>
import { toRaw } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest, useApiRequest } from '@/composables/useApiRequest'
import { channelTrackApi, channelTrackTreeApi, exportTrackCodeApi, trackCodeStatisticApi } from '../../api'
import { debounce } from 'lodash'
import { useModalVisible } from '@/composables/useToggles'
import { filterSelectVuale } from '../../enums'
import { generateRandom } from '@/utils/functions'
import moment from 'moment'
import { useRouter } from 'vue-router'

const router = useRouter()
const defaultDate = moment().subtract(1, 'days').format('YYYY-MM-DD')
const datePicker = ref()
const isExpand = ref(false)

const {
  formState: filterState,
  resetFormState: resetFilterState,
  onRestFormState
} = useFormState({
  conditionKey: 'code',
  conditionValue: undefined,
  count_date: [defaultDate + ' 00:00:00', defaultDate + ' 23:59:59']
})

const panelChange = (value) => {
  if(value[0]) {
    const start = moment(value[0])
    if(!filterState.value.count_date) {
      filterState.value.count_date = []
    }
    filterState.value.count_date[0] = start.format('YYYY-MM-DD') + ' 00:00:00'
    disabledDate(start)
  }
}
// 不可选择的日期
const disabledDate = (current) => {
  if(filterState.value.count_date) {
    // 前后不超过31天
    return !(moment(filterState.value.count_date[0]).subtract(31, 'days') < current && current < moment(filterState.value.count_date[0]).add(31, 'days'))
  } else {
    return null
  }
}

onRestFormState(() => {
  isExpand.value = false
  datePicker.value.reset()
  setPage()
})

const exportExcel = ({ key }) => {
  const filters = {
    ...useTransformQuery(
      filterState.value.conditionKey == 'code'
        ? { code: filterState.value.conditionValue }
        : { title: filterState.value.conditionValue },
      {
        code: 'like',
        title: 'like'
      }
    ),
    count_date: filterState.value.count_date
  }
  let excelExportUrlQuery = encodeURI(`${exportTrackCodeApi(key)}&filters=${JSON.stringify(filters)}`)
  window.open(excelExportUrlQuery)
}

const { data, getData: setPage, loading } = useApiRequest(() =>
  channelTrackTreeApi.get({
    filters: {
      ...useTransformQuery(
        filterState.value.conditionKey == 'code'
          ? { code: filterState.value.conditionValue }
          : { title: filterState.value.conditionValue },
        {
          title: 'like'
        }
      ),
      count_date: filterState.value.count_date
    },
  })
)

const handleSearch = () => {
  if (filterState.value.conditionValue) {
    isExpand.value = true
  } else {
    isExpand.value = false
  }
  setPage()
}

const groupOptions = computed(() => {
  const arr = [];
  data.value.map(item => {
    arr.push({
      value: item.title,
      label: item.title
    })
  })
  return arr
})

const categoryOptions = computed(() => {
  if (!formState.value.group) {
    return []
  }
  const arr = [];
  data.value.filter(item => item.title == formState.value.group)
  .map(item => {
    item.children.map(child => {
      arr.push({
        value: child.title,
        label: child.title
      })
    })
  })
  return arr
})

// const groupOptions = computed(() => {
//   const set = new Set()
//   data.value.map(item => {
//     if (item.group) set.add(item.group)
//   })
//   let arr = [...set]
//   return arr.map(a => {
//     return {
//       value: a,
//       label: a
//     }
//   })
// })

// const categoryOptions = computed(() => {
//   if (!formState.value.group) {
//     return []
//   }
//   const set = new Set()
//   data.value.filter(item => item.group == formState.value.group).map(item => {
//     if (item.category) {
//       set.add(item.category)
//     }
//   })
//   let arr = [...set]
//   return arr.map(a => {
//     return {
//       value: a,
//       label: a
//     }
//   })
// })

// const groupedTrackCodes = computed(() => {
//   if (!data.value) return []
//   const dateItems = toRaw(data.value)

//   /* 分组处理 */
//   const set = new Set()
//   dateItems.map(item => {
//     if (item.group) set.add(item.group)
//   })
//   let arr = [...set]
//   arr = arr.map(a => {
//     return {
//       title: a,
//       group: null,
//       children: []
//     }
//   })
//   const groupData = []
//   const noGroupData = dateItems.filter(item => !item.group)
//   const hasGroupData = dateItems.filter(item => item.group)
//   groupData.push(...noGroupData)
//   arr.map(a => {
//     hasGroupData.map(c => {
//       if (c.group == a.title) {
//         a.children.push(c)
//       }
//     })
//   })

//   arr = arr.map(a => {
//     const obj = a.children[0]
//     Object.keys(obj).forEach(key => {
//       if (typeof obj[key] == 'number') {
//         a[key] = a.children.reduce((pre, next) => pre + next[key], 0)
//       }
//       a.id = generateRandom()
//     })
//     return a
//   })

//   groupData.push(...arr)

//   /* 分类处理 */
//   groupData.map(group => {
//     // group.children
//     const setCategory = new Set()
//     group.children.map(item => {
//       if (item.category) setCategory.add(item.category)
//     })
//     let arrCategory = [...setCategory]
//     arrCategory = arrCategory.map(a => {
//       return {
//         title: a,
//         category: null,
//         children: []
//       }
//     })
//     const categoryData = []
//     const noCategoryData = group.children.filter(item => !item.category)
//     const hasCategoryData = group.children.filter(item => item.category)
//     categoryData.push(...noCategoryData)
//     arrCategory.map(a => {
//       hasCategoryData.map(c => {
//         if (c.category == a.title) {
//           a.children.push(c)
//         }
//       })
//     })

//     arrCategory = arrCategory.map(a => {
//       const obj = a.children[0]
//       Object.keys(obj).forEach(key => {
//         if (typeof obj[key] == 'number') {
//           a[key] = a.children.reduce((pre, next) => pre + next[key], 0)
//         }
//         a.id = generateRandom()
//       })
//       return a
//     })

//     categoryData.push(...arrCategory)

//     group.children = categoryData
//   })

//   return groupData
// })

let inputGroup
const onSelectSearch = input => {
  if (input) inputGroup = input
}
const handleBlur = () => {
  if (inputGroup) {
    formState.value.group = inputGroup
    inputGroup = ''
  }
}

let inputCategory
const onSelectCategorySearch = input => {
  if (input) inputCategory = input
}
const handleBlurCategory = () => {
  if (inputCategory) {
    formState.value.category = inputCategory
    inputCategory = ''
  }
}

const changeConditionKey = type => (filterState.value.conditionValue = undefined)

const handleDelete = async ({ id }) => {
  await channelTrackApi.delete(id)
  message.success('删除完成')
  setPage()
}

const { modalVisible, setModalVisible } = useModalVisible()

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  path: undefined,
  group: undefined,
  category: undefined
})

setFormRules({
  title: { required: true, message: '请输入追踪名称' },
  path: { required: true, message: '请输入链接页面URL' },
  group: { required: true, message: '请输入分组名称' },
  category: { required: true, message: '请输入分类名称' },
})

const showAdd = () => {
  resetFormState()
  setModalVisible(true)
}

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) {
    return
  }

  await channelTrackApi.create({ ...formState.value })
  message.success('创建完成')

  setModalVisible(false)
  setPage()
}, 500)

const trackCode = ref('')
const onDetail = code => {
  trackCode.value = code
  setStatisticPage()
  setModalVisibleDetail(true)
}

const closeModelDetail = () => {
  statisticData.value = {}
  setModalVisibleDetail(false)
}

const {
  formState: statisticFormState,
  resetFormState: resetStatisticFormState,
  onRestFormState: onRestStatisticFormState
} = useFormState({
  count_date: undefined
})
onRestStatisticFormState(() => setStatisticPage())

const {
  data: statisticData,
  setPage: setStatisticPage,
  loading: statisticLoading
} = usePaginatorApiRequest(
  ({ offset, limit }) =>
    trackCodeStatisticApi.paginator({
      filters: useTransformQuery(
        {
          code: trackCode.value,
          count_date: statisticFormState.value.count_date
        },
        {
          code: '=',
          count_date: 'dateRange'
        }
      ),
      sorts: ['-count_date'],
      offset,
      limit
    }),
  undefined,
  false
)

const { modalVisible: modalVisibleDetail, setModalVisible: setModalVisibleDetail } = useModalVisible()

const toOrder = ({ code }) => router.push({
  name: 'shop-list',
  query: { track_code: code }
})

const dateRangeChange = dates => {
  const dateRange = dates
    ? [
      moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00',
      moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
    ]
    : undefined
  filterState.value.count_date = dateRange
  datePicker.value.clear()
}
</script>

<style scoped lang="less">
.track-code-title {
  display: inline-block !important;
  width: calc(100% - 100px);
  :deep(.u-text) {
    width: 100%!important;
  }
}
</style>
