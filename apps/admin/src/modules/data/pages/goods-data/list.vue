<template>
  <uc-layout-list class="flex">
    <template #filter>
      <a-form-item name="goods_title">
        <a-input v-model:value.trim="filterState.goods_title" placeholder="商品名称" class="w-320" />
      </a-form-item>
      <a-form-item name="sku">
        <a-input v-model:value.trim="filterState.sku" placeholder="SKU编码" class="w-320" />
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-model:value="filterState.brand_id"
          placeholder="商品品牌"
          :options="brandTree"
          show-search
          change-on-select
        />
      </a-form-item>
      <a-form-item name="date">
        <uc-date-picker ref="datePicker" v-model="filterState.date" :allow-clear="true" />
      </a-form-item>
      <a-form-item name="date">
        <a-range-picker v-model:value="filterState.date" @change="dateRangeChange" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch"> 查询 </a-button>
        <a-button @click="resetFilterState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-dropdown>
          <template #overlay>
            <a-menu @click="exportExcel">
              <a-menu-item key="xlsx"> 导出Excel </a-menu-item>
              <a-menu-item key="csv"> 导出Csv </a-menu-item>
            </a-menu>
          </template>
          <DownloadOutlined />
        </a-dropdown>
      </a-space>
    </template>
    <template #list>
      <a-card v-if="loading" v-bind="{ loading }" />
      <a-table
        v-if="!loading"
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPaginationSimple(data)"
        :scroll="{ x: 1200 }"
        @change="setPage"
      >
        <a-table-column title="商品信息" width="250px" ellipsis fixed="left">
          <template #default="{ record }">
            <uc-img-text
              v-bind="record"
              :url="record.spec?.photo_url"
              :title="record.goods_title"
              :subtit="`SKU：${record.sku}`"
            />
          </template>
        </a-table-column>

        <a-table-column title="商品品牌" width="120px">
          <template #default="{ record }">
            <span>{{ record.brand?.name }}</span>
          </template>
        </a-table-column>

        <a-table-column title="商品规格" width="200px">
          <template #default="{ record }">
            <span>{{ record.spec_standard_name }}</span>
          </template>
        </a-table-column>

        <a-table-column title="UV/PV" width="100px">
          <template #default="{ record }">
            <span>{{ record.pv }}/{{ record.uv }}</span>
          </template>
        </a-table-column>

        <a-table-column title="销售人数/数量/金额" width="160px">
          <template #default="{ record }">
            <span>{{ record.sales_users }}/{{ record.sale_num }}/{{ $formatters.thousandSeparator(record.sales_amount) }}</span>
          </template>
        </a-table-column>

        <a-table-column title="下单人数/单数/金额" width="160px">
          <template #default="{ record }">
            <span>{{ record.placed_order_users }}/{{ record.placed_order_times }}/{{ $formatters.thousandSeparator(record.placed_order_amount) }}</span>
          </template>
        </a-table-column>

        <a-table-column title="支付人数/单数/件数/金额" width="180px">
          <template #default="{ record }">
            <span>{{ record.paid_users }}/{{ record.paid_order_count }}/{{ record.paid_quantity }}/{{ $formatters.thousandSeparator(record.paid_amount) }}</span>
          </template>
        </a-table-column>

        <a-table-column title="退款人数/单数/金额" width="160px">
          <template #default="{ record }">
            <span>{{ record.refund_order_users }}/{{ record.refund_order_count }}/{{ $formatters.thousandSeparator(record.refund_order_amount) }}</span>
          </template>
        </a-table-column>

        <a-table-column title="加购人数/次数/数量" width="160px">
          <template #default="{ record }">
            <span>{{ record.add_cart_user_count }}/{{ record.add_cart_count }}/{{ record.add_cart_quantity }}</span>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { goodsDataApi, exportGoodsDataApi } from '../../api'
import { DownloadOutlined } from '@ant-design/icons-vue'
import moment from 'moment'

const defaultDate = moment().subtract(1, 'days').format('YYYY-MM-DD')
const datePicker = ref()

const { brandTree } = useBrands()
import { useBrands } from '@/modules/goods/useBrand'

const {
  formState: filterState,
  resetFormState: resetFilterState,
  onRestFormState
} = useFormState({
  goods_title: undefined,
  sku: undefined,
  brand_id: [],
  date: [defaultDate + ' 00:00:00', defaultDate + ' 23:59:59']
})

const getBrandId = () =>
filterState.value.brand_id.length
    ? { brand_id: filterState.value.brand_id[filterState.value.brand_id.length - 1] }
    : {}

onRestFormState(() => {
  datePicker.value.reset()
  setPage()
})

const exportExcel = ({ key }) => {
  const filters = {
    ...useTransformQuery(
      {
        ...getBrandId(),
        goods_title: filterState.value.goods_title,
        sku: filterState.value.sku,
        date: filterState.value.date,
      },
      {
        goods_title: 'like',
        sku: 'like',
        date: 'dateRange',
      }
    )
  }
  let excelExportUrlQuery = encodeURI(`${exportGoodsDataApi(key)}&filters=${JSON.stringify(filters)}&relations=brand`)
  window.open(excelExportUrlQuery)
}

const { data, getData: setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  goodsDataApi.paginator({
    filters: {
      ...useTransformQuery(
        {
          ...getBrandId(),
          goods_title: filterState.value.goods_title,
          sku: filterState.value.sku,
          date: filterState.value.date,
        },
        {
          goods_title: 'like',
          sku: 'like',
          date: 'dateRange',
        }
      ),
    },
    relations: ['brand', 'spec'],
    offset,
    limit,
  })
)

const handleSearch = () => {
  setPage()
}

const dateRangeChange = dates => {
  const dateRange = dates
    ? [
        moment(dates[0]).format('YYYY-MM-DD') + ' 00:00:00',
        moment(dates[1]).format('YYYY-MM-DD') + ' 23:59:59'
      ]
    : undefined
  filterState.value.date = dateRange
  datePicker.value.clear()
}
</script>

<style scoped>
.track-code-title {
  display: inline-block !important;
  width: calc(100% - 100px);
}
</style>
