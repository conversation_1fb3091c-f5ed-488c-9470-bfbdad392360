import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { staffApi, excelExportUrl } from './api'
import { message } from 'ant-design-vue'
import formatters from '@/utils/formatters'
import { useStore } from '@/store/auth'

const { state } = useStore()

export const useEmployee = ({
  id,
  queryFormState,
  formState,
  validateForm,
  setLoading,
  setModalVisible,
  initModalData
}) => {
  const addLimit = ref('')

  const {
    data: employeeList,
    loading: employeeLoading,
    refresh: employeeRefresh,
    setPage
  } = usePaginatorApiRequest(({ offset, limit }) =>
    staffApi.paginatorByPath(`staff`, {
      filters: useTransformQuery(queryFormState, {
        real_name: 'like',
        email: 'like'
      }),
      offset,
      limit,
      pathId: id
    })
  )

  const setPopconfirmVisible = (id, visible) => {
    if (!visible) {
      addLimit.value = null
    }
    employeeList.value.items = employeeList.value.items.map(item => {
      if (item.id == id) {
        item.popconfirmVisible = visible
      } else {
        item.popconfirmVisible = false
      }
      return item
    })
  }

  const onUpdate = async record => {
    await staffApi.update(`${id}/staff/${record.id}`, {
      status: record.status == 1 ? 0 : 1
    })
    message.success('操作成功')
    employeeRefresh()
  }

  const handleConfirm = async record => {
    if (!addLimit.value) {
      message.error('请输入新增额度')
      return
    }
    await staffApi.update(`${id}/staff/${record.id}`, {
      limit: formatters.priceToInteger(addLimit.value)
    })
    addLimit.value = null
    setPopconfirmVisible(record.id, false)
    message.success('操作成功')
    employeeRefresh()
  }

  const handleCreate = async () => {
    setLoading(true)
    if (!(await validateForm())) {
      setLoading(false)
      return
    }
    try {
      await staffApi.createByPath(`${id}/staff`, {
        ...formState.value,
        limit: formState.value.limit ? formatters.priceToInteger(formState.value.limit) : 0,
        activity_id: id
      })
    } catch (error) {
      setLoading(false)
      return
    }
    setModalVisible(false)
    setLoading(false)
    initModalData()
    message.success('新增成功')
    employeeRefresh()
  }

  const handleExport = () => {
    let query = ''
    queryFormState.value.real_name ? (query += `"real_name":"%${queryFormState.value.real_name}%",`) : ''
    queryFormState.value.email ? (query += `"email":"%${queryFormState.value.email}%",`) : ''
    let dd = query.split('')
    if (dd[dd.length - 1] == ',') {
      dd.splice(dd.length - 1, 1)
      query = dd.join('')
    }
    let exportUrl = encodeURI(`${excelExportUrl}/${id}/staff/export?token=${state.token}&filters={${query}}`)
    return exportUrl
  }

  return {
    employeeList,
    employeeLoading,
    employeeRefresh,
    setPage,
    setPopconfirmVisible,
    onUpdate,
    handleConfirm,
    handleCreate,
    handleExport,
    addLimit
  }
}
