<template>
  <uc-layout-list title="额度记录">
    <template #filter>
      <a-form-item name="phone_number">
        <a-input v-model:value.trim="formState.phone_number" placeholder="请输入用户手机号" />
      </a-form-item>
      <a-form-item name="staff_name">
        <a-input v-model:value.trim="formState.staff_name" placeholder="请输入员工姓名" />
      </a-form-item>
      <a-form-item name="staff_email">
        <a-input v-model:value.trim="formState.staff_email" placeholder="请输入员工邮箱" />
      </a-form-item>
      <a-form-item name="sn">
        <a-input v-model:value.trim="formState.sn" placeholder="请输入订单号" />
      </a-form-item>
      <a-form-item name="activity_id">
        <a-select v-model:value="formState.activity_id" placeholder="活动名称" allow-clear>
          <a-select-option v-for="item in activityOptions" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.created_at"
          :placeholder="['开始时间', '结束时间']"
          @change="changeRangePicker"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">查询</a-button>
        <a-button @click="onReset">重置</a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button :href="handleExport()"> 导出 </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="用户昵称">
          <template #default="{ record }">
            <div>{{ record?.user?.nickname }}</div>
          </template>
        </a-table-column>
        <a-table-column title="用户手机号" data-index="phone_number">
          <template #default="{ record }">
            <div>{{ $formatters.numberEncryption(record?.user?.phone_number) }}</div>
          </template>
        </a-table-column>
        <a-table-column title="员工姓名" data-index="staff_name" />
        <a-table-column title="员工邮箱" data-index="staff_email" />
        <a-table-column title="额度" data-index="useLimit">
          <template #default="{ record }">
            -{{ $formatters.thousandSeparator(record.useLimit) }}
          </template>
        </a-table-column>
        <a-table-column title="订单号" data-index="sn" />
        <a-table-column title="活动名称" data-index="activity_title" />
        <a-table-column title="时间" data-index="created_at" />
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { staffLimitApi, activityApi, limitExportUrl } from '../../api'
import { useStore } from '@/store/auth'

const { state } = useStore()

onMounted(() => {
  loadActivity()
})

const { formState, resetFormState, validateForm, setFormRules } = useFormState({
  phone_number: undefined,
  staff_name: undefined,
  staff_email: undefined,
  created_at: undefined,
  sn: undefined,
  activity_id: undefined
})

const activityOptions = ref([])

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  staffLimitApi.paginator({
    relation_filters: {
      user: useTransformQuery({
        phone_number: formState.value.phone_number,
      }, {
        phone_number: '=',
      }),
    },
    filters: useTransformQuery(
      {
        staff_name: formState.value.staff_name,
        staff_email: formState.value.staff_email,
        created_at: formState.value.created_at,
        sn: formState.value.sn,
        activity_id: formState.value.activity_id
      },
      {
        created_at: 'dateRange'
      }
    ),
    offset,
    limit,
    relations: ['user']
  }).then(res => {
    res.items.forEach(item => {
      item.useLimit = Math.max(0, item.total_amount - item.discount_amount - item.refund_amount)
    })
    return res
  })
)

const loadActivity = () => {
  activityApi.list({
    relations: ['staff_count', 'goods_count']
  }).then(res => {
    activityOptions.value = res.map(item => ({
      label: item.title,
      value: item.id
    }))
  })
}
const onReset = () => {
  resetFormState()
  setPage()
}
const handleExport = () => {
  let query = ''
  let filterTime = useTransformQuery({ created_at: formState.value.created_at }, { created_at: 'dateRange' })
  formState.value.staff_name ? (query += `"staff_name":"${formState.value.staff_name}",`) : ''
  formState.value.staff_email ? (query += `"staff_email":"${formState.value.staff_email}",`) : ''
  formState.value.sn ? (query += `"sn":"${formState.value.sn}",`) : ''
  formState.value.activity_id ? (query += `"activity_id":"${formState.value.activity_id}",`) : ''
  formState.value.created_at ? (query += `"created_at":"${filterTime.created_at}",`) : ''
  let dd = query.split('')
  if (dd[dd.length - 1] == ',') {
    dd.splice(dd.length - 1, 1)
    query = dd.join('')
  }
  let relation_filters = formState.value.phone_number ? `"user": {"phone_number":"${formState.value.phone_number}"}` : ''
  let exportUrl = encodeURI(`${limitExportUrl}?token=${state.token}&filters={${query}}&relation_filters={${relation_filters}}`)
  return exportUrl
}

</script>
<style scoped></style>
