<template>
  <uc-layout-list title="活动管理">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.created_at"
          :placeholder="['活动开始时间', '活动结束时间']"
          @change="changeRangePicker"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="onReset"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd()">新增活动</a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动Id" data-index="id" />
        <a-table-column title="活动名称" data-index="title" />
        <a-table-column title="活动时间" data-index="created_at">
          <template #default="{ record }">
            <div>{{ record.start_time }} - {{ record.end_time }}</div>
          </template>
        </a-table-column>
        <a-table-column title="员工数" data-index="staffs_count">
          <template #default="{ record }">
            <div
              class="text-ellipsis color-primary"
              style="cursor: pointer"
              @click="toDetails(record.id, 'employee-list')"
            >
              {{ record.staffs_count }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="商品数" data-index="goods_count">
          <template #default="{ record }">
            <div
              class="text-ellipsis color-primary"
              style="cursor: pointer"
              @click="toDetails(record.id, 'activity-goods-list')"
            >
              {{ record.goods_count }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" @click="onEdit(record.id, 'activity-edit')">
              编辑
            </a-button>
            <a-button type="link" @click="copyLinkByRoute('internalPurchase', { id: record.id })"> 链接 </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.staffs_count > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.staffs_count > 0"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import dayjs from 'dayjs'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useActivity } from '../../useActivity'

const router = useRouter()
const { formState, resetFormState } = useFormState({
  title: undefined,
  created_at: undefined
})

const isBefore = computed(() => (date) => {
  const now = dayjs()
  const target = dayjs(date)
  return target.isBefore(now)
})
const onReset = () => {
  resetFormState()
  refresh()
}
const {
  data,
  setPage,
  loading,
  refresh,
  handleDelete,
  toDetails,
  onAdd,
  onEdit,
  changeRangePicker
} = useActivity({
  formState
})
</script>
<style scoped lang="less"></style>
