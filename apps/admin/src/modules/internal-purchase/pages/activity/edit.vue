<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card class="h-fill" title="基本信息">
          <a-form-item class="required" label="活动海报" name="banner_url">
            <uc-upload v-model:list="formState.banner_url" :disabled="isRead" :max-length="1" isdrag upload-text=" " />
          </a-form-item>
          <a-form-item class="required" label="活动名称" name="title">
            <a-input v-model:value.trim="formState.title" :disabled="isRead" placeholder="请输入活动名称" />
          </a-form-item>
          <a-form-item class="required" label="活动时间">
            <a-date-picker
              v-model:value="formState.start_time"
              :disabled="isRead"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="活动开始时间"
              show-time
              style="width: 240px"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              :disabled="isRead"
              :disabled-date="disabledEndTime"
              :disabled-time="disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              placeholder="活动结束时间"
              show-time
              style="width: 240px"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </a-form-item>
          <a-form-item label="登录提示">
            <a-textarea v-model:value.trim="formState.prompt" :disabled="isRead" placeholder="请输入登录提示" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card class="h-fill" title="注册设置">
          <a-form-item label="邮箱限制" name="email_limits">
            <a-input v-model:value.trim="formState.email_limits" :disabled="isRead" placeholder="可注册邮箱后缀为@XXX.com" />
          </a-form-item>
          <a-form-item label="额度限制" name="limit">
            <a-input
              v-model:value.trim="formState.limit"
              :disabled="isRead"
              addon-after="元的商品"
              addon-before="活动期间内每人可购买"
              placeholder="请输入额度"
              @blur="onBlurLimit"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card class="h-fill" title="寄送设置">
          <a-form-item class="required" label="配送方式" name="shipping_methods">
            <a-checkbox-group v-model:value="formState.shipping_methods" :disabled="isRead">
              <a-checkbox value="express">自选地址</a-checkbox>
              <a-checkbox value="pick_up">自提点提货</a-checkbox>
            </a-checkbox-group>
          </a-form-item>
          <a-form-item
            v-if="formState.shipping_methods?.includes('express')"
            class="required"
            label="选择运费模板"
            name="status"
          >
            <a-select
              v-model:value="formState.freight_id"
              :disabled="isRead"
              :options="freightOption"
              class="w-500"
              placeholder="请选择运费模版"
            />
          </a-form-item>
          <template v-if="formState.shipping_methods?.includes('pick_up')">
            <a-form-item class="required" label="添加自提点" name="status">
              <a-button :disabled="isRead" type="primary" @click="setModalVisible(true), initAddress()">添加自提点</a-button>
            </a-form-item>
            <template v-if="formState.addresses.length">
              <a-table :data-source="formState.addresses" :row-selection="rowSelection" row-key="id">
                <a-table-column data-index="title" title="收货人" />
                <a-table-column data-index="phone_number" title="收货人电话" />
                <a-table-column data-index="addressStr" title="收货人地址" />
                <a-table-column data-index="address" title="详细地址" />
                <a-table-column data-index="action" title="操作">
                  <template #default="{ record }">
                    <a-button :disabled="isRead" type="link" @click="handleEdit(record)"> 编辑 </a-button>
                    <a-popconfirm
                      :disabled="isRead"
                      cancel-text="取消"
                      ok-text="确定"
                      placement="left"
                      title="你确定要删除该地址么？"
                      @confirm="handleDelete(record)"
                    >
                      <a-button :disabled="isRead" class="danger" type="link"> 删除 </a-button>
                    </a-popconfirm>
                  </template>
                </a-table-column>
              </a-table>
            </template>
          </template>
        </a-card>
      </uc-col>
    </uc-row>
    <a-modal
      :title="addressFormState.id ? '编辑自提点' : '新增自提点'"
      :visible="modalVisible"
      @cancel="setModalVisible(false)"
      @ok="handleOk"
    >
      <a-form :label-col="{ span: 6 }" :model="addressFormState">
        <a-form-item class="required" label="收货人" name="title">
          <a-input v-model:value="addressFormState.title" placeholder="请输入收货人" />
        </a-form-item>
        <a-form-item class="required" label="收货人电话" name="phone_number">
          <a-input v-model:value="addressFormState.phone_number" placeholder="请输入收货人电话" />
        </a-form-item>
        <a-form-item class="required" label="收货人地址" name="addressStr">
          <a-cascader
            v-model:value="addressFormState.addressStr"
            :field-names="{ label: 'label', value: 'label', children: 'children' }"
            :options="citys.map_options"
            placeholder="请选择收货人地址"
          />
        </a-form-item>
        <a-form-item class="required" label="详细地址" name="address">
          <a-textarea v-model:value="addressFormState.address" placeholder="请输入详细地址" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="setModalVisible(false)"> 取消 </a-button>
        <a-button :loading="loading" type="primary" @click="handleCreate"> 确定 </a-button>
      </template>
    </a-modal>
    <div class="rich-text">
      <uc-rich-text
        v-model="formState.desc"
        :disabled="isRead"
        :height="300"
        placeholder="请输入活动介绍"
        toolbar-width="calc(100% - 320px)"
      />
    </div>
  </uc-layout-form>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import dayjs from 'dayjs'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { message } from 'ant-design-vue'
import citys from '@/utils/citys'
import { freightApi, addActivityApi, editActivityApi, activityApi } from '../../api'
import formatters from '@/utils/formatters'
const { formState, resetFormState, setFormRules, validateForm, setFormState } = useFormState({
  title: undefined,
  desc: undefined,
  banner_url: undefined,
  shipping_methods: [],
  freight_id: undefined,
  addresses: [],
  start_time: undefined,
  end_time: undefined,
  prompt: undefined,
  email_limits: undefined,
  limit: undefined
})
const {
  formState: addressFormState,
  resetFormState: resetAddressFormState,
  setFormRules: setAddressFormRules,
  validateForm: validateAddressForm
} = useFormState({
  title: undefined,
  phone_number: undefined,
  addressStr: undefined,
  address: undefined,
  address_detail: undefined
})
const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)
const { setLoading, loading } = useLoading()
const { modalVisible, setModalVisible } = useModalVisible()

const router = useRouter()
const { id } = useRoute().params
const freightOption = ref([])
const isStart = ref(false)

setFormRules({
  banner_url: { required: true, message: '请上传活动海报' },
  title: { required: true, message: '请输入活动名称' },
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  shipping_methods: { required: true, message: '请选择配送方式' },
  addresses: {
    required: true,
    validator(_, value) {
      if (formState.value.shipping_methods?.includes('pick_up') && !value.length) {
        return Promise.reject('请添加自提点')
      }
      return Promise.resolve()
    }
  },
  freight_id: {
    required: true,
    validator(_, value) {
      if (formState.value.shipping_methods?.includes('express') && !value) {
        return Promise.reject('请选择运费模版')
      }
      return Promise.resolve()
    }
  }
})
setAddressFormRules({
  title: { required: true, message: '请输入收货人' },
  phone_number: { required: true, message: '请输入收货人电话' },
  addressStr: { required: true, message: '请选择收货人地址' },
  address: { required: true, message: '请输入详细地址' }
})
const isRead = ref(useRoute().name == 'activity-look')

onMounted(() => {
  useLoadingMessage(initData(), { loadingText: '正在加载数据...' })
})

const handleDelete = record => {
  formState.value.addresses = formState.value.addresses.filter(item => item.id !== record.id)
}
const handleEdit = record => {
  console.log(record)
  Object.assign(addressFormState.value, {
    id: record.id,
    title: record.title,
    phone_number: record.phone_number,
    addressStr: [record.province, record.city, record.county],
    address: record.address
  })
  setModalVisible(true)
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  formState.value.addresses.forEach(item => {
    item.status = selectedKeys.value.includes(item.id) ? 1 : 0;
  });
  const addressList = formState.value.shipping_methods?.includes('pick_up') ? formState.value.addresses : []
  const params = { ...formState.value, banner_url: formState.value.banner_url[0], addresses: addressList }
  if (params.limit) {
    params.limit = formatters.priceToInteger(formState.value.limit)
  }
  id ? await activityApi.update(id, params) : await addActivityApi.create(params)
  message.success(`保存成功：${formState.value.title}`)
  router.back()
}
const initAddress = () => {
  addressFormState.value.id = undefined
  addressFormState.value.title = undefined
  addressFormState.value.phone_number = undefined
  addressFormState.value.address = undefined
  addressFormState.value.addressStr = undefined
}
const handleCreate = async () => {
  const addressStr = addressFormState.value.addressStr
  if (!(await validateAddressForm())) {
    setLoading(false)
    return
  }

  if (addressFormState.value.id) {
    formState.value.addresses = formState.value.addresses.map(item => {
      if (item.id === addressFormState.value.id) {
        return {
          ...item,
          title: addressFormState.value.title,
          province: addressStr[0],
          city: addressStr[1],
          county: addressStr[2],
          phone_number: addressFormState.value.phone_number,
          addressStr: addressStr.join('/'),
          address: addressFormState.value.address
        }
      }
      return item
    })
  } else {
    formState.value.addresses.push({
      status: 1,
      id: formState.value.addresses[formState.value.addresses.length - 1]?.id + 1 || 1,
      title: addressFormState.value.title,
      province: addressStr[0],
      city: addressStr[1],
      county: addressStr[2],
      phone_number: addressFormState.value.phone_number,
      addressStr: addressStr.join('/'),
      address: addressFormState.value.address
    })
  }
  selectedKeys.value = formState.value.addresses.filter(item => item.status == 1).map(item => item.id)
  message.success(formState.value.id ? '编辑自提点成功' : '新增自提点成功')
  initAddress()
  setModalVisible(false)
}
const isBefore = (time) => {
  return dayjs(time).isBefore(dayjs())
}
const initData = () => {
  freightApi.get().then(res => {
    freightOption.value = useTransformOptions(res, 'title', 'id')
  })
  if (id) {
    activityApi.get(id, {
      relations: ['addresses']
    }).then(res => {
      res.banner_url = [res.banner_url]
      if (res?.addresses?.length) {
        res.addresses.forEach(item => {
          item.addressStr = item.province + '/' + item.city + '/' + item.county
        })
        selectedKeys.value = res.addresses.filter(item => item.status == 1).map(item => item.id)
      }
      isStart.value = isBefore(res.start_time)
      res.limit = formatters.thousandSeparator(res.limit)
      setFormState(res)
    })
  }
}

const selectedAddress = ref([])
const selectedKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedKeys,
  getCheckboxProps: record => ({
    disabled: isRead.value
  }),
  onChange: selectedRowKeys => {
    selectedKeys.value = selectedRowKeys
    const ids = selectedAddress.value.map(item => item.id)
    const diffIds = selectedRowKeys.filter(key => !ids.includes(key))
    const addAddress = formState.value.addresses.filter(item => diffIds.includes(item.id))
    selectedAddress.value.push(...addAddress)
  }
}
const onBlurLimit = () => {
  if (formState.value.limit) {
    // 只能输入数字和小数点，不能由小数点开头，只能输入2位小数,也不能由 0 开头
    const reg = /^[0-9]*\.?[0-9]{0,2}$/
    if (!reg.test(formState.value.limit)) {

      // 移除所有非数字和小数点的字符
      formState.value.limit = formState.value.limit.replace(/[^0-9.]/g, '')
      // 确保不以小数点开头
      if (formState.value.limit.startsWith('.')) {
        formState.value.limit = '0' + formState.value.limit
      }
      // 确保不能以 0 开头
      if (formState.value.limit.startsWith('0')) {
        formState.value.limit = formState.value.limit.slice(1)
      }
      // 限制小数位数为2位
      const parts = formState.value.limit.split('.')
      if (parts[1] && parts[1].length > 2) {
        formState.value.limit = parts[0] + '.' + parts[1].slice(0, 2)
      }
    }
  }
}
</script>

<style lang="less" scoped>
.danger {
  color: #e54d42;
}

:deep(.ant-table .ant-btn-link:first-child) {
  padding-left: 0 !important;
}
</style>
