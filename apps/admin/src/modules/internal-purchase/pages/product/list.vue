<template>
  <uc-layout-list title="商品管理">
    <template #filter>
      <a-form-item name="goods_title">
        <a-input v-model:value.trim="formState.goods_title" placeholder="请输入商品名称" />
      </a-form-item>
      <a-form-item name="category">
        <a-input v-model:value.trim="formState.category" placeholder="请输入分类名称" />
      </a-form-item>
      <a-form-item name="sku">
        <a-input v-model:value.trim="formState.sku" placeholder="请输入sku编码" />
      </a-form-item>
      <a-form-item name="status">
        <a-select v-model:value="formState.on_sale" placeholder="上下架状态">
          <a-select-option value="1"> 上架 </a-select-option>
          <a-select-option value="0"> 下架 </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="onReset"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button :href="handleExport()"> 导出 </a-button>
        <a-button type="primary" @click="setModalVisible(true)">新增sku</a-button>
      </a-space>
    </template>
    <template #list>
      <a-table
        :row-key="record => record.id"
        :data-source="goodsList.items"
        :loading="goodsLoading"
        :pagination="stdPagination(goodsList)"
        @change="setPage"
      >
        <a-table-column title="商品名称/规格名称" data-index="name" ellipsis :min-width="140">
          <template #default="{ record }">
            <uc-img-text
              :url="record?.photo_url"
              :title="`${record?.goods?.title || record?.goods_title}`"
              :subtit="`${record?.sku}：${record?.spec.attrs[0].value || record?.spec.attrs}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="分类" data-index="category" width="150px">
          <template #default="{ record }">
            <a-input
              v-model:value="record.category"
              placeholder="请输入分类"
              @focus="() => onInputFocus(record.category)"
              @blur="() => onHandleRatio(record, 'category', '分类名称')"
            />
          </template>
        </a-table-column>
        <a-table-column title="库存数" data-index="stock" width="80px" />
        <a-table-column title="占用" data-index="hold" width="80px" />
        <a-table-column title="活动已售" data-index="sold" width="90px" />
        <a-table-column title="活动可售" data-index="vendibility" width="90px" />
        <a-table-column title="商城价" data-index="price" width="100px">
          <template #default="{ record }">
            {{ formatters.thousandSeparator(record.spec.price) }}
          </template>
        </a-table-column>
        <a-table-column title="商品购买限制" width="120px">
          <template #default="{ record }">
            <span class="color-primary hand" @click="handleLimit(record)">
              <template v-if="record.limit == 0">无限制</template>
              <template v-else-if="record.limit_type == '' && record.limit > 0">
                {{ record.limit }}个
              </template>
              <template v-else>
                {{ record.limit_type ? limitType.filter(record.limit_type).label : '无限制' }}{{ record.limit }}个
              </template>
            </span>
          </template>
        </a-table-column>
        <a-table-column title="内购价" data-index="employee_price" width="100px">
          <template #default="{ record }">
            <view class="distributor-ratio">
              <a-input-number
                v-model:value="record.price"
                :precision="2"
                :formatter="formatterNumber"
                :parser="formatterNumber"
                min="0.01"
                :max="9999"
                @focus="() => onInputFocus(record.price)"
                @blur="() => onHandleRatio(record, 'price', '内购价')"
              />
            </view>
          </template>
        </a-table-column>
        <a-table-column title="上架状态" width="100px">
          <template #default="{ record }">
            <a-switch
              v-model:checked="record.on_sale"
              :un-checked-value="0"
              :checked-value="1"
              @change="handleUpdate(record.id, { on_sale: $event }, { title: record.title })"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" data-index="action" width="160px">
          <template #default="{ record }">
            <a-popconfirm
              title=""
              ok-text="确认"
              cancel-text="取消"
              :visible="record.popconfirmVisible"
              @confirm="handleConfirm(record)"
              @cancel="setPopconfirmVisible(record.id, false)"
            >
              <template #icon>
                <div class="flex flex-dc w-200">
                  <a-input-number v-model:value.trim="availableNumber" placeholder="请输入新增可售数" />
                </div>
              </template>
              <a-button type="link" @click="setPopconfirmVisible(record.id, true)">添加库存</a-button>
            </a-popconfirm>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="record.sold > 0"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.sold > 0">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <goods-select
    v-model:visible="modalVisible"
    activity-mode="internal_purchase"
    :filter-goods="{
      exclude_promotions: {
        'internal_purchase': id
      }
    }"
    @ok="onSubmit"
  >
    <template #stock-column>
      <a-table-column title="分类" width="150px">
        <template #default="{ record }">
          <a-input v-model:value="record.category" placeholder="请输入分类" />
        </template>
      </a-table-column>
      <a-table-column title="库存" width="100px">
        <template #default="{ record }">
          <a-input-number v-model:value="record.initialStock" :min="0" :max="9999" />
        </template>
      </a-table-column>
    </template>
  </goods-select>
  <a-modal :visible="limitModalVisible" title="商品购买限制" @cancel="setLimitModalVisible(false)">
    <a-form :model="limitFormState">
      <a-form-item label="时间条件" name="limit_type" class="required">
        <a-select v-model:value="limitFormState.limit_type" placeholder="请选择时间条件">
          <a-select-option v-for="item in limitType.options()" :key="item.value" :value="item.value">{{ item.label }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="限制数量" name="limit" class="required">
        <div>
          <div class="flex flex-cc">
            <a-input-number v-model:value="limitFormState.limit" :precision="0" :min="0" placeholder="请输入限制数量" />
            <span class="m-l-10">个</span>
          </div>
          <div class="m-t-10">0为无限制</div>
        </div>
      </a-form-item>
      <div>例：每人每月最多能买5个</div>
    </a-form>
    <template #footer>
      <a-button @click="setLimitModalVisible(false)"> 取消 </a-button>
      <a-button type="primary" :loading="limitLoading" @click="handleLimitOk(limitFormState)"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import formatters from '@/utils/formatters'
import { useFormState } from '@/composables/useFormState'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import GoodsSelect from '@/components/select-sku-goods/index.vue'
import { useProduct } from '../../useProduct'
import { productExportUrlApi } from '../../api'
import { limitType } from '../../enums'

const { formState, resetFormState } = useFormState({
  goods_title: undefined,
  sku: undefined,
  on_sale: undefined,
  category: undefined
})

const { formState: limitFormState, setFormState } = useFormState({
  limit_type: undefined,
  limit: 0,
  id: undefined
})

const { id } = useRoute().params
const { modalVisible, setModalVisible } = useModalVisible()
const { modalVisible: limitModalVisible, setModalVisible: setLimitModalVisible } = useModalVisible()
const { loading: limitLoading, setLoading: setLimitLoading } = useLoading()

const handleLimit = (record) => {
  setFormState({
    id: record.id,
    limit_type: record.limit_type == "" ? null : record.limit_type,
    limit: record.limit
  })
  setLimitModalVisible(true)
}

const {
  onHandleRatio,
  formatterNumber,
  onInputFocus,
  goodsList,
  goodsLoading,
  goodsRefresh,
  setPage,
  handleConfirm,
  setPopconfirmVisible,
  handleUpdate,
  handleDelete,
  onSubmit,
  availableNumber,
  handleExport,
  handleLimitOk
} = useProduct({
  id,
  formState,
  setModalVisible,
  setLimitModalVisible,
  setLimitLoading
})
const onReset = () => {
  resetFormState()
  goodsRefresh()
}
</script>
<style scoped>
.ant-input-number {
  width: 100%;
}
</style>
