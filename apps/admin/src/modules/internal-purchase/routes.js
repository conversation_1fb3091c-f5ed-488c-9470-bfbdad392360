export default {
  path: 'internal-purchase',
  meta: {
    title: '员工内购',
    antIcon: 'ShopOutlined'
  },
  children: [
    {
      path: 'activity-list',
      name: 'activity-list',
      meta: {
        title: '活动管理',
        keepAlive: true
      },
      component: () => import('./pages/activity/list.vue')
    },
    {
      path: 'activity-edit/:id',
      name: 'activity-edit',
      meta: {
        title: '编辑活动'
      },
      hidden: true,
      component: () => import('./pages/activity/edit.vue')
    },
    {
      path: 'activity-look/:id',
      name: 'activity-look',
      meta: {
        title: '查看活动'
      },
      hidden: true,
      component: () => import('./pages/activity/edit.vue')
    },
    {
      path: 'activity-add',
      name: 'activity-add',
      meta: {
        title: '新增活动'
      },
      hidden: true,
      component: () => import('./pages/activity/edit.vue')
    },
    {
      path: 'employee-list/:id',
      name: 'employee-list',
      meta: {
        title: '员工管理'
      },
      hidden: true,
      component: () => import('./pages/employee-manage/list.vue')
    },
    {
      path: 'activity-goods-list/:id',
      name: 'activity-goods-list',
      meta: {
        title: '活动商品管理',
        keepAlive: false
      },
      hidden: true,
      component: () => import('./pages/product/list.vue')
    },
    {
      path: 'limit-list',
      name: 'limit-list',
      meta: {
        title: '额度记录',
        keepAlive: true
      },
      component: () => import('./pages/limit-details/list.vue')
    }
  ]
}
