import { apiFactory } from '@/api'

export const excelExportUrl = `${import.meta.env.VITE_API_BASE_URL}/internal-purchase/activity` //导出

export const limitExportUrl = `${import.meta.env.VITE_API_BASE_URL}/internal-purchase/staff/limit/export` //额度导出

export const productExportUrlApi = activityId =>
  `${import.meta.env.VITE_API_BASE_URL}/internal-purchase/activity/${activityId}/goods/export` //商品导出

export const freightApi = apiFactory.command('/setting/freights') //运费模板

export const activityApi = apiFactory.restful('/internal-purchase/activities') //活动列表

// 新增活动
export const addActivityApi = apiFactory.restful('/internal-purchase/activities')

// 编辑活动
export const editActivityApi = apiFactory.restful('/internal-purchase/activities')

export const staffApi = apiFactory.restful('/internal-purchase/activity')

// /api/internal-purchase/staff/limit
export const staffLimitApi = apiFactory.restful('/internal-purchase/staff/limit')
