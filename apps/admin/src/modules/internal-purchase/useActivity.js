import { useRouter } from 'vue-router'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { activityApi } from './api'
import { message } from 'ant-design-vue'

export const useActivity = ({ formState }) => {
  const router = useRouter()

  const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
    activityApi.paginator({
      filters: useTransformQuery(
        {
          title: formState.value.title,
          created_at: formState.value.created_at
        },
        {
          created_at: 'dateRange'
        }
      ),
      offset,
      limit,
      relations: ['staff_count', 'goods_count']
    })
  )

  const handleDelete = record => {
    activityApi.delete(record.id).then(() => {
      setPage()
      message.success('删除成功')
    })
  }

  const toDetails = (id, name) => {
    console.log(name)
    console.log(id)
    router.push({
      name: name,
      params: {
        id
      }
    })
  }

  const onAdd = () => {
    router.push({
      name: 'activity-add'
    })
  }

  const onEdit = (id, name) => {
    router.push({
      name: name,
      params: { id }
    })
  }

  const changeRangePicker = () => {
    if (!formState.value.created_at.length) {
      Object.assign(formState.value, {
        created_at: undefined
      })
    }
  }

  return {
    data,
    setPage,
    loading,
    refresh,
    handleDelete,
    toDetails,
    onAdd,
    onEdit,
    changeRangePicker
  }
}
