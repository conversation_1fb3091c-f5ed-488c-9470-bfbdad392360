import { Modal, message } from 'ant-design-vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { createVNode } from 'vue'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { staffApi, productExportUrlApi } from './api'
import { useStore } from '@/store/auth'

const { state } = useStore()

export const useProduct = ({ id, formState, setModalVisible, setLimitModalVisible, setLimitLoading }) => {
  const {
    data: goodsList,
    loading: goodsLoading,
    refresh: goodsRefresh,
    setPage
  } = usePaginatorApiRequest(({ offset, limit }) => {
    return staffApi
      .paginatorByPath(`goods`, {
        filters: useTransformQuery(formState, {
          goods_title: 'like',
          sku: 'like'
        }),
        relations: ['spec'],
        offset,
        limit,
        pathId: id
      })
      .then(response => {
        if (response.items && response.items.length > 0) {
          response.items = response.items.map(item => {
            const price = Math.round(item.price / 100)
            return {
              ...item,
              popconfirmVisible: false,
              vendibility: item.stock - item.hold,
              price
            }
          })
        }
        return response
      })
  })
  const originValue = ref(0)
  const onInputFocus = price => {
    originValue.value = price
  }
  const onHandleRatio = (record, field, title) => {
    const restoreOriginalValue = () => {
      record[field] = originValue.value
    }
    if (record[field] == originValue.value) {
      restoreOriginalValue()
      return
    }
    if (field == 'category' && !record[field]) {
      message.error('分类名称不能为空')
      restoreOriginalValue()
      return
    }
    Modal.confirm({
      title: () => `确认修改${title}吗`,
      content: () => `${record.name ?? ''}`,
      icon: () => createVNode(ExclamationCircleOutlined),
      onOk() {
        return staffApi
          .update(`${id}/goods/${record.id}`, {
            [field]: field === 'price' ? record[field] * 100 : record[field]
          })
          .then(() => {
            message.success(`${title}修改成功`)
            goodsRefresh()
          })
          .catch(() => {
            goodsRefresh()
          })
      },
      onCancel() {
        restoreOriginalValue()
      }
    })
  }

  const formatterNumber = value => {
    if (typeof value === 'string') {
      return !isNaN(Number(value)) ? value.trim() : value.replace(/[^0-9]/gi, '')
    } else if (typeof value === 'number') {
      return !isNaN(value) ? value : 0.01
    } else {
      return 0
    }
  }

  const availableNumber = ref(null)

  const handleConfirm = async record => {
    if (!availableNumber.value) {
      message.error('请输入新增可售数')
      return
    }
    await staffApi.update(`${id}/goods/${record.id}`, {
      stock: availableNumber.value
    })
    message.success('操作成功')
    setPopconfirmVisible(record.id, false)
    goodsRefresh()
  }

  const setPopconfirmVisible = (id, visible) => {
    if (!visible) {
      availableNumber.value = null
    }
    goodsList.value.items = goodsList.value.items.map(item => {
      if (item.id == id) {
        item.popconfirmVisible = visible
      } else {
        item.popconfirmVisible = false
      }
      return item
    })
  }

  const handleUpdate = async (ids, data, title) => {
    await staffApi.update(`${id}/goods/${ids}`, data)
    message.success('操作成功')
    goodsRefresh()
  }

  const handleDelete = async record => {
    await staffApi.delete(`${id}/goods/${record.id}`)
    message.success('操作成功')
    goodsRefresh()
  }

  const handleExport = () => {
    let query = ''
    formState.value.goods_title ? (query += `"goods_title":"${formState.value.goods_title}",`) : ''
    formState.value.sku ? (query += `"sku":"${formState.value.sku}",`) : ''
    formState.value.on_sale ? (query += `"on_sale":"${formState.value.on_sale}",`) : ''
    let dd = query.split('')
    if (dd[dd.length - 1] == ',') {
      dd.splice(dd.length - 1, 1)
      query = dd.join('')
    }
    const pathId = id ? `&pathId=${id}` : ''
    let url = encodeURI(`${productExportUrlApi(id)}?token=${state.token}&filters={${query}}&relations=spec${pathId}`)
    return url
  }

  const onSubmit = async value => {
    if (!value.every(item => item.category)) {
      message.error('请填写商品分类')
      return
    }
    const goods = value.map(item => {
      return {
        goods_id: item.goods_id,
        sku: item.sku,
        goods_title: item.goods.title,
        stock: item.initialStock,
        price: item.price,
        on_sale: 0,
        category: item.category
      }
    })
    await staffApi.createByPath(`${id}/goods`, {
      goods
    })
    message.success('添加成功')
    setModalVisible(false)
    goodsRefresh()
  }
  const handleLimitOk = val => {
    setLimitLoading(true)
    staffApi
      .update(`${id}/goods/${val.id}`, {
        limit_type: val.limit_type || '',
        limit: val.limit
      })
      .then(() => {
        message.success('修改成功')
        setLimitModalVisible(false)
        goodsRefresh()
      })
      .finally(() => {
        setLimitLoading(false)
      })
  }
  return {
    goodsList,
    goodsLoading,
    goodsRefresh,
    setPage,
    onHandleRatio,
    formatterNumber,
    onInputFocus,
    handleConfirm,
    setPopconfirmVisible,
    handleUpdate,
    handleDelete,
    onSubmit,
    availableNumber,
    handleExport,
    handleLimitOk
  }
}
