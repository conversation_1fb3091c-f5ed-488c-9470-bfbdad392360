/** 限制类型 */
export const limitType = Object.freeze({
  /**每日 */
  day: 'day',
  /**每周 */
  week: 'week',
  /**每月 */
  month: 'month',
  /**每年 */
  year: 'year',

  options() {
    return [
      {
        value: undefined,
        label: '无限制'
      },
      {
        value: this.day,
        label: '每日'
      },
      {
        value: this.week,
        label: '每周'
      },
      {
        value: this.month,
        label: '每月'
      },
      {
        value: this.year,
        label: '每年'
      }
    ]
  },

  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})
