<template>
  <a-modal :visible="modalVisible" :title="wordFormState.id ? '编辑敏感词' : '新增敏感词'" @ok="handleCreate" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item label="敏感词" name="word" class="required">
        <a-input v-model:value.trim="wordFormState.word" placeholder="请输入敏感词，不超过32字" :maxlength="32" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="createLoading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>
  <uc-layout-list title="敏感词" class="flex">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onCreate">
          新增
        </a-button>
        <a-button @click="handleExport">
          导出敏感词
        </a-button>
        <a-button type="link" :href="exportProhibitedWordsTemplateApi" class="flex flex-cc">
          <uc-svg-icon name="download" class="m-r-8" />
          <span>下载模板</span>
        </a-button>
        <a-upload :action="importProhibitedWordsApi" :show-upload-list="false" @change="fileChange">
          <a-button type="link" class="flex flex-cc">
            <uc-svg-icon name="tolead" class="m-r-8" />
            <span>导入敏感词</span>
          </a-button>
        </a-upload>
      </a-space>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="敏感词">
          <template #default="{ record }">
            {{ record.word }}
          </template>
        </a-table-column>

        <a-table-column width="160px" title="添加时间">
          <template #default="{ record }">
            {{ record.created_at }}
          </template>
        </a-table-column>

        <a-table-column width="120px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { cloneDeep } from 'lodash'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { searchCondition } from '../../enums'
import { prohibitedWordsApi, importProhibitedWordsApi, exportProhibitedWordsApi, exportProhibitedWordsTemplateApi } from '../../api'
import { message } from 'ant-design-vue'

const queryFormBasic = Object.freeze({
  [searchCondition.word]: undefined,
})


const { formState, resetFormState, onRestFormState } = useFormState({
  ...cloneDeep(queryFormBasic),
  conditionValue: undefined,
})

const conditionOptions = searchCondition.options()
const conditionKey = ref(searchCondition.word)
watch(conditionKey, () => (formState.value.conditionValue = undefined))

const getFilters = () => {
  const filters = {}
  filters[conditionKey.value] = formState.value.conditionValue
  return useTransformQuery(
      filters,
      { word: 'like' }
    )
}

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {

  return prohibitedWordsApi.paginator({
    filters: getFilters(),
    offset,
    limit
  })
})

onRestFormState(() => setPage())

const { formState: wordFormState, setFormState, resetFormState: wordResetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  sort: undefined
})

const { modalVisible, setModalVisible } = useModalVisible()

watch(modalVisible, v => !v && wordResetFormState())

const onCreate = () => {
  setModalVisible(true)
}
const onEdit = props => {
  setFormState(props) || setModalVisible(true)
}

setFormRules({
  word: {
    required: true,
    message: '请填写敏感词'
  },
})
const { loading: createLoading, setLoading } = useLoading()

const handleCreate = async () => {
  setLoading(true)

  if (!(await validateForm())) {
    setLoading(false)
    return
  }

  try {
    const { id } = wordFormState.value
    if (id) {
      await prohibitedWordsApi.replace(id, { ...wordFormState.value })
      message.success('更新完成')
    } else {
      await prohibitedWordsApi.create(wordFormState.value)
      message.success('创建完成')
    }
    setModalVisible(false)
    setPage()
  } finally {
    setLoading(false)
  }
}

const handleDelete = ({ id }) => {
  prohibitedWordsApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}

// 导入
const fileChange = ({ file: { status, response } }) => {
  switch (status) {
    case 'error':
      message.error(response?.tips ?? '上传文件失败')
      return
    case 'done':
      if (response.data) {
        message.success(`导入成功`)
        setPage()
      }
      break
  }
}

const handleExport = () => {
  const query = getFilters()
  let excelExportUrl = `&filters=${JSON.stringify(query)}`
  window.open(encodeURI(`${exportProhibitedWordsApi}${excelExportUrl}`))
}

</script>

<style lang="less" scoped>

</style>
