import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

export const prohibitedWordsApi = apiFactory.restful('/security/prohibited-words') // 敏感词
export const importProhibitedWordsApi = `${import.meta.env.VITE_API_BASE_URL}/security/prohibited-words/excel/import?token=${state.token}` // 导入敏感词
export const exportProhibitedWordsTemplateApi = `${import.meta.env.VITE_API_BASE_URL}/security/prohibited-words/excel/export/template?token=${state.token}` // 导出敏感词模板
export const exportProhibitedWordsApi = `${import.meta.env.VITE_API_BASE_URL}/security/prohibited-words/excel/export?token=${state.token}` // 导出敏感词
