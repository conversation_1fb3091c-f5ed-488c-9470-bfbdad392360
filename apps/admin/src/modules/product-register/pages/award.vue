<template>
  <uc-layout-list title="认证奖励">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select
            v-model:value="formState.type"
            placeholder="请选择"
            :options="AwardQuery.options()"
            @change="formState.keyword = ''"
          />
          <a-input v-model:value.trim="formState.keyword" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage()"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="handleCreate"> 新增认证奖励 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="产品名称" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.goods?.title }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="产品规格" data-index="attrs" />
        <a-table-column title="产品SKU">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.sku }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="认证奖励" data-index="credit" width="120px" />
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button type="link" @click="handleUpdate(record)"> 编辑 </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisible" title="新增认证奖励" @cancel="handleCancel">
    <a-form :model="formStateReward" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item required label="奖励积分" class="required">
        <a-input-number v-model:value="formStateReward.credit" placeholder="请输入奖励积分" min="0" />
      </a-form-item>
      <a-form-item required label="产品SKU" class="required">
        <a-textarea
          v-model:value="formStateReward.sku"
          :disabled="isUpdate"
          placeholder="请输入产品SKU，一行一个"
          :auto-size="{ minRows: 5, maxRows: 10 }"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { ref, watch } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { productRegisterAwardApi } from '../api'
import { AwardQuery } from '../enums'
import { cloneDeep } from 'lodash'
import { useModalVisible } from '@/composables/useToggles'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  const { type, keyword } = formState.value
  const relation_filters = {}
  let filters = {}
  if (type === AwardQuery.title) {
    relation_filters.goods = useTransformQuery({ title: keyword }, { title: 'like' })
  } else {
    filters = useTransformQuery({ sku: keyword }, { sku: 'like' })
  }

  return productRegisterAwardApi.paginator({
    relation_filters,
    filters,
    offset,
    limit,
    relations: ['spec', 'goods']
  })
})

const isUpdate = ref(false)

const { formState, onRestFormState, resetFormState } = useFormState({ type: AwardQuery.sku, keyword: undefined })
onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await productRegisterAwardApi.delete(id)
  message.success('删除完成')
  setPage()
}

const { modalVisible, setModalVisible } = useModalVisible()

const {
  formState: formStateReward,
  resetFormState: resetFormStateReward,
  setFormRules: setFormRulesReward,
  validateForm: validateFormReward
} = useFormState({
  credit: undefined,
  sku: undefined
})

setFormRulesReward({
  credit: { required: true, message: '请输入奖励积分' },
  sku: { required: true, message: '请输入产品SKU' }
})

const handleSubmit = async () => {
  if (!(await validateFormReward())) return
  if (isUpdate.value) {
    await productRegisterAwardApi.update(formStateReward.value.id, formStateReward.value)
  } else {
    await productRegisterAwardApi.create(formStateReward.value)
  }

  message.success('操作成功')
  handleCancel()
  setPage()
}

const handleUpdate = record => {
  isUpdate.value = true
  setModalVisible(true)
  formStateReward.value = record
}

const handleCreate = () => {
  isUpdate.value = false
  setModalVisible(true)
}

const handleCancel = () => {
  setModalVisible(false)
  resetFormStateReward()
}
</script>
