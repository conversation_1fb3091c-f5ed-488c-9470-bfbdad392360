<template>
  <uc-layout-list title="认证产品" class="m-list">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="formState.type" :options="ProductQuery.options()" @change="formState.keyword = ''" />
          <a-input v-model:value.trim="formState.keyword" placeholder="请输入关键词" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.source" placeholder="购买渠道" allow-clear :options="Source.options()" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage()">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="产品认证信息" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.spec?.photo_url"
              :title="record.goods?.title"
              :subtit="`${record.sku}：${record.attrs}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="购买渠道/序列号" width="240px">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ Source.filter(record.source).label }}
            </div>
            <div>{{ record.code?.code || '000-000-00-0' }}</div>
          </template>
        </a-table-column>
        <a-table-column title="认证用户" width="200px">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.user.nickname }}
            </div>
            <div>{{ $formatters.numberEncryption(record.user.phone_number) }}</div>
          </template>
        </a-table-column>
        <a-table-column title="奖积分" data-index="credit" width="120px" />
        <a-table-column title="认证时间" data-index="updated_at" width="120px" />
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { productRegisterRecordApi } from '../api'
import { ProductQuery, Source, AuditStatus } from '../enums'

const { formState, resetFormState, onRestFormState } = useFormState({
  type: ProductQuery.phone_number,
  keyword: '',
  source: undefined // 购买渠道
})

onRestFormState(() => setPage())

// 表格查询
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  const { type, keyword } = formState.value
  const relation_filters = {}
  const filters = { status: AuditStatus.completed, source: formState.value.source }
  const typeVal = { [type]: keyword }
  const likeType = useTransformQuery(typeVal, { [type]: 'like' })

  switch (type) {
    case ProductQuery.sku:
      filters.sku = useTransformQuery({sku:keyword},{ sku:'like'}).sku
      break
    case ProductQuery.code:
      relation_filters.code = likeType
      break
    case ProductQuery.title:
      relation_filters.goods = likeType
      break
    case ProductQuery.phone_number:
      relation_filters.user = typeVal
      break
    case ProductQuery.nickname:
      relation_filters.user = likeType
      break
  }
  return productRegisterRecordApi.paginator({
    filters,
    sort: ['-updated_at'],
    relation_filters,
    offset,
    limit,
    relations: ['code', 'user', 'spec', 'goods']
  })
})
</script>
