<template>
  <uc-layout-list title="产品序列号" class="m-list">
    <template #filter>
      <div class="flex flex-sb w-fill">
        <div class="flex">
          <a-form-item>
            <a-input-group compact>
              <a-select
                v-model:value="formState.type"
                :options="ProductCodeQuery.options()"
                @change="formState.keyword = ''"
              />
              <a-input v-model:value.trim="formState.keyword" placeholder="请输入关键词" />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="setPage()">
              查询
            </a-button>
            <a-button @click="resetFormState">
              重置
            </a-button>
          </a-form-item>
        </div>
        <div class="flex flex-cc h-32">
          <a-switch v-model:checked="isMeanwhile" class="m-r-6" @change="updateProductCodeSetting" />
          <span>开启同步</span>
        </div>
      </div>
    </template>
    <template #extra>
      <a-space v-if="!isMeanwhile" :size="0">
        <a-button type="link" :href="downloadApi" class="flex flex-cc">
          <uc-svg-icon name="download" class="m-r-8" />
          <span>下载模板</span>
        </a-button>
        <a-upload :action="importApi" :show-upload-list="false" @change="handleChange">
          <a-button type="link" class="flex flex-cc">
            <uc-svg-icon name="tolead" class="m-r-8" />
            <span>导入序列号</span>
          </a-button>
        </a-upload>
        <a-button type="primary" @click="setModalVisible(true)">
          新增序列号
        </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="产品名称" data-index="title" ellipsis />
        <a-table-column title="产品规格" data-index="attrs" />
        <a-table-column title="产品SKU" data-index="sku" width="240px" />
        <a-table-column title="产品序列号" data-index="code" width="240px" />
        <a-table-column v-if="!isMeanwhile" title="操作" width="70px">
          <template #default="{ record }">
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.status === 'used'"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.status === 'used'">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisible" title="新增序列号" @cancel="onCancel">
    <a-form :model="formStateReward" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="产品SKU" class="required">
        <a-input v-model:value="formStateReward.sku" placeholder="请输入产品SKU" />
      </a-form-item>
      <a-form-item label="产品序列号" class="required">
        <a-textarea
          v-model:value="formStateReward.code"
          placeholder="请输入产品序列号，一行一个"
          :auto-size="{ minRows: 5, maxRows: 10 }"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onCancel">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import {
  downloadApi,
  importApi,
  productCodesApi,
  productCodesSettingActions,
  productCodesSettingUpdateActions
} from '../api'
import { ProductCodeQuery } from '../enums'

const { formState, resetFormState, onRestFormState } = useFormState({
  type: ProductCodeQuery.sku,
  keyword: ''
})
const isMeanwhile = ref() // 是否同步
const setting = ref({}) //产品序列号设置
// 表格查询
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) => {
  const { type, keyword } = formState.value
  return productCodesApi.paginator({
    filters: useTransformQuery({ [type]: keyword }, { [type]: 'like' }),
    offset,
    limit,
    relations: ['spec']
  })
})
onRestFormState(() => setPage())

// 导入
const handleChange = ({ file: { status, response } }) => {
  switch (status) {
    case 'error':
      message.error(response?.tips ?? '上传文件失败')
      return
    case 'done':
      if (response.data) {
        message.success(`导入成功：已导入${response.data}条序列号`)
        setPage()
      }
      break
  }
}

const productCodeSetting = async () => {
  setting.value = await productCodesSettingActions.get()
  isMeanwhile.value = setting.value.value.is_sync
}
productCodeSetting()

const updateProductCodeSetting = async () => {
  setting.value.value.is_sync = isMeanwhile.value
  await productCodesSettingUpdateActions.post(setting.value)
}

const handleDelete = async ({ id }) => {
  await productCodesApi.delete(id)
  message.success('删除完成')
  setPage()
}

const { modalVisible, setModalVisible } = useModalVisible()

const {
  formState: formStateReward,
  resetFormState: resetFormStateReward,
  setFormRules: setFormRulesReward,
  validateForm: validateFormReward
} = useFormState({
  sku: '',
  code: ''
})

setFormRulesReward({
  sku: { required: true, message: '请输入产品SKU' },
  code: { required: true, message: '请输入产品序列号' }
})

const handleSubmit = async () => {
  if (!(await validateFormReward())) return
  await productCodesApi.create(formStateReward.value)
  message.success('创建完成')
  onCancel()
  setPage()
}

const onCancel = () => {
  setModalVisible(false)
  resetFormStateReward()
}
</script>
