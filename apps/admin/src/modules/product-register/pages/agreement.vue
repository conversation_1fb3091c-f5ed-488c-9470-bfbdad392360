<template>
  <div class="product-register-agreement flex flex-dc">
    <a-card class="g-mg-bt m-b-22">
      <a-form>
        <a-form-item class="m-b-0">
          <a-input v-model:value.trim="formState.title" placeholder="请输入注册协议名称" allow-clear />
        </a-form-item>
      </a-form>
    </a-card>
    <uc-rich-text v-model="formState.content" placeholder="请输入注册协议" :height="600" />
    <div class="footer-box">
      <a-button type="primary" @click="onSubmit">
        保存
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { productRegisterAgreementActions, productRegisterAgreementUpdateActions } from '../api'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'

const layoutLabelCol = { span: 3 }
const layoutWrapperCol = { span: 24 }

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: '',
  content: ''
})

const AGREEMENT_KEY = Object.freeze('product_register_agreement')

const loadData = () => {
  productRegisterAgreementActions.get('', {}).then(res => {
    setFormState(res.value)
  })
}

useLoadingMessage(loadData(), {
  loadingText: '正在加载数据'
})

setFormRules({
  title: { required: true, message: '请输入协议名称', trigger: 'blur' },
  content: { required: true, message: '请输入协议内容', trigger: 'blur' }
})

const onSubmit = async () => {
  if (!(await validateForm())) return
  const params = {
    key: AGREEMENT_KEY,
    group: 'all',
    protected: 0,
    value: cloneDeep(formState.value)
  }
  // console.log(params.value)
  await productRegisterAgreementUpdateActions.post(cloneDeep(params))
  message.success('操作成功')
}
</script>

<style lang="less" scoped>
.layout-form {
  :deep(.ant-form .ant-form-item) {
    .ant-select,
    .ant-input,
    .ant-cascader-picker,
    .ant-input-wrapper {
      max-width: 100% !important;
    }
  }
}
.product-register-agreement {
  height: 100%;
  padding-bottom: 50px;
  .footer-box {
    width: 100%;
    padding: 10px 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9;
    box-shadow: 0 0 8px #f0f1f2;
  }
  :deep(.m-rich-text) {
    padding-bottom: 80px;
  }
}
</style>
>
