<template>
  <uc-layout-list title="认证审核">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="formState.type" :options="RecordQuery.options()" @change="formState.keyword = ''" />
          <a-input v-model:value.trim="formState.keyword" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.source" :options="Source.options()" placeholder="购买渠道" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.status"
          :options="AuditStatus.groupIncomplete()"
          placeholder="审核状态"
          allow-clear
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage()">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="产品认证信息" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_urls"
              :title="Source.filter(record.source)?.label"
              :subtit="record.code?.code || '0000-000-00-0'"
              :tag="record.photo_urls?.length"
            />
          </template>
        </a-table-column>
        <a-table-column title="申请用户" width="200px" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.user?.nickname || '' }}
            </div>
            <div>{{ $formatters.numberEncryption(record.user?.phone_number || '') }}</div>
          </template>
        </a-table-column>
        <a-table-column title="申请时间" data-index="created_at" width="150px" />
        <a-table-column title="审核状态" width="120px">
          <template #default="{ record }">
            <a-tooltip placement="topRight">
              <template v-if="record.status === AuditStatus.reject" #title>
                {{ record.reason }}
              </template>
              <a-badge
                :text="AuditStatus.filter(record.status).label"
                :color="AuditStatus.filter(record.status).color"
                class="cursor-pointer"
              />
            </a-tooltip>
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button type="link" :disabled="record.status != AuditStatus.normal" @click="agreeModel(record)">
              同意
            </a-button>
            <a-button
              type="link"
              class="danger"
              :disabled="record.status != AuditStatus.normal"
              @click="rejectModel(record)"
            >
              拒绝
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisible" :title="`${modalTypeLabel}认证申请`" width="650px" @cancel="handleCancel">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="购物凭证" class="required">
        <a-image-preview-group>
          <a-space :size="10" class="flex-wrap">
            <a-image
              v-for="(item, index) in formStateAuth.photo_urls"
              :key="index"
              :width="100"
              :height="100"
              :src="item"
              class="h-fill mode-contain bg-c-default"
            />
          </a-space>
        </a-image-preview-group>
      </a-form-item>
      <a-form-item label="购买渠道" class="required">
        <a-input :value="formStateAuth.info" disabled :placeholder="Source.filter(formStateAuth.source).label" />
      </a-form-item>
      <a-form-item v-if="modalType === ModalTypes.agree" label="认证产品" class="required">
        <a-select
          v-model:value="formStateAuth.sku"
          :options="productOptions"
          :filter-option="false"
          placeholder="请选择认证产品"
          show-search
          @search="handleSearch"
        />
      </a-form-item>
      <a-form-item required :label="`${modalTypeLabel}原因`" class="required">
        <a-textarea
          v-model:value="formStateAuth.reason"
          :placeholder="`请输入${modalTypeLabel}原因，不超过200字`"
          :maxlength="200"
          :auto-size="{ minRows: 5, maxRows: 10 }"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="handleCancel">
        取消
      </a-button>
      <a-button v-if="modalType === ModalTypes.agree" type="primary" @click="handleSubmit(formStateAuth)">
        同意
      </a-button>
      <a-button v-else type="danger" @click="handleSubmit(formStateAuth)">
        拒绝
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { productRegisterRecordApi, productRegisterAwardApi } from '../api'
import { RecordQuery, Source, AuditStatus, ModalTypes } from '../enums'
import { cloneDeep } from 'lodash'
import { useModalVisible } from '@/composables/useToggles'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  const { type, keyword } = formState.value
  const relation_filters = {}
  let filters = {}

  if (formState.value.source) {
    filters.source = formState.value.source
  }

  filters.status = formState.value.status || `!${AuditStatus.completed}`

  if (type === RecordQuery.code) {
    relation_filters.code = useTransformQuery({ code: keyword }, { code: 'like' })
  } else {
    const query = type == RecordQuery.nickname ? { [type]: 'like' } : undefined
    relation_filters.user = useTransformQuery({ [type]: keyword }, query)
  }

  return productRegisterRecordApi.paginator({
    relation_filters,
    filters,
    offset,
    limit,
    relations: ['code', 'user']
  })
})

const { formState, onRestFormState, resetFormState } = useFormState({
  type: RecordQuery.phone_number,
  keyword: '',
  source: undefined,
  status: undefined
})
onRestFormState(() => setPage())

const { modalVisible, setModalVisible } = useModalVisible()
const modalType = ref(ModalTypes.agree) // 模态框类型, agree: 同意, refuse: 拒绝
const modalTypeLabel = computed(() => ModalTypes.filter(modalType.value).label)
const productOptions = ref([])

// 搜索认证产品
const handleSearch = async keyword => {
  const { items } = await productRegisterAwardApi.paginator({
    relation_filters: { goods: useTransformQuery({ title: keyword }, { title: 'like' }) },
    relations: ['spec', 'goods']
  })

  productOptions.value = useTransformOptions(items, 'goods.title', 'sku')
}
handleSearch()

const {
  formState: formStateAuth,
  resetFormState: resetFormStateAuth,
  validateForm: validateFormAuth,
  setFormState: setFormStateAuth,
  setFormRules: setFormRulesAuth
} = useFormState({
  product_urls: [],
  source: Source.taobao,
  code: undefined,
  sku: undefined,
  reason: undefined
})

setFormRulesAuth({
  sku: {
    validator: (rule, value) => {
      if (modalType.value === ModalTypes.agree && !value) return Promise.reject('请选择认证产品')
      return Promise.resolve()
    }
  },
  reason: { required: true, message: '请输入原因' }
})

const handleSubmit = async record => {
  if (!(await validateFormAuth())) return
  formStateAuth.value.status = modalType.value == ModalTypes.reject ? AuditStatus.reject : AuditStatus.completed
  await productRegisterRecordApi.update(record.id, formStateAuth.value)

  message.success('操作成功')
  handleCancel()
  setPage()
}

const agreeModel = record => {
  modalType.value = ModalTypes.agree
  setModalVisible(true)
  setFormStateAuth(record)
}

const rejectModel = record => {
  modalType.value = ModalTypes.reject
  setModalVisible(true)
  setFormStateAuth(record)
}

const handleCancel = () => {
  setModalVisible(false)
  resetFormStateAuth()
}
</script>
