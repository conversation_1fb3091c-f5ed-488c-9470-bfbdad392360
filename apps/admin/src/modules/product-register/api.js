import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

// 下载模板
export const downloadApi = `${import.meta.env.VITE_API_BASE_URL}/product-register/product-codes/excel/download?token=${
  state.token
}`
// 导出
export const importApi = `${import.meta.env.VITE_API_BASE_URL}/product-register/product-codes/excel/import?token=${
  state.token
}`
export const productRegisterAgreementActions = apiFactory.command('/setting/settings/product-register-agreement') // 获取认证协议
export const productRegisterAgreementUpdateActions = apiFactory.command(
  '/setting/settings/product-register-agreement/update'
) // 修改认证协议
export const productRegisterAwardApi = apiFactory.restful('/product-register/awards') //  认证奖励
export const productRegisterRecordApi = apiFactory.restful('/product-register/records') //  认证审核
export const productApi = apiFactory.restful('/product-register/records') //  认证审核
export const productCodesApi = apiFactory.restful('/product-register/product-codes') // 序列号
export const productCodesSettingActions = apiFactory.command('/setting/settings/product-code') // 获取序列号设置
export const productCodesSettingUpdateActions = apiFactory.command('/setting/settings/product-code/update') // 修改序列号设置
