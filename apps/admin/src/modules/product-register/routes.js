export default {
  path: 'product-register',
  meta: {
    title: '防伪',
    antIcon: 'SafetyCertificateOutlined'
  },
  children: [
    {
      path: 'product-register-agreement',
      name: 'product-register-agreement',
      meta: {
        title: '认证协议'
      },
      component: () => import('./pages/agreement')
    },
    {
      path: 'product-register-award',
      name: 'product-register-award',
      meta: {
        title: '认证奖励'
      },
      component: () => import('./pages/award')
    },
    {
      path: 'product-register-record',
      name: 'product-register-record',
      meta: {
        title: '认证审核',
        keepAlive: true
      },
      component: () => import('./pages/record')
    },
    {
      path: 'product-register-product',
      name: 'product-register-product',
      meta: {
        title: '认证产品',
        keepAlive: true
      },
      component: () => import('./pages/product')
    },
    {
      path: 'product-code',
      name: 'product-code',
      meta: {
        title: '产品序列号',
        keepAlive: true
      },
      component: () => import('./pages/product-code')
    }
  ]
}
