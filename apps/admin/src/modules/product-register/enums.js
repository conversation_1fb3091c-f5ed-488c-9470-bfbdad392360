/**
 * 审核状态
 */
export const AuditStatus = Object.freeze({
  /**
   * 待审核
   */
  normal: 'normal',
  /**
   * 已拒绝
   */
  reject: 'reject',
  /**
   * 已完成
   */
  completed: 'completed',

  options() {
    return [
      { label: '待审核', value: this.normal, color: '#409eff' },
      { label: '已拒绝', value: this.reject, color: '#f56c6c' },
      { label: '已完成', value: this.completed, color: '#67c23a' }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  },
  groupIncomplete() {
    return this.options().filter(item => item.value !== this.completed)
  }
})

/**
 * 查询类型
 */
export const ProductQuery = Object.freeze({
  /**
   * 手机号码
   */
  phone_number: 'phone_number',

  /**
   * 用户昵称
   */
  nickname: 'nickname',

  /**
   * 产品SKU
   */
  sku: 'sku',

  /**
   * 产品名称
   */
  title: 'title',

  /**
   * 产品序列号
   */
  code: 'code',

  options() {
    return [
      { label: '手机号码', value: this.phone_number },
      { label: '用户昵称', value: this.nickname },
      { label: '产品SKU', value: this.sku },
      { label: '产品名称', value: this.title },
      { label: '产品序列号', value: this.code }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 查询类型2
 */
export const RecordQuery = Object.freeze({
  /**
   * 手机号码
   */
  phone_number: 'phone_number',
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 产品序列号
   */
  code: 'code',

  options() {
    return [
      { label: '手机号码', value: this.phone_number },
      { label: '用户昵称', value: this.nickname },
      { label: '产品序列号', value: this.code }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 查询类型3
 */
export const AwardQuery = Object.freeze({
  /**
   * 产品SKU
   */
  sku: 'sku',
  /**
   * 产品名称
   */
  title: 'title',
  /**
   * 产品序列号
   */
  code: 'code',

  options() {
    return [
      { label: '产品SKU', value: this.sku },
      { label: '产品名称', value: this.title }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  },
  groupCode() {
    return this.options().push({ label: '产品序列号', value: this.code })
  }
})

/**
 * 查询类型4
 */
export const ProductCodeQuery = Object.freeze({
  /**
   * 产品SKU
   */
  sku: 'sku',
  /**
   * 产品名称
   */
  title: 'title',
  /**
   * 产品序列号
   */
  code: 'code',

  options() {
    return [
      { label: '产品SKU', value: this.sku },
      { label: '产品名称', value: this.title },
      { label: '产品序列号', value: this.code }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 购买渠道
 */
export const Source = Object.freeze({
  /**
   * 京东
   */
  jd: 'jd',
  /**
   * 天猫
   */
  tmall: 'tmall',
  /**
   * 淘宝
   */
  taobao: 'taobao',
  /**
   * 抖音
   */
  tiktok: 'tiktok',
  /**
   * 门店
   */
  store: 'store',

  options() {
    return [
      { label: '京东', value: this.jd },
      { label: '天猫', value: this.tmall },
      { label: '淘宝', value: this.taobao },
      { label: '抖音', value: this.tiktok },
      { label: '门店', value: this.store }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 模态框状态
 */
export const ModalTypes = Object.freeze({
  /**
   * 同意
   */
  agree: 'agree',
  /**
   * 拒绝
   */
  reject: 'reject',

  options() {
    return [
      { label: '同意', value: this.agree },
      { label: '拒绝', value: this.reject }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})
