<template>
  <uc-layout-list title="分享管理">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.nickname" placeholder="请输入分享客昵称" />
      </a-form-item>
      <a-form-item>
        <a-input v-model:value.trim="formState.phone_number" placeholder="请输入分享客手机号" />
      </a-form-item>

      <a-form-item name="rangeTime" class="range-time">
        <a-date-picker
          v-model:value="formState.start_time"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择开始时间"
          class="w-240"
        />
        <span class="separator">~</span>
        <a-date-picker
          v-model:value="formState.end_time"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          show-time
          placeholder="请选择结束时间"
          class="w-240"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button :href="handleExport()" style="margin-right: 10px">导出</a-button>
      <a-button type="primary" @click="onChangeModalVisible"> 落地页配置 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="分享客昵称/分享客手机号" width="200px">
          <template #default="{ record }">
            <router-link
              :to="{
                name: 'user-details',
                params: { id: record.mp_account.id, user_id: record.mp_account.user_id }
              }"
            >
              <div class="flex flex-dc flex-sb">
                <span class="color-primary hand">{{ record.nickname }}</span>
                <span>{{ $formatters.numberEncryption(record.phone_number) }}</span>
              </div>
            </router-link>
          </template>
        </a-table-column>
        <a-table-column key="inviter_num" title="邀请注册人数">
          <template #default="{ record }"> {{ record.inviter_num }} </template>
        </a-table-column>
        <a-table-column title="订单完成数">
          <template #default="{ record }">
            {{ record.order_num }}
          </template>
        </a-table-column>
        <a-table-column title="订单金额">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.order_amount) }}
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal title="落地页配置" :visible="modalVisible" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item label="分享海报" class="required">
        <div class="flex">
          <uc-upload
            :list="modalFormState.share_img ? [modalFormState.share_img] : []"
            :max-length="1"
            upload-text=" "
            show-label
            label-text="会话"
            @update:list="data => (modalFormState.share_img = data[0])"
          />
          <uc-upload
            :list="modalFormState.poster_url ? [modalFormState.poster_url] : []"
            :max-length="1"
            upload-text=" "
            show-label
            label-text="本地"
            @update:list="data => (modalFormState.poster_url = data[0])"
          />
        </div>
      </a-form-item>
      <a-form-item label="分享文案" class="required">
        <a-input v-model:value="modalFormState.share_title" placeholder="请输入分享文案，不超过20字" :maxlength="20" />
      </a-form-item>
      <a-form-item label="落地页" class="required">
        <a-input v-model:value.trim="modalFormState.share_url" placeholder="请输入落地页地址" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onChangeModalVisible"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmitModal"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useFormState } from '@/composables/useFormState'
import { userInviteApi, excelExportUrl } from '@/modules/invite/api'
import { useModalVisible } from '@/composables/useToggles'
import { customPageConfigApi, customPageConfigUpdateApi } from '@/modules/page-config/api'
import { message } from 'ant-design-vue'

const { formState, resetFormState, onRestFormState } = useFormState({
  nickname: undefined,
  phone_number: undefined,
  start_time: undefined,
  end_time: undefined,
  sorts: {}
})

onRestFormState(() => setPage())

const queryParams = computed(() => {
  return {
    filters: useTransformQuery(
      {
        nickname: formState.value.nickname,
        phone_number: formState.value.phone_number,
        start_time: formState.value.start_time,
        end_time: formState.value.end_time
      },
      { nickname: 'like'}
    ),
    sorts: Object.values(formState.value.sorts)
  }
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  return userInviteApi.paginator({
    ...queryParams.value,
    offset,
    limit,
    relations: ['mpAccount']
  })
})

const onChange = (query, _, { order, columnKey }) => {
  if (order) {
    formState.value.sorts[columnKey] = order === 'ascend' ? columnKey : `-${columnKey}`
  } else {
    formState.value.sorts = {}
  }
  setPage(query)
}

const {
  formState: modalFormState,
  setFormState: setModalFormState,
  validateForm: validateModalForm,
  setFormRules: setModalFormRules
} = useFormState({
  poster_url: undefined,
  share_img: undefined,
  share_title: undefined,
  share_url: undefined
})

setModalFormRules({
  share_img: {
    required: true,
    message: '请输入分享会话海报'
  },
  poster_url: {
    required: true,
    message: '请输入分享本地海报'
  },
  share_title: {
    required: true,
    message: '请输入分享文案'
  },
  share_url: {
    required: true,
    message: '请输入落地页地址'
  }
})

let inviteConfigData = {}

customPageConfigApi('invite')
  .get()
  .then(res => {
    inviteConfigData = res
    const { share_img = undefined, poster_url = undefined, share_title = undefined, share_url = undefined } = res.config
    setModalFormState({ share_img, poster_url, share_title, share_url })
  })

const { modalVisible, setModalVisible } = useModalVisible()
const onChangeModalVisible = () => setModalVisible(!modalVisible.value)

const handleSubmitModal = async () => {
  if (!(await validateModalForm())) {
    return
  }
  await customPageConfigUpdateApi('invite').post({ ...inviteConfigData, config: modalFormState.value })
  onChangeModalVisible()
  message.success('操作成功')
}

const handleExport = () => {
  const params = JSON.parse(JSON.stringify(queryParams.value))
  params.filters = JSON.stringify(params.filters)
  params.sorts = params.sorts.join(',')
  const url = encodeURI(
    `${excelExportUrl}&${Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&')}`
  )
  return url
}
</script>
<style scoped lang="less"></style>
