import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { message } from 'ant-design-vue'
import { distributorApi, distributorSpecApi, distributionSpecApi } from './api'
import config from '@/config'
import { useStore } from '@/store/auth'
import { PRIZE_FILED } from './enum'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue'

const { state: storeInfo } = useStore()
export const useDistributorSpec = () => {
  const { query } = useRoute()
  if (!query || !query.distributor_id) useRouter().back()

  const params = {
    distributorId: query.distributor_id,
    distributorName: query.distributor_name
  }

  const { formState, resetFormState } = useFormState({ conditionKey: 'sku', distributor_id: params.distributorId })

  const exportDistributorSpecPath = `${distributorApi.resourcePath}/${params.distributorId}/specs/distributor-specs-export`
  const downloadTemplatePath = `${distributorApi.resourcePath}/${params.distributorId}/specs/download`
  const importDistributionSpecUrl = `${config.api.baseURL}${distributorApi.resourcePath}/${params.distributorId}/specs/distributor-specs-import?token=${storeInfo.token}`

  const useQueryAndRelations = () => {
    return {
      relations: ['spec', 'product', 'distributionSpec'],
      filters: useTransformQuery(formState, {
        keyword: '=',
        distributor_id: '='
      })
    }
  }

  const { data, setPage, refresh, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    distributorApi
      .paginatorByPath('specs', {
        pathId: formState.value.distributor_id,
        ...useQueryAndRelations(),
        offset,
        limit
      })
      .then(record => {
        return {
          ...record,
          items: record.items.map(item => {
            return {
              ...item,
              basic_price: item.basic_price / 100,
              distribution_price: item.distribution_price / 100,
              spec: {
                ...item.spec,
                distribution_price: item.distribution_spec ? item.distribution_spec.distribution_price / 100 : 0,
                price: item.spec ? item.spec.price / 100 : 0,
                floor_price: item.distribution_spec ? item.distribution_spec.floor_price / 100 : 0,
                discount_price: item.spec ? item.spec.discount_price / 100 : 0
              }
            }
          })
        }
      })
  )

  const onReset = () => {
    resetFormState()
    setPage()
  }
  const getSpecName = (attr = []) => {
    console.log('attr :>> ', attr)
    let specName = ''
    let l = attr.length
    attr.forEach((item, index) => {
      index == l - 1 ? (specName = item.value) : (specName = item.value + '/')
    })
    return specName
  }
  const onDeleteSpec = async record => {
    await distributorSpecApi.delete(record.id)
    setPage()
    message.success('取消成功')
  }

  const modalVisible = ref(false)
  const selectSpecSkus = ref([])
  const selectSpecs = ref([])
  const cache_specs = ref([])
  const diff = (_cache, keys) => {
    let skuArr = []
    _cache.forEach(el => {
      if (!keys.find(s => el == s)) {
        skuArr.push(el)
      }
    })
    return skuArr
  }
  const onSelectSpecChange = (keys = [], rows = []) => {
    //新增
    if (keys.length > cache_specs.value.length) {
      const index = cache_specs.value.length
      const newSpec = keys.slice(index)
      newSpec.forEach(sku => {
        const checkRow = rows.find(el => el.sku == sku)
        const res = { sku, basic_price: checkRow.basic_price, distribution_price: checkRow.distribution_price }
        selectSpecs.value.push(res)
      })
    } else {
      //找到取消的sku
      //对比差异
      const skus = diff(cache_specs.value, keys)
      //删除
      skus.forEach(sku => {
        const reduceSkuIndex = selectSpecs.value.findIndex(el => el.sku === sku)
        selectSpecs.value.splice(reduceSkuIndex, 1)
      })
    }
    //更新缓存
    cache_specs.value = keys
    selectSpecSkus.value = keys
  }
  const changeBasic = (record, field) => {
    const index = selectSpecs.value.findIndex(el => el.sku == record.sku)
    selectSpecs.value[index][field] = record[field]
  }
  const isCheck = record => {
    return !Boolean(selectSpecs.value?.find(d => d.sku == record.sku))
  }
  const { formState: specFilterState, resetFormState: resetSpecFilterState } = useFormState({
    not_distributor_id: params.distributorId,
    price: [undefined, undefined]
  })

  const {
    data: specData,
    setPage: setSpecPage,
    loading: specLoading
  } = usePaginatorApiRequest(
    ({ offset, limit }) =>
      distributionSpecApi
        .paginator({
          ...useRelationsTransformQuery(specFilterState, {
            keyword: '=',
            not_distributor_id: '=',
            price: 'priceRange'
          }),
          relations: ['product', 'spec'],
          offset,
          limit
        })
        .then(record => {
          selectSpecs.value.length &&
            selectSpecs.value.forEach(e => {
              record.items.map(l => {
                if (e.sku == l.sku) {
                  l.basic_price = e.basic_price
                  l.distribution_price = e.distribution_price
                }
                return l
              })
            })

            record.items.map(item => {
              item.basic_price = item.basic_price / 100
              item.distribution_price = item.distribution_price / 100
              return item
            })
          return {
            ...record
          }
        }),
    { current: 1, pageSize: 10 },
    false
  )

  const onSpecReset = () => {
    resetSpecFilterState()
    onSelectSpecChange()
    setSpecPage()
  }

  const onShowModal = () => {
    onSpecReset()
    modalVisible.value = true
  }

  const onHandleBatchCreate = async () => {
    if (selectSpecSkus.value.length <= 0) {
      message.info('请勾选新增分销商品')
      return
    }

    let specs = []
    try {
      specs = selectSpecs.value.map(item => {
        if (!item.distribution_price) {
          throw new Error(`请输入商品 " ${item.sku} " 的分销价！`)
        }
        if (!item.basic_price) {
          throw new Error(`请输入商品 " ${item.sku} " 的拿货价！`)
        }

        return {
          sku: item.sku,
          basic_price: item.basic_price * 100,
          distribution_price: item.distribution_price * 100,
          distributor_id: params.distributorId
        }
      })
    } catch (e) {
      return message.error(e.message)
    }

    await useLoadingMessage(distributorSpecApi.createByPath('batch', { specs }))
    message.success('添加成功')
    modalVisible.value = false
    onReset()
  }

  const originPrice = ref(0);
  const onInputFocus = (price) => {
    originPrice.value = price
  }

  const onHandleRatio = async (distribution, filed, title) => {
    const setOriginPrice = () => {
      distribution[filed] = originPrice.value
    }

    const filedInfo = PRIZE_FILED.find(item => item.filed == filed)
    if (!filedInfo) {
      message.error('无效的操作')
      setOriginPrice()
      return
    }
    if (distribution.distribution_price < distribution.basic_price) {
      message.error('分销价不能小于拿货价,请重新输入')
      setOriginPrice()
      return
    }

    if(distribution[filed] == originPrice.value) {
      return
    }

    Modal.confirm({
      title: () => `确认修改${title}吗`,
      content: () => `${distribution.product.title ?? ''}`,
      icon: () => createVNode(ExclamationCircleOutlined),
      onOk() {
        distributorSpecApi
        .updateByPath(filedInfo.path, {
          id: distribution.id,
          [filed]: distribution[filed] * 100
        })
        .then(() => {
          message.success(`${filedInfo.label}编辑成功`)
          closeModal()
        })
        .catch(() => {
          refresh()
          closeModal()
        })
      },
      cancelText: () => '取消',
      onCancel() {
        // Modal.destroyAll() // 组件bug
        setOriginPrice()
        closeModal()
      },
    })

    const closeModal = () => {
      const modal = document.querySelectorAll('.ant-modal-root')
      for (let i = 0; i < modal.length; i++) {
        modal[i].parentNode.remove()
      }
    }

  }

  const formatterNumber = value => {
    if (typeof value === 'string') {
      return !isNaN(Number(value)) ? value.trim() : value.replace(/[^0-9]/gi, '')
    } else if (typeof value === 'number') {
      return !isNaN(value) ? value : 0.01
    } else {
      return 0
    }
  }

  const handleImport = e => {
    const { file } = e
    if (file && file.status === 'done') {
      const { success = 0, fail = 0 } = file.response?.data || {}
      message.success(`导入完成，成功：${success}个，失败：${fail}个！`)
      onReset()
    }
  }
  return {
    downloadTemplatePath,
    importDistributionSpecUrl,
    handleImport,
    formatterNumber,
    exportDistributorSpecPath,
    useQueryAndRelations,
    params,
    onDeleteSpec,
    onReset,
    getSpecName,
    formState,
    data,
    setPage,
    loading,
    onInputFocus,
    onHandleRatio,
    modalVisible,
    isCheck,
    selectSpecSkus,
    changeBasic,
    onSelectSpecChange,
    specFilterState,
    resetSpecFilterState,
    specData,
    setSpecPage,
    specLoading,
    onSpecReset,
    onShowModal,
    onHandleBatchCreate
  }
}
