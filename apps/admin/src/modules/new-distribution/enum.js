export const INVITE_OPTIONS = [
  {label:'分销商',value:'name'},
  {label:'邀请人',value:'invitee'},
  {label:'代理商',value:'proxy'},
]

export const USER_OPTIONS = [
  {
    label: '用户姓名',
    value: 'name'
  },
  {
    label: '用户昵称',
    value: 'nickname'
  },
  {
    label: '用户手机号',
    value: 'phone_number'
  }
]

export const APPLY_AGREED = 'agreed'
export const APPLY_REFUSED = 'refused'

export const APPLY_STATUS = [
  {
    label: '已通过',
    value: APPLY_AGREED,
    allowOperate: false,
    badge: 'success'
  },
  {
    label: '已拒绝',
    value: APPLY_REFUSED,
    allowOperate: false,
    badge: 'error'
  },
  {
    label: '正常',
    value: 'normal',
    allowOperate: true,
    badge: 'processing'
  }
]

export const DISTRIBUTOR_STATUS = [
  {
    conditions: {
      is_clear: 0,
      is_signature: 0
    },
    label: '待签协议',
    badge: 'warning',
    allowReject: true
  },
  {
    conditions: {
      is_clear: 1,
      is_signature: 0
    },
    label: '已驳回',
    badge: 'error'
  },
  {
    conditions: {
      is_clear: 1,
      is_signature: 1
    },
    label: '已清退',
    badge: 'error'
  },
  {
    conditions: {
      is_shut_down: 1
    },
    label: '已封号',
    badge: 'default',
    allowShutDown: true,
    allowUnseal: true
  },
  {
    conditions: {},
    label: '正常',
    badge: 'processing',
    allowShutDown: true,
    allowDisabled: true
  }
]

export const PRIZE_FILED = [
  {
    filed: 'basic_price',
    path: 'basic-price',
    label: '拿货价'
  },
  {
    filed: 'distribution_price',
    path: 'distribution-price',
    label: '分销价'
  }
]

export const SPEC_PHOTO_URL_DELETE = Object.freeze({
  /**
   * 正常
   */
  NOT_DELETE:0,
  /**
   * 已删除
   */
  IS_DELETE:1,
})

export const SPEC_PHOTO_URL_SOURCE = Object.freeze({
  /**
   * 本地上传
   */
  LOCAL:1,
  /**
   * PEC
   */
  IS_DELETE:2,
})

// 分销账单
export const PROFIT_RECORD_STATUS = [
  {
    label: '待成单',
    value: 'normal',
    class: 'text-warning',
    badge: 'warning'
  },
  {
    label: '待结算',
    value: 'paid',
    class: 'text-processing',
    badge: 'processing'
  },
  {
    label: '已分佣',
    value: 'completed',
    class: 'text-success',
    badge: 'success'
  },
  {
    label: '已失效',
    value: 'canceled',
    class: 'text-default',
    badge: 'default'
  }
]

// 提现审核状态
export const WITHDRAWAL_NORMAL = 'normal'
export const WITHDRAWAL_AGREE = 'agree'
export const WITHDRAWAL_REFUSE = 'refuse'

export const WITHDRAWAL_STATUS = [
  {
    label: '待审核',
    value: WITHDRAWAL_NORMAL,
    badge: 'default',
    allowOperate: true
  },
  {
    label: '通过',
    value: WITHDRAWAL_AGREE,
    badge: 'success'
  },
  {
    label: '拒绝',
    value: WITHDRAWAL_REFUSE,
    badge: 'error'
  }
]

// 提现状态
export const WITHDRAWAL_TRANSFORM_NORMAL = 'normal'
export const WITHDRAWAL_TRANSFORM_PENDING = 'pending'
export const WITHDRAWAL_TRANSFORM_SUCCESS = 'success'
export const WITHDRAWAL_TRANSFORM_FAIL = 'fail'

export const WITHDRAWAL_TRANSFORM_STATUS = [
  {
    label: '待提现',
    value: WITHDRAWAL_TRANSFORM_NORMAL,
    badge: 'default',
    allowOperate: true
  },
  {
    label: '提现中',
    value: WITHDRAWAL_TRANSFORM_PENDING,
    badge: 'processing',
  },
  {
    label: '提现成功',
    value: WITHDRAWAL_TRANSFORM_SUCCESS,
    badge: 'success'
  },
  {
    label: '提现失败',
    value: WITHDRAWAL_TRANSFORM_FAIL,
    badge: 'error'
  }
]

// 提现失败原因
export const WITHDRAWAL_FAIL_REASON = [
  {value: 'ACCOUNT_FROZEN',label:'该用户账户被冻结'},
  {value: 'REAL_NAME_CHECK_FAIL',label:'收款人未实名认证，需要用户完成微信实名认证'},
  {value: 'NAME_NOT_CORRECT',label:'收款人姓名校验不通过，请核实信息'},
  {value: 'OPENID_INVALID',label:'Openid格式错误或者不属于商家公众账号'},
  {value: 'TRANSFER_QUOTA_EXCEED',label:'超过用户单笔收款额度，核实产品设置是否准确'},
  {value: 'DAY_RECEIVED_QUOTA_EXCEED',label:'超过用户单日收款额度，核实产品设置是否准确'},
  {value: 'MONTH_RECEIVED_QUOTA_EXCEED',label:'超过用户单月收款额度，核实产品设置是否准确'},
  {value: 'DAY_RECEIVED_COUNT_EXCEED',label:'超过用户单日收款次数，核实产品设置是否准确'},
  {value: 'PRODUCT_AUTH_CHECK_FAIL',label:'未开通该权限或权限被冻结，请核实产品权限状态'},
  {value: 'OVERDUE_CLOSE',label:'超过系统重试期，系统自动关闭'},
  {value: 'ID_CARD_NOT_CORRECT',label:'收款人身份证校验不通过，请核实信息'},
  {value: 'ACCOUNT_NOT_EXIST',label:'该用户账户不存在'},
  {value: 'TRANSFER_RISK',label:'该笔转账可能存在风险，已被微信拦截'},
  {value: 'OTHER_FAIL_REASON_TYPE',label:'其它失败原因'},
  {value: 'REALNAME_ACCOUNT_RECEIVED_QUOTA_EXCEED',label:'用户账户收款受限，请引导用户在微信支付查看详情'},
  {value: 'RECEIVE_ACCOUNT_NOT_PERMMIT',label:'未配置该用户为转账收款人，请在产品设置中调整，添加该用户为收款人'},
  {value: 'PAYEE_ACCOUNT_ABNORMAL',label:'用户账户收款异常，请联系用户完善其在微信支付的身份信息以继续收款'},
  {value: 'PAYER_ACCOUNT_ABNORMAL',label:'商户账户付款受限，可前往商户平台获取解除功能限制指引'},
  {value: 'TRANSFER_SCENE_UNAVAILABLE',label:'该转账场景暂不可用，请确认转账场景ID是否正确'},
  {value: 'TRANSFER_SCENE_INVALID',label:'你尚未获取该转账场景，请确认转账场景ID是否正确'},
  {value: 'TRANSFER_REMARK_SET_FAIL',label:'转账备注设置失败， 请调整后重新再试'},
  {value: 'RECEIVE_ACCOUNT_NOT_CONFIGURE',label:'请前往商户平台-商家转账到零钱-前往功能-转账场景中添加'},
  {value: 'BLOCK_B2C_USERLIMITAMOUNT_BSRULE_MONTH',label:'超出用户单月转账收款20w限额，本月不支持继续向该用户付款'},
  {value: 'BLOCK_B2C_USERLIMITAMOUNT_MONTH',label:'用户账户存在风险收款受限，本月不支持继续向该用户付款'},
  {value: 'MERCHANT_REJECT',label:'商户员工（转账验密人）已驳回转账'},
  {value: 'MERCHANT_NOT_CONFIRM',label:'商户员工（转账验密人）超时未验密'}
]