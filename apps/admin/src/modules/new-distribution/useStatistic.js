import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { distributorStatisticApi } from './api'
import { computed, ref } from 'vue'
import { USER_OPTIONS } from './enum'
import formatters from '@/utils/formatters'
import { cloneDeep } from 'lodash'

export const useStatistic = () => {
  const { formState: filterState, resetFormState: resetFilterState } = useFormState({ conditionKey: 'name' })
  const sorted = ref({})

  const exportStatisticPath = `${distributorStatisticApi.resourcePath}/export`

  const useQuery = () => {
    return {
      ...useRelationsTransformQuery(filterState, {
        'distributor.name': 'like',
        'user.nickname': 'like',
        'user.phone_number': '=',
        date: 'dateRange'
      }),
      relations: ['distributor'],
      sorts: sorted.value.order ? [`${sorted.value.order == 'descend' ? '-' : ''}${sorted.value.columnKey}`] : []
    }
  }

  const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    distributorStatisticApi
      .paginator({
        ...useQuery(),
        offset,
        limit
      })
      .then(data => {
        return {
          ...data,
          items: data.items.map(item => {
            return {
              ...item,
              distributor: item.distributor
                ? {
                    ...item.distributor,
                    id_card_number: formatters.encrypt(item.distributor.id_card_number)
                  }
                : {}
            }
          })
        }
      })
  )

  const placeholder = computed(() => {
    return item => {
      return USER_OPTIONS.find(option => option.value == item)?.label
    }
  })

  const search = () => {
    sorted.value = {}
    setPage()
  }

  const onReset = () => {
    resetFilterState()
    sorted.value = {}
    setPage()
  }

  const onChange = (pagination, __, sorter) => {
    sorted.value = cloneDeep(sorter)
    console.log(sorted.value)
    setPage(pagination)
  }

  return {
    exportStatisticPath,
    useQuery,
    filterState,
    data,
    setPage,
    loading,
    placeholder,
    onReset,
    onChange,
    search,
    sorted
  }
}
