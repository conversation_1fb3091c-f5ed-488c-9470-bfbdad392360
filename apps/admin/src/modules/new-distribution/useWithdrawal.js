import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { message } from 'ant-design-vue'
import { withdrawalApi } from './api'
import { USER_OPTIONS, WITHDRAWAL_STATUS, WITHDRAWAL_TRANSFORM_STATUS, WITHDRAWAL_FAIL_REASON } from './enum'
import { computed, ref } from 'vue'
import formatters from '@/utils/formatters'

export const useWithdrawal = () => {
  const { formState: filterState, resetFormState: resetFilterState } = useFormState({ 
    conditionKey: 'name',
    status: undefined,
    transfer_status: undefined
  })

  const exportWithdrawalPath = `${withdrawalApi.resourcePath}/export`
  const useQueryAndRelations = () => {
    return {
      ...useRelationsTransformQuery(filterState, {
        'distributor.name': 'like',
        'user.nickname': 'like',
        'user.phone_number': '=',
        created_at: 'dateRange',
        status: '=',
        transfer_status: '='
      }),
      relations: ['user', 'distributor']
    }
  }

  const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
    withdrawalApi
      .paginator({
        ...useQueryAndRelations(),
        offset,
        limit
      })
      .then(data => {
        return {
          ...data,
          items: data.items.map(item => {
            const fail_reasons = []
            const fail_reason_tips = []
            
            item.withdrawal_bill?.items?.forEach((bill) => {
              fail_reasons.push(bill.fail_reason)
              fail_reason_tips.push(WITHDRAWAL_FAIL_REASON.find(reason => reason.value == bill.fail_reason)?.label)
            })

            return {
              ...item,
              amount: item.amount / 100,
              success_amount: item.success_amount / 100,
              statusOption: WITHDRAWAL_STATUS.find(status => status.value == item.status),
              transformStatusOption: WITHDRAWAL_TRANSFORM_STATUS.find(status => status.value == item.transfer_status),
              fail_reasons,
              fail_reason_tips,
              distributor: item.distributor
                ? {
                    ...item.distributor,
                    id_card_number: formatters.encrypt(item.distributor.id_card_number)
                  }
                : {},
              user: item.user
                ? {
                    ...item.user,
                    phone_number: formatters.encrypt(item.user.phone_number || '')
                  }
                : {}
            }
          })
        }
      })
  )

  const placeholder = computed(() => {
    return item => {
      return USER_OPTIONS.find(option => option.value == item)?.label
    }
  })

  const onReset = () => {
    resetFilterState()
    setPage()
  }

  const handleAudit = async (record, status) => {
    loading.value = true
    await withdrawalApi
      .updateByPath('audit', {
        id: record.id,
        status
      })
      .finally(() => (loading.value = false))
    message.success('审核成功')
    refresh()
  }

  const handleWithdrawal = async (record) => {
    loading.value = true
    await withdrawalApi
      .updateByPath('retry', {
        id: record.id,
      })
      .finally(() => (loading.value = false))
    message.success('操作成功')
    refresh()
  }

  return {
    exportWithdrawalPath,
    useQueryAndRelations,
    filterState,
    data,
    setPage,
    loading,
    onReset,
    handleAudit,
    placeholder,
    handleWithdrawal
  }
}
