import { apiFactory } from '@/api'

export const applyApi = apiFactory.restful('/distribution/applies')
export const distributorApi = apiFactory.restful('/distribution/distributors')
export const distributorSpecApi = apiFactory.restful('/distribution/distributor-specs')
export const distributionSpecApi = apiFactory.restful('/distribution/specs')
export const distributorStatisticApi = apiFactory.restful('/distribution/distributor-statistics')
export const getDaisy = apiFactory.command('/image/get-daisy')
export const inviteBindApi = apiFactory.restful('/distribution/invite-bind')
export const bannerApi = apiFactory.restful('/banners')

export const specApi = apiFactory.restful('/goods/goods-specs')

export const profitRecordApi = apiFactory.restful('/distribution/orders')

export const withdrawalApi = apiFactory.restful('/distribution/withdrawals')

/**
 * 分销分组
 */
export const distributorGroupApi = apiFactory.restful('/distributor/group')

/**
 * 分销奖金
 */
export const distributorPrizeApi = apiFactory.restful('/distribution/bonus')
