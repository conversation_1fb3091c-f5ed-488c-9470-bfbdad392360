<template>
  <!-- 表格 -->
  <uc-layout-list title="分销商品列表">
    <template #filter>
      <a-form-item>
        <a-input v-model:value="filterState.keyword" placeholder="请输入关键词" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="link" @click="exportFile(downloadTemplatePath)">
          <template #icon>
            <CloudDownloadOutlined />
          </template>
          下载模板
        </a-button>
        <a-upload
          :action="importDistributionSpecUrl"
          accept=".xlsx,.xls,.csv"
          :show-upload-list="false"
          @change="handleImport"
        >
          <a-button type="link">
            <template #icon>
              <CloudUploadOutlined />
            </template>
            批量导入
          </a-button>
        </a-upload>
        <a-button type="primary" @click="exportFile(exportDistributionSpecPath, useQueryAndRelations())">
          导出
        </a-button>
        <a-button type="primary" @click="onShowModal()">
          新增分销商品
        </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="商品名称/商品规格" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-bind="record"
              :url="record?.spec?.photo_url"
              :title="record?.product?.title"
              :label="getSpecName(record?.spec?.attrs)"
            />
          </template>
        </a-table-column>
        <a-table-column key="sku" align="center" :width="200" title="商品SKU" data-index="sku" />
        <a-table-column align="center" :width="120" title="可定价分销员">
          <template #default="{ record }">
            <router-link
              :to="{
                name: 'spec-distributor',
                query: {
                  spec_id: record.id,
                  title: record?.product?.title,
                  attrs: getSpecName(record?.spec?.attrs),
                  sku: record.sku
                }
              }"
            >
              <a-button type="link">
                {{ record.distributor_specs_count }}
              </a-button>
            </router-link>
          </template>
        </a-table-column>
        <a-table-column key="price" align="center" :width="120" title="市场价" data-index="price" />

        <a-table-column align="center" :width="100" title="分销价">
          <template #default="{record}">
            <view class="distributor-ratio">
              <a-input-number
                v-model:value="record.distribution_price"
                :precision="2"
                :formatter="formatterNumber"
                :parser="formatterNumber"
                min="0.01"
                :max="record.spec.price"
                @focus="() => onInputFocus(record.distribution_price)"
                @blur="() => onHandleRatio(record, 'distribution_price', '分销价')"
              />
            </view>
          </template>
        </a-table-column>
        <a-table-column :width="100" align="center" title="拿货价">
          <template #default="{record}">
            <view class="distributor-ratio">
              <a-input-number
                v-model:value="record.basic_price"
                :precision="2"
                :formatter="formatterNumber"
                :parser="formatterNumber"
                min="0.01"
                @focus="() => onInputFocus(record.basic_price)"
                @blur="() => onHandleRatio(record, 'basic_price', '拿货价')"
              />
            </view>
          </template>
        </a-table-column>

        <a-table-column title="操作" :width="120" align="center">
          <template #default="{ record }">
            <a-popconfirm placement="left" ok-text="确定" cancel-text="取消" @confirm="handleDelete(record)">
              <template #title>
                <div class="m-b-10">
                  <div>移除选品后将移除对所有分销商的授权，</div>
                  <div>是否确认移除。</div>
                </div>
              </template>
              <a-button type="link" class="danger">
                移除选品
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
      <a-modal :visible="modalVisible" title="新增分销商品" @cancel="modalVisible = false" @ok="handleCreate">
        <a-form
          :model="modelFormState"
          :rules="modelFormStateRules"
          :label-col="modalLabelCol"
          :wrapper-col="modalWrapperCol"
        >
          <a-form-item label="商品SKU" class="required">
            <a-select
              v-model:value="modelFormState.sku"
              show-search
              placeholder="请输入商品sku"
              @search="handleSkuSearch"
              @change="handleSkuChange"
            >
              <a-select-option v-for="spec in searchData.items" :key="spec.sku">
                {{ spec.sku }}({{ getSpecName(spec.attrs) }})
              </a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="市场价">
            <a-input v-model:value="modelFormState.price" placeholder="0" prefix="￥" disabled />
          </a-form-item>

          <a-form-item label="分销价格" class="required">
            <a-input-number v-model:value="modelFormState.distribution_price" :precision="2" :max="modelFormState.price" />
          </a-form-item>

          <a-form-item label="拿货价格" class="required">
            <a-input-number v-model:value="modelFormState.basic_price" :precision="2" :max="modelFormState.distribution_price" />
          </a-form-item>
        </a-form>
      </a-modal>

      <a-modal
        :width="1200"
        :visible="specPhotoModalVisible"
        :title="specPhotoModalTitle"
        @cancel="specPhotoModalVisible = false"
        @ok="handleEditSpecPhoto"
      >
        <a-row>
          <a-col :span="8">
            <img :src="defaultSpecPhoto" alt="" class="image" />
          </a-col>
          <a-col :span="16" style="border: 1px dashed rgb(235, 238, 245); padding: 10px;">
            <div class="m-10">
              拖拽图片可调整相册图片顺序，排序第一张的图片视为默认图
            </div>
            <div class="draggable-box">
              <draggable v-model="localRecord.spec.spec_photo_urls" item-key="url">
                <template #item="{element,index}">
                  <a-col v-show="!element.is_delete" class="m-b-11 p-5" :span="6">
                    <div class="relative" @mouseenter="onMouseOver(index)" @mouseleave="currentIndex = -1">
                      <div :class="['fade',{'fade-in':currentIndex !== index},{'fade-out':currentIndex===index}]">
                        <div :class="['photo-item',{'photo-default':index === 0}]">
                          <img :src="element.url" class="image" />
                        </div>
                      </div>
                      <div v-show="currentIndex === index" class="operator">
                        <DeleteOutlined
                          :style="{color:'#78B6F5FF',fontSize:'18px',cursor:'pointer'}"
                          @click="onRemovePhoto(index)"
                        />
                      </div>
                    </div>
                  </a-col>
                </template>
              </draggable>
            </div>
            <a-col :span="6">
              <uc-upload
                :max-length="1"
                :list="[]"
                size="large"
                @update:list="onAddPhoto"
              />
            </a-col>
          </a-col>
        </a-row>
      </a-modal>
    </template>
  </uc-layout-list>
</template>

<script setup>
import {CloudDownloadOutlined, CloudUploadOutlined, DeleteOutlined} from '@ant-design/icons-vue'
import draggable from 'vuedraggable'
import {useSpecs} from '../../useDistributionSpec'

const {
  onInputFocus,
  onHandleRatio,
  formatterNumber,
  importDistributionSpecUrl,
  exportDistributionSpecPath,
  downloadTemplatePath,
  useQueryAndRelations,
  data,
  loading,
  setPage,
  filterState,
  onReset,
  handleImport,
  handleDelete,
  getSpecName,
  modalVisible,
  modelFormState,
  onShowModal,
  handleCreate,
  searchData,
  handleSkuSearch,
  handleSkuChange,
  specPhotoModalVisible,
  specPhotoModalTitle,
  handleEditSpecPhoto,
  localRecord,
  defaultSpecPhoto,
  onMouseOver,
  currentIndex,
  onAddPhoto,
  onRemovePhoto,
} = useSpecs()
</script>

<style lang="less">
.draggable-box>div{
  display: flex;
  flex-wrap: wrap;
}
.photo-default {
  border: 1px solid #409EFF;
  position: relative;
}

.photo-default:before {
  position: absolute;
  content: "\9ED8\8BA4\56FE";
  bottom: 0;
  right: 0;
  font-size: 12px;
  line-height: 12px;
  padding: 3px;
  color: #ffffff;
  background-color: #409EFF;

}

.operator {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  display: flex;
  justify-content: center;
  align-items: center;
}

.fade {
  transition: opacity 200ms ease-in-out;
}

.fade-in {
  opacity: 1;
}


.fade-out {
  opacity: 0.2;
}

.image {
  width: 100%;
  display: block;
}

.distributor-ratio {
  .ant-input-number-input {
    text-align: center;
  }
}
</style>
