<template>
  <uc-layout-list>
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="filterState.conditionKey" :options="USER_OPTIONS" />
          <a-input v-model:value="filterState.conditionValue" :placeholder="placeholder(filterState.conditionKey)" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="filterState.created_at" :placeholder="['加入时间','加入时间']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #title>
      <span>{{ params.title + ' | ' +params.attrs }}</span>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="exportFile(exportSpecDistributorPath, useQueryAndRelations())">
          导出
        </a-button>
        <a-button type="primary" @click="onShowModal">
          新增授权
        </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="已授权分销商">
          <template #default="{record}">
            {{ record.distributor?.name }}({{ record.distributor?.id_card_number }})
          </template>
        </a-table-column>
        <!-- <a-table-column :width="100" align="center" title="该商品的分佣比例" data-index="ratio" :custom-render="({text}) => `${text}%`" /> -->
        <a-table-column :width="100" align="center" title="市场价" data-index="distributor.price" :custom-render="({text}) => text || 0" />
        <a-table-column :width="100" align="center" title="分销价" data-index="distributor.distribution_price" :custom-render="({text}) => text || 0" />
        <a-table-column :width="100" align="center" title="拿货价" data-index="distributor.basic_price" :custom-render="({text}) => text || 0" />
        <a-table-column
          key="total_amount"
          :width="100"
          align="center"
          title="总销售额"
          data-index="distributor.total_amount"
          :custom-render="({text}) => text || 0"
        />
        <a-table-column
          key="total_profit"
          :width="100"
          align="center"
          title="总收益"
          data-index="distributor.total_profit"
          :custom-render="({text}) => text || 0"
        />
        <!-- <a-table-column key="withdrawal_amount" :width="100" align="center" title="可提现" :custom-render="({record}) => record.distributor?.allowWithdraw.toFixed(2)" />
        <a-table-column :width="100" align="center" title="已提现" :custom-render="({record}) => record.distributor.already_withdrawal_amount" />
        <a-table-column :width="100" align="center" title="状态">
          <template #default="{record}">
            <a-badge :status="record.distributor?.statusOption?.badge" :text="record.distributor?.statusOption?.label" />
          </template>
        </a-table-column> -->
        <a-table-column
          key="created_at"
          :width="120"
          align="center"
          title="加入时间"
          data-index="distributor.created_at"
          :custom-render="({text}) => text || '-'"
        />
        <a-table-column :width="100" align="center" title="操作">
          <template #default="{record}">
            <a-space>
              <a-popconfirm placement="left" @confirm="onDeleteSpec(record)">
                <template #title>
                  <div class="m-b-10">
                    取消授权后，分销商可继续分销当前商品，分销价和拿货价使用商品默认配置，是否确认操作？
                  </div>
                </template>
                <a-button type="link" class="danger">
                  取消授权
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
      <a-modal :visible="modalVisible" :width="1000" title="新增授权" @cancel="modalVisible = false" @ok="onHandleBatchCreate">
        <a-form layout="inline m-b-10 distributor-form-item">
          <a-form-item class="condition-form-item">
            <a-input-group compact>
              <a-select v-model:value="distributorFilterState.conditionKey" :options="USER_OPTIONS" />
              <a-input v-model:value="distributorFilterState.conditionValue" :placeholder="placeholder(distributorFilterState.conditionKey)" />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-range-picker v-model:value="distributorFilterState.created_at" :placeholder="['加入时间','加入时间']" />
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="setDistributorPage">
              查询
            </a-button>
          </a-form-item>
          <a-form-item>
            <a-button @click="onDistributorReset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
        <a-table
          :data-source="distributorData.items"
          row-key="id"
          :loading="distributorLoading"
          :pagination="stdPagination(distributorData)"
          :row-selection="{ selectedRowKeys: selectDistributorIds, onChange: onSelectDistributorChange }"
          @change="setDistributorPage"
        >
          <a-table-column title="分销商姓名" align="center">
            <template #default="{record}">
              {{ record.name }}({{ record.id_card_number }})
            </template>
          </a-table-column>
          <a-table-column title="加入时间" :width="180" align="center" data-index="created_at" />
          <a-table-column title="分销价" :width="180" align="center" data-index="created_at">
            <template #default="{record}">
              <view class="distributor-ratio">
                <a-input-number
                  v-model:value="record.distribution_price"
                  :formatter="formatterNumber"
                  :parser="formatterNumber"
                  :precision="2"
                  min="0.01"
                  :disabled="isCheck(record)"
                  @blur="changeBasic(record,'distribution_price')"
                />
              </view>
            </template>
          </a-table-column>
          <a-table-column title="拿货价" :width="180" align="center" data-index="created_at">
            <template #default="{record}">
              <view class="distributor-ratio">
                <a-input-number
                  v-model:value="record.basic_price"
                  :formatter="formatterNumber"
                  :parser="formatterNumber"
                  :precision="2"
                  min="0.01"
                  :max="record.distribution_price ? record.distribution_price - 0.01:undefined"
                  :disabled="isCheck(record)"
                  @blur="changeBasic(record,'basic_price')"
                />
              </view>
            </template>
          </a-table-column>
        </a-table>
        <div>
          <span>已选择分销商数量：</span>
          <span>{{ selectDistributorIds.length }}</span>
          <a-button type="link" @click="onSelectDistributorChange()">
            清空
          </a-button>
        </div>
      </a-modal>
    </template>
  </uc-layout-list>
</template>

<script setup>

import {useDistributor} from '../../useSpecDistributor'

import { USER_OPTIONS } from '../../enum'
const {exportSpecDistributorPath,useQueryAndRelations,onReset,formState,filterState,data,setPage,loading,visible,onDeleteSpec,params,placeholder,
modalVisible,isCheck,changeBasic,formatterNumber, selectDistributorIds, onSelectDistributorChange, distributorFilterState, resetDistributorState, distributorData, setDistributorPage, distributorLoading, onDistributorReset, onShowModal, onHandleBatchCreate } = useDistributor()

</script>

<style lang="less">
.distributor-ratio {
  .ant-input-number-input {
    text-align: center;
  }
}

.distributor-form-item {
  .condition-form-item {
    .ant-input-group {
      display: flex;
      .ant-select {
        width: 120px;
      }
    }
  }
}
</style>
