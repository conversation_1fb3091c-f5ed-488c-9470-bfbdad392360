<template>
  <uc-layout-list title="邀请绑定记录">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="formState.conditionKey" :options="INVITE_OPTIONS" />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入内容" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="formState.created_at" />
      </a-form-item>
      <a-form-item>
        <a-space>
          <a-button type="primary" @click="setPage(1)">
            查询
          </a-button>
          <a-button @click="onReset">
            重置
          </a-button>
          <a-button @click="handleExport">
            导出业绩报表
          </a-button>
        </a-space>
      </a-form-item>
    </template>
    <template #extra>
      <uc-import-export v-model:importPath="importPath" v-model:tempPath="downloadTemplatePath" @imported="refresh" />
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="分销商姓名" width="200px" align="center">
          <template #default="{ record }">
            {{ record.distributor.name }}({{ record.distributor.id_card_number }})
          </template>
        </a-table-column>
        <a-table-column title="地址信息">
          <template #default="{ record }">
            {{ record.province }}{{ record.city }}
          </template>
        </a-table-column>
        <a-table-column title="代理商" data-index="proxy" width="150px" align="center" />
        <a-table-column :width="110" align="center" title="状态">
          <template #default="{ record }">
            <a-badge :status="record?.statusOption?.badge" :text="record?.statusOption?.label" />
          </template>
        </a-table-column>
        <a-table-column title="邀请人" data-index="invitee" width="150px" align="center" />
        <a-table-column :width="120" align="center" title="加入时间" data-index="distributor.created_at" />
        <a-table-column title="操作" width="120px" align="center">
          <template #default="{ record }">
            <a-space>
              <a-button type="link" @click="onEdit(record)">
                编辑
              </a-button>
              <a-popconfirm title="确认操作后将取消绑定关系，是否确认；操作确认后当前分销商与邀请人无关联关系" @confirm="handleDel(record)">
                <a-button type="link" danger>
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal v-model:visible="visible" :title="title" @ok="handleOk">
    <a-form ref="formRef" :model="row" :rules="rules" :label-col="{ span: 6 }">
      <a-form-item label="分销商姓名">
        <a-input v-model:value="row.distributor.name" disabled />
      </a-form-item>
      <a-form-item label="证件信息">
        <a-input v-model:value="row.distributor.id_card_number" disabled />
      </a-form-item>
      <a-form-item label="所在地区" required style="margin-bottom: 0 !important">
        <a-row :gutter="10">
          <a-col :span="12">
            <a-form-item name="province">
              <a-input v-model:value.trim="row.province" />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item name="city">
              <a-input v-model:value.trim="row.city" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form-item>
      <a-form-item label="所属代理商" name="proxy">
        <a-input v-model:value="row.proxy" />
      </a-form-item>
      <a-form-item label="邀请人" name="invitee">
        <a-input v-model:value="row.invitee" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { INVITE_OPTIONS } from '../../enum'
import { useFormState } from '@/composables/useFormState'
import { inviteBindApi } from '@/modules/new-distribution/api'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import formatters from '@/utils/formatters'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { formatDistributorStatus } from '@/modules/new-distribution/useDistributor'
import { message } from 'ant-design-vue'
import qs from 'qs'
import { useStore } from '@/store/auth'


const { state } = useStore()

const downloadTemplatePath = `${inviteBindApi.resourcePath}/template`
const importPath = `${inviteBindApi.fullPath}/import`
const visible = ref(false)
const row = ref(undefined)
const formRef = ref()
const { formState, resetFormState } = useFormState({
  conditionKey: 'name',
  conditionValue: undefined,
  created_at: undefined
})

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  inviteBindApi
    .paginator({
      ...useRelationsTransformQuery(formState, {
        'distributor.name': 'like',
        invitee: 'like',
        proxy: 'like',
        'distributor.created_at': 'dateRange'
      }),
      relations: ['distributor'],
      offset,
      limit
    })
    .then(record => {
      record.items.forEach(item => {
        item.distributor.id_card_number = formatters.encrypt(item.distributor.id_card_number)
        item.statusOption = formatDistributorStatus(item.distributor)
      })
      return record
    })
)

const handleDel = record => {
  inviteBindApi.delete(record.id).then(res => {
    refresh()
    message.success('操作成功')
  })
}

const onReset = () => {
  resetFormState()
  setPage(1)
}

const onEdit = record => {
  row.value = { ...record }
  visible.value = true
}

const title = computed(() => {
  return row.value ? row.value.distributor.name + ' | 分销商信息编辑' : ''
})

const rules = {
  province: [{ required: true, message: '请填写省份', trigger: 'blur' }],
  city: [{ required: true, message: '请填写城市', trigger: 'blur' }],
  proxy: [{ required: true, message: '请填写代理商', trigger: 'blur' }],
  invitee: [{ required: true, message: '请填写邀请人', trigger: 'blur' }]
}

const handleOk = () => {
  formRef.value.validate().then(async () => {
    await inviteBindApi.update(row.value.id, row.value)
    await refresh()
    message.success('操作成功')
    visible.value = false
  })
}

const handleExport = () => {
  let query = {
    ...useRelationsTransformQuery(formState, {
      'distributor.name': 'like',
      invitee: 'like',
      proxy: 'like',
      'distributor.created_at': 'dateRange'
    })
  }
  query = qs.stringify(query)
  let path = inviteBindApi.fullPath + '/export?token=' + state.token + '&' + query
  let a = document.createElement('a')
  a.href = path
  a.click()
}
</script>

<style scoped></style>
