<template>
  <uc-layout-list>
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="filterStatus.conditionKey" :options="USER_OPTIONS" />
          <a-input v-model:value="filterStatus.conditionValue" :placeholder="placeholder(filterStatus.conditionKey)" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="filterStatus.created_at" :placeholder="['申请日期','申请日期']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" class="link" @click="copyLinkByRoute('distributorApply')">
          邀请页链接
        </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="申请人">
          <template #default="{record}">
            {{ record?.user?.nickname }}({{ record?.user?.phone_number }})
          </template>
        </a-table-column>
        <a-table-column key="id_card_number" :width="160" align="center" title="身份证号" data-index="id_card_number" />
        <a-table-column key="name" :width="160" align="center" title="姓名" data-index="name" />
        <a-table-column :width="160" align="center" title="身份证图片">
          <template #default="{record}">
            <uc-img-group :urls="[record.card_front_url, record.card_back_url]" />
          </template>
        </a-table-column>
        <a-table-column :width="160" align="center" title="申请状态">
          <template #default="{record}">
            <a-badge :text="record?.statusOption?.label" :status="record?.statusOption?.badge" />
          </template>
        </a-table-column>
        <a-table-column key="created_at" :width="160" align="center" title="申请时间" data-index="created_at" />
        <a-table-column :width="100" align="center" title="操作">
          <template #default="{record}">
            <a-space v-if="record?.statusOption?.allowOperate">
              <a-button type="link" class="success" @click="onHandleVisible(record,APPLY_AGREED )">
                同意
              </a-button>
              <a-button type="link" class="danger" @click="onHandleVisible(record,APPLY_REFUSED )">
                拒绝
              </a-button>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
      <a-modal v-model:visible="visible" title="申请通过" @ok="onHandleOperate">
        <a-form>
          <a-form-item label="申请人姓名">
            <view class="">
              {{ formState.name }}
            </view>
          </a-form-item>
          <!-- <a-form-item v-if="formState.status == APPLY_AGREED" label="分佣比例" required>
            <view class="flex">
              <a-input-number v-model:value="formState.ratio" :step="step" />
              <span class="ant-input-group-addon p-lr-20 flex flex-cc flex-sc">%</span>
            </view>
          </a-form-item> -->
          <a-form-item v-if="formState.status == APPLY_REFUSED" label="拒绝原因" required>
            <a-textarea v-model:value="formState.reason" :maxlength="200" :autosize="{ minRows: 5, maxRows: 5}" placeholder="最多200字" />
          </a-form-item>
        </a-form>
      </a-modal>
    </template>
  </uc-layout-list>
</template>

<script setup>

import {useApply} from '../../useApply'
import {USER_OPTIONS, APPLY_AGREED, APPLY_REFUSED} from '../../enum'

const {placeholder,onReset,onHandleVisible,formState,filterStatus,data,setPage,loading,visible,onHandleOperate} = useApply()

</script>

<style>
</style>
