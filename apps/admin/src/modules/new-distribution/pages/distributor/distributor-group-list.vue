<template>
  <uc-layout-list title="分销分组">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-input v-model:value="formState.name" placeholder="分组名称" allow-clear />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">新增</a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="名称" data-index="name" />
        <a-table-column title="分销商数">
          <template #="{ record }">
            <router-link :to="{ name: 'distributor', query: { group_id: record.id } }">
              {{ record.distributors_count }}
            </router-link>
          </template>
        </a-table-column>
        <a-table-column key="created_at" :width="110" title="创建时间" data-index="created_at" />
        <a-table-column :width="100" title="操作">
          <template #="{ record }">
            <a-space>
              <a-button type="link" @click="onEdit(record)"> 编辑 </a-button>
              <a-popconfirm placement="left" title="您确定要删除该数据么？" @confirm="handleDelete(record)">
                <a-button type="link" class="danger"> 删除 </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal v-model:visible="modalVisible" :title="modalFormState.id ? '编辑分组' : '新增分组'" @ok="handleModalSubmit">
    <a-form :label-col="{ span: modalLabelCol }" :wrapper-col="{ span: modalWrapperCol }">
      <a-form-item label="分组名称" class="required">
        <a-input v-model:value="modalFormState.name" placeholder="请输入分组名称" allow-clear />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useModalVisible } from '@/composables/useToggles'
import { distributorGroupApi } from '../../api'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  distributorGroupApi.paginator({
    filters: useTransformQuery(formState, {
      name: 'like'
    }),
    relations: ['distributors'],
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  name: undefined
})

onRestFormState(() => setPage())

const handleDelete = async record => {
  await distributorGroupApi.delete(record.id)
  message.success('删除成功')
  setPage()
}

const {
  formState: modalFormState,
  resetFormState: resetModalFormState,
  validateForm: validateModalForm,
  setFormRules: setFormModalRules,
  setFormState: setModalFormState
} = useFormState({
  name: undefined
})

setFormModalRules({
  name: { required: true, message: '请输入分组名称' }
})

const { modalVisible, setModalVisible } = useModalVisible()
const onAdd = () => {
  resetModalFormState()
  setModalVisible(true)
}
const onEdit = record => {
  setModalFormState(cloneDeep(record))
  setModalVisible(true)
}

const handleModalSubmit = async () => {
  if (!(await validateModalForm())) return
  const params = cloneDeep(modalFormState.value)
  await (params.id ? distributorGroupApi.update(params.id, params) : distributorGroupApi.create(params))
  message.success('操作成功')
  setPage()
  setModalVisible(false)
}
</script>
