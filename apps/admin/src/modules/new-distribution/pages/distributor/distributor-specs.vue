<template>
  <uc-layout-list :title="params.distributorName ? `${params.distributorName} | 授权商品列表`: '授权商品列表'">
    <template #filter>
      <a-form-item>
        <a-input v-model:value="formState.keyword" placeholder="请输入关键词" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="link" @click="exportFile(downloadTemplatePath)">
          <template #icon>
            <CloudDownloadOutlined />
          </template>
          下载模板
        </a-button>
        <a-upload :action="importDistributionSpecUrl" accept=".xlsx,.xls,.csv" :show-upload-list="false" @change="handleImport">
          <a-button type="link">
            <template #icon>
              <CloudUploadOutlined />
            </template>
            批量导入
          </a-button>
        </a-upload>
        <a-button type="primary" @click="exportFile(exportDistributorSpecPath, useQueryAndRelations())">
          导出
        </a-button>
        <a-button type="primary" @click="onShowModal">
          新增授权
        </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="商品名称/商品规格" ellipsis>
          <template #default="{record}">
            <uc-img-text v-bind="record" :url="record?.spec?.photo_url" :title="record?.product?.title" :label="getSpecName(record?.spec?.attrs)" />
          </template>
        </a-table-column>
        <a-table-column :width="180" align="center" title="商品SKU" data-index="spec.sku" index="spec.sku" />
        <a-table-column :width="100" align="center" title="平台价" data-index="spec.price" index="spec.price" />
        <a-table-column align="center" :width="100" title="分销价">
          <template #default="{record}">
            <view class="distributor-ratio">
              <a-input-number
                v-model:value="record.distribution_price"
                :precision="2"
                :formatter="formatterNumber"
                :parser="formatterNumber"
                min="0.01"
                :max="record.spec.price"
                @focus="() => onInputFocus(record.distribution_price)"
                @blur="() => onHandleRatio(record, 'distribution_price', '分销价')"
              />
            </view>
          </template>
        </a-table-column>
        <a-table-column :width="100" align="center" title="拿货价">
          <template #default="{record}">
            <view class="distributor-ratio">
              <a-input-number
                v-model:value="record.basic_price"
                :precision="2"
                :formatter="formatterNumber"
                :parser="formatterNumber"
                min="0.01"
                @focus="() => onInputFocus(record.basic_price)"
                @blur="() => onHandleRatio(record, 'basic_price', '拿货价')"
              />
            </view>
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="100" align="center">
          <template #default="{record}">
            <a-popconfirm placement="left" @confirm="onDeleteSpec(record)">
              <template #title>
                <div>取消授权后，分销商可继续分销当前商品，分销价和拿货价使用商品默认配置，是否确认操作？</div>
              </template>
              <a-button type="link" class="danger">
                取消授权
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
      <a-modal
        wrap-class-name="modal"
        :visible="modalVisible"
        :width="1000"
        title="新增可分销商品"
        @cancel="modalVisible = false"
        @ok="onHandleBatchCreate"
      >
        <a-form layout="inline m-b-10 spec-form-item">
          <a-form-item>
            <a-input v-model:value="specFilterState.keyword" class="keyword-input" placeholder="请输入商品名称或sku进行搜索查询" />
          </a-form-item>
          <a-form-item class="price-form-item">
            <a-input-group compact>
              <a-input v-model:value="specFilterState.price[0]" class="min-price-input" addon-before="价格区间" placeholder="请输入" />
              <a-input class="character-input" placeholder="~" disabled />
              <a-input v-model:value="specFilterState.price[1]" class="max-price-input" placeholder="请输入" />
            </a-input-group>
          </a-form-item>
          <a-form-item>
            <a-button type="primary" @click="setSpecPage">
              查询
            </a-button>
          </a-form-item>
          <a-form-item>
            <a-button @click="onSpecReset">
              重置
            </a-button>
          </a-form-item>
        </a-form>
        <a-table
          :data-source="specData.items"
          row-key="sku"
          :loading="specLoading"
          :pagination="stdPagination(specData)"
          :row-selection="{ selectedRowKeys: selectSpecSkus, onChange: onSelectSpecChange }"
          @change="setSpecPage"
        >
          <a-table-column title="商品名称" data-index="product.title" :custom-render="({text}) => text || ''" />
          <a-table-column title="sku" align="center" data-index="sku" />
          <a-table-column :width="100" align="center" title="市场价" data-index="spec.price" :custom-render="({text}) => (text/100)" />
          <a-table-column title="分销价" align="center" class="min-width-80" data-index="distribution_price">
            <template #default="{record}">
              <view class="distributor-ratio">
                <a-input-number
                  v-model:value="record.distribution_price"
                  :disabled="isCheck(record)"
                  :precision="2"
                  :formatter="formatterNumber"
                  :parser="formatterNumber"
                  min="0.01"
                  @blur="changeBasic(record,'distribution_price')"
                />
              </view>
            </template>
          </a-table-column>
          <a-table-column title="拿货价" align="center" data-index="basic_price">
            <template #default="{record}">
              <view class="distributor-ratio">
                <a-input-number
                  v-model:value="record.basic_price"
                  :disabled="isCheck(record)"
                  :precision="2"
                  :formatter="formatterNumber"
                  :parser="formatterNumber"
                  min="0.01"
                  :max="record.distribution_price"
                  @blur="changeBasic(record,'basic_price')"
                />
              </view>
            </template>
          </a-table-column>
        </a-table>
        <div>
          <span>已选择商品数量：</span>
          <span>{{ selectSpecSkus.length }}</span>
          <a-button type="link" @click="onSelectSpecChange()">
            清空
          </a-button>
        </div>
      </a-modal>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { CloudDownloadOutlined, CloudUploadOutlined } from '@ant-design/icons-vue'
import { useDistributorSpec } from '../../useDistributorSpec'
const {downloadTemplatePath,importDistributionSpecUrl,handleImport, exportDistributorSpecPath, useQueryAndRelations, data, loading, setPage, formState, onReset, onDeleteSpec, title, params, onInputFocus,onHandleRatio,
  modalVisible,isCheck,formatterNumber, selectSpecSkus,changeBasic, onSelectSpecChange,getSpecName, specFilterState, resetSpecFilterState, specData, setSpecPage, specLoading, onSpecReset, onShowModal, onHandleBatchCreate } = useDistributorSpec()

</script>

<style scoped lang="less">
.spec-form-item {
  .keyword-input {
    width: 250px;
  }
}
.price-form-item {
  .ant-form-item-control-input-content {
    display: flex;
  }
  .min-price-input {
    text-align: center;
    width: 200px;
  }
  .character-input {
    width: 30px;
    border-left: 0;
    pointer-events: none;
    background-color: #fff;
  }
  .max-price-input {
    text-align: center;
    border-left: 0;
    width: 120px;
  }
}
</style>

<style lang="less">
.ant-table-thead > tr > .min-width-80 {
  min-width: 80px;
}
</style>
