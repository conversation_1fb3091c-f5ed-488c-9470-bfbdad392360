<template>
  <uc-layout-list title="分销商列表">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="filterStatus.conditionKey" :options="USER_OPTIONS" />
          <a-input v-model:value="filterStatus.conditionValue" :placeholder="placeholder(filterStatus.conditionKey)" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="filterStatus.group_id"
          :options="distributorGoroupOptions"
          placeholder="分销分组"
          allow-clear
        />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="filterStatus.created_at" :placeholder="['加入日期', '加入日期']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="exportFile(exportDistributorPath, useQueryAndRelations())"> 导出 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        :scroll="{ x: 1560 }"
        @change="setPage"
      >
        <a-table-column title="分销商" :min-width="120">
          <template #default="{ record }"> {{ record.name }}({{ record.id_card_number }}) </template>
        </a-table-column>
        <a-table-column title="分销分组" align="center" :width="120" ellipsis>
          <template #default="{ record }">
            <a-tooltip placement="top" :title="record.group?.name || '默认'">
              {{ record.group?.name || '默认' }}
            </a-tooltip>
          </template>
        </a-table-column>
        <a-table-column title="奖金等级" align="center" :width="100">
          <template #="{ record }">
            {{ record.max_bonus_name ?? '--' }}
          </template>
        </a-table-column>
        <a-table-column title="奖金金额" align="center" :width="100">
          <template #="{ record }">
            {{ $formatters.thousandSeparator(record.bonus) }}
          </template>
        </a-table-column>
        <a-table-column title="分销订单数" align="center" data-index="orders" :width="120" />
        <!-- <a-table-column :width="100" align="center" title="拿货价">
          <template #default="{record}">
            <view class="distributor-ratio">
              <a-input-number v-model:value="record.basic_price" @blur="() => onHandleRatio(record)" />
            </view>
          </template>
        </a-table-column> -->
        <a-table-column :width="80" align="center" title="可定价商品">
          <template #default="{ record }">
            <router-link
              v-if="!record.is_clear"
              :to="{ name: 'distributor-spec', query: { distributor_id: record.id, distributor_name: record.name } }"
            >
              <a-button type="link" class="processing">
                {{ record.total }}
              </a-button>
            </router-link>
            <div v-else>
              {{ record.total }}
            </div>
          </template>
        </a-table-column>
        <a-table-column key="total_amount" :width="100" align="center" title="总销售额" data-index="total_amount" />
        <a-table-column
          key="tax_pre_total_profit"
          :width="90"
          align="center"
          title="税前收益"
          data-index="tax_pre_total_profit"
        />
        <a-table-column key="total_tax" :width="90" align="center" title="个税扣除" data-index="total_tax" />
        <a-table-column key="total_profit" :width="90" align="center" title="税后结余" data-index="total_profit" />
        <a-table-column
          key="withdrawal_amount"
          :width="90"
          align="center"
          title="可提现"
          :custom-render="({ record }) => record.allowWithdraw.toFixed(2)"
        />
        <a-table-column
          :width="90"
          align="center"
          title="已提现"
          :custom-render="({ record }) => record.already_withdrawal_amount.toFixed(2)"
          data-index=""
        />
        <a-table-column :width="110" align="center" title="状态">
          <template #default="{ record }">
            <a-badge :status="record?.statusOption?.badge" :text="record?.statusOption?.label" />
          </template>
        </a-table-column>
        <a-table-column key="created_at" :width="110" align="center" title="加入时间" data-index="created_at" />
        <a-table-column :width="150" align="center" title="操作" fixed="right">
          <template #default="{ record }">
            <a-space>
              <a-popconfirm
                v-if="record?.statusOption?.allowShutDown"
                placement="left"
                @confirm="onHandleClear(record)"
              >
                <template #title>
                  <div class="m-b-10">
                    确认清退当前分销商账号吗？点击确认后，当前分销商将被清退。订单正常分佣，但分销商不能正常登录账号。
                  </div>
                  <div>！！！注意：清退不可恢复，只能用户自己重新申请。</div>
                </template>
                <a-button type="link" class="danger"> 清退 </a-button>
              </a-popconfirm>
              <a-popconfirm v-if="record?.statusOption?.allowReject" placement="left" @confirm="onHandleClear(record)">
                <template #title>
                  <div class="m-b-10">确认驳回当前分销商账号吗？点击确认后，当前分销商将被驳回。</div>
                  <div>！！！注意：驳回不可恢复，只能用户自己重新申请。</div>
                </template>
                <a-button type="link" class="danger"> 驳回 </a-button>
              </a-popconfirm>
              <a-popconfirm
                v-if="record?.statusOption?.allowDisabled"
                placement="left"
                @confirm="onHandleShutDown(record)"
              >
                <template #title>
                  <div class="m-b-10">确认封号当前分销商账号吗？点击确认后，当前分销商将被封号。</div>
                  <div>订单正常分佣，但分销商不能正常登录账号。</div>
                </template>
                <a-button type="link" class="danger"> 封号 </a-button>
              </a-popconfirm>
              <a-popconfirm placement="left" @confirm="onHandleShutDown(record)">
                <template #title>
                  <div class="m-b-10">确认解封当前分销商账号吗？点击确认后，当前分销商将被解封。</div>
                  <div>订单正常分佣，分销商可正常登录账号。</div>
                </template>
                <a-button v-if="record?.statusOption?.allowUnseal" type="link" class="danger"> 解封 </a-button>
              </a-popconfirm>
              <a-button type="link" @click="onEdit(record)">编辑</a-button>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal v-model:visible="modalVisible" title="编辑" @ok="handelEditSubmit">
    <a-form :label-col="{ span: modalLabelCol }" :wrapper-col="{ span: modalWrapperCol }">
      <a-form-item label="分销分组" class="required">
        <a-select v-model:value="modalFormState.group_id" :options="distributorGoroupOptions" placeholder="请选择分销分组" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { useRoute } from 'vue-router'
import { cloneDeep } from 'lodash'
import { useModalVisible } from '@/composables/useToggles'
import { useFormState } from '@/composables/useFormState'
import { useDistributor, useDistributorGoroup } from '../../useDistributor'
import { USER_OPTIONS } from '../../enum'
import { distributorApi } from '../../api'

const { query } = useRoute()
const {
  exportDistributorPath,
  useQueryAndRelations,
  placeholder,
  onReset,
  onHandleVisible,
  formState,
  filterStatus,
  data,
  setPage,
  loading,
  visible,
  onHandleOperate,
  onHandleRatio,
  onHandleClear,
  onHandleShutDown
} = useDistributor({ query })
const [distributorGoroupOptions] = useDistributorGoroup()

const { modalVisible, setModalVisible } = useModalVisible()
const {
  formState: modalFormState,
  setFormRules: setModalFormRules,
  validateForm: validateModalForm,
  setFormState: setModalFormState
} = useFormState({
  id: 0,
  group_id: undefined
})
setModalFormRules({
  group_id: { required: true, message: '请选择分销分组' }
})
const onEdit = item => {
  setModalFormState(cloneDeep(item))
  setModalVisible(true)
}
const handelEditSubmit = async () => {
  if (!(await validateModalForm())) return
  const { id, group_id } = modalFormState.value
  await distributorApi.update(id, { group_id })
  message.success('操作成功')
  setPage()
  setModalVisible(false)
}
</script>

<style lang="less">
.distributor-ratio {
  .ant-input-number-input {
    text-align: center;
  }
}
</style>
