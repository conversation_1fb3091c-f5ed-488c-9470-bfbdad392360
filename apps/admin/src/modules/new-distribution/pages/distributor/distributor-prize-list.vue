<template>
  <uc-layout-list title="分销奖金">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-input v-model:value="formState.name" placeholder="奖金名称" allow-clear />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">新增</a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="名称" data-index="name" :min-width="120" />
        <a-table-column title="奖金条件/奖金">
          <template #="{ record }">
            <div v-for="(item, index) in record.items" :key="index">
              <div v-if="item.group_id == 0">
                {{ $formatters.thousandSeparator(item.condition) }}/{{ $formatters.thousandSeparator(item.bonus) }}
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column key="created_at" :width="110" title="创建时间" data-index="created_at" />
        <a-table-column :width="100" title="操作">
          <template #="{ record }">
            <a-space>
              <a-button type="link" @click="onEdit(record)"> 编辑 </a-button>
              <a-popconfirm v-if="true" placement="left" title="您确定要删除该数据么？" @confirm="handleDelete(record)">
                <a-button type="link" class="danger"> 删除 </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal
    v-model:visible="modalVisible"
    :title="modalFormState.id ? '编辑奖金' : '新增奖金'"
    :width="680"
    :body-style="{ maxHeight: '70vh', overflowY: 'auto' }"
    @ok="handleModalSubmit"
  >
    <a-form :label-col="{ span: modalLabelCol }" :wrapper-col="{ span: modalWrapperCol }">
      <a-form-item label="奖金名称" class="required">
        <a-input v-model:value="modalFormState.name" placeholder="请输入奖金名称" allow-clear />
      </a-form-item>
      <a-form-item label="奖金规则" class="required">
        <uc-level-list v-model="modalFormState.items" :max="99" add-text="添加规则" @add="onAddRule">
          <template #item="{ item, index }">
            <a-select
              v-model:value="item.group_id"
              :disabled="!index"
              class="w-120"
              placeholder="分销分组"
              allow-clear
              :options="distributorGroupOptions"
            >
              <template #dropdownRender="{ menuNode: menu }">
                <v-nodes :vnodes="menu" />
              </template>
            </a-select>
            <a-input-number
              v-model:value="item.condition"
              v-bind="inputPriceAttrs"
              placeholder="奖金条件"
              allow-clear
            />
            <a-input-number v-model:value="item.bonus" v-bind="inputPriceAttrs" placeholder="奖金" allow-clear />
          </template>
        </uc-level-list>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useModalVisible } from '@/composables/useToggles'
import { distributorPrizeApi } from '../../api'
import { useDistributorGoroup } from '../../useDistributor'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  distributorPrizeApi.paginator({
    filters: useTransformQuery(formState, {
      name: 'like'
    }),
    relations: ['items'],
    offset,
    limit
  })
)
const [distributorGroupOptions] = useDistributorGoroup()

// const filterDistributorGroupOptions = computed(() => [...distributorGroupOptions.value, { label: '默认', value: 0 }])

const VNodes = (_, { attrs }) => {
  attrs.vnodes.props.flattenOptions = attrs.vnodes.props.flattenOptions.filter(
    option => !modalFormState.value.items.some(item => item.group_id === option.data.value)
  )
  return attrs.vnodes
}

const { formState, onRestFormState, resetFormState } = useFormState({
  name: undefined
})

onRestFormState(() => setPage())

const handleDelete = async (record) => {
  await distributorPrizeApi.delete(record.id)
  message.success('删除成功')
  setPage()
}

const onAdd = () => {
  resetModalFormState()
  setModalVisible(true)
}
const onEdit = record => {
  const form = cloneDeep(record)
  form.items.some(item => {
    if (item.group_id == 0) {
      item.disabled = true
      return true
    }
  })
  setModalFormState(form)
  setModalVisible(true)
}

const getInitRule = (option = {}) => ({
  group_id: undefined,
  condition: undefined,
  bonus: undefined,
  ...option
})

const {
  formState: modalFormState,
  resetFormState: resetModalFormState,
  validateForm: validateModalForm,
  setFormRules: setFormModalRules,
  setFormState: setModalFormState
} = useFormState({
  name: undefined,
  items: [getInitRule({ group_id: 0, disabled: true })]
})

setFormModalRules({
  name: { required: true, message: '请输入奖金名称' },
  items: {
    validator(_, value) {
      const validatorField = ['group_id', 'condition', 'bonus']
      if (value.some(item => validatorField.some(key => item[key] === undefined || item[key] === '')))
        return Promise.reject('请填写奖金规则')
      return Promise.resolve(true)
    }
  }
})

const { modalVisible, setModalVisible } = useModalVisible()

const onAddRule = () => {
  modalFormState.value.items.push(getInitRule())
}

const handleModalSubmit = async () => {
  if (!(await validateModalForm())) return
  const params = cloneDeep(modalFormState.value)
  await (params.id ? distributorPrizeApi.update(params.id, params) : distributorPrizeApi.create(params))
  message.success('操作成功')
  setPage()
  setModalVisible(false)
}
</script>
