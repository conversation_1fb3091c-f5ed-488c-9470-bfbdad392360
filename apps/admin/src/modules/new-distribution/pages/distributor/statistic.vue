<template>
  <uc-layout-list title="数据统计">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="filterState.conditionKey" :options="USER_OPTIONS" />
          <a-input v-model:value="filterState.conditionValue" :placeholder="placeholder(filterState.conditionKey)" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="filterState.date" :placeholder="['开始时间','结束时间']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="search">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="exportFile(exportStatisticPath, useQuery())">
        导出
      </a-button>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="onChange">
        <a-table-column title="分销商">
          <template #default="{record}">
            {{ record.distributor?.name }}({{ record.distributor?.id_card_number }})
          </template>
        </a-table-column>
        <a-table-column
          key="sum_share_count"
          title="分享次数"
          align="center"
          data-index="sum_share_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_share_count' && sorted.order"
        />
        <a-table-column
          key="sum_open_share_count"
          title="分享成功次"
          align="center"
          data-index="sum_open_share_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_open_share_count' && sorted.order"
        />
        <a-table-column
          key="sum_order_count"
          title="提单数"
          align="center"
          data-index="sum_order_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_order_count' && sorted.order"
        />
        <a-table-column
          key="sum_paid_order_count"
          title="付款单数"
          align="center"
          data-index="sum_paid_order_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_paid_order_count' && sorted.order"
        />
        <a-table-column
          key="sum_completed_order_count"
          title="完成订单数"
          align="center"
          data-index="sum_completed_order_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_completed_order_count' && sorted.order"
        />
        <a-table-column
          key="sum_after_sale_count"
          title="售后申请单"
          align="center"
          data-index="sum_after_sale_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_after_sale_count' && sorted.order"
        />
        <a-table-column
          key="sum_completed_after_sale_count"
          title="售后完成单"
          align="center"
          data-index="sum_completed_after_sale_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_completed_after_sale_count' && sorted.order"
        />
        <a-table-column
          key="sum_cancelled_after_sale_count"
          title="售后关闭单"
          align="center"
          data-index="sum_cancelled_after_sale_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_cancelled_after_sale_count' && sorted.order"
        />
        <a-table-column
          key="sum_after_sale_order_count"
          title="售后订单"
          align="center"
          data-index="sum_after_sale_order_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_after_sale_order_count' && sorted.order"
        />
        <a-table-column
          key="sum_completed_after_dale_order_count"
          title="售后完成订单"
          align="center"
          data-index="sum_completed_after_dale_order_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_completed_after_dale_order_count' && sorted.order"
        />
        <a-table-column
          key="sum_cancelled_after_sale_order_count"
          title="售后关闭订单"
          align="center"
          data-index="sum_cancelled_after_sale_order_count"
          :custom-render="({text}) => text || 0"
          :sorter="true"
          :sort-order="sorted.columnKey == 'sum_cancelled_after_sale_order_count' && sorted.order"
        />
        <a-table-column
          key="distributor.created_at"
          title="加入时间"
          :width="160"
          align="center"
          data-index="created_at"
          :custom-render="({text}) => text || ''"
        />
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>

import { useStatistic } from '../../useStatistic'

import { USER_OPTIONS } from '../../enum'

const { exportStatisticPath, useQuery, filterState, data, onChange, loading, placeholder, onReset, search, sorted } = useStatistic()

</script>

<style lang="less">
</style>