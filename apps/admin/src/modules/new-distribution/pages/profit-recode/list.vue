<template>
  <uc-layout-list>
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="filterState.conditionKey" :options="USER_OPTIONS" />
          <a-input v-model:value="filterState.conditionValue" :placeholder="placeholder(filterState.conditionKey)" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="filterState.status" :options="PROFIT_RECORD_STATUS" placeholder="状态" />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="filterState.created_at" :placeholder="['账单创建时间','账单创建时间']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="exportFile(exportDistributorPath, useQueryAndRelations())">
        导出
      </a-button>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="分销商">
          <template #default="{record}">
            <div v-if="record.distributor">
              {{ record?.distributor?.name }}({{ record?.distributor?.id_card_number }})
            </div>
            <div v-else>
              -
            </div>
          </template>
        </a-table-column>
        <a-table-column key="sn" align="center" title="分销编号" data-index="sn" />
        <a-table-column key="source_order-sn" align="center" title="订单编号" data-index="source_order.sn" />
        <a-table-column align="center" title="金额">
          <template #default="{record}">
            <text :class="record?.statusOption?.class">
              + {{ (record.total_profit - record.expired_profit).toFixed(2) }}
            </text>
          </template>
        </a-table-column>
        <a-table-column align="center" :width="100" title="状态">
          <template #default="{record}">
            <a-badge :text="record?.statusOption?.label" :status="record?.statusOption?.badge" />
          </template>
        </a-table-column>
        <a-table-column key="created_at" :width="200" align="center" title="账单创建时间" data-index="created_at" />
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import {useProfitRecord} from '../../useProfitRecord'

import { USER_OPTIONS, PROFIT_RECORD_STATUS } from '../../enum'

const {exportDistributorPath,useQueryAndRelations,data,setPage,loading,filterState,placeholder,onReset} = useProfitRecord()
</script>

<style>
</style>
