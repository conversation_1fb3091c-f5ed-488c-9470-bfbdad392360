<template>
  <uc-layout-list title="提现记录">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="filterState.conditionKey" :options="USER_OPTIONS" />
          <a-input v-model:value="filterState.conditionValue" :placeholder="placeholder(filterState.conditionKey)" />
        </a-input-group>
      </a-form-item>

      <a-form-item>
        <a-select v-model:value="filterState.status" allow-clear placeholder="审核状态" :options="WITHDRAWAL_STATUS" />
      </a-form-item>

      <a-form-item>
        <a-select v-model:value="filterState.transfer_status" allow-clear placeholder="提现状态" :options="WITHDRAWAL_TRANSFORM_STATUS" />
      </a-form-item>

      <a-form-item>
        <a-range-picker v-model:value="filterState.created_at" :placeholder="['申请日期', '申请日期']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
      </a-form-item>
      <a-form-item>
        <a-button @click="onReset">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="exportFile(exportWithdrawalPath, useQueryAndRelations())">
        导出
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        :scroll="{x: 1550}"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="申请人" ellipsis>
          <template #default="{record}">
            {{ record.user?.nickname }}({{ record.user?.phone_number }})
          </template>
        </a-table-column>
        <a-table-column
          key="name"
          title="姓名"
          :width="100"
          align="center"
          data-index="distributor.name"
          :custom-render="({text}) => text || ''"
        />
        <a-table-column title="申请提现金额" :width="120" align="center" data-index="amount" :custom-render="({text}) => `${text}元`" />
        <a-table-column title="提现成功金额" :width="120" align="center" data-index="success_amount" :custom-render="({text}) => `${text}元`" />
        <a-table-column title="申请时间" :width="160" align="center" data-index="created_at" />
        <a-table-column title="审核时间" :width="160" align="center" data-index="updated_at" :custom-render="({text, record}) => record.statusOption?.allowOperate ? '' : text" />
        <a-table-column title="审核状态" :width="150" align="center">
          <template #default="{record}">
            <a-badge :text="record.statusOption?.label" :status="record.statusOption?.badge" />
          </template>
        </a-table-column>
        <a-table-column title="提现状态" :width="150" align="center">
          <template #default="{record}">
            <a-badge :text="record.transformStatusOption?.label" :status="record.transformStatusOption?.badge" />
          </template>
        </a-table-column>
        <a-table-column title="提现失败原因" :width="360" ellipsis>
          <template #default="{record}">
            <a-tooltip v-if="record.fail_reasons.length > 0">
              <template v-if="record.fail_reason_tips.length > 0" #title>
                <div v-for="item,index in record.fail_reason_tips" :key="index">
                  {{ index + 1 }}.{{ item }}
                </div>
              </template>
              <div v-for="item,index in record.fail_reasons" :key="index">
                {{ item }}
              </div>
            </a-tooltip>
            <span v-else>-</span>
          </template>
        </a-table-column>
        <a-table-column title="操作" :width="200" align="center" fixed="right">
          <template #default="{record}">
            <a-space v-if="record.statusOption?.allowOperate">
              <a-popconfirm placement="left" title="确认同意当前分销商的提现申请吗？" ok-text="确认" cancel-text="取消" @confirm="handleAudit(record, WITHDRAWAL_AGREE)">
                <a-button type="link">
                  同意申请
                </a-button>
              </a-popconfirm>
              <a-popconfirm placement="left" title="确认拒绝当前分销商的提现申请吗？" ok-text="确认" cancel-text="取消" @confirm="handleAudit(record, WITHDRAWAL_REFUSE)">
                <a-button type="link" class="danger">
                  拒绝申请
                </a-button>
              </a-popconfirm>
            </a-space>
            
            <a-space v-if="record.transfer_status == WITHDRAWAL_TRANSFORM_FAIL">
              <a-popconfirm
                placement="left"
                title="确认再次提现吗？"
                ok-text="确认"
                cancel-text="取消"
                @confirm="handleWithdrawal(record)"
              >
                <a-button type="link">
                  再次提现
                </a-button>
              </a-popconfirm>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>

import { useWithdrawal } from '../../useWithdrawal'
import { USER_OPTIONS, WITHDRAWAL_AGREE, WITHDRAWAL_REFUSE, WITHDRAWAL_TRANSFORM_FAIL, WITHDRAWAL_STATUS, WITHDRAWAL_TRANSFORM_STATUS } from '../../enum'

const { exportWithdrawalPath, useQueryAndRelations, filterState, data, setPage, loading, onReset, handleAudit, placeholder, handleWithdrawal } = useWithdrawal()

</script>

<style>
</style>
