import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { profitRecordApi } from './api'
import { USER_OPTIONS, PROFIT_RECORD_STATUS } from './enum'
import formatters from '@/utils/formatters'
import { computed } from 'vue'

export const useProfitRecord = () => {
  const { formState: filterState, resetFormState: resetFilterState } = useFormState({ conditionKey: 'name' })

  const exportDistributorPath = `${profitRecordApi.resourcePath}/export`

  const useQueryAndRelations = () => {
    return {
      relations: ['distributor', 'sourceOrder'],
      ...useRelationsTransformQuery(filterState, {
        'distributor.name': 'like',
        'distributionUser.nickname': 'like',
        'distributionUser.phone_number': '=',
        status: '=',
        created_at: 'dateRange'
      }),
      sorts: ['int_status']
    }
  }

  const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    profitRecordApi
      .paginator({
        ...useQueryAndRelations(),
        offset,
        limit
      })
      .then(record => {
        return {
          ...record,
          items: record.items.map(item => {
            return {
              ...item,
              statusOption: PROFIT_RECORD_STATUS.find(status => status.value == item.status),
              total_amount: item.total_amount / 100,
              refund_amount: item.refund_amount / 100,
              total_profit: item.total_profit / 100,
              expired_profit: item.expired_profit / 100,
              distributor: item.distributor
                ? {
                    ...item.distributor,
                    id_card_number: formatters.encrypt(item.distributor.id_card_number)
                  }
                : {}
            }
          })
        }
      })
  )

  const placeholder = computed(() => {
    return item => {
      return USER_OPTIONS.find(option => option.value == item)?.label
    }
  })

  const onReset = () => {
    resetFilterState()
    setPage()
  }

  return {
    exportDistributorPath,
    useQueryAndRelations,
    onReset,
    filterState,
    data,
    setPage,
    loading,
    placeholder
  }
}
