import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { message } from 'ant-design-vue'
import { computed } from 'vue'
import { distributionSpecApi, specApi } from './api'
import {SPEC_PHOTO_URL_DELETE, SPEC_PHOTO_URL_SOURCE, PRIZE_FILED} from './enum'
import config from '@/config'
import { useStore } from '@/store/auth'
import { ref } from 'vue'
import { cloneDeep } from 'lodash'
import {goodsSpecsApi} from "@/modules/promotion/api"
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import { Modal } from 'ant-design-vue'

const { state: storeInfo } = useStore()

export const useSpecs = () => {
  const modalVisible = ref(false)
  const specPhotoModalVisible = ref(false)
  const specPhotoModalTitle = ref('')
  const localRecord = ref(undefined)
  const currentIndex = ref(-1)

  const { formState: modelFormState, resetFormState: resetModelFormState } = useFormState()

  const { formState: searchSkuState, resetFormState: resetSearchSkuState } = useFormState({
    sku: '',
    is_distribute_product: false
  })

  const { formState: filterState, resetFormState: resetFilterState } = useFormState({
    keyword: ''
  })

  const exportDistributionSpecPath = `${distributionSpecApi.resourcePath}/export`
  const downloadTemplatePath = `${distributionSpecApi.resourcePath}/download`
  const importDistributionSpecUrl = `${config.api.baseURL}${distributionSpecApi.resourcePath}/import?token=${storeInfo.token}`

  const useQueryAndRelations = () => {
    return {
      relations: ['product', 'spec'],
      ...useRelationsTransformQuery(filterState, {
        keyword: '='
      })
    }
  }

  const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
    distributionSpecApi
      .paginator({
        ...useQueryAndRelations(),
        sorts: ['-updated_at'],
        offset,
        limit
      })
      .then(record => {
        return {
          ...record,
          items: record.items.map(item => {
            const commissionRadix = (item.distribution_price - item.floor_price) / 100
            return {
              ...item,
              price: item.spec.price / 100,
              floor_price: item.floor_price / 100,
              commission_radix: commissionRadix >= 0 ? commissionRadix : 0,
              basic_price: item.basic_price / 100,
              distribution_price: item.distribution_price / 100,
            }
          })
        }
      })
  )

  const onReset = () => {
    resetFilterState()
    setPage()
  }

  const handleImport = e => {
    const { file } = e
    if (file && file.status == 'done') {
      console.log(file.response)
      const { success = 0, fail = 0 } = file.response?.data || {}
      message.success(`导入完成，成功：${success}个，失败：${fail}个！`)
      onReset()
    }
  }

  const { data: searchData, setPage: setSearchPage } = usePaginatorApiRequest(
    ({ offset, limit }) => {
      return specApi
        .paginator({
          ...useRelationsTransformQuery(searchSkuState, {
            sku: 'like',
            is_distribute_product: 'boolean'
          }),
          offset,
          limit
        })
        .then(data => {
          return {
            ...data,
            items: data.items.map(item => {
              return {
                ...item,
                basic_price: 0,
                distribution_price: 0,
                price: item.price / 100,
                floor_price: 0
              }
            })
          }
        })
    },
    { current: 1, pageSize: 10 },
    false
  )

  const handleSkuSearch = value => {
    searchSkuState.value.sku = value
    setSearchPage()
  }

  const getSpecName = (attr = []) => {
    let specName = ''
    let l = attr.length
    attr.forEach((item, index) => {
      index == l - 1 ? (specName = item.value) : (specName = item.value + '/')
    })
    return specName
  }
  const handleSkuChange = value => {
    const selectSpec = searchData.value?.items?.find(item => item.sku == value)
    if (selectSpec) {
      modelFormState.value = selectSpec
    }
  }

  const onShowModal = () => {
    resetModelFormState()
    modalVisible.value = true
    handleSkuSearch()
  }

  const handleCreate = async () => {
    if (!modelFormState.value.id || !modelFormState.value.sku) {
      message.error('请先输入sku选择商品')
      return false
    }

    await distributionSpecApi
      .create({
        sku: modelFormState.value.sku,
        distribution_price: modelFormState.value.distribution_price * 100,
        basic_price: modelFormState.value.basic_price * 100,
        floor_price: modelFormState.value.floor_price * 100
      })
      .then(() => {
        message.success('新增成功')
        modalVisible.value = false
        setPage()
      })
  }
  const currentSpec = ref({})

  const onShowSpecPhoto = (record)=>{
    localRecord.value = cloneDeep(record)
    specPhotoModalTitle.value = `编辑${record.product?.title}的属性`

    specPhotoModalVisible.value = true
  }

  const handleEditSpecPhoto =async ()=>{
    await goodsSpecsApi.update(localRecord.value.spec.id,localRecord.value.spec)

    message.success('操作成功')

    setPage()

    specPhotoModalVisible.value = false
  }

  const defaultSpecPhoto = computed(()=>{
    return localRecord.value?.spec?.spec_photo_urls.filter(item=>item.is_delete === SPEC_PHOTO_URL_DELETE.NOT_DELETE)[0]?.url
  })

  const onMouseOver = (index) =>{
    currentIndex.value = index
  }

  const onAddPhoto = (list)=>{
    localRecord.value.spec.spec_photo_urls.push(...list.map(item=>{
      return {
        url:item,
        is_delete:SPEC_PHOTO_URL_DELETE.NOT_DELETE,
        source:SPEC_PHOTO_URL_SOURCE.LOCAL,
      }
    }))
  }

  const onRemovePhoto = (index)=>{
    if (localRecord.value.spec.spec_photo_urls[index].source === SPEC_PHOTO_URL_SOURCE.LOCAL){
      localRecord.value.spec.spec_photo_urls.splice(index,1)
    }else{
      localRecord.value.spec.spec_photo_urls[index].is_delete = SPEC_PHOTO_URL_DELETE.IS_DELETE
    }
  }

  const formatterNumber = value => {
    if (typeof value === 'string') {
      return !isNaN(Number(value)) ? value.trim() : value.replace(/[^0-9]/gi, '')
    } else if (typeof value === 'number') {
      return !isNaN(value) ? value : 0.01
    } else {
      return 0
    }
  }

  const originPrice = ref(0);
  const onInputFocus = (price) => {
    originPrice.value = price
  }

  const onHandleRatio = async (distribution, filed, title) => {
    const setOriginPrice = () => {
      distribution[filed] = originPrice.value
    }

    const filedInfo = PRIZE_FILED.find(item => item.filed == filed)
    if (!filedInfo) {
      message.error('无效的操作')
      setOriginPrice()
      return
    }
    if (distribution.distribution_price < distribution.basic_price) {
      message.error('分销价不能小于拿货价,请重新输入')
      setOriginPrice()
      return
    }

    if(distribution[filed] == originPrice.value) {
      return
    }

    Modal.confirm({
      title: () => `确认修改${title}吗`,
      content: () => `${distribution.product.title ?? ''}`,
      icon: () => createVNode(ExclamationCircleOutlined),
      onOk() {
        return distributionSpecApi
        .updateByPath(filedInfo.path, {
          id: distribution.id,
          [filed]: distribution[filed] * 100
        })
        .then(() => {
          message.success(`${filedInfo.label}编辑成功`)
          closeModal()
        })
        .catch(() => refresh())
      },
      cancelText: () => '取消',
      onCancel() {
        // Modal.destroyAll() // 组件bug
        setOriginPrice()
        closeModal()
      },
    });

    const closeModal = () => {
      const modal = document.querySelectorAll('.ant-modal-root')
      for (let i = 0; i < modal.length; i++) {
        modal[i].parentNode.remove()
      }
    }

  }

  return {
    onInputFocus,
    onHandleRatio,
    formatterNumber,
    importDistributionSpecUrl,
    exportDistributionSpecPath,
    downloadTemplatePath,
    useQueryAndRelations,
    getSpecName,
    onReset,
    filterState,
    data,
    setPage,
    loading,
    handleImport,
    modalVisible,
    modelFormState,
    searchData,
    handleSkuSearch,
    handleSkuChange,
    onShowModal,
    handleCreate,
    handleDelete: async record => {
      await distributionSpecApi.delete(record.id)
      message.success('移除成功')
      refresh()
    },
    onShowSpecPhoto,
    specPhotoModalVisible,
    specPhotoModalTitle,
    handleEditSpecPhoto,
    localRecord,
    defaultSpecPhoto,
    onMouseOver,
    currentIndex,
    onAddPhoto,
    onRemovePhoto,
  }
}
