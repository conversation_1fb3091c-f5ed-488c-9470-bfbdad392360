import {useFormState} from "@/composables/useFormState";
import {usePaginatorApiRequest} from "@/composables/useApiRequest";
import {bannerApi} from "@/modules/new-distribution/api";
import {useTransformQuery} from "@/composables/useTransformQuery";

export const useBanner = ()=>{
  const { formState: filterStatus, resetFormState: filterResetFormStatus } = useFormState({
    name: '',
    status: undefined,
  })

  const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    bannerApi
      .paginator({
        filters:useTransformQuery(filterStatus, {
          name: 'like',
        }),
        offset,
        limit
      })
      .then(banners => {
        return banners
      })
  )

  const onReset = () => {
    filterResetFormStatus()
    setPage()
  }


  return {
    filterStatus,
    onReset,
    setPage,
    data,
    loading,
  }
}
