import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { applyApi, distributorApi, distributorGroupApi } from './api'
import { APPLY_AGREED, USER_OPTIONS, APPLY_REFUSED, APPLY_STATUS, DISTRIBUTOR_STATUS } from './enum'
import formatters from '@/utils/formatters'
import { useTransformOptions } from '@/composables/useTransformOptions'

export const useDistributor = ({ query = {} } = {}) => {
  const { group_id } = query
  const visible = ref(false)
  const { formState: filterStatus, resetFormState: filterResetFormStatus } = useFormState({
    conditionKey: 'name',
    group_id: group_id && +group_id
  })
  const { formState, resetFormState } = useFormState()

  const exportDistributorPath = `${distributorApi.resourcePath}/export`
  const useQueryAndRelations = () => {
    return {
      relations: ['user', 'group'],
      ...useRelationsTransformQuery(filterStatus, {
        name: 'like',
        'user.nickname': 'like',
        'user.phone_number': '=',
        created_at: 'dateRange',
        group_id: '='
      })
    }
  }

  const { data, setPage, refresh, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    distributorApi
      .paginator({
        ...useQueryAndRelations(),
        offset,
        limit,
        relation_filters: { apply: { status: 'agreed' } }
      })
      .then(record => {
        return {
          ...record,
          items: record.items.map(item => {
            const allowWithdraw = (item.balance - item.frozen_amount - item.invalid_profit) / 100
            return {
              ...item,
              id_card_number: formatters.encrypt(item.id_card_number),
              total_amount: item.total_amount / 100,
              tax_pre_total_profit: item.tax_pre_total_profit / 100 || 0,
              total_profit: item.total_profit / 100,
              total_tax: item.total_tax / 100 || 0,
              allowWithdraw: allowWithdraw >= 0 ? allowWithdraw : 0,
              frozen_amount: item.frozen_amount / 100,
              already_withdrawal_amount: item.already_withdrawal_amount / 100,
              statusOption: formatDistributorStatus(item)
            }
          })
        }
      })
  )

  const placeholder = computed(() => {
    return item => {
      return USER_OPTIONS.find(option => option.value == item)?.label
    }
  })

  const onReset = () => {
    filterResetFormStatus()
    setPage()
  }

  const onHandleOperate = async () => {
    const { ratio, id, status, reason } = formState.value
    if (status == APPLY_AGREED) {
      if (!ratio || ratio > 100 || ratio <= 0) {
        return message.error('请确认分销基数')
      }
    }

    if (status == APPLY_REFUSED) {
      if (!reason) {
        return message.error('请填写拒绝原因')
      }
    }
    visible.value = false
    await applyApi.updateByPath('audit', formState.value)
    visible.value = false
    message.success('审核成功')
    setPage()
  }
  const onHandleVisible = (apply, status) => {
    formState.value = {
      name: apply.name,
      id: apply.id,
      status,
      ratio: 100
    }
    visible.value = true
  }

  const onHandleRatio = async distributor => {
    distributor.basic_price = Math.floor(distributor.basic_price)
    await distributorApi
      .updateByPath('basic-price', {
        id: distributor.id,
        basic_price: distributor.basic_price
      })
      .then(() => {
        message.success('分佣比例编辑成功')
      })
      .catch(() => refresh())
  }

  const onHandleClear = async distributor => {
    await distributorApi.updateByPath('clear', { id: distributor.id })
    message.success('清退成功')
    refresh()
  }

  const onHandleShutDown = async distributor => {
    await distributorApi.updateByPath('shut-down', {
      id: distributor.id,
      is_shut_down: !distributor.is_shut_down
    })
    message.success(`${distributor.is_shut_down ? '解封' : '封号'}成功`)
    refresh()
  }

  return {
    exportDistributorPath,
    useQueryAndRelations,
    placeholder,
    onHandleVisible,
    onHandleOperate,
    visible,
    onReset,
    formState,
    filterStatus,
    data,
    setPage,
    loading,
    onHandleRatio,
    onHandleClear,
    onHandleShutDown
  }
}

export const formatDistributorStatus = item => {
  return DISTRIBUTOR_STATUS.find(option => {
    const conditions = option.conditions
    const keys = Object.keys(conditions)
    let flag = true
    for (let key of keys) {
      if (!flag) break
      if (item[key] != conditions[key]) flag = false
    }
    return flag
  })
}

/**
 * 分销分组
 */
export const useDistributorGoroup = ({ isLoaded = true } = {}) => {
  const data = ref([])
  const loadData = async () => {
    const res = await distributorGroupApi.list()
    const options = useTransformOptions(res, 'name', 'id', Object.keys(res))
    data.value = [{ label: '默认', value: 0 }, ...options]
  }
  isLoaded && loadData()

  return [data, loadData]
}
