import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { message } from 'ant-design-vue'
import config from '@/config'
import { computed, ref } from 'vue'
import { applyApi, getDaisy } from './api'
import { APPLY_AGREED, USER_OPTIONS, APPLY_REFUSED, APPLY_STATUS } from './enum'
import formatters from '@/utils/formatters'
import useClipboard from 'vue-clipboard3'

export const useApply = () => {
  const visible = ref(false)
  const { formState: filterStatus, resetFormState: filterResetFormStatus } = useFormState({
    conditionKey: 'name',
    personal_auth_status: 'success'
  })
  const { formState, resetFormState } = useFormState()
  const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    applyApi
      .paginator({
        relations: ['user'],
        ...useRelationsTransformQuery(filterStatus, {
          name: 'like',
          'user.nickname': 'like',
          'user.phone_number': '=',
          created_at: 'dateRange',
          personal_auth_status: '='
        }),
        offset,
        limit
      })
      .then(applies => {
        return {
          ...applies,
          items: applies.items.map(item => {
            return {
              ...item,
              statusOption: APPLY_STATUS.find(status => status.value == item.status),
              id_card_number: formatters.encrypt(item.id_card_number),
              user: {
                ...item.user,
                phone_number: formatters.encrypt(item.user?.phone_number || '')
              }
            }
          })
        }
      })
  )

  const placeholder = computed(() => {
    return item => {
      return USER_OPTIONS.find(option => option.value == item)?.label
    }
  })

  const onReset = () => {
    filterResetFormStatus()
    setPage()
  }

  const onHandleOperate = async () => {
    const { ratio, id, status, reason } = formState.value
    if (status == APPLY_AGREED) {
      if (!ratio || ratio > 100 || ratio <= 0) {
        return message.error('请确认分销基数')
      }
    }

    if (status == APPLY_REFUSED) {
      if (!reason) {
        return message.error('请填写拒绝原因')
      }
    }
    await applyApi.updateByPath('audit', formState.value)
    visible.value = false
    message.success('审核成功')
    setPage()
  }
  const onHandleVisible = (apply, status) => {
    formState.value = {
      name: apply.name,
      id: apply.id,
      status,
      ratio: 100
    }
    visible.value = true
  }
  const pageUrl = 'pages/apply/index'
  const copyLink = async () => {
    const { toClipboard } = useClipboard()
    try {
      await toClipboard(`/${pageUrl}`)
      message.success('复制链接成功')
    } catch (e) {
      console.error(e)
    }
  }
  const downloadQRcode = async () => {
    // const url = `${config.api.baseURL}/image/get-daisy?scene=1&page=${pageUrl}&check_path=0`
    // window.open(url)
    const data = await getDaisy.loadImg({
      scene: 1,
      page: pageUrl,
      check_path: 0
    })
    const blob = new Blob([data])
    const fileName = '分销商邀请码.jpg'
    const dlink = document.createElement('a')
    dlink.download = fileName
    dlink.style.display = 'none'
    dlink.href = URL.createObjectURL(blob)
    document.body.appendChild(dlink)
    dlink.click()
    URL.revokeObjectURL(dlink.href) // 释放URL 对象
    document.body.removeChild(dlink)
  }
  return {
    copyLink,
    downloadQRcode,
    placeholder,
    onHandleVisible,
    onHandleOperate,
    visible,
    onReset,
    formState,
    filterStatus,
    data,
    setPage,
    loading
  }
}
