import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useRelationsTransformQuery } from '@/composables/useRelationsTransformQuery'
import { message } from 'ant-design-vue'
import { computed, ref } from 'vue'
import { useRoute } from 'vue-router'
import { distributionSpecApi, distributorSpecApi, distributorApi } from './api'
import { DISTRIBUTOR_STATUS, USER_OPTIONS } from './enum'
import formatters from '@/utils/formatters'
import { useLoadingMessage } from '@/composables/useLoadingMessage'

export const useDistributor = () => {
  const { query } = useRoute()

  if (!query || !query.spec_id || !query.sku) useRouter().back()

  const params = {
    specId: query.spec_id,
    title: query.title,
    attrs:query.attrs,
    sku: query.sku
  }


  const visible = ref(false)
  const { formState: filterState, resetFormState: resetFilterState } = useFormState({
    conditionKey: 'name',
    sku: params.sku
  })
  const { formState, resetFormState } = useFormState()

  const exportSpecDistributorPath = `${distributorSpecApi.resourcePath}/spec-distributors-export`
  const useQueryAndRelations = () => {
    return {
      ...useRelationsTransformQuery(filterState, {
        sku: '=',
        'distributor.name': 'like',
        'user.nickname': 'like',
        'user.phone_number': '=',
        'distributor.created_at': 'dateRange'
      }),
      relations: ['distributor','spec']
    }
  }

  const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
    distributorSpecApi
      .paginator({
        ...useQueryAndRelations(),
        offset,
        limit
      })
      .then(applies => {
        return {
          ...applies,
          items: applies.items.map(item => {
            const statusOption = DISTRIBUTOR_STATUS.find(option => {
              const conditions = option.conditions
              const keys = Object.keys(conditions)
              let flag = true
              for (let key of keys) {
                if (!flag) break
                if (item[key] != conditions[key]) flag = false
              }
              return flag
            })
            return {
              ...item,
              distributor: item.distributor
                ? {
                    ...item.distributor,
                    id_card_number: formatters.encrypt(item.distributor ? item.distributor.id_card_number : 0),
                    total_amount: item.distributor ? item.distributor.total_amount / 100 : 0,
                    total_profit: item.distributor ? item.distributor.total_profit / 100 : 0,
                    balance: item.distributor ? item.distributor.balance / 100 : 0,
                    frozen_amount: item.distributor ? item.distributor.frozen_amount / 100 : 0,
                    already_withdrawal_amount: item.distributor ? item.distributor.already_withdrawal_amount / 100 : 0,
                    basic_price: item.basic_price / 100,
                    distribution_price: item.distribution_price / 100,
                    price:item.spec.price /100,
                    statusOption,
                    allowWithdraw: item.distributor
                      ? (item.distributor.balance - item.distributor.frozen_amount - item.distributor.invalid_profit) /
                        100
                      : 0
                  }
                : null
            }
          })
        }
      })
  )

  const onReset = () => {
    resetFilterState()
    setPage()
  }

  const onDeleteSpec = async record => {
    await distributorSpecApi.delete(record.id)
    setPage()
    message.success('取消成功')
  }

  const placeholder = computed(() => {
    return item => {
      return USER_OPTIONS.find(option => option.value == item)?.label
    }
  })

  const modalVisible = ref(false)
  const selectDistributor = ref([])
  const selectDistributorIds = ref([])

  const cache_distributor = ref([])
  const diff = (_cache, keys) => {
    let skuArr = []
    _cache.forEach(el => {
      if (!keys.find(s => el == s)) {
        skuArr.push(el)
      }
    })
    return skuArr
  }
  const onSelectDistributorChange = (keys = [], rows = []) => {
    //新增
    if (keys.length > cache_distributor.value.length) {
      const index = cache_distributor.value.length
      const newDis = keys.slice(index)
      newDis.forEach(el => {
        const checkRow = rows.find(r => r.id == el)
        const res = { id: el, name: checkRow.name, basic_price: checkRow.basic_price,distribution_price:checkRow.distribution_price }
        selectDistributor.value.push(res)
      })
    } else {
      //找到取消的sku
      //对比差异
      const distributors = diff(cache_distributor.value, keys)
      //删除
      distributors.forEach(id => {
        const reduceDisIndex = selectDistributor.value.findIndex(el => el.id === id)
        selectDistributor.value.splice(reduceDisIndex, 1)
      })
    }
    cache_distributor.value = keys
    selectDistributorIds.value = keys
  }

  const changeBasic = (record,field) => {
    const index = selectDistributor.value.findIndex(el => el.id == record.id)
    selectDistributor.value[index][field] = record[field]
  }
  const isCheck = record => {
    return !Boolean(selectDistributor.value?.find(d => d.id == record.id))
  }
  const { formState: distributorFilterState, resetFormState: resetDistributorState } = useFormState({
    conditionKey: 'name',
    not_sku: params.sku,
    is_clear: false,
    is_shut_down: false
  })

  const {
    data: distributorData,
    setPage: setDistributorPage,
    loading: distributorLoading
  } = usePaginatorApiRequest(
    ({ offset, limit }) =>
      distributorApi
        .paginator({
          ...useRelationsTransformQuery(distributorFilterState, {
            not_sku: '=',
            is_clear: 'bool',
            is_shut_down: 'bool',
            name: 'like',
            'user.nickname': 'like',
            'user.phone_number': '=',
            created_at: 'dataRange'
          }),
          offset,
          limit
        })
        .then(data => {
          selectDistributor.value.length &&
            selectDistributor.value.forEach(e => {
              data.items.map(l => {
                if (e.id == l.id) {
                  l.basic_price = e.basic_price
                  l.distribution_price = e.distribution_price
                }
                return l
              })
              console.log('data.items :>> ', data.items)
            })
          return {
            ...data,
            items: data.items.map(item => {
              return {
                ...item,
                id_card_number: formatters.encrypt(item.id_card_number)
              }
            })
          }
        }),
    { current: 1, pageSize: 10 },
    false
  )

  const onDistributorReset = () => {
    resetDistributorState(), onSelectDistributorChange(), setDistributorPage()
  }

  const onShowModal = () => {
    onDistributorReset(), (modalVisible.value = true)
  }

  const onHandleBatchCreate = async () => {
    if (selectDistributor.value.length <= 0) {
      message.info('请勾选分销商')
      return
    }
    let specs = []
    try {
      specs = selectDistributor.value.map(item => {
        if (!item.distribution_price){
          throw new Error(`请输入分销商${item.name}的分销价！`)
        }
        if (!item.basic_price) {
        throw new Error(`请输入分销商${item.name}的拿货价！`)
        }

        return {
          distributor_id: item.id,
          sku: params.sku,
          basic_price: item.basic_price * 100,
          distribution_price: item.distribution_price * 100
        }
      })
    }catch (e){
      return message.error(e.message)
    }

    await useLoadingMessage(distributorSpecApi.createByPath('batch', { specs }))
    message.success('添加成功')
    modalVisible.value = false
    onReset()
  }

  const formatterNumber = value => {
    if (typeof value === 'string') {
      return !isNaN(Number(value)) ? value.trim() : value.replace(/[^0-9]/gi, '')
    } else if (typeof value === 'number') {
      return !isNaN(value) ? value : 0.01
    } else {
      return 0
    }
  }
  return {
    formatterNumber,
    changeBasic,
    exportSpecDistributorPath,
    useQueryAndRelations,
    params,
    onDeleteSpec,
    visible,
    onReset,
    formState,
    filterState,
    data,
    setPage,
    loading,
    placeholder,
    modalVisible,
    isCheck,
    selectDistributor,
    selectDistributorIds,
    onSelectDistributorChange,
    distributorFilterState,
    resetDistributorState,
    distributorData,
    setDistributorPage,
    distributorLoading,
    onDistributorReset,
    onShowModal,
    onHandleBatchCreate
  }
}
