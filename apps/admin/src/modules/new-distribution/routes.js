export default {
  path: 'distributor',
  meta: {
    title: '分销管理',
    antIcon: 'ApartmentOutlined'
  },
  children: [
    // 分销商品
    {
      path: 'distribution-spec',
      name: 'distribution-spec',
      meta: {
        title: '分销商品',
        keepAlive: true
      },
      component: () => import('./pages/goods/distribution-specs')
    },
    {
      path: 'distributor-group',
      name: 'distributor-group-list',
      meta: {
        title: '分销分组',
        keepAlive: true
      },
      component: () => import('./pages/distributor/distributor-group-list.vue')
    },
    {
      path: 'distributor-prize',
      name: 'distributor-prize-list',
      meta: {
        title: '分销奖金',
        keepAlive: true
      },
      component: () => import('./pages/distributor/distributor-prize-list.vue')
    },
    {
      path: 'apply',
      name: 'distribution-apply',
      meta: {
        title: '分销商申请',
        keepAlive: true
      },
      component: () => import('./pages/distributor/distributor-apply')
    },
    {
      path: 'distributor',
      name: 'distributor',
      meta: {
        title: '分销商列表',
        keepAlive: true
      },
      component: () => import('./pages/distributor/distributor-list')
    },
    {
      path: 'spec-distributor',
      name: 'spec-distributor',
      hidden: true,
      meta: {
        title: '销售授权'
      },
      component: () => import('./pages/goods/spec-distributor')
    },
    {
      path: 'spec',
      name: 'distributor-spec',
      hidden: true,
      meta: {
        title: '授权商品列表',
        keepAlive: true
      },
      component: () => import('./pages/distributor/distributor-specs')
    },


    // 分销账单
    {
      path: 'profit',
      name: 'distribution-profit',
      meta: {
        title: '分销账单',
        keepAlive: true
      },
      component: () => import('./pages/profit-recode/list')
    },

    // 提现
    {
      path: 'record',
      name: 'withdrawal',
      meta: {
        title: '提现记录',
        keepAlive: true
      },
      component: () => import('./pages/withdrawal/record.vue')
    },
    {
      path: 'statistic',
      name: 'distributor-statistic',
      meta: {
        title: '分销商统计',
        keepAlive: true
      },
      component: () => import('./pages/distributor/statistic')
    },
    {
      path: 'invite-bind',
      name: 'invite-bind',
      meta: {
        title: '邀请绑定',
        keepAlive: true
      },
      component: () => import('./pages/distributor/invite-bind')
    },
  ]
}
