import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useBatchTransformMedia } from '@/composables/useTransformFormat'
import { useAward } from './useAward'
import { giftType, awardBizType } from './enums'

/**
 * 更新奖项数据
 */
export const usePrizeAwardUpdate = () => {
  /**
   * 更改礼品类型
   */
  const onChangeGiftType = record => {
    record.option_id = undefined
  }

  /**
   * 积分默认值
   */
  const onPrizeDefault = (option = {}) =>
    Object.assign(
      {
        type: undefined,
        quantity: undefined,
        stock: undefined,
        options: [],
        disabled: false
      },
      option
    )

  /**
   * 处理奖项
   */
  const handleAward = (item, filters = {}) =>
    useAward(
      data => {
        item.options = useTransformOptions(data.items, 'title', 'id', ['type', 'photo_url'])
      },
      20,
      filters
    )

  /**
   * 更新礼品下拉列表
   */
  const handleShowGifts = record => {
    handleAward(record, useTransformQuery({ type: record.type }))
  }

  /**
   * 搜索
   */
  const handleSearchGifts = (title, record) => {
    handleAward(
      record,
      useTransformQuery(
        {
          type: record.type,
          title
        },
        {
          title: 'like'
        }
      )
    )
  }

  return { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts }
}

// 转换积分
export const useTransformPrize = () => {
  const defaultOptionId = { option_id: 1 }
  const defaultQuantity = { quantity: 1 }

  const transformPrizeRequest = prize => {
    const giftTypeItem = giftType.filterValue(prize.type)
    switch (giftTypeItem.biz_type) {
      case awardBizType.credit:
        // 发放积分
        Object.assign(prize, defaultOptionId)
        break
      case awardBizType.none:
        Object.assign(prize, defaultOptionId, defaultQuantity)
        break
      default:
        Object.assign(prize, defaultQuantity)
        break
    }

    return prize
  }

  // 转化积分
  const transformPrizesRequest = params => {
    params.award_settings.forEach(prize => {
      transformPrizeRequest(prize)
    })
  }

  return { transformPrizesRequest, transformPrizeRequest }
}
