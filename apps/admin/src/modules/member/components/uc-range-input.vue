<template>
  <a-input-group compact>
    <a-input v-model:value="rangeArr[0]" class="range-input__left" :placeholder="placeholder[0]" @change="change()" />
    <a-input class="range-input__center" placeholder="~" disabled />
    <a-input v-model:value="rangeArr[1]" class="range-input__right" :placeholder="placeholder[1]" @change="change()" />
  </a-input-group>
</template>

<script setup>
const emit = defineEmits(['update'])

const props = defineProps({
  minValue: {
    type: String,
    default: ''
  },
  maxValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: Object,
    default: function () {
      return []
    }
  }
})

const rangeArr = computed(() => [props.minValue, props.maxValue])

const change = () => emit('update', rangeArr.value)
</script>
<style lang="less" scoped>
.range-input {
  &__left {
    width: 100px !important;
    text-align: center !important;
  }
  &__center {
    width: 30px !important;
    border-left: 0;
    pointer-events: none;
    background-color: #fff !important;
  }
  &__right {
    width: 100px !important;
    text-align: center !important;
    border-left: 0;
  }
}
</style>
