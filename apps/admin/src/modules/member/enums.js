/**
 * 任务类型
 */
export const taskTypeList = Object.freeze({
  /**
   * 新人任务
   */
  once: 'once',
  /**
   * 每日任务
   */
  day: 'day',
  /**
   * 每月任务
   */
  month: 'month',

  options() {
    return [
      {
        value: this.once,
        label: '新人任务'
      },
      {
        value: this.day,
        label: '每日任务'
      },
      {
        value: this.month,
        label: '每月任务'
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 单次行为
 */
export const aSingleBehavior = Object.freeze({
  /**
   * 完善信息
   */
  info: 'info',
  /**
   * 关注公众号
   */
  follow: 'follow',
  /**
   * 生日福利
   */
  birthday: 'birthday',

  options() {
    return [
      {
        value: this.info,
        label: '完善信息'
      },
      {
        value: this.follow,
        label: '关注公众号'
      },
      {
        value: this.birthday,
        label: '生日福利'
      }
    ]
  },

  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 活动行为
 */
export const activityBehavior = Object.freeze({
  /**
   * 签到有礼
   */
  signActivity: 'sign_activity',
  /**
   * 邀请有礼
   */
  inviteActivity: 'invite_activity',
  /**
   * 入会有礼
   */
  registerActivity: 'register_activity',
  /**
   * 拼图有礼
   */
  jigsawActivity: 'jigsaw_activity',
  /**
   * 有奖调查
   */
  awardQuestionnaire: 'award_questionnaire',
  /**
   * 幸运大转盘
   */
  turntableDrawActivity: 'turntable_draw_activity',
  /**
   * 惊爆九宫格
   */
  squareDrawActivity: 'square_draw_activity',

  options() {
    return [
      {
        value: this.signActivity,
        label: '签到有礼'
      },
      {
        value: this.inviteActivity,
        label: '邀请有礼'
      },
      {
        value: this.registerActivity,
        label: '入会有礼'
      },
      {
        value: this.jigsawActivity,
        label: '拼图有礼'
      },
      {
        value: this.awardQuestionnaire,
        label: '有奖调查'
      },
      {
        value: this.turntableDrawActivity,
        label: '幸运大转盘'
      },
      {
        value: this.squareDrawActivity,
        label: '惊爆九宫格'
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 活跃行为
 */
export const activeBehavior = Object.freeze({
  /**
   * 浏览动态
   */
  viewFind: 'view_find',
  /**
   * 发布动态
   */
  releaseFind: 'release_find',
  /**
   * 评论动态
   */
  commentFind: 'comment_find',
  /**
   * 点赞动态
   */
  likeFind: 'like_find',
  /**
   * 分享动态
   */
  shareFind: 'share_find',
  /**
   * 精选动态
   */
  topFind: 'top_find',
  /**
   * 浏览话题
   */
  viewConversations: 'view_conversations',
  /**
   * 话题动态
   */
  conversationsInteraction: 'conversations_interaction',
  /**
   * 分享话题
   */
  shareConversations: 'share_conversations',
  /**
   * 注册产品
   */
  registerProduct: 'register_product',

  /**
   * 浏览文章
   */
  viewArticles: 'view_articles',

  /**
   * 评论文章
   */
  commentArticles: 'comment_articles',

  /**
   * 点赞文章
   */
  likeArticles: 'like_articles',

  /**
   * 分享文章
   */
  shareArticles: 'share_articles',

  options() {
    return [
      {
        value: this.viewFind,
        label: '浏览动态'
      },
      {
        value: this.releaseFind,
        label: '发布动态'
      },
      {
        value: this.commentFind,
        label: '评论动态'
      },
      {
        value: this.likeFind,
        label: '点赞动态'
      },
      {
        value: this.shareFind,
        label: '分享动态'
      },
      {
        value: this.topFind,
        label: '精华动态'
      },
      {
        value: this.viewConversations,
        label: '浏览话题'
      },
      {
        value: this.conversationsInteraction,
        label: '话题互动'
      },
      {
        value: this.shareConversations,
        label: '分享话题'
      },
      {
        value: this.registerProduct,
        label: '注册产品'
      },
      {
        value: this.viewArticles,
        label: '浏览文章'
      },
      {
        value: this.commentArticles,
        label: '评论文章'
      },
      {
        value: this.likeArticles,
        label: '点赞文章'
      },
      {
        value: this.shareArticles,
        label: '分享文章'
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value)
  }
})

/**
 * 搜索条件
 */
export const searchCondition = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 手机号码
   */
  phone_number: 'phone_number',
  open_id: 'open_id',
  union_id: 'union_id',

  options() {
    return [
      { label: '手机号码', value: this.phone_number },
      { label: '用户昵称', value: this.nickname },
      { label: 'openId', value: this.open_id },
      { label: 'unionId', value: this.union_id }
    ]
  }
})

/**
 * 任务类型
 */
export const taskTypes = Object.freeze({
  /**
   * 基础任务
   */
  basic: 'basic',
  /**
   * 消费任务
   */
  consume: 'consume',
  /**
   * 活跃任务
   */
  active: 'active',

  option() {
    return [
      {
        value: this.basic,
        label: '基础任务'
      },
      {
        value: this.consume,
        label: '消费任务'
      },
      {
        value: this.active,
        label: '活跃任务'
      }
    ]
  },
  disabled(value) {
    return this.option().map(item => ({ ...item, disabled: item.value === value }))
  },
  filter(value) {
    return this.option().find(item => item.value == value)
  },
  show(showValue = '', value = []) {
    return value.includes(showValue)
  }
})

/**
 * 上线类型
 */
export const limitTypes = Object.freeze({
  /**
   * 日限制
   */
  day: 'day',
  /**
   * 月限制
   */
  month: 'month',
  /**
   * 人限制
   */
  user: 'user',

  option() {
    return [
      {
        label: '日上限',
        value: this.day
      },
      {
        label: '月上限',
        value: this.month
      },
      {
        label: '人上限',
        value: this.user
      }
    ]
  }
})

/**
 * 任务列表[积分任务页-废弃]
 */
export const taskList = Object.freeze({
  /**
   * 会员注册
   */
  registerUser: 10001,
  /**
   * 注销账号
   */
  cancellationAccount: 10002,
  /**
   * 完善信息
   */
  completeInfo: 10003,
  /**
   * 关注公众号
   */
  followWechatAccounts: 10004,
  /**
   * 取消关注公众号
   */
  cancelFollowWechatAccounts: 10005,
  /**
   * 加购商品
   */
  addToCart: 10006,
  /**
   * 移出购物车
   */
  removeToCart: 10007,
  /**
   * 购物车结算
   */
  cartSettlement: 10008,
  /**
   * 立即购买
   */
  quickBuy: 10009,
  /**
   * 提交订单
   */
  submitOrder: 10010,
  /**
   * 支付订单
   */
  payOrder: 10011,
  /**
   * 确认收货
   */
  confirmShipped: 10012,
  /**
   * 申请退款
   */
  applyRefund: 10013,
  /**
   * 申请退货
   */
  applyReturn: 10014,
  /**
   * 搜索商品
   */
  searchProduct: 10015,
  /**
   * 页面浏览
   */
  viewPage: 10016,
  /**
   * 页面分享
   */
  sharePage: 10017,
  /**
   * 邀请好友
   */
  inviteActivity: 10018,
  /**
   * 签到打卡
   */
  signActivity: 10019,
  /**
   * 浏览商品
   */
  viewProduct: 10020,
  /**
   * 分享商品
   */
  shareProduct: 10021,
  /**
   * 参与抽奖
   */
  luckDrawActivity: 10022,
  /**
   * 浏览活动
   */
  viewActivity: 10023,
  /**
   * 分享活动
   */
  shareActivity: 10024,
  /**
   * 点击首屏弹窗
   */
  popup: 10025,
  /**
   * 领取优惠券
   */
  receiveCoupon: 10026,

  option() {
    return [
      { label: '会员注册', value: this.registerUser },
      { label: '注销账号', value: this.cancellationAccount },
      { label: '完善信息', value: this.completeInfo },
      { label: '关注公众号', value: this.followWechatAccounts },
      { label: '取消关注公众号', value: this.cancelFollowWechatAccounts },
      { label: '加购商品', value: this.addToCart },
      { label: '移出购物车', value: this.removeToCart },
      { label: '购物车结算', value: this.cartSettlement },
      { label: '立即购买', value: this.quickBuy },
      { label: '提交订单', value: this.submitOrder },
      { label: '支付订单', value: this.payOrder },
      { label: '确认收货', value: this.confirmShipped },
      { label: '申请退款', value: this.applyRefund },
      { label: '申请退货', value: this.applyReturn },
      { label: '搜索商品', value: this.searchProduct },
      { label: '页面浏览', value: this.viewPage },
      { label: '页面分享', value: this.sharePage },
      { label: '邀请好友', value: this.inviteActivity },
      { label: '签到打卡', value: this.signActivity },
      { label: '浏览商品', value: this.viewProduct },
      { label: '分享商品', value: this.shareProduct },
      { label: '参与抽奖', value: this.luckDrawActivity },
      { label: '浏览活动', value: this.viewActivity },
      { label: '分享活动', value: this.shareActivity },
      { label: '点击首屏弹窗', value: this.popup },
      { label: '领取优惠券', value: this.receive_coupon }
    ]
  },
  filter(value) {
    return this.option().find(item => item.value === value)
  }
})

/**
 * 规则类型
 */
export const ruleTypes = Object.freeze({
  /**
   * 且
   */
  and: 'and',
  /**
   * 或
   */
  or: 'or',

  option() {
    return [
      { label: '且', value: this.and, btnType: 'primary' },
      { label: '或', value: this.or, btnType: 'success' }
    ]
  },
  filter(value) {
    return this.option().find(item => item.value === value)
  },
  reverse(value) {
    return this.or === value ? this.and : this.or
  }
})

/**
 * 礼品类型
 */
export const giftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',
  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 积分
   */
  credit: 'credit',

  options() {
    return [
      { label: '实物礼品', value: this.entity, biz_type: awardBizType.gift },
      { label: '虚拟卡券', value: this.invented, biz_type: awardBizType.gift },
      { label: '购物卡券', value: this.coupon, biz_type: awardBizType.coupon },
      { label: '会员积分', value: this.credit, biz_type: awardBizType.credit }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 奖项业务类型
 */
export const awardBizType = Object.freeze({
  /**
   * 积分
   */
  credit: 'credit',
  /**
   * 礼品[实物|虚拟]
   */
  gift: 'gift',
  /**
   * 购物卡券
   */
  coupon: 'coupon',
  /**
   * 谢谢参与
   */
  none: 'none'
})

/**
 * 等级类型
 */
export const levelBizType = Object.freeze({
  /**
   * 用户等级
   */
  user_level: 'user_level',
  /**
   * 用户生日
   */
  user_birthday: 'user_birthday',

  groupType(list, value) {
    return list?.filter(item => item.type === value) ?? []
  }
})

/* 订单来源 */
export const orderSource = Object.freeze({
  /**
   * 积分商城
   */
  credit: 'credit_good',
  /**
   * 试用活动
   */
  trial: 'trial',
  /**
   * 抽奖活动
   */
  draw: 'draw_activity',
  /**
   * 签到活动
   */
  sign: 'sign_activity',
  /**
   * 邀请活动
   */
  invite: 'invite_activity',
  /**
   * 注册活动
   */
  register: 'register_activity',

  options() {
    return [
      { label: '积分商城', value: this.credit },
      { label: '抽奖活动', value: this.draw },
      { label: '签到活动', value: this.sign },
      { label: '邀请活动', value: this.invite },
      { label: '注册活动', value: this.register }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 支付状态 */
export const orderPayStatus = Object.freeze({
  /**
   * 未支付
   */
  unpaid: 'unpaid',

  /**
   * 已支付
   */
  paid: 'paid',

  /**
   * 已退款
   */
  refund: 'refund',

  options() {
    return [
      { label: '未支付', value: 'unpaid' },
      { label: '已支付', value: 'paid' },
      { label: '已退款', value: 'refund' }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 积分类型 */
export const integralActionType = Object.freeze({
  /**
   * 收入明细
   */
  incr: 'incr',

  /**
   * 支出明细
   */
  decr: 'decr',

  options() {
    return [
      { label: '收入明细', value: 'incr' },
      { label: '支出明细', value: 'decr' }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/**
 * 创建方式
 */
export const createMethod = Object.freeze({
  /**
   * 自定义人群
   */
  custom: 'custom',
  /**
   * 手动上传人群
   */
  manual: 'manual',

  options() {
    return [
      {
        label: '自定义人群',
        value: this.custom
      },
      {
        label: '手动上传人群',
        value: this.manual
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value) ?? {}
  }
})

/**
 * 推送状态
 */
export const pushStatus = Object.freeze({
  /**
   * 待推送
   */
  normal: 'normal',
  /**
   * 推送中
   */
  working: 'working',
  /**
   * 已推送
   */
  completed: 'completed',
  /**
   * 已暂停
   */
  suspend: 'suspend',

  options() {
    return [
      {
        label: '待推送',
        value: this.normal,
        colorType: 'processing'
      },
      {
        label: '推送中',
        value: this.working,
        colorType: 'error'
      },
      {
        label: '已推送',
        value: this.completed,
        colorType: 'success'
      },
      {
        label: '已暂停',
        value: this.suspend,
        colorType: 'default'
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value) ?? {}
  }
})

/**
 * 推送时间类型
 */
export const pushTimeType = Object.freeze({
  /**
   * 立即推送
   */
  now: 'now',
  /**
   * 定时推送
   */
  custom: 'custom',

  options() {
    return [
      {
        label: '立即推送',
        value: this.now
      },
      {
        label: '定时推送',
        value: this.custom
      }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value) ?? {}
  }
})

/**
 * 消息推送方式
 */
export const newsPushMethod = Object.freeze({
  /**
   * 公众号模板消息
   */
  wechat_public: 'wechat_public',
  /**
   * 腾讯短信通知
   */
  tencent_message: 'tencent_message',
  /**
   * 企业微信
   */
  wechat_work: 'wechat_work',
  /**
   * 钉钉
   */
  ding_ding: 'ding_ding',

  options() {
    return [
      {
        label: '公众号模板消息',
        value: this.wechat_public
      },
      {
        label: '腾讯短信通知',
        value: this.tencent_message
      }/* ,
      {
        label: '企业微信',
        value: this.wechat_work
      },
      {
        label: '钉钉消息',
        value: this.ding_ding
      } */
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value) ?? {}
  }
})

/**
 * 微信场景值
 */
export const wxScene = Object.freeze({
  1000: '其他',
  1001: '发现页小程序「最近使用」列表',
  1005: '微信首页顶部搜索框的搜索结果页',
  1006: '发现栏小程序主入口搜索框的搜索结果页',
  1007: '单人聊天会话中的小程序消息卡片',
  1008: '群聊会话中的小程序消息卡片',
  1010: '收藏夹',
  1011: '扫描二维码',
  1012: '长按图片识别二维码',
  1013: '扫描手机相册中选取的二维码',
  1014: '小程序订阅消息',
  1017: '前往小程序体验版的入口页',
  1019: '微信钱包',
  1020: '公众号 profile 页相关小程序列表',
  1022: '聊天顶部置顶小程序入口',
  1023: '安卓系统桌面图标',
  1024: '小程序 profile 页',
  1025: '扫描一维码',
  1026: '发现栏小程序主入口，「附近的小程序」列表',
  1027: '微信首页顶部搜索框搜索结果页「使用过的小程序」列表',
  1028: '我的卡包',
  1029: '小程序中的卡券详情页',
  1030: '自动化测试下打开小程序',
  1031: '长按图片识别一维码',
  1032: '扫描手机相册中选取的一维码',
  1034: '微信支付完成页',
  1035: '公众号自定义菜单',
  1036: 'App 分享消息卡片',
  1037: '小程序打开小程序',
  1038: '从另一个小程序返回',
  1039: '摇电视',
  1042: '添加好友搜索框的搜索结果页',
  1043: '公众号模板消息',
  1044: '带 shareTicket 的小程序消息卡片 详情',
  1045: '朋友圈广告',
  1046: '朋友圈广告详情页',
  1047: '扫描小程序码',
  1048: '长按图片识别小程序码',
  1049: '扫描手机相册中选取的小程序码',
  1052: '卡券的适用门店列表',
  1053: '搜一搜的结果页',
  1054: '顶部搜索框小程序快捷入口',
  1056: '聊天顶部音乐播放器右上角菜单',
  1057: '钱包中的银行卡详情页',
  1058: '公众号文章',
  1059: '体验版小程序绑定邀请页',
  1060: '微信支付完成页',
  1064: '微信首页连Wi-Fi状态栏',
  1065: 'URL scheme 详情',
  1067: '公众号文章广告',
  1068: '附近小程序列表广告',
  1069: '移动应用通过openSDK进入微信，打开小程序',
  1071: '钱包中的银行卡列表页',
  1072: '二维码收款页面',
  1073: '客服消息列表下发的小程序消息卡片',
  1074: '公众号会话下发的小程序消息卡片',
  1077: '摇周边',
  1078: '微信连Wi-Fi成功提示页',
  1079: '微信游戏中心',
  1081: '客服消息下发的文字链',
  1082: '公众号会话下发的文字链',
  1084: '朋友圈广告原生页',
  1088: '会话中系统消息，打开小程序',
  1089: '微信聊天主界面下拉，「最近使用」栏',
  1090: '长按小程序右上角菜单唤出最近使用历史',
  1091: '公众号文章商品卡片',
  1092: '城市服务入口',
  1095: '小程序广告组件',
  1096: '聊天记录，打开小程序',
  1097: '微信支付签约原生页，打开小程序',
  1099: '页面内嵌插件',
  1100: '红包封面详情页打开小程序',
  1101: '远程调试热更新',
  1102: '公众号 profile 页服务预览',
  1103: '发现页小程序「我的小程序」列表',
  1104: '微信聊天主界面下拉，「我的小程序」栏',
  1106: '聊天主界面下拉，从顶部搜索结果页，打开小程序',
  1107: '订阅消息，打开小程序',
  1113: '安卓手机负一屏，打开小程序',
  1114: '安卓手机侧边栏，打开小程序',
  1119: '【企业微信】工作台内打开小程序',
  1120: '【企业微信】个人资料页内打开小程序',
  1121: '【企业微信】聊天加号附件框内打开小程序',
  1124: '扫“一物一码”打开小程序',
  1125: '长按图片识别“一物一码”',
  1126: '扫描手机相册中选取的“一物一码”',
  1129: '微信爬虫访问 详情',
  1131: '浮窗',
  1133: '硬件设备打开小程序 详情',
  1135: '小程序profile页相关小程序列表，打开小程序',
  1144: '公众号文章 - 视频贴片',
  1145: '发现栏 - 发现小程序',
  1146: '地理位置信息打开出行类小程序',
  1148: '卡包-交通卡，打开小程序',
  1150: '扫一扫商品条码结果页打开小程序',
  1151: '发现栏 - 我的订单',
  1152: '订阅号视频打开小程序',
  1153: '“识物”结果页打开小程序',
  1154: '朋友圈内打开“单页模式”',
  1155: '“单页模式”打开小程序',
  1157: '服务号会话页打开小程序',
  1158: '群工具打开小程序',
  1160: '群待办',
  1167: 'H5 通过开放标签打开小程序 详情',
  1168: '移动网站应用直接运行小程序',
  1169: '发现栏小程序主入口，各个生活服务入口',
  1171: '微信运动记录',
  1173: '聊天素材用小程序打开 详情',
  1175: '视频号主页商店入口',
  1176: '视频号直播间主播打开小程序',
  1177: '视频号直播商品',
  1178: '在电脑打开手机上打开的小程序',
  1179: '#话题页打开小程序',
  1181: '网站应用打开PC小程序',
  1183: 'PC微信 - 小程序面板 - 发现小程序 - 搜索',
  1184: '视频号链接打开小程序',
  1185: '群公告',
  1186: '收藏 - 笔记',
  1187: '浮窗',
  1189: '表情雨广告',
  1191: '视频号活动',
  1192: '企业微信联系人profile页',
  1193: '视频号主页服务菜单打开小程序',
  1194: 'URL Link 详情',
  1195: '视频号主页商品tab',
  1196: '个人状态打开小程序',
  1197: '视频号主播从直播间返回小游戏',
  1198: '视频号开播界面打开小游戏',
  1200: '视频号广告打开小程序',
  1201: '视频号广告详情页打开小程序',
  1202: '企微客服号会话打开小程序卡片',
  1203: '微信小程序压测工具的请求',
  1206: '视频号小游戏直播间打开小游戏',
  1207: '企微客服号会话打开小程序文字链',
  1208: '聊天打开商品卡片',
  1212: '青少年模式申请页打开小程序',
  1215: '广告预约打开小程序',
  1216: '视频号订单中心打开小程序',
  1218: '微信键盘预览打开小程序',
  1219: '视频号直播间小游戏一键上车',
  1220: '发现页设备卡片打开小程序',
  1223: '安卓桌面Widget打开小程序',
  1225: '音视频通话打开小程序',
  1226: '聊天消息在设备打开后打开小程序',
  1228: '视频号原生广告组件打开小程序',
  1230: '订阅号H5广告进入小程序',
  1231: '动态消息提醒入口打开小程序',
  1232: '搜一搜竞价广告打开小程序',
  1233: '小程序搜索页人气游戏模块打开小游戏',
  1238: '看一看信息流广告打开小程序',
  1242: '小程序发现页门店快送模块频道页进入小程序',
  1244: '#tag搜索结果页打开小程序',
  1245: '小程序发现页门店快送搜索结果页进入小程序',
  1248: '通过小程序账号迁移进入小程序',
  1252: '搜一搜小程序搜索页「小功能」模块进入小程序',
  1254: '发现页「动态」卡片 打开小程序',
  1255: '发现页「我的」卡片 打开小程序',
  1256: 'pc端小程序面板「最近使用」列表',
  1257: 'pc端小程序面板「我的小程序」列表',
  1258: 'pc端小程序面板「为电脑端优化」模块',
  1259: 'pc端小程序面板「小游戏专区」模块',
  1260: 'pc端小程序面板「推荐在电脑端使用」列表',
  1261: '公众号返佣商品卡片',
  1265: '小程序图片详情页打开小程序',
  1266: '小程序图片长按半屏入口打开小程序',
  1267: '小程序图片会话角标打开小程序',
  1272: '发现页「游戏」服务tab打开小程序',
  1273: '发现页「常用的小程序」列表',
  1278: '发现页「发现小程序」列表打开小程序',
  1279: '发现页「发现小程序」合集页打开小程序',
  1280: '下拉任务栏小程序垂搜「建议使用」打开小程序',
  1281: '下拉任务栏小程序垂搜「发现小程序」打开小程序',
  1286: '明文scheme打开小程序',
  1292: '发现页「发现小程序」poi 详情页打开小程序',
  1295: '下拉任务栏小程序垂搜「发现小程序」广告打开小程序',
  1297: '发现-小程序-搜索「发现小程序」打开小程序',
  1298: '下拉任务栏小程序垂搜「发现小程序」打开的合集访问小程序',
  1299: '下拉任务栏小程序垂搜「发现小程序」poi 详情页打开小程序',
  1300: '发现-小程序-搜索「发现小程序」打开的合集访问小程序',
  1301: '发现-小程序-搜索「发现小程序」poi 详情页打开小程序',
})


/**
 * 微信隐私是否同意授权
 */
export const authorizationAction = Object.freeze({

  /**
   * 同意
   */
  agree: 'agree',

  /**
   * 拒绝
   */
  disagree: 'disagree',

  filter(val) {
    return val === this.agree ? '同意' : '拒绝'
  }
})

/**
 * 搜索条件
 */
export const tagsSearchCondition = Object.freeze({
  /**
   * 标签名称
   */
  name: 'name',

  options() {
    return [
      { label: '标签名称', value: this.name },
    ]
  }
})

export const tagsType = Object.freeze({

  /**
   * 自动分析
   */
  analysis: 'analysis',

  /**
   * 手动
   */
  manual: 'manual',

  options() {
    return [
      { label: '自动', value: this.analysis },
      { label: '手动', value: this.manual },
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value)
  },
})

export const analysisType = Object.freeze({

  /**
   * rfm
   */
  rfm: 'rfm',

  /**
   * 品牌
   */
  brand: 'brand',
})

export const tagsRuleFields = Object.freeze({
  rfm: {
    /**
     * 最近购买距离天数
     */
    r: 'r_value',

    /**
     * 购买次数
     */
    f: 'f_value',

    /**
     * 购买金额
     */
    m: 'm_value',
  },

  brand: {
    /**
     * 所有品牌
     */
    brand: 'brand',

    /**
     * 一级品牌
     */
    first_brand: 'first_brand',
  },


  options(type) {
    const options = {
      rfm : [
        { label: '最近购买距离天数', value: this.rfm.r },
        { label: '购买次数', value: this.rfm.f },
        { label: '购买金额', value: this.rfm.m },
      ],
      brand : [
        { label: '所有品牌', value: this.brand.brand },
        { label: '一级品牌', value: this.brand.first_brand }
      ],
    }

    return options[type]
  },
  default(type) {
    for(let item in this[type]) {
      if(this[type][item]) {
        return this[type][item]
      }
    }
    return ''
  }
})

export const tagsRuleOperator = Object.freeze({
  /**
   * 等于
   */
  equal: '=',

  /**
   * 不等于
   */
  notEqual: '!=',
  
  /**
   * 大于
   */
  greaterThan: '>',
  
  /**
   * 小于
   */
  lessThan: '<',
  
  /**
   * 大于等于
   */
  greaterThanOrEqual: '>=',
  
  
  /**
   * 小于等于
   */
  lessThanOrEqual: '<=',

  options() {
    return [
      { label: '等于', value: this.equal },
      { label: '不等于', value: this.notEqual },
      { label: '大于', value: this.greaterThan },
      { label: '小于', value: this.lessThan },
      { label: '大于等于', value: this.greaterThanOrEqual },
      { label: '小于等于', value: this.lessThanOrEqual },
    ]
  },
})