import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { userLevelsApi } from './api'

export function useUserLevel(callback = null, filters = {}) {
  const { data: levels } = useApiRequest(() => {
    return userLevelsApi.list({ ...filters }).then(cates => (callback ? callback(cates) : cates))
  })

  return {
    levels
  }
}

export function useUserLevelOptions(filters) {
  const { levels: levelOptions } = useUserLevel(cates => useTransformOptions(cates, 'level_name', 'id'), filters)
  return {
    levelOptions
  }
}

export function usUserLevelFilter(arr, value) {
  if (arr) {
    return arr.find(item => item.value == value) ?? {}
  }
}
