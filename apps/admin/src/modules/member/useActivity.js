import { useTransformOptions } from '@/composables/useTransformOptions'
import { signActivityApi, inviteActivitiesApi, registerActivitiesApi } from "../activity/api"

export async function useActivity(callback = null) {
  const activity = [...await signActivityApi.list(), ...await inviteActivitiesApi.list()
    , ...await registerActivitiesApi.list()]
  return callback(activity)
}

export async function useActivityOptions() {
  const activityOptions = await useActivity((activity) => useTransformOptions(activity, 'title', 'id'))
  return { activityOptions }
}

