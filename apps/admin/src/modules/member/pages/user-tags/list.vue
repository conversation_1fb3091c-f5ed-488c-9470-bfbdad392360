<template>
  <uc-layout-list title="用户标签">
    <template #filter>
      <a-form-item name="name">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>

      <a-form-item>
        <a-select v-model:value="formState.type" placeholder="标签类型" allow-clear :options="tagsType.options()" />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="handleSearch">
          查询
        </a-button>
        <a-button @click="handleResetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="handleAdd">
        新增标签
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="标签名称">
          <template #default="{ record }">
            {{ record.name }}
          </template>
        </a-table-column>

        <a-table-column title="标签类型">
          <template #default="{ record }">
            {{ tagsType.filter(record.type).label }}
          </template>
        </a-table-column>

        <a-table-column title="标签人数">
          <template #default="{ record }">
            <router-link
              :to="{
                name: 'user-list',
                query: {
                  tag_id: record.id
                }
              }"
            >
              {{ record.tags_count }}
            </router-link>
          </template>
        </a-table-column>

        <a-table-column title="操作" width="120px">
          <template #default="{ record }">
            <a-button type="link" @click="showModalEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.tags_count > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.tags_count > 0">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisibleEdit" :title="modalTitle" width="600px" @cancel="closeModalEdit">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="标签名称" class="required ">
        <a-input v-model:value="currentFormState.name" />
      </a-form-item>

      <a-form-item label="标签类型" class="required">
        <a-select
          v-model:value="currentFormState.type"
          placeholder="标签类型"
          allow-clear
          :options="tagsType.options()"
          :disabled="currentFormState.id"
        />
      </a-form-item>

      <a-form-item v-if="currentFormState.type === tagsType.analysis" label="分析策略" class="required">
        <a-select
          v-model:value="currentFormState.analysis_id"
          show-search
          placeholder="请选择"
          :options="analysisOptions"
          @change="analysisOptionsChange"
        />
      </a-form-item>

      <a-form-item v-if="currentFormState.type === tagsType.analysis" label="标签规则" class="required">
        <a-input-group v-for="rule, index in currentFormState.rules" :key="index" class="m-b-10" compact>
          <a-select
            v-model:value="rule.field"
            :options="tagsRuleFields.options(analysisOptionsBrand)"
            style="width: 170px;"
          />
          <a-select v-model:value="rule.operator" :options="tagsRuleOperator.options()" style="width: 100px;" />
          <a-input v-model:value="rule.value" style="width: 133px;" placeholder="请输入规则值" />
          <close-outlined v-if="index !== 0" class="rule-sub" @click="subRule(index)" />
        </a-input-group>
        <a-button type="link" class="p-0" @click="addRule">添加规则</a-button>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="closeModalEdit">
        取消
      </a-button>
      <a-button type="primary" :loading="formLoading" @click="handleSubmitEdit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { cloneDeep, debounce } from 'lodash'
import { message } from 'ant-design-vue'
import { CloseOutlined } from '@ant-design/icons-vue';
import { tagsApi } from '../../api'
import { tagsSearchCondition, tagsType, tagsRuleFields, tagsRuleOperator, analysisType } from '../../enums'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { computed, ref, watch } from 'vue'
import { useUserAnalysis } from '../../useUserAnalysis'

const { analysisList, analysisOptions } = useUserAnalysis()
const analysisOptionsBrand = ref(analysisType.rfm)
const analysisOptionsBrandChange = (analysis_id) => {

  let type = analysisOptions.value.filter((item) => {
    return item.value === analysis_id
  })[0]?.type

  if (type) {
    analysisOptionsBrand.value = type
  }
}
const analysisOptionsChange = (e) => {
  analysisOptionsBrandChange(e)
  currentFormState.value.rules.forEach(item => {
    item.field = tagsRuleFields.default(analysisOptionsBrand.value)
  })
}

const conditionOptions = tagsSearchCondition.options()
const conditionKey = ref(tagsSearchCondition.name)
const conditionValue = ref()

watch(conditionKey, () => (conditionValue.value = undefined))

const analysisModel = computed(() => {
  if (currentFormState.value.type === tagsType.analysis && currentFormState.value.analysis_id > 0) {
    return analysisList.value.find(x => x.id == currentFormState.value.analysis_id)
  }
  return null
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  const filters = useTransformQuery(
    {
      name: formState.value.name,
      type: formState.value.type,
    },
    {
      name: 'like',
      type: '='
    }
  )

  return tagsApi.paginator({
    filters,
    relations: ['rule'],
    offset,
    limit
  })
})

const queryFormBasic = Object.freeze({
  [tagsSearchCondition.name]: undefined,
})

const handleSearch = () => {
  Object.assign(formState.value, queryFormBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

const handleResetFormState = () => {
  Object.assign(formState.value, queryFormBasic)
  conditionValue.value = undefined
  resetFormState()
}

const { formState, resetFormState } = useFormState({
  ...cloneDeep(queryFormBasic),
  type: undefined
})

// 标签编辑
const { modalVisible: modalVisibleEdit, setModalVisible: setModalVisibleEdit } = useModalVisible()
const formLoading = ref(false)

const {
  formState: currentFormState,
  resetFormState: currentResetFormState,
  setFormRules,
  validateForm,
} = useFormState({
  name: undefined,
  type: tagsType.manual,
  rules: [{
    field: tagsRuleFields.default(analysisOptionsBrand.value),
    operator: tagsRuleOperator.equal,
    value: '',
  }],
  analysis_id: undefined,
})

setFormRules({
  name: { required: true, message: '请填写标签名称' },
  type: { required: true, message: '请选择标签类型' },
})

const modalTitle = ref()

const showModalEdit = record => {
  currentFormState.value = cloneDeep(record)
  currentFormState.value.rules = currentFormState.value.rule?.rules || []
  currentFormState.value.analysis_id = currentFormState.value.rule?.analysis_id || undefined
  analysisOptionsBrandChange(currentFormState.value.analysis_id)
  modalTitle.value = '编辑标签'
  setModalVisibleEdit(true)
}

const closeModalEdit = () => {
  setModalVisibleEdit(false)
}

const handleSubmitEdit = debounce(async () => {
  if (!(await validateForm())) {
    return
  }
  const formData = cloneDeep(currentFormState.value)

  if (formData.type === tagsType.analysis) {
    if (!formData.analysis_id) {
      message.error('请选择分析策略')
      return
    }
    if (formData.rules.length < 1) {
      message.error('请设置规则')
      return
    }
    for (let index in formData.rules) {
      if (formData.rules[index].field === ''
        || formData.rules[index].operator === ''
        || formData.rules[index].value === ''
      ) {
        message.error('规则选项不能为空')
        return
      }
    }
  } else {
    delete formData.rules
    delete formData.analysis_id
  }
  formLoading.value = true
  const form = formData.id
    ? tagsApi.replace(formData.id, formData)
    : tagsApi.create(formData)

  form.then(() => {
    message.success('操作成功')
    closeModalEdit()
    setPage()
  }).finally(() => {
    formLoading.value = false
  })
}, 50)

const handleAdd = () => {
  currentResetFormState()
  analysisOptionsBrand.value = analysisType.rfm
  modalTitle.value = '创建标签'
  setModalVisibleEdit(true)
}

const addRule = () => {
  currentFormState.value.rules.push({
    field: tagsRuleFields.default(analysisOptionsBrand.value),
    operator: tagsRuleOperator.equal,
    value: '',
  })
}

const subRule = (index) => {
  currentFormState.value.rules.splice(index, 1)
}

const handleDelete = ({ id, name }) => {
  tagsApi
    .delete(id)
    .then(() => {
      message.success(`${name}删除完成`)
    })
    .finally(setPage)
}
</script>

<style lang="less" scoped>
.rule-sub {
  width: 20px;
  height: 20px;
  font-size: 12px;
  line-height: 22px;
  margin-left: 10px;
  margin-top: 6px;
  color: white;
  background-color: #e54d42;
  text-align: center;
  border-radius: 50% !important;
}
</style>