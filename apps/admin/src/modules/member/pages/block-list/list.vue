<template>
  <uc-layout-list title="全部黑名单">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.level" placeholder="拉黑原因" allow-clear>
          <a-select-option v-for="(item, index) in reasonaArr" :key="index" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.black_time"
          :placeholder="['拉黑时间', '拉黑时间']"
          :allow-clear="false"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch">
          查询
        </a-button>
        <a-button @click="handleResetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="handleAddBlock">
        新增黑名单
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="用户昵称" width="200px" data-index="nickname" />
        <a-table-column title="手机号码" width="140px">
          <template #default="{ record }">
            <span>{{ $formatters.numberEncryption(record.phone_number) }}</span>
          </template>
        </a-table-column>
        <a-table-column title="拉黑原因" data-index="black_info.reason" />
        <a-table-column title="拉黑时间" width="190px" data-index="black_time" />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.stock && record.stock.hold > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deleteBlock(record)"
            >
              <a-button type="link" danger>
                移除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal :visible="modalVisible" title="新增黑名单" @cancel="onClose">
    <a-form :model="formState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="拉黑原因" class="required">
        <a-select v-model:value="formState.reason" placeholder="拉黑原因" allow-clear>
          <a-select-option v-for="(item, index) in reasonaArr" :key="index" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="拉黑名单" class="required">
        <a-textarea
          v-model:value="formState.phone_numbers"
          :rows="10"
          style="max-width: 700px !important"
          placeholder="请输入拉黑用户手机号码，一行一个"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onClose">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { watch, ref } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { blockAll, userSettingApi, cancelBlock } from '../../api'
import { searchCondition } from '../../enums'
import { useModalVisible } from '@/composables/useToggles'
import RangeInput from '../../components/uc-range-input.vue'
import { cloneDeep } from 'lodash'
import moment from 'moment'
import { useRouter } from 'vue-router'
import { nextTick } from 'vue'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { phoneVerify } from '@/utils/index'

const router = useRouter()
// 手机号码还是用户昵称
const conditionOptions = searchCondition.options()
const conditionKey = ref(searchCondition.phone_number)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  const relation_filters = {
    blackInfo: useTransformQuery({
      // created_at: formState.value.created_at,
      // nickname: formState.value.nickname,
      // phone_number: formState.value.phone_number,
      reason: formState.value.reason
    })
  }

  const filters = useTransformQuery(
    {
      nickname: formState.value.nickname,
      phone_number: formState.value.phone_number,
      black_time: formState.value.black_time,
      reason: formState.value.reason
    },
    {
      nickname: 'like',
      phone_number: '=',
      black_time: 'dateRange',
      reason: formState.value.reason
    }
  )

  if (formState.value.level == 0) {
    filters.reason = formState.value.level
  } else {
    relation_filters.blackInfo = { ...relation_filters.blackInfo, reason: formState.value.level }
  }

  return blockAll.paginator({
    filters,
    relation_filters,
    relations: ['blackInfo'],
    offset,
    limit,
    sorts: ['-black_time']
  })
})

let reasonaArr = ref([])
const loadData = () => {
  userSettingApi.get({}).then(data => {
    let dataConfig = data.value.black_setting.split('\n')
    dataConfig.forEach(v => {
      reasonaArr.value.push({ value: v, label: v })
    })
    // console.log(reasonaArr);
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

const queryFormBasic = Object.freeze({
  [searchCondition.nickname]: undefined,
  [searchCondition.phone_number]: undefined
})

const handleSearch = () => {
  Object.assign(formState.value, queryFormBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

const handleResetFormState = () => {
  Object.assign(formState.value, queryFormBasic)
  conditionValue.value = undefined
  resetFormState()
}

const { formState, onRestFormState, setFormRules, resetFormState, validateForm } = useFormState({
  reason: undefined,
  phone_numbers: undefined
})

onRestFormState(() => setPage())

const currentHandleRecord = ref()

// 添加黑名单
const { modalVisible, setModalVisible } = useModalVisible()

const handleAddBlock = record => {
  currentHandleRecord.value = record
  setModalVisible(true)
}

const closeModalBlock = () => {
  currentHandleRecord.value = null
  setModalVisible(false)
}

// 移除黑名单
const deleteBlock = async ({ id }) => {
  await cancelBlock(id)
    .post()
    .then(res => {
      message.success('成功移除黑名单')
      setPage()
    })
}

const phoneNumber = (_, value) => {
  if (!value) return Promise.reject('请输入拉黑名单')
  const arr = value.split('\n').map(value => value.trim())
  const resArr = [...new Set(arr)]
  if (!resArr.some(item => phoneVerify(item))) return Promise.reject('请输入正确的手机号')
  if (arr.length !== resArr.length) return Promise.reject('拉黑名单不可重复')
  return Promise.resolve()
}

setFormRules({
  reason: { required: true, message: '请选择拉黑原因', trigger: 'blur' },
  phone_numbers: { validator: phoneNumber, trigger: 'blur' }
})

const handleSubmit = async () => {
  if (!(await validateForm())) {
    return
  }
  const data = await blockAll.create(formState.value)
  message.success(`操作成功,共拉黑${data.length}人`)
  setModalVisible(false)
  resetFormState()
  setPage()
}

const onClose = () => {
  setModalVisible(false)
}
</script>
