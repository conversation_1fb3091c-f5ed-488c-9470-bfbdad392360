<template>
  <uc-layout-list title="会员等级" class="flex">
    <template #filter>
      <a-form-item v-if="userLevelSetting" name="settlement_cycle">
        <a-input
          v-model:value.trim="userLevelSetting.value.effective_cycle"
          addon-before="等级有效期"
          placeholder="有效期按天计算"
        />
      </a-form-item>
      <a-form-item name="btn">
        <a-button type="primary" @click="onSave">
          保存
        </a-button>
      </a-form-item>
    </template>

    <template #list>
      <a-table row-key="id" :pagination="false" :data-source="data" :loading="loading">
        <a-table-column title="等级" width="100px">
          <template #default="{ record }">
            {{ record.level }}
          </template>
        </a-table-column>
        <a-table-column title="等级名称">
          <template #default="{ record }">
            {{ record.level_name || '--' }}
          </template>
        </a-table-column>
        <a-table-column title="积分倍率" width="120px">
          <template #default="{ record }">
            <template v-if="record.credit_magnification">
              {{ record.credit_magnification }}倍积分
            </template>
            <template v-else>
              --
            </template>
          </template>
        </a-table-column>
        <a-table-column title="升级条件" ellipsis>
          <template #default="{ record }">
            <template v-if="record.rule">
              行为次数：{{ record.rule.growth_times }}，行为成长值：{{ record.rule.growth_value }}
            </template>
            <template v-else>
              --
            </template>
          </template>
        </a-table-column>
        <a-table-column title="降级规则" width="120px">
          <template #default="{ record }">
            {{ record.rule ? record.rule.down_level_name : '--' }}
          </template>
        </a-table-column>
        <a-table-column title="最后更新时间" width="200px" data-index="updated_at" />
        <a-table-column width="110px" title="操作">
          <template #default="{ record }">
            <a-button type="link" :disabled="!record.is_sure_edit" @click="onEdit(record)">
              编辑
            </a-button>
            <a-button type="link" :disabled="!record.is_sure_enable" @click="handleStop(record)">
              停用
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useApiRequest } from '@/composables/useApiRequest'
import { message } from 'ant-design-vue'
import { userLevelSettingApi, userLevelSettingUpdateApi, userLevelsApi, userLevelsDisableApi } from '../../api'

const router = useRouter()

const { data: userLevelSetting } = useApiRequest(() => userLevelSettingApi.get())

const { formState, setFormState, setFormRules, validateForm } = useFormState({})

const onSave = () => {
  if (!userLevelSetting.value.value.effective_cycle) return message.error('请输入等级有效期天数')
  userLevelSettingUpdateApi.post(userLevelSetting.value).then(() => {
    message.success('操作成功')
  })
}

const { data, getData, loading } = useApiRequest(() => userLevelsApi.list({ sorts: ['id'] }))

const onEdit = ({ id }) => {
  router.push({
    name: 'member-level-edit',
    params: { id }
  })
}

const handleStop = ({ id }) => {
  userLevelsDisableApi(id).post({ is_enable: false }).then(getData)
}
</script>
