<template>
  <uc-layout-form @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="等级封面" name="photo_url" class="required">
            <uc-upload v-model:list="formState.photo_url" :max-length="1" multiple />
          </a-form-item>
          <a-form-item label="等级编码" name="level" class="required">
            <a-input :value="formState.level" disabled />
          </a-form-item>
          <a-form-item label="等级名称" name="level_name" class="required">
            <a-input v-model:value="formState.level_name" placeholder="请输入等级名称，不超过10字" :maxlength="10" />
          </a-form-item>
          <a-form-item label="积分倍率" name="credit_magnification" class="required">
            <a-input-number
              v-model:value="formState.credit_magnification"
              placeholder="请输入日常积分倍数权益，默认1倍"
              :min="1"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="等级规则" class="h-fill">
          <a-form-item label="升级规则" name="rule" class="required">
            <a-space :size="10">
              <a-input
                v-model:value="formState.rule.growth_times"
                addon-before="行为次数"
                placeholder="请输入行为次数"
                class="w-217"
              />
              <a-button :type="ruleTypes.filter(formState.rule.type).btnType" @click="onLevelRule">
                {{ ruleTypes.filter(formState.rule.type).label }}
              </a-button>
              <a-input
                v-model:value="formState.rule.growth_value"
                addon-before="行为成长值"
                placeholder="请输入成长值"
                class="w-217"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="降级规则" name="down_level" class="required">
            <a-select
              v-if="levelList && levelList.length"
              v-model:value="formState.rule.down_level"
              :disabled="disableRule"
              placeholder="到期降级为"
            >
              <a-select-option v-for="item in levelList" :key="item.id" :value="item.id">
                {{ item.level_name }}
              </a-select-option>
            </a-select>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="升级礼包">
          <a-form-item
            v-for="(rule, ruleIndex) in formState.upRules"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '升级赠礼'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex }"
            class="m-b-10"
          >
            <uc-level-list
              v-model="rule.award_settings"
              :min="formState.upRules.length <= 1 ? 1 : 0"
              disabled-delete
              @delete="onRemoveRule('up', ruleIndex)"
            >
              <template #item="{ item }">
                <a-select
                  v-model:value="item.type"
                  placeholder="请选择礼品类型"
                  :options="giftType.options()"
                  class="w-100"
                  @change="onChangeGiftType(item)"
                />
                <a-input-number
                  v-if="item.type === giftType.credit"
                  v-model:value="item.quantity"
                  style="width: 300px"
                  show-search
                  placeholder="请输入积分值"
                  :disabled="item.disabled || isRead"
                />
                <a-select
                  v-else
                  v-model:value="item.option_id"
                  style="width: 300px"
                  placeholder="请选择奖励礼品"
                  :options="item.options || []"
                  option-filter-prop="label"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="item.disabled || isRead"
                  @focus="handleShowGifts(item)"
                  @search="handleSearchGifts($event, item)"
                />
                <a-input-number
                  v-model:value="item.stock"
                  :formatter="$formatters.number"
                  placeholder="发放数量"
                  class="w-90"
                />
              </template>
            </uc-level-list>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button type="link" class="p-0" :disabled="formState.upRules.length >= 5" @click="onAddRule('up')">
              添加发放层级
            </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="生日关怀">
          <a-form-item label="首单奖励" name="birth_month_first_order_credit_magnification" class="required">
            <a-input-number
              v-model:value="formState.birth_month_first_order_credit_magnification"
              :min="0"
              placeholder="请输入生日月首单积分倍率"
              :maxlength="10"
              :formatter="value => value / 100"
              :parser="value => value * 100"
            />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.birthdayRules"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '生日贺礼'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex }"
            class="m-b-10"
          >
            <uc-level-list
              v-model="rule.award_settings"
              :min="formState.birthdayRules.length <= 1 ? 1 : 0"
              disabled-delete
              @delete="onRemoveRule('birthday', ruleIndex)"
            >
              <template #item="{ item }">
                <a-select
                  v-model:value="item.type"
                  placeholder="请选择礼品类型"
                  :options="giftType.options()"
                  class="w-100"
                  @change="onChangeGiftType(item)"
                />
                <a-input-number
                  v-if="item.type === giftType.credit"
                  v-model:value="item.quantity"
                  style="width: 300px"
                  show-search
                  placeholder="请输入积分值"
                  :disabled="item.disabled || isRead"
                />
                <a-select
                  v-else
                  v-model:value="item.option_id"
                  style="width: 300px"
                  placeholder="请选择奖励礼品"
                  :options="item.options || []"
                  option-filter-prop="label"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="item.disabled || isRead"
                  @focus="handleShowGifts(item)"
                  @search="handleSearchGifts($event, item)"
                />
                <a-input-number
                  v-model:value="item.stock"
                  :formatter="$formatters.number"
                  placeholder="发放数量"
                  class="w-90"
                />
              </template>
            </uc-level-list>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button
              type="link"
              class="p-0"
              :disabled="formState.birthdayRules.length >= 5"
              @click="onAddRule('birthday')"
            >
              添加发放层级
            </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { cloneDeep } from 'lodash'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useApiRequest } from '@/composables/useApiRequest'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { userLevelsApi } from '../../api'
import { ruleTypes, giftType } from '../../enums'
import { useMemberLevel } from '../../useTransformData'
import { usePrizeAwardUpdate } from '../../usePrizeAward'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()

const { initRule, upRuleDefault, birthdayRuleDefault, transformToShow, transformToRequest } = useMemberLevel()
const router = useRouter()

const { data: levelData } = useApiRequest(() => userLevelsApi.list({ sorts: ['id'] }))

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  photo_url: [],
  birth_month_first_order_credit_magnification: 100, // 首单奖励默认100
  level_name: '',
  level: '',
  credit_magnification: '',
  rule: initRule(),
  upRules: [upRuleDefault()],
  birthdayRules: [birthdayRuleDefault()]
})

const levelList = computed(() => {
  let limitIndex = 0
  return levelData.value?.filter((item, i) => {
    item.level === formState.value.level && (limitIndex = i)
    return !(limitIndex && i >= limitIndex)
  })
})

const { id } = useRoute().params
const disableRule = +id === 1 // 等级一不能修改规则

const loadData = async () => {
  const data = await userLevelsApi.get(id, { relations: ['rules'] })
  let showPrizePrm = []
  data.rules.forEach(rule => {
    rule.award_settings.forEach(prize => {
      showPrizePrm.push(handleAward(prize, { id: prize.option_id }))
    })
  })
  await Promise.all(showPrizePrm)
  setFormState(transformToShow(data))
}

if (id) {
  useLoadingMessage(loadData(), { loadingText: '正在加载数据...' })
}

// 校验升级规则
const validatorRule = (_, value) => {
  if (!value.growth_value) {
    return Promise.reject('请输入行为成长值')
  } else if (!value.growth_times) {
    return Promise.reject('请输入行为次数')
  } else if (!value.down_level) {
    if (!disableRule) {
      return Promise.reject('请选择降级规则')
    }
  }

  return Promise.resolve()
}

// 校验赠送礼品
const validatorRules = tips => (_, value) => {
  const valid = value.some(rule =>
    rule.award_settings.some(({ quantity, type, option_id, stock }) => {
      if (type === giftType.credit) {
        if (!quantity) return true
      } else {
        if (!option_id) return true
      }
      return !(type && stock)
    })
  )

  return valid ? Promise.reject(`${tips}不完整`) : Promise.resolve()
}

setFormRules({
  photo_url: { required: true, message: '请上传等级封面' },
  level_name: { required: true, message: '请输入等级名称' },
  credit_magnification: { required: true, message: '请输入日常积分倍数权益' },
  rule: { validator: validatorRule },
  upRules: { validator: validatorRules('赠送礼品') },
  birthdayRules: { validator: validatorRules('生日贺礼') }
})

// 等级规则
const onLevelRule = () => {
  formState.value.rule.type = ruleTypes.reverse(formState.value.rule.type)
}

// 添加升级礼包发放层级
const onAddRule = type => {
  if (type == 'up') {
    formState.value.upRules.push(upRuleDefault())
  } else {
    formState.value.birthdayRules.push(birthdayRuleDefault())
  }
}

// 删除
const onRemoveRule = (type, index) => {
  if (type == 'up') {
    formState.value.upRules.splice(index, 1)
  } else {
    formState.value.birthdayRules.splice(index, 1)
  }
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)

  id && (await userLevelsApi.replace(id, transformToRequest(params)))
  message.success('操作成功')
  router.back()
}
</script>
