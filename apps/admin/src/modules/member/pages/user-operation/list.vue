<template>
  <uc-layout-list title="人群运营">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" placeholder="人群运营名称" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="推送状态" :options="pushStatus.options()" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <router-link :to="{ name: 'member-user-operation-add' }">
        <a-button type="primary">
          新增消息推送
        </a-button>
      </router-link>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="人群运营名称/模版ID" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.title }}
            </div>
            <div>{{ record.template_param?.id }}</div>
          </template>
        </a-table-column>
        <a-table-column title="目标" data-index="target_number" width="120px" align="right" />
        <a-table-column title="触达" data-index="request_number" width="120px" align="right" />
        <a-table-column title="响应" data-index="response_number" width="120px" align="right" />
        <a-table-column title="推送时间" data-index="push_time" width="160px" />
        <a-table-column title="推送状态" width="110px">
          <template #default="{ record }">
            <a-badge
              :status="pushStatus.filter(record.status).colorType"
              :text="pushStatus.filter(record.status).label"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button v-if="record.status === pushStatus.normal" type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              v-if="record.status === pushStatus.working"
              placement="left"
              title="你确定要暂停该推送吗？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleSuspend(record)"
            >
              <a-button type="link" class="danger">
                暂停
              </a-button>
            </a-popconfirm>
            <a-button v-if="record.status !== pushStatus.normal" type="link" @click="onEdit(record)">
              查看
            </a-button>
            <a-popconfirm
              v-if="record.status !== pushStatus.working"
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.status !== pushStatus.normal">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { userOperationApi } from '../../api'
import { pushStatus } from '../../enums'
import { message } from 'ant-design-vue'

const router = useRouter()
// 表格
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  userOperationApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)
const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  status: undefined
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await userOperationApi.delete(id)
  message.success('删除完成')
  setPage()
}

const onEdit = ({ id }) => {
  router.push({
    name: 'member-user-operation-edit',
    query: { id }
  })
}
// 暂停
const handleSuspend = async ({ id }) => {
  await userOperationApi.update(id, { status: 'suspend' })
  message.success('操作成功')
  setPage()

}
</script>
