<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="运营名称" class="required">
            <a-input
              v-model:value="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="运营人群" class="required">
            <a-select
              v-model:value="formState.biz_id"
              :options="operateGroup"
              :disabled="isRead"
              show-search
              allow-clear
              :filter-option="false"
              :not-found-content="null"
              placeholder="请选择运营人群"
              @search="handleSearchGroup({ title: $event })"
              @change="onChangeGroup"
            />
          </a-form-item>
          <a-form-item label="推送时间" class="required">
            <a-space>
              <a-select
                v-model:value="formState.push_time_type"
                :options="pushTimeType.options()"
                :disabled="isRead"
                placeholder="请选择推送时间类型"
                style="width: 170px"
              />
              <a-date-picker
                v-model:value="formState.push_time"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择推送时间"
                :disabled="isRead || formState.push_time_type === pushTimeType.now"
                style="width: 322px"
              />
            </a-space>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="消息配置">
          <a-form-item label="推送方式" class="required">
            <a-select
              v-model:value="formState.push_message_type"
              :options="newsPushMethod.options()"
              :disabled="isRead"
              placeholder="请选择推送方式"
              @change="handleChangeTemplateList"
            />
          </a-form-item>
          <a-form-item label="推送模版" class="required">
            <a-space direction="vertical">
              <a-select
                v-model:value="formState.template_id"
                :options="templateList"
                :disabled="isRead"
                placeholder="请选择模板"
                show-search
                allow-clear
              />
              <a-textarea disabled :value="templateCont.template_content" :auto-size="{ minRows: 5 }" />
            </a-space>
          </a-form-item>
          <a-form-item label="消息参数" :class="{ required: templateCont.template_param_num }">
            <a-select
              v-model:value="formState.require_param"
              mode="multiple"
              :options="newsParams"
              :disabled="isRead"
              placeholder="请按照模版消息参数顺序选择参数"
              show-search
              allow-clear
            />
          </a-form-item>
          <a-form-item
            v-if="formState.push_message_type === newsPushMethod.wechat_public"
            label="群发链接"
            class="required"
          >
            <a-input v-model:value="formState.news_link" :disabled="isRead" placeholder="请输入群发链接" />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { userGroupApi, userOperationApi, pushTemplateApi, newsParamApi } from '../../api'
import { newsPushMethod, pushTimeType, pushStatus } from '../../enums'
import assets from '../../assets.config'

const router = useRouter()
const { id, userGroupId } = useRoute().query

const isEdit = ref(false)
const isRead = ref(false)

const operateGroup = ref([]) // 运营人群
const handleSearchGroup = async ({ title, id } = {}) => {
  const filters = {}
  title && (filters.title = `%${title}%`)
  id && (filters.id = id)

  const { items } = await userGroupApi.paginator({ limit: 20, offset: 1, filters })
  operateGroup.value = useTransformOptions(items, 'title', 'id')
}
const onChangeGroup = e => {
  !e && handleSearchGroup()
}

const templateList = ref([]) // 推送模版
// 模板内容
const templateCont = computed(() => templateList.value.find(item => item.value === formState.value.template_id) || {})
const handleChangeTemplateList = async () => {
  const items = await pushTemplateApi.get({ notify_type: formState.value.push_message_type })
  if (items && items.length) {
    templateList.value = useTransformOptions(items, 'template_title', 'template_id', [
      'template_title',
      'template_id',
      'template_type',
      'template_content',
      'template_param_num'
    ])
  }
}

const newsParams = ref([]) // 消息参数
const handleChangeNewsParam = async () => {
  const items = await newsParamApi.get()
  newsParams.value = useTransformOptions(items, 'title', 'type')
}

nextTick(() => {
  if (id) {
    const hideLoading = message.loading('正在加载数据...')
    isEdit.value = true

    userOperationApi
      .get(id, { relations: ['template_param', 'require_param'] })
      .then(async res => {
        isRead.value = res.status !== pushStatus.normal
        const form = {
          ...res,
          template_id: res.template_param.template_id,
          require_param: res.require_param.map(item => item.type),
          news_link: res.template_param.page_url
        }

        setFormState(form)
        handleSearchGroup({ id: res.biz_id })
        handleChangeTemplateList()
      })
      .finally(hideLoading)
  } else {
    handleSearchGroup()
    handleChangeTemplateList()
  }
  handleChangeNewsParam()
})

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  biz_id: +userGroupId || undefined,
  biz_type: 'user_group',
  push_time_type: undefined,
  push_time: undefined,
  // push_message_type: newsPushMethod.wechat_public,
  push_message_type: newsPushMethod.tencent_message,
  template_id: undefined,
  require_param: [],
  news_link: undefined
})

setFormRules({
  title: { required: true, message: '请输入活动名称' },
  biz_id: { required: true, message: '请选择运营人群' },
  push_time_type: { required: true, message: '请选择推送时间类型' },
  push_time: {
    validator(rule, value) {
      if (formState.value.push_time_type === pushTimeType.custom && !value) return Promise.reject('请选择推送时间')
      return Promise.resolve()
    }
  },
  push_message_type: { required: true, message: '请选择推送方式' },
  template_id: { required: true, message: '请选择模板' },
  require_param: {
    validator(OvO, value) {
      const curTemplate = templateCont.value
      const num = curTemplate.template_param_num
      if (!Object.keys(curTemplate).length) {
        return Promise.reject('请先选择推送模板')
      } else if (value.length !== num) {
        return Promise.reject(`消息参数只能选择${num}个`)
      }
      return Promise.resolve()
    }
  },
  news_link: {
    validator(rule, value) {
      if (formState.value.push_message_type === newsPushMethod.wechat_public && !value)
        return Promise.reject('请输入推送模版API')
      return Promise.resolve()
    }
  }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  const { template_id, template_title, template_content } = templateList.value.find(
    item => item.value === params.template_id
  )
  params.template_param = {
    template_id,
    template_title,
    template_content,
    page_url: params.news_link
  }
  params.require_param = params.require_param.map(type => {
    const { label, value } = newsParams.value.find(it => it.value === type)
    return { type: value, title: label }
  })
  id ? await userOperationApi.update(id, params) : await userOperationApi.create(params)
  message.success('操作完成')
  router.replace({ name: 'member-user-operation-list' })
}
</script>
