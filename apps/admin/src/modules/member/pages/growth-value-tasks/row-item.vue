<template>
  <a-form-item v-bind="{ label, name }" :class="required ? 'required' : ''">
    <a-space :size="12" wrap>
      <a-input-group compact class="inline-block">
        <a-select v-model:value="curForm.limit_type" :options="limitTypes.option()" class="w-90" />
        <a-input
          v-model:value.trim="curForm.limit_value"
          :min="0"
          placeholder="奖励上限"
          class="w-120"
          @change="onChange('limit_value')"
        />
      </a-input-group>
      <a-input
        v-model:value.trim="curForm.first_value"
        v-bind="{ addonBefore, min: 0 }"
        placeholder="请输入成长值"
        class="w-200"
        @change="onChange('first_value')"
      />
      <a-input
        v-model:value.trim="curForm.every_time_value"
        v-bind="{ addonAfter, min: 0 }"
        placeholder="请输入成长值"
        class="w-200"
        @change="onChange('every_time_value')"
      />
    </a-space>
  </a-form-item>
</template>
<script>
export default {
  name: 'RowItem'
}
</script>
<script setup>
import { limitTypes } from '../../enums'

const emit = defineEmits(['update:form'])
const props = defineProps({
  form: {
    type: Object,
    default: () => ({})
  },
  label: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  name: {
    type: String,
    default: ''
  },
  addonBefore: {
    type: String,
    default: '首次'
  },
  addonAfter: {
    type: String,
    default: '成长值/次'
  },
  formatter: {
    type: Function,
    default: value => parseInt(value) || ''
  }
})
const curForm = computed(() => props.form)
watch(
  curForm,
  value => {
    emit('update:form', value)
  },
  { deep: true }
)

// 输入框改变
const onChange = key => {
  curForm.value[key] = props.formatter(curForm.value[key])
}
</script>
