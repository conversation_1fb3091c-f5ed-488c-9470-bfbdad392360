<template>
  <a-modal :visible="props.visible" title="加入黑名单" @cancel="onClose">
    <a-form :model="formState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="拉黑名单" class="required">
        <a-input v-model:value="formState.phone_number" disabled />
        <!-- <a-input v-model:value="formState.nickname" disabled /> -->
      </a-form-item>
      <a-form-item label="拉黑原因" class="required">
        <a-select v-model:value="formState.reason" placeholder="拉黑原因" allow-clear>
          <a-select-option v-for="(item, index) in reasonaArr" :key="index" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onClose">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { useFormState } from "@/composables/useFormState"
import { computed, watch, ref } from "vue"
import { message } from "ant-design-vue"
import { cloneDeep } from 'lodash'
import { userSettingApi } from '../../api'
import { nextTick } from "vue";
import { useLoadingMessage } from '@/composables/useLoadingMessage'
const emit = defineEmits(['handleClose', 'handleOk'])
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: function () {
      return {}
    }
  }
});

watch(
  () => props.data,
  (v) => {
    if (props.data) setFormState(props.data)
  },
  { deep: true }
)

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  phone_number: undefined,
  reason: undefined,
})

setFormRules({
  reason: { required: true, message: "请选择拉黑原因" },
})

let reasonaArr = ref([])
const loadData = () => {
  userSettingApi.get({}).then((data) => {
    let dataConfig = data.value.black_setting.split("\n")
    dataConfig.forEach(v => {
      reasonaArr.value.push({ value: v, label: v })
    });
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

const handleSubmit = async () => {
  if (!(await validateForm())) {
    return
  }
  emit('handleOk', formState.value)
  onClose()
}

const onClose = () => {
  resetFormState()
  emit('handleClose')
}

</script>
