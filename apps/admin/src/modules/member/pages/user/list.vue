<template>
  <uc-layout-list title="全部用户">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.track_code"
          placeholder="注册渠道"
          allow-clear
          :options="trackCodesOptions"
          show-search
          :filter-option="(input, option) => option.label.indexOf(input) >= 0"
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.scene"
          placeholder="注册微信场景"
          allow-clear
          :options="sceneOptions"
          show-search
          :filter-option="filterOption"
          style="width: 300px"
        />
      </a-form-item>
      <a-form-item v-if="!hideGrade">
        <a-select v-model:value="formState.level" placeholder="会员等级" allow-clear :options="levelOptions" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.birthday_month" class="w-120" allow-clear placeholder="会员生日">
          <a-select-option v-for="(item, index) in birthdayMonthOptions()" :key="index" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <RangeInput
          :max-value="formState.consume_total[1]"
          :min-value="formState.consume_total[0]"
          :placeholder="['消耗金额', '消耗金额']"
          @update="data => (formState.consume_total = data)"
        />
      </a-form-item>
      <a-form-item v-if="!hideCredit">
        <RangeInput
          :max-value="formState.credit[1]"
          :min-value="formState.credit[0]"
          :placeholder="['可用积分', '可用积分']"
          @update="data => (formState.credit = data)"
        />
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.register_at"
          :placeholder="['入会时间', '入会时间']"
          :allow-clear="false"
        />
      </a-form-item>
      <a-form-item>
        <a-spin :spinning="userTagLoading">
          <a-select
            v-model:value="formState.tag_id"
            mode="multiple"
            show-search
            :filter-option="userTagFilterOption"
            allow-clear
            placeholder="用户标签"
            style="width: 390px"
            :options="userTagOptions"
          />
        </a-spin>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch"> 查询 </a-button>
        <a-button @click="handleResetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <export-optional-fields
        placement="bottomRight"
        :fields="data.export_fields"
        :search="searchFields"
        @ok="handleExport"
      />
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        :scroll="{ x: 1760 }"
        @change="setPage"
      >
        <a-table-column title="用户昵称/手机号码" width="150px">
          <template #default="{ record }">
            <div class="flex flex-dc flex-sb" @click="toDetails(record)">
              <span class="color-primary hand">{{ record.user.nickname }}</span>
              <span>{{ $formatters.numberEncryption(record.phone_number) }}</span>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="注册渠道来源">
          <template #default="{ record }">
            {{ useTrackCodeFilter(trackCodesOptions, record.user.track_code)?.label }}
          </template>
        </a-table-column>
        <a-table-column title="注册微信场景来源" :min-width="160">
          <template #default="{ record }">
            {{ useSceneFilter(sceneOptions, record.user.scene)?.label }}
          </template>
        </a-table-column>
        <a-table-column title="openID/unionID" width="370px">
          <template #default="{ record }">
            <div class="flex flex-dc flex-sb">
              <span>openID： {{ record.open_id }}</span>
              <span>unionID： {{ record.union_id }}</span>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="入会时间" width="130px" data-index="register_at" />
        <a-table-column title="备注">
          <template #default="{ record }">
            {{ record.user.remark }}
          </template>
        </a-table-column>
        <a-table-column v-if="!hideGrade" title="会员等级" width="130px" ellipsis>
          <template #default="{ record }">
            {{ record.level_name }}
          </template>
        </a-table-column>
        <a-table-column title="总消费" width="110px" align="right">
          <template #default="{ record }">
            <span class="color-red">{{ $formatters.thousandSeparator(record.consume_total) }}</span>
          </template>
        </a-table-column>
        <a-table-column v-if="!hideCredit" title="可用/总积分" width="130px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.credit, false, false) }}/{{
              $formatters.thousandSeparator(record.total_credit, false, false)
            }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="210px" fixed="right">
          <template #default="{ record }">
            <a-button type="link" @click="showModalEdit(record)"> 编辑 </a-button>
            <a-button v-if="!hideCredit" type="link" :disabled="!record.is_member" @click="showModalIntegral(record)">
              增减积分
            </a-button>
            <a-button v-if="record.user.status == 'is_black'" type="link" disabled @click="showModalBlock(record)">
              拉黑
            </a-button>
            <a-button v-else type="link" @click="showModalBlock(record)"> 拉黑 </a-button>
            <a-popconfirm title="是否确定注销？" ok-text="确定" cancel-text="取消" @confirm="logoffConfirm(record)">
              <a-button type="link"> 注销 </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisibleEdit" title="用户编辑" @cancel="closeModalEdit">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="备注" class="required">
        <a-input v-model:value="currentHandleRecord.user.remark" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="closeModalEdit"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmitEdit"> 确定 </a-button>
    </template>
  </a-modal>
  <a-modal :data="currentHandleRecord" :visible="modalVisible" title="加入黑名单" @cancel="onClose">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="拉黑名单" class="required">
        <div class="bigInput">
          <a-input v-model:value="currentHandleRecord.phone_number" :bordered="false" disabled class="phone-input" />
          <a-input v-model:value="currentHandleRecord.nickname" :bordered="false" disabled class="phone-input" />
        </div>
      </a-form-item>
      <a-form-item label="拉黑原因" class="required">
        <a-select v-model:value="currentHandleRecord.reason" placeholder="拉黑原因" allow-clear>
          <a-select-option v-for="(item, index) in reasonaArr" :key="index" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onClose"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit"> 确定 </a-button>
    </template>
  </a-modal>
  <integral-modal
    :visible="modalVisibleIntegral"
    :data="currentHandleRecord"
    @close="closeModalIntegral"
    @ok="handleSubmitIntegral"
  />
</template>
<script setup>
import { watch, nextTick, watchEffect } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { userApi, updateIntegralUpdateApi, blockAll, userSettingApi, userInfoApi, exportAccountApi } from '../../api'
import { searchCondition } from '../../enums'
import { useModalVisible } from '@/composables/useToggles'
import { useUserLevelOptions } from '../../useUserLevel'
import { useTrackCodeOptions, useTrackCodeFilter } from '../../useTrackCode'
import { useSceneOptions, useSceneFilter } from '../../useScene'
import { useUserTag } from '../../useUserTag'
import integralModal from './integral-modal.vue'
import RangeInput from '../../components/uc-range-input.vue'
import { cloneDeep } from 'lodash'
import moment from 'moment'
import { useRouter, useRoute } from 'vue-router'
import ExportOptionalFields from '@/components/export-optional-fields/index.vue'

import config from '@/config'
const featureConfigMember = config.featureConfig.member
const hideCredit = featureConfigMember.hideCredit
const hideGrade = featureConfigMember.hideGrade

const { userTagOptions, userTagFilterOption, userTagLoading } = useUserTag()

const router = useRouter()
const route = useRoute()

const { tag_id } = route.query

const { trackCodesOptions } = useTrackCodeOptions()
const { sceneOptions } = useSceneOptions()

const { levelOptions: options } = useUserLevelOptions({ sorts: ['id'], filters: { is_enable: true } })
const levelOptions = computed(() => {
  if (options.value) {
    options.value.unshift({ label: '临时访客', value: 0 })
  }
  return options.value
})
const conditionOptions = searchCondition.options()
const conditionKey = ref(searchCondition.phone_number)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))

const getFilters = () => {
  // 会员生日
  const { birthday_month } = formState.value
  if (birthday_month) {
    formState.value.birthday =
      birthday_month < 10 ? `-0${formState.value.birthday_month}-` : `-${formState.value.birthday_month}-`
  }

  // 消耗金额 consume_total
  let consume_total = []
  if (formState.value.consume_total[0] || formState.value.consume_total[1]) {
    consume_total = [formState.value.consume_total[0] * 100, formState.value.consume_total[1] * 100]
  }

  const relation_filters = {
    user: useTransformQuery(
      {
        register_at: formState.value.register_at,
        nickname: formState.value.nickname,
        phone_number: formState.value.phone_number,
        consume_total: consume_total,
        credit: formState.value.credit,
        birthday: formState.value.birthday,
        track_code: formState.value.track_code,
        scene: formState.value.scene
      },
      {
        nickname: 'like',
        phone_number: '=',
        register_at: 'dateRange',
        consume_total: 'range',
        credit: 'range',
        birthday: 'like',
        track_code: '=',
        scene: '='
      }
    )
  }

  const filters = useTransformQuery(
    {
      created_at: formState.value.created_at,
      tag_id: formState.value.tag_id,
      open_id: formState.value.open_id,
      union_id: formState.value.union_id
    },
    {
      created_at: 'dateRange',
      tag_id: '=',
      open_id: '=',
      union_id: '='
    }
  )

  if (formState.value.level == 0) {
    filters.user_id = formState.value.level
  } else {
    relation_filters.user = { ...relation_filters.user, user_level_id: formState.value.level }
  }
  return {
    filters,
    relation_filters
  }
}

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  return userApi.paginator({
    ...getFilters(),
    relations: ['user'],
    offset,
    limit
  })
})

const queryFormBasic = Object.freeze({
  [searchCondition.nickname]: undefined,
  [searchCondition.phone_number]: undefined
})

const handleSearch = () => {
  Object.assign(formState.value, queryFormBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

const handleResetFormState = () => {
  Object.assign(formState.value, queryFormBasic)
  conditionValue.value = undefined
  resetFormState()
}

const { formState, onRestFormState, resetFormState, validateForm } = useFormState({
  ...cloneDeep(queryFormBasic),
  consume_total: [undefined, undefined],
  credit: [undefined, undefined],
  birthday_month: undefined,
  reason: undefined,
  phone_number: undefined,
  track_code: undefined,
  scene: undefined,
  tag_id: tag_id ? [parseInt(tag_id)] : []
})

onRestFormState(() => setPage())

const currentHandleRecord = ref()

const filterOption = (input, option) => {
  return option.label.includes(input)
}

// 黑名单
const { modalVisible, setModalVisible } = useModalVisible()

const showModalBlock = record => {
  currentHandleRecord.value = record
  setModalVisible(true)
}

const nameValue = undefined

const closeModalBlock = () => {
  currentHandleRecord.value = null
  setModalVisible(false)
}

let reasonaArr = ref([])
const loadData = () => {
  userSettingApi.get({}).then(data => {
    let dataConfig = data.value.black_setting.split('\n')
    dataConfig.forEach(v => {
      reasonaArr.value.push({ value: v, label: v })
    })
  })
  // console.log(reasonaArr);
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

// 增减积分
const { modalVisible: modalVisibleIntegral, setModalVisible: setModalVisibleIntegral } = useModalVisible()

const showModalIntegral = record => {
  currentHandleRecord.value = record
  setModalVisibleIntegral(true)
}

const closeModalIntegral = () => {
  currentHandleRecord.value = null
  setModalVisibleIntegral(false)
}

const toDetails = ({ id, user_id }) => router.push({ name: 'user-details', params: { id, user_id } })

const handleSubmitIntegral = async params => {
  const { credit_value, remark, user_id } = params
  await updateIntegralUpdateApi(user_id).post({ user_id: user_id, credit_value, remark })
  message.success('操作成功')
  closeModalIntegral()
  setPage()
}

const handleSubmit = async () => {
  await blockAll.create({
    user: { status: 'is_black', id: currentHandleRecord.value.user.id },
    reason: currentHandleRecord.value.reason,
    phone_numbers: currentHandleRecord.value.phone_number
  })
  message.success('操作成功')
  onClose()
  setPage()
}

const onClose = () => {
  setModalVisible(false)
  resetFormState()
}

// 用户编辑
const { modalVisible: modalVisibleEdit, setModalVisible: setModalVisibleEdit } = useModalVisible()

const showModalEdit = record => {
  ;(currentHandleRecord.value = cloneDeep(record)), setModalVisibleEdit(true)
}

const closeModalEdit = () => {
  setModalVisibleEdit(false)
}

const handleSubmitEdit = async () => {
  await userInfoApi(currentHandleRecord.value.user.id).post({
    remark: currentHandleRecord.value.user.remark
  })
  message.success('操作成功')
  closeModalEdit()
  setPage()
}

const logoffConfirm = async ({ id }) => {
  await userApi.delete(id)
  message.success('操作成功')
  setPage()
}

// 导出显示查询字段
const searchFields = ref()
const searchChangeLabel = (options, value, multiple = false) => {
  if (!value) {
    return value
  }
  let label = ''
  let char = ''
  for (let index = 0; index < options.length; index++) {
    const condition = Array.isArray(value) ? value.indexOf(options[index].value) !== -1 : options[index].value == value

    if (condition) {
      if (multiple) {
        label += char + options[index].label
      } else {
        label = options[index].label
        break
      }
      char = ' | '
    }
  }
  return label
}
const birthdayMonthOptions = () => {
  const options = []
  for (let index = 0; index < 12; index++) {
    options.push({
      label: index + 1 + '月',
      value: index + 1
    })
  }
  return options
}
watchEffect(() => {
  const searchFormQuery = useTransformQuery({
    register_at: formState.value.register_at
  })
  let register_at = '--'
  if (searchFormQuery.register_at) {
    register_at =
      searchFormQuery.register_at[0].format('YYYY-MM-DD') + ' - ' + searchFormQuery.register_at[1].format('YYYY-MM-DD')
  }
  // 消耗金额 consume_total
  let consume_total = '--'
  if (formState.value.consume_total[0] || formState.value.consume_total[1]) {
    consume_total = formState.value.consume_total[0] + ' - ' + formState.value.consume_total[1]
  }
  // 可用积分
  let credit = '--'
  if (formState.value.credit[0] || formState.value.credit[1]) {
    credit = formState.value.credit[0] + ' - ' + formState.value.credit[1]
  }

  searchFields.value = [
    {
      label: searchChangeLabel(conditionOptions, conditionKey.value),
      value: conditionValue.value
    },
    {
      label: '注册渠道',
      value: searchChangeLabel(trackCodesOptions.value, formState.value.track_code)
    },
    {
      label: '注册微信场景',
      value: searchChangeLabel(sceneOptions, formState.value.scene)
    },
    {
      label: '会员等级',
      value: searchChangeLabel(levelOptions.value, formState.value.level)
    },
    {
      label: '会员生日',
      value: searchChangeLabel(birthdayMonthOptions(), formState.value.birthday_month)
    },
    {
      label: '可用积分',
      value: credit
    },
    {
      label: '消耗金额',
      value: consume_total
    },
    {
      label: '入会时间',
      value: register_at
    },
    {
      label: '用户标签',
      value: searchChangeLabel(userTagOptions.value, formState.value.tag_id, true)
    }
  ]
})

const handleExport = (fields, token) => {
  const query = getFilters()
  let excelExportUrl = `&filters=${JSON.stringify(query.filters)}&relation_filters=${JSON.stringify(
    query.relation_filters
  )}`

  if (fields) {
    excelExportUrl += `&export_fields=${fields}`
  }

  window.open(encodeURI(`${exportAccountApi}?token=${token}${excelExportUrl}`))
}
</script>
<style lang="less" scoped>
.bigInput {
  display: flex;
  border: 1px solid #ccc;

  :deep(.phone-input) {
    border: none;
  }
}
</style>
