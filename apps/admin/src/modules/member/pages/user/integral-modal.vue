<template>
  <a-modal :visible="props.visible" title="增减积分" @cancel="onClose">
    <a-form :model="formState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="用户昵称" class="required">
        <a-input v-model:value="formState.nickname" disabled />
      </a-form-item>
      <a-form-item label="可用积分" class="required">
        <a-input v-model:value="formState.credit" disabled />
      </a-form-item>
      <a-form-item label="增减积分" class="required">
        <a-input-number v-model:value="formState.credit_value" placeholder="负数表示减积分，正数表示加积分" />
      </a-form-item>
      <a-form-item label="操作说明" class="required">
        <a-textarea
          v-model:value="formState.remark"
          placeholder="请输入操作说明，不超过200字"
          :rows="4"
          maxlength="200"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onClose">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleOk">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { watch } from 'vue'
import { decimalVerify } from '@/utils/index'

const emit = defineEmits(['close', 'ok'])

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  data: {
    type: Object,
    default: null
  }
})

const formBasic = Object.freeze({
  credit_value: undefined,
  remark: undefined
})

watch(
  () => props.data,
  v => {
    if (v) setFormState({ ...v, ...formBasic })
  },
  { deep: true }
)

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({ ...formBasic })

setFormRules({
  credit_value: {
    validator(_, value) {
      if (!value) return Promise.reject('请输入增减积分')
      if (decimalVerify(value)) return Promise.reject('不可输入小数')
      return Promise.resolve()
    }
  },
  remark: { required: true, message: '请输入操作说明' }
})

const handleOk = async () => {
  if (!(await validateForm())) return
  emit('ok', formState.value)
}

const onClose = () => {
  resetFormState()
  emit('close')
}
</script>
