<template>
  <uc-layout-list title="用户标签">
    <template #list>
      <div class="user-tags-container">
        <template v-if="loading">
          <div class="user-tags-container__loading">
            <a-spin />
          </div>
        </template>
        <template v-else-if="dataUserTags">
          <a-tag v-for="item, index in dataUserTags" :key="index" color="blue" class="user-tags-container__tag">
            <span v-if="item.tag.type != tagsType.analysis">
              {{ item.tag.name }}
              <a-popconfirm
                placement="top"
                title="你确定要删除该标签么？"
                ok-text="确定"
                cancel-text="取消"
                class="user-tags-container__close"
                @confirm="close(item)"
              >
              
                <close-outlined />
              </a-popconfirm>
            </span>
            <a-tooltip v-else placement="top" title="自动标签">
              <span>{{ item.tag.name }}</span>
            </a-tooltip>
          </a-tag>

          <a-tag class="user-tags-container__add" @click="add()">
            <plus-outlined />
            增加标签
          </a-tag>
        </template>
        <template v-else>
          <a-empty :image="Empty.PRESENTED_IMAGE_SIMPLE" />
        </template>
      </div>
    </template>
  </uc-layout-list>
  <a-modal :visible="modalVisible" title="添加用户标签" @cancel="closeModal">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="用户标签" class="required">
        <a-select
          v-model:value="formState.tag_id"
          mode="multiple"
          show-search
          :filter-option="userTagFilterOption"
          placeholder="请选择"
          :options="userTagOptions"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="closeModal">
        取消
      </a-button>
      <a-button type="primary" :loading="formLoading" @click="handleSubmitTag">
        确定
      </a-button>
    </template>
  </a-modal>
</template>

<script setup>
import {
  userTaggedTagApi
} from '../../api'
import { CloseOutlined, PlusOutlined } from '@ant-design/icons-vue';
import { useApiRequest } from '@/composables/useApiRequest'
import { Empty, message } from 'ant-design-vue'
import { tagsType } from '../../enums'
import { useModalVisible } from '@/composables/useToggles'
import { useFormState } from '@/composables/useFormState'
import { useUserTag } from '../../useUserTag'
import { watch } from 'vue'
import { cloneDeep } from 'lodash'

const props = defineProps({
  userId: {
    type: [String, Number],
    required: true,
  }
})

const {data: dataUserTags, getData, loading} = useApiRequest(() => userTaggedTagApi.list({
  filters:{
    user_id: props.userId,
  },
  relations: ['tag']
}))

const close = (item) => {
  userTaggedTagApi
    .delete(item.id)
    .then(() => {
      message.success(`${item.tag.name}删除完成`)
      getData()
    })
}

const { userTagOptions, load, userTagFilterOption } = useUserTag({
  onMounted: false
})

watch(() => dataUserTags.value, (val) => {
  const tagIds = []

  val.forEach(item => {
    tagIds.push(item.tag_id)
  });

  load({
    filters: {
      type: tagsType.manual,
      exclude_ids: tagIds
    }
  })
})


const { formState, resetFormState, setFormRules, validateForm } = useFormState({
  tag_id: undefined
})

setFormRules({
  tag_id: { required: true, message: '请选择标签' },
})

const { modalVisible, setModalVisible } = useModalVisible()
const formLoading = ref(false)

const add = () => {
  resetFormState()
  setModalVisible(true)
}

const closeModal = () => {
  setModalVisible(false)
}

const handleSubmitTag = async() => {
  if(!(await validateForm())) {
    return
  }
  formLoading.value = true
  const form = cloneDeep(formState.value)
  form.user_id = props.userId
  userTaggedTagApi.create(form).then(() => {
    message.success('操作成功')
    setModalVisible(false)
    getData()
  }).finally(() => {
    formLoading.value = false
  })

}
</script>

<style lang="less" scoped>
.user-tags-container{
  width: 100%;
  min-height: 102px;
  &__tag{
    margin-bottom: 10px;
  }
  &__loading{
    width: 100%;
    line-height: 102px;
    text-align: center;
  }
  &__close{
    color: rgba(0, 0, 0, 0.45);
    font-size: 10px;
    transition: all 0.3s;
    margin-left: 7px;
  }
  &__close:hover{
    color: rgba(0, 0, 0, 1);
  }
  &__add{
    background: #fff;
    border-style: dashed;
    cursor: pointer;
  }
}
</style>