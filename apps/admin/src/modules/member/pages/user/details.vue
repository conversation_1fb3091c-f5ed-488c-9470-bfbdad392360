<template>
  <div class="flex flex-sb h-fill">
    <div class="flex flex-dc flex-1 p-24" style="overflow-x: auto; margin: -24px">
      <div class="info flex">
        <div class="item">
          <span class="count">{{ dataStats?.total_order_count }}</span>
          <span class="desc">累计购买单数</span>
        </div>
        <div class="item">
          <span class="count">{{ dataStats?.total_goods_quantity }}</span>
          <span class="desc">累计购买件数</span>
        </div>
        <div class="item">
          <span class="count">{{ dataStats?.prev_order_time }}</span>
          <span class="desc">最近购买周期</span>
        </div>
        <div class="item">
          <span class="count">{{ $formatters.thousandSeparator(dataStats?.total_paid_amount) }}</span>
          <span class="desc">累计消费金额</span>
        </div>
        <div class="item">
          <span class="count">{{ $formatters.thousandSeparator(dataStats?.total_refund_amount) }}</span>
          <span class="desc">累计退款金额</span>
        </div>
      </div>

      <user-tags :user-id="user_id" />

      <uc-layout-list title="全部订单">
        <template #extra>
          <div class="flex">
            <a-select
              v-model:value="formStateOrder.pay_status"
              class="w-100 m-r-14"
              style="flex-shrink: 0"
              :options="orderPayStatus.options()"
              placeholder="支付状态"
              allow-clear
              @change="setPageOrder"
            />
            <!-- <a-select
              v-model:value="formStateOrder.source"
              class="w-100 m-r-14"
              :options="orderSource.options()"
              placeholder="订单来源"
              allow-clear
              @change="setPageOrder"
            /> -->
            <a-input
              v-model:value.trim="formStateOrder.sn"
              placeholder="请输入订单编号"
              allow-clear
              @blur="setPageOrder"
            />
          </div>
        </template>
        <template #list>
          <a-table
            :data-source="dataOrder.items || []"
            row-key="id"
            :loading="loadingOrder"
            :pagination="stdPagination(dataOrder)"
            @change="setPageOrder"
          >
            <a-table-column title="订单编号" ellipsis>
              <template #default="{ record }">
                <span class="color-primary hand" @click="toOrderDetails(record)">{{ record.sn }}</span>
              </template>
            </a-table-column>
            <a-table-column title="应付款" width="150px" align="right">
              <template #default="{ record }">
                <span class="color-red">{{ $formatters.thousandSeparator(record.payable_amount) }}</span>
              </template>
            </a-table-column>
            <a-table-column title="支付状态" data-index="accumulate_days" width="120px">
              <template #default="{ record }">
                {{ orderPayStatus.filterValue(record.pay_status).label }}
              </template>
            </a-table-column>
            <a-table-column title="下单时间" data-index="created_at" width="190px" />
          </a-table>
        </template>
      </uc-layout-list>
      <uc-layout-list v-if="!hideCredit" title="积分记录">
        <template #extra>
          <div class="flex">
            <a-select
              v-model:value="formStateIntegral.action"
              class="w-100 m-r-14"
              style="flex-shrink: 0"
              placeholder="积分类型"
              :options="integralActionType.options()"
              allow-clear
              @change="setPageIntegral"
            />
            <a-select
              v-model:value="formStateIntegral.desc_type"
              class="w-200"
              style="flex-shrink: 0"
              placeholder="积分明细"
              :options="integralDetailedOptions"
              allow-clear
              @change="setPageIntegral"
            />
          </div>
        </template>
        <template #list>
          <a-table
            :data-source="dataIntegral.items || []"
            row-key="user_id"
            :loading="loadingIntegral"
            :pagination="stdPagination(dataIntegral)"
            @change="setPageIntegral"
          >
            <a-table-column title="积分明细" data-index="desc" width="230px" ellipsis />
            <a-table-column title="积分值" data-index="title" width="150px" ellipsis>
              <template #default="{ record }">
                <span :class="record.action === integralActionType.incr ? 'credit-incr' : 'credit-decr'">{{
                  record.credit
                }}</span>
              </template>
            </a-table-column>
            <a-table-column title="备注说明" data-index="remark" ellipsis />
            <a-table-column title="创建时间" data-index="created_at" width="190px" />
          </a-table>
        </template>
      </uc-layout-list>
      <uc-layout-list title="商品加购">
        <template #list>
          <a-table
            :data-source="dataShoppingCart.items || []"
            row-key="id"
            :loading="loadingShoppingCart"
            :pagination="stdPagination(dataShoppingCart)"
            @change="setPageShoppingCart"
          >
            <a-table-column title="商品名称" data-index="goods_title" ellipsis>
              <template #default="{ record }">
                <uc-img-text
                  v-bind="record"
                  :url="record.photo_url"
                  :title="record.goods_title"
                  :label="record.goods_id"
                />
              </template>
            </a-table-column>
            <a-table-column title="sku" data-index="sku" ellipsis />
            <a-table-column title="价格" data-index="price" width="150px" ellipsis align="right">
              <template #default="{ record }">
                {{ $formatters.thousandSeparator(record.price) }}
              </template>
            </a-table-column>
            <a-table-column title="加购数量" data-index="quantity" width="150px" ellipsis />
            <a-table-column title="加购时间" data-index="edited_at" width="180px" ellipsis />
          </a-table>
        </template>
      </uc-layout-list>
      <uc-layout-list title="商品浏览">
        <template #list>
          <a-table
            :data-source="dataGoods.items || []"
            row-key="id"
            :loading="loadingGoods"
            :pagination="stdPagination(dataGoods)"
            @change="setPageGoods"
          >
            <a-table-column title="商品名称/id" ellipsis>
              <template #default="{ record }">
                <uc-img-text
                  v-bind="record.goods"
                  :url="record.goods?.photo_urls[0]"
                  :title="record.goods?.title"
                  :label="record.goods?.id"
                />
              </template>
            </a-table-column>
            <a-table-column title="商品分类" width="120px">
              <template #default="{ record }">
                {{ getCategory(categories, record.goods?.category_id) }}
              </template>
            </a-table-column>
            <a-table-column title="浏览时间" width="150px">
              <template #default="{ record }">
                {{ record.created_at }}
              </template>
            </a-table-column>
          </a-table>
        </template>
      </uc-layout-list>
      <uc-layout-list title="小程序授权记录">
        <template #list>
          <a-table
            :data-source="authorizations.items || []"
            row-key="id"
            :loading="loadingAuthorizations"
            :pagination="stdPagination(authorizations)"
            @change="setPageAuthorizations"
          >
            <a-table-column title="授权的接口或组件名" ellipsis>
              <template #default="{ record }">
                {{ record.referrer }}
              </template>
            </a-table-column>
            <a-table-column title="同意/拒绝" width="120px">
              <template #default="{ record }">
                {{ authorizationAction.filter(record.action) }}
              </template>
            </a-table-column>
            <a-table-column title="操作时间" width="180px">
              <template #default="{ record }">
                {{ record.created_at }}
              </template>
            </a-table-column>
          </a-table>
        </template>
      </uc-layout-list>
    </div>
    <div class="user-info bgc-white m-l-20" style="width: 300px">
      <div class="flex flex-cc p-24 p-tb-30">
        <div class="flex flex-1 flex-cc text-ellipsis">
          <div class="m-r-20" style="flex-shrink: 0">
            <uc-avatar :src="dataUserInfo?.avatar" :nickname="dataUserInfo?.nickname" />
            <!-- <img
              class="avatar"
              :src="dataUserInfo && dataUserInfo.avatar ? dataUserInfo?.avatar : shopStyleConfig?.config?.logo_white"
              alt=""
            /> -->
          </div>
          <span class="fs-18 fw-bold text-ellipsis color-85">{{ dataUserInfo?.user?.nickname }}</span>
        </div>
      </div>
      <div class="basic-info p-lr-24 color-65">
        <div>手机：{{ dataUserInfo?.phone_number }}</div>
        <div>年龄：{{ dataUserInfo?.age || '未知' }}</div>
        <div>性别：{{ dataUserInfo?.sex || '未知' }}</div>
        <div>生日：{{ dataUserInfo?.birthday || '未知' }}</div>
        <div>城市：{{ dataUserInfo?.city || '未知' }}</div>
        <div>信息完善时间：{{ dataUserInfo?.user?.complete_info_at || '未知' }}</div>
        <div>注册渠道来源：{{ useTrackCodeFilter(trackCodesOptions, dataUserInfo?.user?.track_code)?.label }}</div>
        <div>注册微信场景：{{ useSceneFilter(sceneOptions, dataUserInfo?.user?.scene)?.label }}</div>
        <div>unionID：{{ dataUserInfo?.union_id }}</div>
        <div>openID：{{ dataUserInfo?.open_id }}</div>

        <div class="fs-16 fw-bold m-t-30 color-85">会员信息</div>
        <div v-if="!hideGrade">成长值：{{ dataUserInfo?.growth || 0 }}</div>
        <div v-if="!hideCredit">可用积分：{{ dataUserInfo?.credit || 0 }}</div>
        <div v-if="!hideCredit">累计积分：{{ dataUserInfo?.total_credit || 0 }}</div>
        <div v-if="!hideGrade">会员等级：{{ dataUserInfo?.level_name || '未知' }}</div>
        <div v-if="!hideGrade">到期时间：{{ dataUserInfo?.level_expired_time || '未知' }}</div>
        <div>入会时间：{{ dataUserInfo?.register_at || '未知' }}</div>
        <div>入会渠道：{{ dataUserInfo?.register_channel || '未知' }}</div>
        <div v-if="dataUserInfo?.child_info">孩子信息：{{ dataUserInfo?.child_info || '未知' }}</div>
        <div v-if="dataUserInfo?.user?.profile?.child_list?.length > 0">
          孩子年龄：
          <span v-for="(item, index) in dataUserInfo.user.profile.child_list" :key="index" class="m-r-4">
            {{ item.label }}
          </span>
        </div>
        <div v-if="dataUserInfo?.user?.profile?.brand_preference?.length > 0">
          品牌偏好：
          <a-tag v-for="(item, index) in dataUserInfo.user.profile.brand_preference" :key="index">
            {{ item }}
          </a-tag>
        </div>
        <a-button type="primary" class="m-t-10" @click="toDispatchCoupon">定向发券</a-button>
      </div>
    </div>
  </div>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useApiRequest, usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'
import {
  creditDetailedApi,
  shopOrdersApi,
  userCreditApi,
  shopStyleActions,
  userStatsAction,
  userDetailsAction,
  buyCartApi,
  trackApi,
  privacyAuthorizationLogApi
} from '../../api'
import { orderSource, orderPayStatus, integralActionType, authorizationAction } from '../../enums'
import { useRoute, useRouter } from 'vue-router'
import { getCategory, useCategories } from '@/modules/goods/useCategory'
import { useTrackCodeOptions, useTrackCodeFilter } from '../../useTrackCode'
import { useSceneOptions, useSceneFilter } from '../../useScene'
import userTags from './user-tags.vue'
import config from '@/config'
const featureConfigMember = config.featureConfig.member
const hideCredit = featureConfigMember.hideCredit
const hideGrade = featureConfigMember.hideGrade

const { id, user_id } = useRoute().params

const { data: dataStats, loading: loadingStats } = useApiRequest(() => userStatsAction(id).get().then())
const { data: dataUserInfo, loading: loadingUserInfo } = useApiRequest(() => userDetailsAction(id).get().then())
const { data: shopStyleConfig } = useApiRequest(() => shopStyleActions.get({}).then())
const { data: integralDetailedOptions } = useApiRequest(() =>
  creditDetailedApi.list().then(res => useTransformOptions(res, 'desc_show', 'desc_type'))
)
const { trackCodesOptions } = useTrackCodeOptions()
const { sceneOptions } = useSceneOptions()

const { formState: formStateOrder } = useFormState({
  pay_status: undefined,
  // source: undefined,
  sn: undefined
})

const { formState: formStateIntegral } = useFormState({
  action: undefined,
  desc_type: undefined
})

const {
  data: dataOrder,
  setPage: setPageOrder,
  loading: loadingOrder
} = usePaginatorApiRequest(({ offset, limit }) =>
  shopOrdersApi.paginator({
    filters: useTransformQuery(formStateOrder.value, {
      sn: 'like',
      source: '='
    }),
    relation_filters: {
      user: useTransformQuery({ id: user_id }, { id: '=' })
    },
    offset,
    limit,
    relations: ['items', 'user', 'spec']
  })
)

const {
  data: dataIntegral,
  setPage: setPageIntegral,
  loading: loadingIntegral
} = usePaginatorApiRequest(({ offset, limit }) =>
  userCreditApi.paginator({
    filters: useTransformQuery(
      { ...formStateIntegral.value, user_id },
      {
        user_id: '=',
        action: '=',
        desc_type: '='
      }
    ),
    offset,
    limit
  })
)

const {
  data: dataShoppingCart,
  setPage: setPageShoppingCart,
  loading: loadingShoppingCart
} = usePaginatorApiRequest(({ offset, limit }) =>
  buyCartApi(user_id).paginator({
    offset,
    limit
  })
)

const {
  data: dataGoods,
  setPage: setPageGoods,
  loading: loadingGoods
} = usePaginatorApiRequest(({ offset, limit }) =>
  trackApi.paginator({
    filters: useTransformQuery(
      {
        page_type: 'goods-detail',
        account_id: id,
        action: 'view'
      },
      {
        page_type: '=',
        account_id: '='
      }
    ),
    offset,
    limit
  })
)

// 商品分类
const { categories } = useCategories()

const router = useRouter()

const toOrderDetails = ({ id }) => router.push({ name: 'shop-detail', params: { id } })

// 用户授权记录
const {
  data: authorizations,
  setPage: setPageAuthorizations,
  loading: loadingAuthorizations
} = usePaginatorApiRequest(({ offset, limit }) =>
  privacyAuthorizationLogApi(user_id).paginator({
    relation_filters: {
      account: {
        user_id
      }
    },
    offset,
    limit
  })
)

const toDispatchCoupon = () => {
  router.push({
    name: 'promotion-directional-dispatch-coupon-add',
    query: {
      phone: dataUserInfo.value.phone_number
    }
  })
}
</script>
<style scoped lang="less">
.info {
  .item {
    display: flex;
    flex: 1;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    background: #fff;
    padding: 15px 0;

    .count {
      color: rgba(0, 0, 0, 0.85);
      font-size: 20px;
    }

    .desc {
      color: rgba(0, 0, 0, 0.45);
    }
  }

  .item:last-child {
    margin-right: 0;
  }
}

.user-info {
  margin: -24px -24px -24px 0;

  .avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
  }

  .default-avatar {
    width: 50px;
    height: 50px;
    background: #d7d7d7;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;

    .avatar {
      width: 80%;
      height: auto;
      border-radius: 0;
    }
  }

  .basic-info {
    div {
      line-height: 2;
    }
  }
}

.credit-incr {
  color: #52c41a;
}

.credit-decr {
  color: #f52222;
}
</style>
