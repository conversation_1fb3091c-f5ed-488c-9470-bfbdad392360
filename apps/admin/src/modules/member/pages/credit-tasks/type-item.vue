<template>
  <a-form-item v-bind="{ label, name }" :class="required ? 'required' : ''">
    <a-space :size="12" class="flex-wrap">
      <a-space v-if="model == 'one'" :size="12" wrap>
        <a-input-group compact class="inline-block">
          <a-select v-model:value="curForm.limit_type" :options="limitTypes.option()" class="w-90" />
          <a-input
            v-model:value.trim="curForm.limit_value"
            :min="0"
            placeholder="奖励上限"
            class="w-120"
            @change="onChange('limit_value')"
          />
        </a-input-group>
        <a-input
          v-model:value.trim="curForm.first_value"
          v-bind="{ addonBefore, min: 0 }"
          placeholder="请输入积分值"
          class="w-200"
          @change="onChange('first_value')"
        />
        <a-input
          v-model:value.trim="curForm.every_time_value"
          v-bind="{ addonAfter, min: 0 }"
          placeholder="请输入积分值"
          class="w-200"
          @change="onChange('every_time_value')"
        />
      </a-space>
      <!-- 目前model全是为one,two和three 暂无 -->
      <a-space v-if="model == 'two'" :size="12">
        <a-input-number v-model:value.trim="curForm.first_value" :min="0" placeholder="奖励积分" class="w-210" />
      </a-space>
      <a-space v-if="model == 'three'" :size="12">
        <a-select
          v-model:value="curForm.biz_id"
          placeholder="请选择关联活动"
          show-search
          class="w-210"
          :filter-option="false"
          :options="activityList ?? activityOptions"
          @search="handleSearchActivity($event, curForm.event_id)"
          @focus="onFocus(curForm.event_id)"
        />
      </a-space>
      <!-- 目前不要任务标题、任务描述 -->
      <!-- <a-input v-model:value.trim="curForm.title" placeholder="任务标题" :maxlength="10" class="w-150" />
      <a-input v-model:value.trim="curForm.desc" placeholder="任务描述" :maxlength="20" class="w-300" /> -->
    </a-space>
  </a-form-item>
</template>
<script>
export default {
  name: 'RowItem'
}
</script>
<script setup>
import { limitTypes, taskList } from '../../enums'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformQuery } from '@/composables/useTransformQuery'

import {
  signActivityApi,
  inviteActivitiesApi,
  registerActivitiesApi,
  turnplateApi,
  sudokuApi
} from '../../../activity/api'

const emit = defineEmits(['update:form'])
const props = defineProps({
  model: {
    type: String,
    default: 'one'
  },
  form: {
    type: Object,
    default: () => ({})
  },
  label: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  name: {
    type: String,
    default: ''
  },
  addonBefore: {
    type: String,
    default: '首次'
  },
  addonAfter: {
    type: String,
    default: '积分值/次'
  },
  formatter: {
    type: Function,
    default: value => parseInt(value) || ''
  }
})
const curForm = computed(() => props.form)
watch(
  curForm,
  value => {
    emit('update:form', value)
  },
  { deep: true }
)

// 输入框改变
const onChange = key => {
  curForm.value[key] = props.formatter(curForm.value[key])
}

// 关联活动
const activityList = ref([])

const handleSearchActivity = (title, eventId) => {
  onFocus(
    eventId,
    useTransformQuery(
      {
        title
      },
      {
        title: 'like'
      }
    )
  )
}

// 获取关联活动
const onFocus = async (eventId, filters) => {
  // 根据类型调用不同接口
  switch (eventId) {
    case taskList.signActivity: // 签到有礼
      const signData = await signActivityApi.paginator({ filters: { ...filters, status: 'normal' } })
      activityList.value = useTransformOptions(signData.items, 'title', 'id')
      break
    case taskList.inviteActivity: // 邀请有礼
      const inviteData = await inviteActivitiesApi.paginator({ filters: { ...filters, status: 'normal' } })
      activityList.value = useTransformOptions(inviteData.items, 'title', 'id')
      break
    case taskList.registerUser: // 入会有礼
      const registerData = await registerActivitiesApi.paginator({ filters: { ...filters, status: 'normal' } })
      activityList.value = useTransformOptions(registerData.items, 'title', 'id')
      break
    case taskList.luckDrawActivity: // 抽奖有礼
      activityList.value = []
      const turnplateData = await turnplateApi.paginator({ filters: { ...filters, status: 'normal' } })
      const turnplateOp = useTransformOptions(turnplateData.items, 'title', 'id')
      const sudokuData = await sudokuApi.paginator({ filters: { ...filters, status: 'normal' } })
      const sudokuOp = useTransformOptions(sudokuData.items, 'title', 'id')
      Object.assign(activityList.value, turnplateOp, sudokuOp)
      break
  }
}
onFocus(curForm.value.event_id)
</script>
