<template>
  <uc-layout-form class="m-growth padding-bottom-80" :is-cancel="false" @submit="handleSubmit">
    <a-space direction="vertical" :size="20">
      <uc-row>
        <uc-col>
          <a-card title="基本信息" class="h-fill">
            <!-- <a-form-item label="分享海报" name="poster" class="required photo-flex">
          <uc-upload
            :list="formState.poster ? [formState.poster] : []"
            :max-length="1"
            size="large"
            @update:list="data => (formState.poster = data[0])"
          />
        </a-form-item> -->
            <a-form-item label="有效期限" class="required">
              <a-input-number
                v-model:value.trim="formState.effective_cycle"
                :min="0"
                placeholder="请输入积分有效期，按天计算（0表示不限制）"
              />
            </a-form-item>
            <a-form-item label="消费抵扣" class="required">
              <a-input-number
                v-model:value.trim="formState.consume_grow_magnification"
                :min="0"
                placeholder="请输入每消费1元抵扣积分值"
              />
            </a-form-item>
          </a-card>
        </uc-col>
        <uc-col>
          <a-card v-if="formState.basic.length" title="基础任务">
            <a-form-item
              v-for="(item, index) in formState.basic"
              :key="index"
              :label="item.label"
              name="basic"
              class="required"
            >
              <a-input-number v-model:value="item.form.first_value" :min="0" placeholder="请输入积分值" />
            </a-form-item>
          </a-card>
        </uc-col>
        <uc-col>
          <a-card v-if="formState.consume.length" title="消费任务">
            <type-item
              v-for="item in formState.consume"
              :key="item.key"
              v-model:form="item.form"
              :model="selectEventIdForModel(item.value)"
              :label="item.label"
              :addon-after="item.addonAfter"
              name="consume"
              required
            />
          </a-card>
        </uc-col>
        <uc-col>
          <a-card v-if="formState.active.length" title="活跃任务">
            <type-item
              v-for="item in formState.active"
              :key="item.key"
              v-model:form="item.form"
              :model="selectEventIdForModel(item.value)"
              :label="item.label"
              name="active"
              required
            />
          </a-card>
        </uc-col>
      </uc-row>
    </a-space>
  </uc-layout-form>
</template>
<script setup>
import { message } from 'ant-design-vue'
import typeItem from './type-item.vue'
import { useFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { taskTypes, limitTypes, taskList } from '../../enums'
import { useGrowthTask } from '../../useTransformData'
import { creditTasksApi } from '../../api'
import { cloneDeep } from 'lodash'
import { InfoCircleFilled } from '@ant-design/icons-vue'
import formatters from '@/utils/formatters'

const router = useRouter()
const { id } = useRoute().params
const { transformToShow, transformToRequest } = useGrowthTask()

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  effective_cycle: '',
  basic: [],
  consume: [],
  active: []
})

const validatorArr = type => (_, value) => {
  // old
  const validField = ['first_value']
  type !== taskTypes.basic && validField.push('limit_value', 'every_time_value')

  // const validField = ['limit_value', 'first_value', 'every_time_value']
  const label = value.find(item =>
    validField.some(key => item.form[key] === null || item.form[key] === undefined)
  )?.label
  if (label) return Promise.reject(`${label}信息不完整`)
  return Promise.resolve()
}

const selectEventIdForModel = value => {
  return 'one'
  // old
  if (
    [taskList.signActivity, taskList.inviteActivity, taskList.registerUser, taskList.luckDrawActivity].includes(value)
  ) {
    return 'three'
  } else if (value == taskList.completeInfo) {
    return 'two'
  }
  return 'one'
}

setFormRules({
  effective_cycle: { required: true, message: '请输入积分有效期，按天计算（0表示不限制）' },
  basic: { validator: validatorArr(taskTypes.basic) },
  consume: { validator: validatorArr(taskTypes.consume) },
  active: { validator: validatorArr(taskTypes.active) }
})

// 加载数据
const loadData = async () => {
  const data = await creditTasksApi.get(1, { relations: ['rules'] })
  data.consume_grow_magnification /= 100

  setFormState(transformToShow(data))
}

useLoadingMessage(loadData(), {
  loadingText: '正在加载数据'
})

// 提交
const handleSubmit = async () => {
  if (!(await validateForm())) return

  let params = cloneDeep(formState.value)
  params.consume_grow_magnification *= 100
  params.basic.forEach(i => {
    Object.assign(i.form, {
      every_time_value: 0,
      limit_value: 0
    })
  })

  await creditTasksApi.replace(1, transformToRequest(params))
  message.success('操作成功')
}
</script>
