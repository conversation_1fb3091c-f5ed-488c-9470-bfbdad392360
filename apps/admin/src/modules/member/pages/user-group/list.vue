<template>
  <uc-layout-list title="用户分群">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" placeholder="人群名称" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.type" placeholder="创建方式" :options="createMethod.options()" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" @click="onManualAdd">
          手动上传人群
        </a-button>
        <a-button type="primary" @click="onCustomAdd">
          自定义人群
        </a-button>
      </a-space>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="人群名称" data-index="title" ellipsis />
        <a-table-column title="用户数" data-index="user_number" width="120px" align="right" />
        <a-table-column title="创建方式" width="150px">
          <template #default="{ record }">
            {{ createMethod.filter(record.type).label }}
          </template>
        </a-table-column>
        <a-table-column title="最后计算时间" data-index="updated_at" width="200px" />
        <a-table-column title="操作" width="120px">
          <template #default="{ record }">
            <router-link :to="{ name: 'member-user-operation-add', query: { userGroupId: record.id } }">
              <a-button type="link">
                去运营
              </a-button>
            </router-link>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal :visible="customModalVisible" title="自定义人群" @cancel="onCustomModalCancel">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="人群名称" class="required">
        <a-input v-model:value="customFormState.title" :maxlength="30" placeholder="请输入人群名称，不超过30字" />
      </a-form-item>
      <a-form-item label="人群接口" class="required">
        <a-input v-model:value="customFormState.require_url" placeholder="请输入人群接口API" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onCustomModalCancel">
        取消
      </a-button>
      <a-button type="primary" @click="handleCustomSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
  <a-modal :visible="manualModalVisible" title="手动上传人群" @cancel="onManualModalCancel">
    <a-form :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item label="人群名称" class="required">
        <a-input v-model:value="manualFormState.title" :maxlength="30" placeholder="请输入人群名称，不超过30字" />
      </a-form-item>
      <a-form-item label="手机号码" class="required">
        <a-textarea
          v-model:value="manualFormState.phone_numbers"
          placeholder="请输入用户手机号码，一行一个"
          :auto-size="{ minRows: 4, maxRows: 10 }"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="onManualModalCancel">
        取消
      </a-button>
      <a-button type="primary" @click="handleManualSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useModalVisible } from '@/composables/useToggles'
import { userGroupApi } from '../../api'
import { createMethod } from '../../enums'
import router from '@/router'
import { cloneDeep } from 'lodash'
import { message } from 'ant-design-vue'

const { statusFilter, statusList, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus()

// 表格
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  userGroupApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)
const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  type: undefined
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await userGroupApi.delete(id)
  message.success('删除成功')
  setPage()
}

// 自定义人群模态框
const { modalVisible: customModalVisible, setModalVisible: customSetModalVisible } = useModalVisible()
const {
  formState: customFormState,
  setFormRules: customSetFormRules,
  resetFormState: customResetFormState,
  validateForm: customValidateForm
} = useFormState({
  title: undefined,
  require_url: undefined
})

customSetFormRules({
  title: { required: true, message: '请输入人群名称' },
  require_url: { required: true, message: '请输入人群接口API' }
})

const onCustomModalCancel = () => {
  customSetModalVisible(false)
  customResetFormState()
}

const onCustomAdd = () => {
  customResetFormState()
  customSetModalVisible(true)
}

const handleCustomSubmit = async () => {
  if (!(await customValidateForm())) return
  await userGroupApi.create({ ...customFormState.value, type: createMethod.custom })
  message.success('创建成功')
  setPage()
  onCustomModalCancel()
}

// 手动上传人群模态框
const { modalVisible: manualModalVisible, setModalVisible: manualSetModalVisible } = useModalVisible()
const {
  formState: manualFormState,
  setFormRules: manualSetFormRules,
  resetFormState: manualResetFormState,
  validateForm: manualValidateForm
} = useFormState({
  title: undefined,
  phone_numbers: undefined
})

manualSetFormRules({
  title: { required: true, message: '请输入人群名称' },
  phone_numbers: { required: true, message: '请输入用户手机号码' }
})

const onManualModalCancel = () => {
  manualSetModalVisible(false)
  manualResetFormState()
}

const onManualAdd = () => {
  manualResetFormState()
  manualSetModalVisible(true)
}

const handleManualSubmit = async () => {
  if (!(await manualValidateForm())) return
  await userGroupApi.create({ ...manualFormState.value, type: createMethod.manual })
  message.success('创建成功')
  setPage()
  onManualModalCancel()
}
</script>
