import { onMounted } from 'vue'
import { tagsApi } from './api'

export function useUserTag(config = {
  onMounted: true
}) {
  const userTagList = ref()
  const userTagOptions = ref([])
  const userTagLoading = ref(false)

  const load = async (query = {}) => {
    userTagLoading.value = true
    const res = await tagsApi.list({...query})
    userTagLoading.value = false
    userTagList.value = res
    userTagOptions.value = []
    userTagList.value.forEach((item) => {
      userTagOptions.value.push({
        label: item.name,
        value: item.id 
      })
    })
  }

  if(config.onMounted) {
    onMounted(load)
  }

  const userTagFilterOption = (input, option) => {
    const label = userTagOptions.value.find(item => item.value === option.value)?.label || ''
    return label.toLowerCase().includes(input.toLowerCase())
  }

  return {
    userTagList,
    userTagOptions,
    load,
    userTagLoading,
    userTagFilterOption
  }
}