import { apiFactory } from '@/api'
export const userApi = apiFactory.restful('/user/accounts')
export const updateIntegralApi = userId => apiFactory.command(`/user/${userId}/credit`) // 增减积分
export const updateIntegralUpdateApi = userId => apiFactory.command(`/user/${userId}/credit/update`) // 增减积分

export const creditTasksApi = apiFactory.restful(`/credit-tasks`) // 积分任务
export const growthValueTasksApi = apiFactory.restful('/growth-value-tasks') // 成长值
export const userLevelSettingApi = apiFactory.command('/setting/settings/user-level') // 用户等级设置
export const userLevelSettingUpdateApi = apiFactory.command('/setting/settings/user-level/update') // 用户等级设置
export const userLevelsApi = apiFactory.restful('/user/user-levels') // 获取会员等级列表
export const userLevelsDisableApi = id => apiFactory.command(`/user/user-levels/${id}/disable`) // 停用会员等级
export const giftApi = apiFactory.restful('/gift/gifts')
export const awardOptionsApi = apiFactory.restful('/user-dispatch-prize/options') // 奖项选项

export const shopOrdersApi = apiFactory.restful(`/shop-order/shop-orders`)
export const userCreditApi = apiFactory.restful(`/user/credit-record`) // 积分记录
export const userStatsAction = accountId => apiFactory.command(`/user/accounts/${accountId}/statistics`) // 统计数据
export const userDetailsAction = accountId => apiFactory.command(`/user/accounts/${accountId}`) // 用户详情
export const shopStyleActions = apiFactory.command('/page-config/mp-custom/store')
export const creditDetailedApi = apiFactory.restful('/user/credit-record/config')
export const userGroupApi = apiFactory.restful('/user/user-groups') // 用户分群
export const userOperationApi = apiFactory.restful('/user/user-operations') // 人群运营
export const pushTemplateApi = apiFactory.command('/notifies') // 推送模板
export const newsParamApi = apiFactory.command('/notifies/parameter-config') // 消息参数

export const blockAll = apiFactory.restful('/user/user-blacks') // 全部黑名单
export const userSettingApi = apiFactory.command('/setting/settings/user-config') // 用户设置
export const cancelBlock = id => apiFactory.command(`/user/user-blacks/${id}/cancel`) // 移除黑名单

export const userInfoApi = userId => apiFactory.command(`/user/${userId}/user-info/update`) // 更新用户信息
export const trackApi = apiFactory.restful(`/track`) // 用户统计信息
export const buyCartApi = userId => apiFactory.restful(`/user/${userId}/shop-carts`) // 购物车
export const privacyAuthorizationLogApi = userId =>
  apiFactory.restful(`/user/accounts/${userId}/privacy-authorization-logs`) // 账号授权记录


export const tagsApi = apiFactory.restful('/tags/user-tags') // 标签

export const tagsUserAnalysisApi = apiFactory.restful('/tags/user-analysis') // 分析策略

export const userTaggedTagApi = apiFactory.restful('/tags/user-tagged-tags') // 用户标签

export const exportAccountApi = `${import.meta.env.VITE_API_BASE_URL}/user/accounts/excel/export` // 会员信息-导出