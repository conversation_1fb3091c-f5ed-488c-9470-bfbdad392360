import { cloneDeep } from 'lodash'
import { useTransformImg } from '@/composables/useTransformFormat'
import { ruleTypes, taskTypes, taskList, giftType, awardBizType, levelBizType } from './enums'
import { usePrizeAwardUpdate, useTransformPrize } from './usePrizeAward'

/**
 * 成长值数据转换
 */
export const useGrowthTask = () => {
  /**
   * 转化成显示的数据
   */
  const transformToShow = data => {
    try {
      const cloneData = cloneDeep(data)
      taskTypes.option().forEach(item => {
        cloneData[item.value] = []
      })
      cloneData.rules.forEach(item => {
        const { title, type, event_id } = item
        const taskItem = { label: title, event_id }
        Object.assign(taskItem, { form: item })
        cloneData[type].push(taskItem)
      })
      return cloneData
    } catch (error) {
      console.log('error', error)
    }
  }
  // const needField = ['id','limit_type','first_value','every_time_value','limit_value']
  /**
   * 转化成请求的数据
   */
  const transformToRequest = data => {
    const cloneData = cloneDeep(data)
    const rules = []
    taskTypes.option().forEach(item => {
      const list = cloneData[item.value].map(({ form }) => {
        // 无此逻辑？默认为0，目前不会根据条件修改输入的值
        // item.value === taskTypes.basic && (form.every_time_value = form.limit_value = 0)
        return form
      })
      rules.push(...list)
    })
    cloneData.rules = rules
    return cloneData
  }

  return { transformToShow, transformToRequest }
}

/**
 * 会员等级编辑
 */
export const useMemberLevel = () => {
  const transformKeys = ['photo_url']
  const { batchTransformImg } = useTransformImg()
  const { onPrizeDefault } = usePrizeAwardUpdate()
  const { transformPrizesRequest } = useTransformPrize()

  const prizeDefault = {
    award_settings: [onPrizeDefault({ type: giftType.entity })]
  }

  /**
   * 初始化规则数据
   */
  const initRule = () => ({
    type: ruleTypes.and,
    growth_value: '',
    growth_times: '',
    down_level: ''
  })

  /**
   * 升级默认值
   */
  const upRuleDefault = () => ({
    award_settings: [onPrizeDefault({ biz_type: levelBizType.user_level })],
    type: levelBizType.user_level
  })

  /**
   * 生日默认值
   */
  const birthdayRuleDefault = () => ({
    award_settings: [onPrizeDefault({ biz_type: levelBizType.user_birthday })],
    type: levelBizType.user_birthday
  })

  /**
   * 转化成显示的数据
   */
  const transformToShow = data => {
    const cloneData = cloneDeep(data)
    batchTransformImg(cloneData, 'array', transformKeys)

    const upRules = levelBizType.groupType(cloneData.rules, levelBizType.user_level)
    const birthdayRules = levelBizType.groupType(cloneData.rules, levelBizType.user_birthday)

    !upRules.length && upRules.push(upRuleDefault())
    !birthdayRules.length && birthdayRules.push(birthdayRuleDefault())

    cloneData.upRules = upRules
    cloneData.birthdayRules = birthdayRules

    cloneData.rule = Object.assign(initRule(), cloneData.rule || {})
    cloneData.rule.down_level = cloneData.rule.down_level || undefined

    return cloneData
  }

  /**
   * 转化成请求的数据
   */
  const transformToRequest = data => {
    const cloneData = cloneDeep(data)
    batchTransformImg(cloneData, 'string', transformKeys)
    Object.assign(cloneData, { is_enable: true })

    cloneData.upRules.forEach(rule => {
      transformPrizesRequest(rule)
    })
    cloneData.birthdayRules.forEach(rule => {
      transformPrizesRequest(rule)
    })
    cloneData.rules = cloneData.upRules.concat(cloneData.birthdayRules)

    return cloneData
  }

  return { initRule, upRuleDefault, birthdayRuleDefault, transformToShow, transformToRequest }
}
