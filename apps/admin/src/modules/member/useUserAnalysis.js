import { ref } from 'vue'
import { tagsUserAnalysisApi } from './api'

export function useUserAnalysis(query = {}) {
  const analysisList = ref()
  const analysisOptions = ref([])
  tagsUserAnalysisApi.list({...query}).then(res => {
    analysisList.value = res

    analysisOptions.value = []
    analysisList.value.forEach((item) => {
      analysisOptions.value.push({
        label: item.title,
        value: item.id,
        type: item.type,
      })
    })
  })

  return {
    analysisList,
    analysisOptions
  }
}

