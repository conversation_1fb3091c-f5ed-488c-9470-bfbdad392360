export default {
  path: 'member',
  meta: {
    title: '会员',
    antIcon: 'CrownOutlined'
  },
  children: [
    {
      path: 'member-level',
      name: 'member-level',
      meta: {
        title: '会员等级',
        keepAlive: true
      },
      component: () => import('./pages/member-level/list')
    },
    {
      path: 'member-level-add',
      name: 'member-level-add',
      meta: {
        title: '新增会员等级'
      },
      component: () => import('./pages/member-level/edit'),
      hidden: true
    },
    {
      path: 'member-level-edit/:id',
      name: 'member-level-edit',
      meta: {
        title: '编辑会员等级'
      },
      component: () => import('./pages/member-level/edit'),
      hidden: true
    },
    {
      path: 'member-growth-value-tasks',
      name: 'member-growth-value-tasks',
      meta: {
        title: '成长任务',
      },
      component: () => import('./pages/growth-value-tasks/index')
    },
    {
      path: 'member-credit-tasks',
      name: 'member-credit-tasks',
      meta: {
        title: '积分任务',
        keepAlive: true
      },
      component: () => import('./pages/credit-tasks/index')
    },
    {
      path: 'user-list',
      name: 'user-list',
      meta: {
        title: '全部用户',
        keepAlive: true
      },
      component: () => import('./pages/user/list')
    },
    {
      path: 'user-tags',
      name: 'user-tags',
      meta: {
        title: '用户标签',
        keepAlive: true
      },
      component: () => import('./pages/user-tags/list')
    },
    {
      path: 'user-details/:id/:user_id',
      name: 'user-details',
      meta: {
        title: '用户详情'
      },
      component: () => import('./pages/user/details'),
      hidden: true
    },
    {
      path: 'user-group-list',
      name: 'member-user-group-list',
      meta: {
        title: '用户分群',
        keepAlive: true
      },
      component: () => import('./pages/user-group/list')
    },
    {
      path: 'user-operation-list',
      name: 'member-user-operation-list',
      meta: {
        title: '人群运营',
        keepAlive: true
      },
      component: () => import('./pages/user-operation/list')
    },
    {
      path: 'user-operation-add',
      name: 'member-user-operation-add',
      meta: {
        title: '新增人群运营'
      },
      component: () => import('./pages/user-operation/edit'),
      hidden: true
    },
    {
      path: 'user-operation-edit',
      name: 'member-user-operation-edit',
      meta: {
        title: '编辑人群运营'
      },
      component: () => import('./pages/user-operation/edit'),
      hidden: true
    },
    {
      path: 'block-list',
      name: 'block-list',
      meta: {
        title: '全部黑名单',
        keepAlive: true
      },
      component: () => import('./pages/block-list/list')
    }
  ]
}
