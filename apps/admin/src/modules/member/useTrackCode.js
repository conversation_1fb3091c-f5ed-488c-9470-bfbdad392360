import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { channelTrackApi } from '@/modules/data/api'

export function useTrackCode(callback = null, filters = {}) {
  const { data: trackCodes } = useApiRequest(() => {
    return channelTrackApi.list({ ...filters }).then(cates => (callback ? callback(cates) : cates))
  })

  return {
    trackCodes
  }
}

export function useTrackCodeOptions(filters) {
  const { trackCodes: trackCodesOptions } = useTrackCode(cates => useTransformOptions(cates, 'title', 'code'), filters)
  return {
    trackCodesOptions
  }
}

export function useTrackCodeFilter(arr, value) {
  if (arr) {
    return arr.find(item => item.value == value) ?? {}
  }
}
