<template>
  <!-- 表格 -->
  <uc-layout-list title="操作日志">
    <template #filter>
      <a-form-item name="operation">
        <a-input-group compact>
          <a-select v-model:value="formState.conditionKey" class="w-120" placeholder="请选择" :options="operation.options()" @change="changeConditionKey()" />
          <a-input v-model:value.trim="formState.conditionValue" class="w-420" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item class="interval">
        <a-cascader v-model:value="formState.operationModule" class="w-200" :options="cascaderOptions" change-on-select placeholder="操作模块" />
      </a-form-item>
      <a-form-item class="alone interval">
        <a-range-picker v-model:value="formState.created_at" class="w-250">
          <template #suffixIcon>
            <CalendarOutlined />
          </template>
        </a-range-picker>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table :data-source="data.items" :pagination="stdPagination(data)" row-key="id" :loading="loading" @change="setPage">
        <a-table-column title="操作人" data-index="operator" width="150px" ellipsis />
        <a-table-column title="操作模块" width="170px">
          <template #default="{ record }">
            {{ moduleFuc(record.module)?moduleFuc(record.module).label:'' }}/{{ moduleFuc(record.sub_module)?moduleFuc(record.sub_module).label:'' }}
          </template>
        </a-table-column>
        <a-table-column title="操作内容" data-index="message" ellipsis />
        <a-table-column title="操作时间" data-index="created_at" width="190px" />
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { CalendarOutlined } from '@ant-design/icons-vue'
import { operationLogApi, operationLogCategoryTreeApi } from '../api'
import { operation } from '../enums'

const router = useRouter() // 路由操作

const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: 'operator',
  conditionValue: undefined
}) // 查询表单

const cascaderOptions = ref([])
operationLogCategoryTreeApi.get().then(res => {
  cascaderOptions.value = res
})

const moduleFuc = (value, arr = cascaderOptions.value) => {
  let selectItem = null
  arr.find(item => {
    if (item.value === value) {
      selectItem = item
      return true
    }
    if (item.children) {
      const result = moduleFuc(value, item.children)
      if (result) {
        selectItem = result
        return true
      }
    }
  })

  if (selectItem) {
    const result = JSON.parse(JSON.stringify(selectItem))
    delete result.children
    return result
  } else {
    return null
  }
}

// 改变就清空
const changeConditionKey = () => {
  formState.value.conditionValue = undefined
}

const filterParams = () => {
  if (formState.value.conditionKey && formState.value.conditionValue) {
    return { [formState.value.conditionKey]: '%' + formState.value.conditionValue + '%' }
  }
  return {}
}

const filterCreatedAt = () => {
  if (formState.value.created_at) {
    return { created_at: formState.value.created_at }
  }
  return {}
}

const filterModule = () => {
  if (formState.value.operationModule) {
    return formState.value.operationModule.length == 1
      ? { module: formState.value.operationModule[0] }
      : { sub_module: formState.value.operationModule[1] }
  }
  return {}
}

// 表格请求
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  operationLogApi.paginator({
    filters: useTransformQuery(
      { ...filterModule(), ...filterParams(), ...filterCreatedAt() },
      { created_at: 'dateRange' }
    ),
    offset,
    limit
  })
)

onRestFormState(() => setPage())
</script>
<style scoped lang="less">
</style>
