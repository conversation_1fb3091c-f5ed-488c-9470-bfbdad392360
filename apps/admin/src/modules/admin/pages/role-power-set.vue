<template>
  <div class="role">
    <a-card title="角色权限" class="alone">
      <div class="role-flex">
        <!-- 左侧菜单 -->
        <div class="flex-left">
          <a-menu v-model:selectedKeys="selectedKeys" style="width: 200px" mode="inline" @click="checkSelect">
            <a-menu-item v-for="(item, index) in listData" :key="index" style="margin-top: 12px">
              {{ item.name }}
              <span>&nbsp;&nbsp;{{ `(${item?.admin?.length}人)` }}</span>
            </a-menu-item>
          </a-menu>
        </div>
        <!-- 右侧复选框 -->
        <div class="flex-right">
          <div v-for="item in checkFor" :key="item.title" class="right-for">
            <div class="right-title">
              {{ item.title }}
            </div>
            <div class="right-two">
              <template v-for="items in item.child" :key="items.code">
                <a-checkbox
                  :checked="getChecked(items.id)"
                  :disabled="isAdmin || (!isAdmin && [25, 26].includes(items.id))"
                  :class="{ fontColor: !items.hidden, fixation: true }"
                  class="m-l-0 m-r-8"
                  @change="setChecked(items.id)"
                >
                  {{ items.title }}
                </a-checkbox>
              </template>
            </div>
          </div>
        </div>
      </div>
    </a-card>
    <div class="button_box">
      <!-- 超级管理员时禁用 -->
      <a-button style="margin-left: 10px" type="primary" :disabled="isAdmin" @click="saveRoles()">
        保存
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { roleApi, permissionsApi } from '../api'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'

const isAdmin = ref(false)
// 左侧菜单栏
const listData = ref([])
// 设置初始化选中
let selectedKeys = ref([0])
const getRoles = () => {
  roleApi.get('', { relations: ['admin'] }).then(res => {
    listData.value = res.items
    //初始化选中状态
    checkValue.value = cloneDeep(listData.value[selectedKeys.value].permission_ids)
  })
}
getRoles()

// 切换菜单栏
const checkSelect = async ({ key }) => {
  // 是否是超级管理员 super:1是---拥有全部权限,并且禁用选择
  isAdmin.value = !!cloneDeep(listData.value[key].super)
  if (isAdmin.value) {
    const data = await permissionsApi.get()
    const checkArr = []
    data.forEach(item => {
      item.child.forEach(item2 => {
        checkArr.push(item2.id)
      })
    })
    checkValue.value = checkArr
  } else {
    // 改变选中结果，
    checkValue.value = cloneDeep(listData.value[key].permission_ids)
  }
}

// 右侧复选框
// 复选框-选项
let checkFor = ref([])
permissionsApi.get().then(res => {
  checkFor.value = res
})

//选中的复选框项
let checkValue = ref([])
//按钮是否选中
const getChecked = value => checkValue.value.find(item => item == value)

//改变复选框状态
const setChecked = value => {
  for (let i = 0; i < checkValue.value.length; i++) {
    if (checkValue.value[i] == value) {
      if (checkValue.value.length > 1) {
        checkValue.value.splice(i, 1)
      } else {
        message.error('至少保留一个权限')
      }
      return
    }
  }
  checkValue.value.push(value)
}

const saveRoles = async () => {
  await roleApi.update(listData.value[selectedKeys.value].id, { permission_ids: checkValue.value })
  message.success('修改成功')
  getRoles()
}
</script>

<style scoped lang="less">
.alone {
  :deep(.ant-card-body) {
    padding: 0;
    padding-top: 1px;
  }

  :deep(.ant-menu-item-selected) {
    &:after {
      border: none;
      opacity: 0;
    }

    &:before {
      position: absolute;
      top: 0;
      left: 0;
      bottom: 0;
      border-left: 3px solid #1890ff;
      content: '';
      transform: scaleY(1);
      opacity: 1;
      transition: transform 0.15s cubic-bezier(0.645, 0.045, 0.355, 1),
        opacity 0.15s cubic-bezier(0.645, 0.045, 0.355, 1);
    }
  }
}

.role {
  padding-bottom: 96px;

  .button_box {
    width: 100%;
    background: #fff;
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9;
  }
}

.role-flex {
  display: flex;

  .flex-left {
    width: 200px;
    border-right: 1px solid #f0f0f0;
  }

  .flex-right {
    padding: 12px 30px;
    width: 100%;

    .right-for {
      display: flex;
      flex-flow: column;
      padding-bottom: 25px;

      .right-title {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        height: 40px;
        font-weight: 600;
      }

      .right-two {
        margin-bottom: 5px;
      }
    }
  }
}
</style>
