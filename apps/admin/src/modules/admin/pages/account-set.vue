<template>
  <a-modal
    :visible="modalVisible"
    :title="formState.id ? '编辑账号' : '新增账号'"
    @ok="handleCreate"
    @cancel="setModalVisible(false)"
  >
    <a-form>
      <a-form-item :label="formState.id ? '用户账号' : '登录账号'" name="account" class="required">
        <a-input
          v-model:value.trim="formState.account"
          :disabled="formState.id"
          placeholder="请输入手机号码，初始密码888888"
        />
      </a-form-item>
      <a-form-item label="用户姓名" name="name" class="required">
        <a-input v-model:value.trim="formState.name" placeholder="请输入用户姓名，不超过10字" :maxlength="10" />
      </a-form-item>
      <a-form-item label="所属角色" name="role_id" class="required">
        <a-select v-model:value="formState.role_id" placeholder="所属角色" allow-clear :options="roleOptions" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>
  <uc-layout-list title="账号设置">
    <template #filter>
      <a-form-item name="name">
        <a-input-group compact>
          <a-select
            v-model:value="accountFormState.conditionKey"
            class="w-120"
            :options="filterSelectVuale.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="accountFormState.conditionValue" placeholder="请输入关键词" class="w-320" />
        </a-input-group>
      </a-form-item>
      <a-form-item name="role_id">
        <a-select v-model:value="accountFormState.role_id" placeholder="所属角色" allow-clear :options="roleOptions" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="accountResetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onCreate">
        新增账号
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="用户姓名" data-index="name" />
        <a-table-column title="登录账号" width="150px">
          <template #default="{ record }">
            {{ $formatters.numberEncryption(record.account) }}
          </template>
        </a-table-column>
        <a-table-column title="所属角色" width="150px">
          <template #default="{ record }">
            {{ useRoleFilter(roleOptions, record.role_id) }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="180px">
          <template #default="{ record }">
            <a-button type="link" @click="handleResetPassword(record)">
              重置密码
            </a-button>
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.role_id == 1"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <template #icon>
                <uc-ant-icon name="QuestionCircleOutlined" type="danger" />
              </template>
              <a-button type="link" class="danger" :disabled="record.role_id == 1">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { accountApi, resetEditPasswordApi } from '../api'
import { useRoleOptions, useRoleFilter } from '../useRole'
import { message } from 'ant-design-vue'
import { phoneVerify } from '@/utils/index'
import { filterSelectVuale } from '../enums'

const { roleOptions } = useRoleOptions()

const {
  formState: accountFormState,
  resetFormState: accountResetFormState,
  onRestFormState: accountOnRestFormState
} = useFormState({
  conditionKey: 'name',
  conditionValue: undefined,
}) // 查询表单

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  accountApi.paginator({
    filters: {
      ...useTransformQuery(
        accountFormState.value.conditionKey == 'name'
          ? { name: accountFormState.value.conditionValue }
          : { account: accountFormState.value.conditionValue },
        {
          name: 'like',
          account: 'like',
        }
      ),
      role_id: accountFormState.value.role_id
    },
    offset,
    limit
  })
)
accountOnRestFormState(() => setPage())

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  account: undefined,
  name: undefined,
  role_id: undefined
})

const { modalVisible, setModalVisible } = useModalVisible()

watch(modalVisible, v => !v && resetFormState())

const onCreate = () => setModalVisible(true)
const onEdit = props => setFormState(props) || setModalVisible(true)

setFormRules({
  account: {
    validator(rule, value) {
      if (!value) return Promise.reject('请输入手机号')
      if (!phoneVerify(value)) return Promise.reject('请输入正确的手机号')
      return Promise.resolve()
    },
    trigger: 'blur'
  },
  name: {
    required: true,
    message: '请输入用户姓名'
  },
  role_id: {
    required: true,
    message: '请选择用户角色'
  }
})

//  处理分类创建/更新
const { loading: btnLoading, setLoading } = useLoading()
const handleCreate = async () => {
  setLoading(true)

  if (!(await validateForm())) {
    setLoading(false)
    return
  }

  try {
    const { id } = formState.value

    if (id) {
      await accountApi.update(id, formState.value)
      message.success('编辑完成')
    } else {
      await accountApi.create(formState.value)
      message.success('创建完成')
    }
    setModalVisible(false)
    resetFormState()
  } finally {
    setLoading(false)
    setPage()
  }
}

const handleResetPassword = async ({ id, name }) => {
  await resetEditPasswordApi(id).post()
  message.success(`操作成功：${name}已重置密码`)
}

const handleDelete = async ({ id }) => {
  await accountApi.delete(id)
  message.success('删除完成')
  setPage()
}

const changeConditionKey = type => (accountFormState.value.conditionValue = undefined)
</script>
<style scoped lang="less"></style>
