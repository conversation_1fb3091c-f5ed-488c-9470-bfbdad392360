import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { roleApi } from './api'

export function useRoles(callback = null,filters={}) {
  const { loading, setLoading } = useLoading()
  const { data: roles, getData: refreshRoles } = useApiRequest(() => {
    setLoading(true)
    return roleApi
      .list({
        ...filters,
      })
      .then((roles) => (callback ? callback(roles) : roles))
      .finally(() => setLoading(false))
  })

  return {
    categoryLoading: loading,
    roles,
    refreshRoles
  }
}

export function useRoleOptions(filters) {
  const { roles: roleOptions } = useRoles((cates) => useTransformOptions(cates, 'name', 'id'),filters)
  return {
    roleOptions
  }
}

export function useRoleFilter(arr,value){
  if(arr){
    return arr.find(item=>item.value==value)?.label
  }
}
