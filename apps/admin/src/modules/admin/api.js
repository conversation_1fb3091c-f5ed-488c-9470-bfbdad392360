import { apiFactory } from '@/api'

export const roleApi = apiFactory.restful('/admin/roles') // 角色权限
export const accountApi = apiFactory.restful('/admin/admins') // 账号管理
export const resetEditPasswordApi = (id) => apiFactory.command(`/admin/admins/${id}/reset-password`) // 重置密码
export const permissionsApi = apiFactory.command(`/admin/roles/permissions`) // 权限
export const operationLogApi = apiFactory.restful(`/operation-log/operation-logs`) // 操作日志
export const operationLogCategoryTreeApi = apiFactory.command(`/operation-log/category-tree`) // 操作日志分类

