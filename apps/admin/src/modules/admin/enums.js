/**
 * 操作人 or 操作内容
 */
 export const operation = Object.freeze({
  /**
   * 操作人
   */
   operator: 'operator',

  /**
   * 操作内容
   */
   message: 'message',

  options() {
    return [
      { label: '操作人', value: this.operator },
      { label: '操作内容', value: this.message },
    ]
  },
})

/**
 * 可筛选的值
 */
export const filterSelectVuale = Object.freeze({
  /**
   * 用户姓名
   */
  name: 'name',

  /**
   * 登录账号
   */
  account: 'account',

  options() {
    return [
      { label: '用户姓名', value: this.name},
      { label: '登录账号', value: this.account},
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})