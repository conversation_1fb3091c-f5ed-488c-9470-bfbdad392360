import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

export const giftApi = apiFactory.restful('/gift/gifts')

export const giftCategoryApi = apiFactory.restful('/gift/categories')

export const giftStockApi = apiFactory.restful('/gift/stocks')

export const importSTockApi = `${import.meta.env.VITE_API_BASE_URL}/gift/stocks/excel/import?token=${state.token}`

export const downloadStockApi = `${import.meta.env.VITE_API_BASE_URL}/gift/stocks/excel/download-template?token=${
  state.token
}`

export const exportStockApi = `${import.meta.env.VITE_API_BASE_URL}/gift/stocks/excel/export?token=${state.token}`

export const creditGoodApi = apiFactory.restful('/gift/credit-goods')

export const userLevelsApi = apiFactory.command('/user/user-levels')

export const giftStockSyncApi = apiFactory.command('/setting/settings/sync-gift-stock')
export const giftStockSyncUpdateApi = apiFactory.command('/setting/settings/sync-gift-stock/update')

export const couponApi = apiFactory.restful('/promotion/coupons')
