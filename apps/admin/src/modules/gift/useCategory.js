import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { giftCategoryApi } from './api'

export function useCategories(callback = null,filters={}) {
  const { loading, setLoading } = useLoading()
  const { data: categories, getData: refreshCategories } = useApiRequest(() => {
    setLoading(true)
    return giftCategoryApi
      .list({
        ...filters,
        sorts: ['+sort']
      })
      .then((cates) => (callback ? callback(cates) : cates))
      .finally(() => setLoading(false))
  })

  return {
    categoryLoading: loading,
    categories,
    refreshCategories
  }
}

export function useCategoryOptions(filters) {
  const { categories: categoryOptions } = useCategories((cates) => useTransformOptions(cates, 'title', 'id'),filters)

  return {
    categoryOptions
  }
}

export function useCategoryFilter(arr,value){
  if(arr){
    return arr.find(item=>item.value==value)?.label
  }
}
