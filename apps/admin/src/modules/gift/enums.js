/**
 * 礼品类型
 */
export const GiftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',

  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 积分
   */
  credit: 'credit',

  /**
   * 成长值
   */
  growthValue: 'growth_value',

  options() {
    return [
      { label: '实物礼品', value: this.entity },
      { label: '虚拟卡券', value: this.invented },
      { label: '购物卡券', value: this.coupon }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value)?.label
  }
})

/**
 * 礼品标签
 */
export const GiftLabel = Object.freeze({
  /**
   * 新品
   */
  newProduct: 'is_new',
  /**
   * 特惠
   */
  preference: 'is_promotion',
  /**
   * 推荐
   */
  recommend: 'is_recommend',

  options() {
    return [
      { label: '新品', value: this.newProduct },
      { label: '特惠', value: this.preference },
      { label: '推荐', value: this.recommend }
    ]
  }
})

/**
 * 礼品收货方式
 */
export const GiftTakeMethod = Object.freeze({
  /**
   * 快递邮寄
   */
  post: 'post',

  /**
   * 线下领取
   */
  offline: 'offline',

  /**
   * 手机号码
   */
  phone: 'phone',

  /**
   * 充值账号
   */
  account: 'account',

  /**
   * 系统直发
   */
  system: 'system',

  options(giftType) {
    const allDisabled = [GiftType.credit, GiftType.growthValue].indexOf(giftType) >= 0

    return [
      {
        label: '快递邮寄',
        value: this.post,
        disabled: (giftType && giftType != GiftType.entity) || allDisabled
      },
      {
        label: '线下领取',
        value: this.offline,
        disabled: (giftType && giftType != GiftType.entity) || allDisabled
      },
      {
        label: '手机号码',
        value: this.phone,
        disabled: (giftType && giftType == GiftType.entity) || giftType == GiftType.coupon || allDisabled
      },
      {
        label: '充值账号',
        value: this.account,
        disabled: (giftType && giftType == GiftType.entity) || giftType == GiftType.coupon || allDisabled
      },
      {
        label: '系统直发',
        value: this.system,
        disabled: (giftType && giftType != GiftType.coupon) || allDisabled
      }
    ]
  }
})

export const GiftOption = Object.freeze({
  /**
   * 礼品编码
   */
  giftCoding: 'code',
  /**
   * 礼品名称
   */
  giftName: 'title',

  option() {
    return [
      { label: '礼品编码', value: this.giftCoding },
      { label: '礼品名称', value: this.giftName }
    ]
  }
})

/**
 * 礼品兑换上限
 */
export const GiftExchangeCap = Object.freeze({
  /**
   * 每人上限
   */
  person: 'user_times',
  /**
   * 每月上限
   */
  month: 'month_times',
  /**
   * 周期上限
   */
  week: 'cycle_times',

  options() {
    return [
      { label: '每人上限', value: this.person },
      { label: '每月上限', value: this.month },
      { label: '周期上限', value: this.week }
    ]
  }
})
