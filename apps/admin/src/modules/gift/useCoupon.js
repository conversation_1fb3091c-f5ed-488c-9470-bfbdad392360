import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { couponApi } from './api'

export function useUserLevel(callback = null, filters = {}) {
  const { data: coupons } = useApiRequest(() => {
    return couponApi.list({ filters }).then(res => (callback ? callback(res) : res))
  })

  return {
    coupons
  }
}

export function useCouponOptions(filters) {
  const { coupons: couponOptions } = useUserLevel(res => useTransformOptions(res, 'title', 'code'), filters)
  return {
    couponOptions
  }
}
