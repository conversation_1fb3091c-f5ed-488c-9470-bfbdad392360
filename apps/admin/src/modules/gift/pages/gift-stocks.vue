<template>
  <uc-layout-list title="礼品库存" class="m-list">
    <template #filter>
      <a-form-item name="title" class="user-input">
        <a-input-group compact>
          <a-select v-model:value="formState.option" @change="handGiftOption">
            <a-select-option v-for="item in GiftOption.option()" :key="item.value" :value="item.value">
              {{ item.label }}
            </a-select-option>
          </a-select>
          <a-input v-model:value.trim="formState[codeOrTitle()]" placeholder="请输入关键词" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <div class="user-flex">
          <div v-if="!stockSyncStep.value.checked" @click="handleDown">
            <a :href="downloadStockApi" class="user-flex icon-margin">
              <uc-svg-icon name="download" size="22px" color="#409eff" />
              <span>下载模板</span>
            </a>
          </div>
          <div v-if="!stockSyncStep.value.checked">
            <a-upload
              :action="importSTockApi"
              :before-upload="beforeUpload"
              :on-error="errorUpload"
              :show-upload-list="false"
              @change="handleChange"
            >
              <a href="javascript:;" class="user-flex icon-margin">
                <uc-svg-icon name="tolead" size="22px" color="#409eff" />
                <span>导入库存</span>
              </a>
            </a-upload>
          </div>
          <div>
            <a href="javascript:;" class="user-flex">
              <a-switch v-model:checked="stockSyncStep.value.checked" @change="updateStockSync" />
              <span class="m-l-10">{{ stockSyncStep.value.checked ? '开启库存同步' : '关闭库存同步' }}</span>
            </a>
          </div>
        </div>
        <a-button :href="handleExport()">导出</a-button>
      </a-space>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="礼品名称/规格" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-if="record.gift"
              v-bind="record.gift"
              :url="record.gift.photo_urls[0]"
              :label="record.code"
              symbol=":&nbsp"
              :subtit="record.gift.attr"
            />
            <uc-img-text v-else-if="record.code" :subtit="record.code" />
          </template>
        </a-table-column>
        <a-table-column
          title="库存数"
          :align="stockSyncStep.value.checked ? 'right' : 'center'"
          :width="stockSyncStep.value.checked ? '150px' : '200px'"
        >
          <template #default="{ record }">
            <span v-if="stockSyncStep.value.checked">{{
              $formatters.thousandSeparator(record.stock, false, false)
            }}</span>
            <a-input
              v-else
              v-model:value="record.stock"
              style="width: 100px"
              class="t-center"
              placeholder="库存数"
              @blur="handleUpdateStock(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="占用" data-index="hold" width="150px" align="right" />
        <a-table-column title="剩余" width="150px" align="right">
          <template #default="{ record }">
            {{ record.stock - record.hold }}
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import {
  giftStockApi,
  importSTockApi,
  downloadStockApi,
  exportStockApi,
  giftStockSyncApi,
  giftStockSyncUpdateApi
} from '../api'
import { GiftOption } from '@/modules/gift/enums'
import { useStore } from '@/store/auth'

const { state } = useStore()

const { formState, resetFormState, onRestFormState } = useFormState({
  option: 'code',
  code: undefined,
  title: undefined
})

const codeOrTitle = type => {
  const obj = formState.value.option == 'code' ? 'code' : 'title'
  return obj
}

const handGiftOption = type => {
  //改变就清空
  if (type == 'code') {
    formState.value.title = undefined
  } else {
    formState.value.code = undefined
  }
}

const stockSyncStep = ref({
  value: { checked: true }
})

const loadStockSync = () => {
  giftStockSyncApi.get().then(res => {
    stockSyncStep.value = res
  })
}

const updateStockSync = async () => {
  await giftStockSyncUpdateApi.post(stockSyncStep.value)
  message.success('操作成功')
}

nextTick(() => {
  loadStockSync()
})

// 表格查询
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  giftStockApi.paginator({
    filters: useTransformQuery({ code: formState.value.code }, { code: 'like' }),
    relation_filters: formState.value.title ? { gift: { title: `%${formState.value.title}%` } } : null,
    offset,
    limit,
    relations: ['gift']
  })
)
onRestFormState(() => setPage())

// 导出
const handleExport = () => {
  let query = ''
  formState.value.code ? (query = `filters={"code":"%${formState.value.code}%"}`) : ''
  formState.value.title ? (query = `relation_filters={ "gift": { "title": "%${formState.value.title}%" } }`) : ''
  let excelExportUrl = encodeURI(`${exportStockApi}&${query}`)
  return excelExportUrl
}

// 导入
const handleChange = ({ file: { status, response } }) => {
  switch (status) {
    case 'uploading':
      return
    case 'error':
      message.error(response?.data?.message ?? '上传文件失败')
      return
    case 'done':
      if (response.data) {
        message.success('库存导入成功')
        setPage()
      }
      break
    default:
      break
  }
}

const handleDelete = async ({ id }) => {
  await giftStockApi.delete(id)
  message.success('礼品库存删除成功')
  setPage()
}

const handleUpdateStock = async ({ id, stock }) => {
  if (stock === '') {
    return
  }

  await giftStockApi.update(id, { stock })
  message.success('礼品库存更新成功')
  setPage()
}
</script>
<style scoped lang="less">
.m-list {
  a {
    text-decoration: none;
  }
  .user-input {
    :deep(.ant-input-group-wrapper) {
      width: 420px !important;
    }
  }
  .user-flex {
    display: flex;
    align-items: center;
    .u-svg {
      margin-right: 10px;
    }
  }
  .icon-margin {
    margin-right: 30px;
    span {
      margin-left: 5px;
    }
  }
}
</style>
