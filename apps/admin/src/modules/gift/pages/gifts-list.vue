<template>
  <uc-layout-list title="全部礼品">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入礼品名称" />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.type"
          placeholder="礼品类型"
          allow-clear
          :options="GiftType.options()"
        />
      </a-form-item>
      <a-form-item name="category_id">
        <a-select
          v-model:value="formState.category_id"
          placeholder="礼品分类"
          allow-clear
          class="giftType"
          :options="categoryOptions"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增礼品
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="礼品名称/分类" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-if="record.title"
              v-bind="record"
              :url="record.photo_urls[0]"
              :title="record.title"
              :label="GiftType.filterValue(record.type)"
              symbol="&nbsp/&nbsp"
              :subtit="useCategoryFilter(categoryOptions, record.category_id)"
            />
          </template>
        </a-table-column>
        <a-table-column title="占用/剩余/总库存" data-index="age" width="230px" align="right">
          <template #default="{ record }">
            <div v-if="record.stock != null">
              {{ record.stock.hold }} / {{ record.stock.stock - record.stock.hold }} /
              {{ record.stock.stock }}
            </div>
            <div v-else>
              0/0/0
            </div>
          </template>
        </a-table-column>
        <a-table-column title="最后更新日期" width="120px">
          <template #default="{ record }">
            {{ record.updated_at }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button v-if="record.status === 'finished'" type="link" @click="onEdit(record)">
              查看
            </a-button>
            <a-button v-else type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.stock && record.stock.hold > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button
                type="link"
                class="danger"
                :disabled="record.stock && record.stock.hold > 0"
              >
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref, watch, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useApiRequest, usePaginatorApiRequest } from '@/composables/useApiRequest'
import { findIndex } from 'lodash'
import { GiftType } from '../enums'
import { giftApi } from '../api'
import { useCategoryOptions, useCategoryFilter } from '../useCategory'

const { formState, onRestFormState, resetFormState } = useFormState() // 查询表单
const router = useRouter()
const route = useRoute()

// 表格查询
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  giftApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit,
    relations: ['stock', 'category']
  })
)

onRestFormState(() => setPage())

// 分类选项
const { categoryOptions } = useCategoryOptions()

// 新增
const onAdd = () => {
  router.push({
    name: 'gift-add'
  })
}
// 点击编辑
const onEdit = ({ id }) => {
  router.push({
    name: 'gift-edit',
    params: {
      id
    }
  })
}
// 点击删除
const handleDelete = async ({ id }) => {
  giftApi.delete(id).then(res => {
    message.success('礼品删除完成')
    setPage()
  })
}
</script>
