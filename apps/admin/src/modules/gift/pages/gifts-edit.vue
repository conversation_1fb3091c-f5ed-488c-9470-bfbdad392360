<template>
  <uc-layout-form class="m-edit" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="礼品相册" name="photo" class="required photo-flex">
            <uc-upload v-model:list="formState.photo_urls" isdrag show-label :max-length="9" multiple />
          </a-form-item>
          <a-form-item label="礼品类型" name="type" class="required">
            <a-select
              v-model:value="formState.type"
              placeholder="请选择类型"
              class="input-width"
              :options="GiftType.options()"
              @change="onType"
            />
          </a-form-item>
          <a-form-item label="礼品分类" name="category_id" class="required">
            <a-select
              v-model:value="formState.category_id"
              placeholder="请选择分类"
              class="input-width"
              :options="categoryOptions"
            />
          </a-form-item>
          <a-form-item v-if="formState.type === GiftType.coupon" label="购物卡券" name="type" class="required">
            <a-select
              v-model:value="formState.code"
              placeholder="请选择卡券"
              class="input-width"
              :options="couponOptions"
            />
          </a-form-item>
          <a-form-item label="礼品名称" name="title" class="required">
            <a-input
              v-model:value.trim="formState.title"
              class="input-width"
              placeholder="请输入礼品名称，不超过30字"
              :maxlength="30"
            />
          </a-form-item>
          <a-form-item label="礼品编码" name="code" class="required">
            <a-input
              v-model:value.trim="formState.code"
              class="input-width"
              placeholder="请输入礼品编码"
              :disabled="id || formState.type === GiftType.coupon"
            />
          </a-form-item>
          <a-form-item label="礼品规格" name="attr" class="required">
            <a-input v-model:value.trim="formState.attr" class="input-width" :placeholder="attrHint" :maxlength="20" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="发货设置" class="user-radio h-fill">
          <a-form-item label="收货方式：" name="take_type" class="required user-radio">
            <a-radio-group
              v-model:value="formState.take_type"
              name="radioGroup"
              :options="GiftTakeMethod.options(formState.type)"
            />
          </a-form-item>
          <a-form-item label="发货方式" name="hook">
            <a-input v-model:value.trim="formState.hook.hook_url" class="input-width" placeholder="请输入发货API" />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <uc-rich-text v-model="formState.desc" :height="300" placeholder="请输入礼品介绍" />
  </uc-layout-form>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { cloneDeep } from 'lodash'
import { message } from 'ant-design-vue'
import { giftApi, couponApi } from '../api'
import { GiftType, GiftTakeMethod } from '../enums'
import { useCategoryOptions } from '../useCategory'
import { useCouponOptions } from '../useCoupon'

const { categoryOptions } = useCategoryOptions()

const { couponOptions } = useCouponOptions()

const { id } = useRoute().params

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  giftApi
    .get(id, { relations: ['hook'] })
    .then(gift => {
      if (!gift.hook) {
        gift.hook = {
          hook_url: undefined
        }
      }
      setFormState(gift)
    })
    .finally(hideLoading)
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  photo_urls: [],
  type: undefined,
  category_id: undefined,
  title: undefined,
  code: undefined,
  attr: undefined,
  take_type: undefined,
  shipments: undefined,
  desc: undefined,
  hook: {
    hook_url: undefined
  }
})

setFormRules({
  photo_urls: { required: true, message: '请上传礼品相册图片' },
  type: { required: true, message: '请选择礼品类型' },
  category_id: { required: true, message: '请选择礼品分类' },
  title: { required: true, message: '请填写礼品名称' },
  code: { required: true, message: '请选择礼品编码' },
  attr: { required: true, message: '请填写礼品规格' },
  take_type: { required: true, message: '请选择收货方式' },
  desc: { required: true, message: '请输入礼品介绍' }
})

const router = useRouter()

const attrHint = ref('请输入礼品规格，不超过20字')

const onType = value => {
  // 礼品类型为积分--(take_type全部禁用,并且为system)
  if (value == 'credit') {
    setFormState({ ...formState.value, take_type: 'system' })
    attrHint.value = '请输入积分值，且积分值不可重复'
  } else {
    setFormState({ ...formState.value, take_type: undefined })
    attrHint.value = '请输入礼品规格，不超过20字'
  }
}

const handleSubmit = async () => {
  if (!(await validateForm())) {
    return
  }

  const form = cloneDeep(formState.value)
  if (form.hook.hook_url) {
    form.hook.biz_type = 'gift'
    if (id) {
      form.hook.biz_id = id
    }
  } else {
    delete form.hook
  }

  if (id) {
    await giftApi.update(id, form)
    message.success('礼品编辑完成')
  } else {
    await giftApi.create(form)
    message.success('礼品创建完成')
  }
  router.back()
}
</script>
