<template>
  <uc-layout-form v-if="!isLoading" class="m-edit" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="兑换标签：" name="label" class="user-radio">
            <a-checkbox-group v-model:value="formState.label" :options="GiftLabel.options()" />
          </a-form-item>
          <a-form-item label="兑换时间：" name="rangeTime" class="required range-time">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              :disabled-date="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              :disabled-date="disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
            />
          </a-form-item>
          <a-form-item label="兑换礼品：" name="gift_id" class="required">
            <a-select
              v-model:value="formState.gift_id"
              :filter-option="filterOption"
              :options="giftList"
              show-search
              placeholder="请选择兑换礼品，可输入关键词搜索"
              class="input-width"
              :default-active-first-option="false"
              :not-found-content="null"
              :disabled="!canEditGift"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="兑换库存：" name="stock" class="required">
            <a-input-number
              v-model:value.trim="formState.stock"
              class="w-500 input-width"
              placeholder="请输入礼品库存数"
              :min="0"
            />
          </a-form-item>
          <a-form-item name="exchange_limit" label="兑换上限：" class="required">
            <div class="user_fromitem input-width">
              <a-select v-model:value="formState.limit_type">
                <a-select-option v-for="item in GiftExchangeCap.options()" :key="item.value" :value="item.value">
                  {{ item.label }}
                </a-select-option>
              </a-select>
              <a-input-number v-model:value.trim="formState.exchange_limit" placeholder="请输入兑换上限" :min="0" />
            </div>
          </a-form-item>
          <a-form-item name="config" label="参与资格：" class="required">
            <div class="user_quali">
              <a-select
                :value="formState.config.map(({ condition }) => condition)"
                show-arrow
                placeholder="请选择参与资格"
                mode="multiple"
                :options="GiftPartakeQualify"
                class="input-width"
                @change="onChange"
              />
              <a-input
                v-model:value.trim="formState.requirement"
                placeholder="请输入参与资格校验API（选填）"
                class="input-width"
              />
            </div>
          </a-form-item>
          <a-form-item v-if="formState.config.length > 0" name="config" label="兑换消耗：" class="required">
            <template #default>
              <div v-for="item in formState.config" :key="item" class="input-width user_consume">
                <a-input :value="fileValue(item.condition)" class="user_consume_input" disabled />
                <a-input-number
                  v-model:value.trim="item.value"
                  class="w-400"
                  placeholder="请输入兑换消耗积分"
                  :min="0"
                />
              </div>
            </template>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card class="card-rich-text h-fill">
          <uc-rich-text v-model="formState.desc" :height="300" placeholder="请输入兑换说明（不超过500字）" />
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { debounce } from 'lodash-es'
import { GiftLabel, GiftExchangeCap } from '../enums'
import { creditGoodApi, giftApi, userLevelsApi } from '../api'
import { useGiftOptions, useGiftOptionsFilter } from '../useGift'
import { useTransformQuery } from '@/composables/useTransformQuery'

const router = useRouter() // 路由操作
const isLoading = ref(false) // 加载中
const { id } = useRoute().params // 参数
const canEditGift = ref(true)
const GiftPartakeQualify = ref([])
// 参与资格-会员
userLevelsApi.get().then(res => {
  GiftPartakeQualify.value = res
    .filter(i => i.level_name)
    .map(item => {
      return { label: item.level_name, value: item.id }
    })
  GiftPartakeQualify.value.push({ label: '其他资格', value: 0 })
})

// 更改-参与资格
const onChange = (value, option) => {
  const configArr = formState.value.config.map(({ condition }) => condition)
  if (value.length > formState.value.config.length) {
    // 增加
    value.forEach(item => {
      if (!configArr.includes(item)) {
        // 不存在就添加进config
        formState.value.config.push({
          condition: item,
          value: undefined
        })
      }
    })
  } else {
    // 减少
    configArr.forEach((item, index) => {
      if (!value.includes(item)) {
        formState.value.config.splice(index, 1)
      }
    })
  }
}

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  creditGoodApi
    .get(id, {})
    .then(async res => {
      await setFormState(res)
      formState.value.label = formattingArr({
        is_new: res.is_new,
        is_promotion: res.is_promotion,
        is_recommend: res.is_recommend
      })
      canEditGift.value = res.status === 'not_start'
      await setExcludeId()
      await initList()
    })
    .finally(hideLoading)
}

// 表单
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  label: [],
  start_time: null,
  end_time: null,
  gift_id: undefined,
  stock: undefined,
  limit_type: 'user_times',
  exchange_limit: undefined,
  config: [],
  common: undefined,
  gold: undefined,
  platinum: undefined,
  diamond: undefined,
  extreme: undefined,
  other: undefined,
  desc: undefined
})

// 自定义验证
const validator = (_, value) => {
  let message = null
  if (value.length == 0) {
    return Promise.reject(`请选择参与资格`)
  }
  value.some(item => {
    if (!item.value && item.value !== 0) {
      message = `请输入${fileValue(item.condition)}兑换消耗积分`
      return true
    }
  })
  if (message) {
    return Promise.reject(message)
  }
  return Promise.resolve()
}

setFormRules({
  start_time: { required: true, message: '请选择兑换开始时间' },
  gift_id: { required: true, message: '请选择兑换礼品' },
  stock: { required: true, message: '请输入兑换库存' },
  exchange_limit: { required: true, message: '请输入兑换上限' },
  config: { validator: validator, trigger: 'blur' },
  desc: { required: true, message: '请输入兑换说明（不超过500字）' }
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState) // 禁止时间

// 积分商城正在进行中的活动或者未开始活动
const excludeId = ref([])
const setExcludeId = async () => {
  const arr = await creditGoodApi.list()
  excludeId.value = arr.map(item => {
    if (item.status != 'ended') {
      return item.gift_id
    }
  })
  excludeId.value = excludeId.value.filter(item => item)
  if (id) {
    // 去掉自己
    // console.log(excludeId.value,id);
    const index = excludeId.value.findIndex(item => item == formState.value.gift_id)
    excludeId.value.splice(index, 1)
    // console.log(index, excludeId.value, formState.value.gift_id, "excludeId.value------");
  }
}

// 分类列表
const giftList = ref([])
// 初始化20条数--分类列表
const initList = async () => {
  // 积分商城正在进行中的活动或者未开始活动
  const { giftOptions } = await useGiftOptions(20, { id: `![${[excludeId.value]}]` })
  giftList.value = giftOptions
}

// 新增---初始化
setExcludeId()
initList()

const filterOption = (inputValue, { label }) => {
  return !!label && label.includes(inputValue)
}

// 搜索
let handleSearch = async value => {
  if (!!value) {
    const { giftOptionsFilter } = await useGiftOptionsFilter({
      title: `%${value}%`,
      type: '!credit',
      id: `![${[excludeId.value]}]`
    })
    giftList.value = giftOptionsFilter
  } else {
    initList()
  }
}
handleSearch = debounce(handleSearch, 300)

// 过滤值
const fileValue = value => {
  const dataItem = GiftPartakeQualify.value.filter(item => item.value == value)
  return dataItem[0]?.label
}

// 格式化-label ({}->[])-全局
const formattingArr = obj => {
  let arr = []
  Object.keys(obj).forEach(item => {
    obj[item] ? arr.push(item) : ''
  })
  return arr
}

// 格式化-label ([]->{})
const formattingObj = arr => {
  let obj = { is_new: 0, is_promotion: 0, is_recommend: 0 }
  let newObj = {}
  arr.forEach(item => {
    newObj[item] = 1
  })
  return Object.assign(obj, newObj)
}

// 点击提交按钮
const handleSubmit = async () => {
  if (!(await validateForm())) {
    return
  }

  // 处理label
  const query = useTransformQuery(
    { ...formState.value, ...formattingObj(formState.value.label) },
    { start_time: 'datetime', end_time: 'datetime' }
  )

  query.config.forEach(level => {
    level.condition = GiftPartakeQualify.value.find(i => i.value === level.condition).value
  })

  if (id) {
    await creditGoodApi.update(id, query)
    message.success('积分商城编辑完成')
  } else {
    await creditGoodApi.create(query)
    message.success('积分商城创建完成')
  }
  router.back()
}
</script>

<style scoped lang="less">
.m-edit {
  padding-bottom: 80px;
  .user-radio {
    :deep(.ant-radio-inner) {
      border-radius: 0;
    }
  }
  .range-time {
    :deep(.ant-calendar-picker) {
      width: 240px;
    }
    .separator {
      .inline-block();
      width: 20px;
      text-align: center;
    }
  }
  .input-width {
    width: 500px;
  }
  .user_fromitem {
    display: flex;
    :deep(.ant-select) {
      width: 128px;
      margin-right: 10px;
    }
  }

  .user_quali {
    display: flex;
    flex-flow: column;
    :deep(.ant-input) {
      margin-top: 10px;
    }
  }
  .user_consume {
    display: flex;
    margin-bottom: 10px;
    .user_consume_input {
      width: 90px;
      margin-right: 10px;
    }
  }
  .user_consume:last-child {
    margin-bottom: 0px;
  }
}
</style>
