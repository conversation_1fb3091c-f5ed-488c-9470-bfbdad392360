<template>
  <a-modal :visible="modalVisible" title="创建礼品分类" @ok="handleCreate" @cancel="setModalVisible(false)">
    <a-form>
      <a-form-item label="分类名称" name="title" class="required">
        <a-input v-model:value.trim="formState.title" placeholder="请输入分类名称，不超过10字" :maxlength="10" />
      </a-form-item>
      <a-form-item label="分类排序" name="sort" class="required">
        <a-input-number v-model:value.trim="formState.sort" :min="0" :max="99999" placeholder="请输入分类排序值（排序值越大排名越靠前）" :formatter="$formatters.number" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>

  <uc-layout-list title="礼品分类" class="flex">
    <template #extra>
      <a-button type="primary" @click="onCreate">
        新增分类
      </a-button>
    </template>
    <template #list>
      <a-table row-key="id" :pagination="false" :data-source="categories" :loading="categoryLoading">
        <a-table-column class="hideclass" title="分类名称" ellipsis data-index="title" />
        <a-table-column title="分类排序">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              :formatter="$formatters.number"
              :min="0"
              :max="99999"
              class="classify textstyle"
              @blur="handleUpdate(record.id, { sort: record.sort })"
            />
          </template>
        </a-table-column>
        <a-table-column title="是否显示">
          <template #default="{ record }">
            <a-switch v-model:checked="record.on_show" :un-checked-value="0" :checked-value="1" @change="handleUpdate(record.id, { on_show: $event },{title:record.title})" />
          </template>
        </a-table-column>
        <a-table-column width="110px" title="操作">
          <template #default="{ record }">
            <a-button v-if="record.can_delete === 'true'" type="link" @click="onEdit(record)">
              查看
            </a-button>
            <a-button v-else type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.gift_count && record.gift_count > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.gift_count && record.gift_count > 0">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useApiRequest } from '@/composables/useApiRequest'
import { useFormState } from '@/composables/useFormState'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { message } from 'ant-design-vue'
import { giftCategoryApi } from '../api'
import { useCategories } from '../useCategory'

// 获取分类数据
const { categories, refreshCategories, categoryLoading } = useCategories()

// 创建分类表单
const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  sort: undefined
})

// 分类表单对话框
const { modalVisible, setModalVisible } = useModalVisible()

// 表单对话框隐藏后重置表单数据
watch(modalVisible, (v) => !v && resetFormState())

// 按钮点击显示对话框
const onCreate = () => setModalVisible(true)
const onEdit = (props) => setFormState(props) || setModalVisible(true)

// 设置表单校验规则
setFormRules({
  title: {
    required: true,
    message: '请填写礼品分类名称'
  },
  sort: {
    required: true,
    message: '请填写礼品分类排序'
  }
})

//  处理分类创建/更新
const { loading, setLoading } = useLoading()
const handleCreate = async () => {
  setLoading(true)

  if (!(await validateForm())) {
    setLoading(false)
    return
  }

  try {
    const { id } = formState.value

    if (id > 0) {
      await giftCategoryApi.update(id, formState.value)
      message.success('礼品分类更新完成')
    } else {
      await giftCategoryApi.create({ ...formState.value })
      message.success('礼品分类创建完成')
    }
    setModalVisible(false)
    refreshCategories()
  } finally {
    setLoading(false)
  }
}

//  处理分类更新
const handleUpdate = async (id, props,{title}={}) => {
  await giftCategoryApi.update(id, props)
  if(title){
    message.success(`修改礼品分类(${title})为${props.on_show?'显示':'隐藏'}`)
  }else{
    message.success("修改成功")
  }
}

// 处理分类删除
const handleDelete = async (category) => {
  await giftCategoryApi.delete(category.id)
  refreshCategories()
  message.success('礼品分类删除完成')
}
</script>
