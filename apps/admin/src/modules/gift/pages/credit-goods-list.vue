<template>
  <uc-layout-list title="积分商城" class="m-list">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入礼品名称" />
      </a-form-item>
      <a-form-item name="type">
        <a-select v-model:value="formState.type" placeholder="礼品类型" allow-clear>
          <a-select-option
            v-for="item in GiftType.options()"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item name="category_id">
        <a-select v-model:value="formState.category_id" placeholder="礼品分类" allow-clear>
          <a-select-option
            v-for="item in categoryOptions"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item name="label">
        <a-select v-model:value="formState.label" placeholder="礼品标签" allow-clear>
          <a-select-option
            v-for="item in GiftLabel.options()"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage()">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增积分兑换
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="礼品名称/分类" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-if="record.gift"
              v-bind="record.gift"
              :url="record.gift.photo_urls[0]"
              :symbol="islabel(record)"
              :subtit="`${record.start_time}~${$formatters.transformActivityEndTime(record.end_time)}`"
            >
              <template #label>
                <div class="label">
                  <span v-show="record.is_new" class="label-new">新品</span>
                  <span v-show="record.is_promotion" class="label-promotion">特惠</span>
                  <span v-show="record.is_recommend" class="label-recommend">推荐</span>
                </div>
              </template>
            </uc-img-text>
          </template>
        </a-table-column>
        <a-table-column title="已兑/剩余" width="150px" align="right">
          <template #default="{ record }">
            {{ `${record.sale_num}/${record.stock}` }}
          </template>
        </a-table-column>
        <a-table-column title="上架状态" width="120px">
          <template #default="{ record }">
            <a-badge
              :status="statusFilter(record.status).colorType"
              :text="statusFilter(record.status).label"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.use_num && record.use_num > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <template #icon>
                <uc-ant-icon name="QuestionCircleOutlined" type="danger" />
              </template>
              <a-button
                type="link"
                class="danger"
                :disabled="record.use_num && record.use_num > 0"
              >
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useApiRequest, usePaginatorApiRequest } from '@/composables/useApiRequest'
import { GiftType, GiftLabel } from '../enums'
import { message } from 'ant-design-vue'
import { creditGoodApi } from '../api'
import { useCategoryOptions } from '../useCategory'

const router = useRouter() // 路由操作
const { statusList, statusFilter } = useTimeStatus() // 时间状态
const { formState, resetFormState, onRestFormState } = useFormState() // 查询表单

// 表格查询
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  creditGoodApi.paginator({
    filters: { ...(formState.value.label ? { [formState.value.label]: 1 } : {}), gift_category_id: formState.value.category_id },
    relation_filters: { gift: useTransformQuery({ title: formState.value.title, type: formState.value.type }, { title: 'like' }) },
    offset,
    limit,
    relations: ['gift']
  })
)
onRestFormState(() => setPage())

// 分类选项
const { categoryOptions } = useCategoryOptions()
// label是否存在
const islabel = ({ is_new, is_promotion, is_recommend }) => {
  if (is_new || is_promotion || is_recommend) {
    return '/ '
  }
  return ''
}

// 新增
const onAdd = () => {
  router.push({
    name: 'credit-goods-add'
  })
}
// 点击编辑
const onEdit = ({ id }) => {
  router.push({
    name: 'credit-goods-edit',
    params: {
      id
    }
  })
}
// 点击删除
const handleDelete = ({ id }) => {
  creditGoodApi.delete(id).then(res => {
    message.success('删除完成')
    setPage()
  })
}
</script>
<style scoped lang="less">
.m-list {
  .label {
    display: inline-block;
    span {
      margin-right: 5px;
    }

    &-new {
      color: #1890ff;
    }
    &-promotion {
      color: #f5222d;
    }
    &-recommend {
      color: #52c41a;
    }
  }
}
</style>
