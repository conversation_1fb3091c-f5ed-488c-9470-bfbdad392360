import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { giftApi } from './api'

export async function useGift(callback = null, limit,filter) {
  const cates = await giftApi.paginator({
    filters:{
      type:"!credit",...filter
    },
    offset: 1,
    limit,
  })
  return callback(cates)
}
export async function useGiftFilter(callback = null, filters) {
  const cates = await giftApi.list({
    filters,
  })
  return callback(cates)
}

export async function useGiftOptions(query,filter) {
  const giftOptions = await useGift((cates) => useTransformOptions(cates.items, 'title', 'id'), query,filter)
  return { giftOptions }
}

export async function useGiftOptionsFilter(query) {
  const giftOptionsFilter = await useGiftFilter((cates) => useTransformOptions(cates, 'title', 'id'), query)
  return { giftOptionsFilter }
}
