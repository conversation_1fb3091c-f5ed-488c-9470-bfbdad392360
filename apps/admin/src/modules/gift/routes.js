export default {
  path: 'gift',
  meta: {
    title: '礼品',
    antIcon: 'GiftOutlined'
  },
  children: [
    {
      path: 'categories',
      name: 'gift-categories',
      meta: {
        title: '礼品分类'
      },
      component: () => import('./pages/gift-categories')
    },
    {
      path: 'stocks',
      name: 'gift-stocks',
      meta: {
        title: '礼品库存'
      },
      component: () => import('./pages/gift-stocks'),
    },
    {
      path: 'list',
      name: 'gift-list',
      meta: {
        title: '全部礼品',
        keepAlive: true
      },
      component: () => import('./pages/gifts-list')
    },
    {
      path: 'add',
      name: 'gift-add',
      meta: {
        title: '新增礼品'
      },
      component: () => import('./pages/gifts-edit'),
      hidden: true
    },
    {
      path: 'edit/:id',
      name: 'gift-edit',
      meta: {
        title: '编辑礼品'
      },
      component: () => import('./pages/gifts-edit'),
      hidden: true
    },

    {
      path: "credit-goods",
      name: "credit-goods",
      meta: {
        title: "积分商城",
        keepAlive: true
      },
      component: () => import("./pages/credit-goods-list")
    },
    {
      path: "credit-goods-add",
      name: "credit-goods-add",
      meta: {
        title: "新增积分商城"
      },
      component: () => import("./pages/credit-goods-edit"),
      hidden: true
    },
    {
      path: "credit-goods-edit/:id",
      name: "credit-goods-edit",
      meta: {
        title: "编辑积分商城"
      },
      component: () => import("./pages/credit-goods-edit"),
      hidden: true
    }
  ]
}
