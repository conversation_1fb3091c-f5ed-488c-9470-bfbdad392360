export default {
  path: 'forum',
  meta: {
    title: '社区',
    antIcon: 'CompassOutlined'
  },
  children: [
    {
      path: 'release-agreement',
      name: 'release-agreement',
      meta: {
        title: '发布协议'
      },
      component: () => import('./pages/release-agreement/index'),
      hidden: true,
    },
    {
      path: 'find-category',
      name: 'find-category',
      meta: {
        title: '发现分类'
      },
      component: () => import('./pages/find-category/list')
    },
    {
      path: 'award-interact',
      name: 'award-interact',
      meta: {
        title: '有奖话题',
        keepAlive: true
      },
      component: () => import('./pages/award-interact/list')
    },
    {
      path: 'award-interact-add',
      name: 'award-interact-add',
      hidden: true,
      meta: {
        title: '新增有奖话题'
      },
      component: () => import('./pages/award-interact/edit')
    },
    {
      path: 'award-interact-edit/:id',
      name: 'award-interact-edit',
      hidden: true,
      meta: {
        title: '编辑有奖话题'
      },
      component: () => import('./pages/award-interact/edit')
    },
    {
      path: 'official-image-text',
      name: 'official-image-text',
      meta: {
        title: '官方图文',
        keepAlive: true
      },
      component: () => import('./pages/official-image-text/list')
    },
    {
      path: 'official-image-text-edit/:id',
      name: 'official-image-text-edit',
      hidden: true,
      meta: {
        title: '编辑官方图文'
      },
      component: () => import('./pages/official-image-text/edit')
    },
    {
      path: 'official-image-text-add',
      name: 'official-image-text-add',
      hidden: true,
      meta: {
        title: '新增官方图文'
      },
      component: () => import('./pages/official-image-text/edit')
    },
    {
      path: 'image-text-dynamic',
      name: 'image-text-dynamic',
      meta: {
        title: '图文动态',
        keepAlive: true
      },
      component: () => import('./pages/image-text-dynamic/list')
    },
    {
      path: 'interact-comment',
      name: 'interact-comment',
      meta: {
        title: '互动评论'
      },
      component: () => import('./pages/interact-comment/list')
    },
  ]
}
