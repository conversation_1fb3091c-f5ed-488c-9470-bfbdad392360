<template>
  <div class="comment-item">
    <div v-for="(item, index) in list" :key="index">
      <div v-if="father.openMore === undefined || index < 3 || father.openMore">
        <a-comment>
          <template #author>
            <div class="flex flex-sb flex-cc" style="height: 32px">
              <span class="fw-bold text-ellipsis color-85">{{ item?.user?.nickname }}</span>
              <span class="m-r-10"><uc-ant-icon name="LikeOutlined" /> {{ item.like_count }}</span>
            </div>
          </template>
          <template #avatar>
            <a-avatar v-if="item?.user?.avatar" :src="item?.user?.avatar" />
            <div v-else class="avatar-empty flex flex-center">
              <img :src="defaultAvatar" />
            </div>
          </template>
          <template #content>
            <p class="color-65">
              {{ item.content }}<span class="color-grey m-l-20">{{ item.created_at }}</span>
            </p>
          </template>
          <comment-tree
            v-if="item.sub_comments"
            :list="item.sub_comments"
            :father="item"
            :default-avatar="defaultAvatar"
          />
        </a-comment>
      </div>
    </div>
    <a-button
      v-if="father && father.sub_comments && father.sub_comments.length > 3"
      class="more p-0"
      type="link"
      @click="onSeeMore(father)"
    >
      {{ father.openMore ? '收起更多回复' : '查看更多回复' }}
    </a-button>
  </div>
</template>
<script>
export default {
  name: 'CommentTree'
}
</script>
<script setup>
const props = defineProps({
  list: {
    type: Array,
    required: true
  },
  defaultAvatar: {
    type: String,
    default: ''
  },
  father: {
    type: Object,
    default: () => ({})
  }
})

const onSeeMore = father => (father.openMore = !father.openMore)
</script>
<style scoped lang="less">
.more {
  margin-left: 48px;
}
.avatar-empty {
  width: 32px;
  height: 32px;
  background: #d7d7d7;
  border-radius: 50%;
  img {
    width: 70%;
    height: auto;
  }
}
</style>
