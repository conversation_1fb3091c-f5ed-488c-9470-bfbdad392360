import { apiFactory } from '@/api'

export const officialImageTextApi = apiFactory.restful('/forum/official-feeds') // 官方图文
export const communityCategoriesApi = apiFactory.restful('/forum/categories') // 社区分类
export const imageTextDynamicApi = apiFactory.restful('/forum/dynamic-feeds') // 图文动态
export const communityAgreementActions = apiFactory.command('/setting/settings/forum-agreement') // 社区协议
export const communityAgreementUpdateActions = apiFactory.command('/setting/settings/forum-agreement/update') // 社区协议更新
export const interactCommentApi = apiFactory.restful('/forum/comments') // 互动评论
export const awardInteractApi = apiFactory.restful('/forum/topics') // 有奖话题
export const awardOptionsApi = apiFactory.restful('/user-dispatch-prize/options') // 奖项选项
export const dispatchNameListActions = id => apiFactory.command(`/forum/topics/${id}/dispatch`) // 中奖名单
export const shopStyleActions = apiFactory.command('/page-config/mp-custom/store')
