/**
 * 互动类型
 */
export const interactType = Object.freeze({
  /**
   * 话题互动
   */
  normal: 'normal',
  /**
   * 晒单种草
   */
  recommend: 'recommend',

  options() {
    return [
      { label: '话题互动', value: this.normal },
      // { label: '晒单种草', value: this.recommend }
    ]
  }
})

/**
 * 开奖状态
 */
export const interactDispatchStatus = Object.freeze({
  /**
   * 正常
   */
  normal: 'normal',
  /**
   * 中奖名单
   */
  completed: 'completed',

  options() {
    return [
      { label: '正常', value: this.normal },
      { label: '中奖名单', value: this.completed }
    ]
  }
})

/**
 * 礼品类型
 */
export const giftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',

  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 抽奖资格
   */
  draw: 'draw',

  /**
   * 积分
   */
  credit: 'credit',

  /**
   * 成长值
   */
  // growthValue: 'growth_value',
  options() {
    return [
      // { label: '实物礼品', value: this.entity, biz_type: 'gift' },
      // { label: '虚拟卡券', value: this.invented, biz_type: 'gift' },
      { label: '购物卡券', value: this.coupon, biz_type: 'coupon' }
      // { label: '抽奖资格', value: this.draw, biz_type: 'turnplate_draw'},
      // { label: '会员积分', value: this.credit, biz_type: 'credit' }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 奖项业务类型
 */
export const awardBizType = Object.freeze({
  /**
   * 积分
   */
  credit: 'credit',
  /**
   * 礼品[实物|虚拟]
   */
  gift: 'gift',
  /**
   * 购物卡券
   */
  coupon: 'coupon',
  /**
   * 谢谢参与
   */
  none: 'none'
})

/**
 * 搜索条件
 */
export const searchCondition = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 手机号码
   */
  phone_number: 'phone_number',

  options() {
    return [
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phone_number }
    ]
  }
})

/**
 * 图文状态
 */
export const imageTextStatus = Object.freeze({
  /**
   * 待审[item.examine_status]
   */
  examine: 'examine',

  /**
   * 不通过-审核不通过
   */
  reject: 'reject',
  /**
   * 精华
   */
  essence: 4,
  /**
   * 推荐
   */
  recommend: 3,
  /**
   * 普通
   */
  normal: 2,
  /**
   * 沉底
   */
  bottom: 1,
  /**
   * 隐藏
   */
  hidden: 0,

  options() {
    return [
      { label: '精华', value: this.essence },
      { label: '推荐', value: this.recommend },
      { label: '普通', value: this.normal },
      { label: '沉底', value: this.bottom },
      { label: '隐藏', value: this.hidden }
    ]
  },
  imageTextDynamicOptions() {
    return [{ label: '待审', value: this.examine }, ...this.options(), { label: '不通过', value: this.reject }]
  }
})

/**
 * 动态审核状态
 */
export const dynamicExamineStatus = Object.freeze({
  /**
   * 普通
   */
  normal: 'normal',
  /**
   * 通过
   */
  pass: 'pass',
  /**
   * 拒绝
   */
  reject: 'reject',

  options() {
    return [
      { label: '审核中', value: this.normal, desc: '审核通过后即可查看' },
      { label: '通过审核', value: this.pass },
      { label: '已拒绝', value: this.reject, desc: '发布内容不符合要求' }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 评论类型
 */
export const commentType = Object.freeze({
  /**
   * 发现
   */
  feed: 'feed',
  /**
   * 图文
   */
  article: 'article',
  /**
   * 评论[多级评论]
   */
  comment: 'comment'
})

export const ArticleState = Object.freeze({
  /**
   * 精华
   */
  essence: 'essence',
  /**
   * 推荐
   */
  recommend: 'recommend',
  /**
   * 普通
   */
  normal: 'normal',
  /**
   * 隐藏
   */
  hidden: 'hidden',

  options() {
    return [
      { label: '精华', value: this.essence },
      { label: '推荐', value: this.recommend },
      { label: '普通', value: this.normal },
      { label: '隐藏', value: this.hidden }
    ]
  }
})

export const ActiveStatus = Object.freeze({
  /**
   * 有奖话题
   */
  normal: 'normal',
  /**
   * 中奖名单
   */
  complate: 'complate',

  option() {
    return [
      {
        value: this.normal,
        label: '有奖话题'
      },
      {
        value: this.complate,
        label: '中奖名单'
      }
    ]
  }
})

export const ExtractOptions = Object.freeze({
  /**
   * 从评论用户中随机抽取
   */
  userCommentRandom: 'user_comment_random',
  /**
   * 从点赞用户中随机抽取
   */
  userLikeRandom: 'user_like_random',
  /**
   * 从分享用户中随机抽取
   */
  userShareRandom: 'user_share_random',
  /**
   * 从关联话题动态中随机抽取
   */
  relatedTopicsRandom: 'related_topics_random',
  /**
   * 从累计点赞中抽取最多的
   */
  accumulateLikesMax: 'accumulate_likes_max',
  /**
   * 从累计分享中抽取最多的
   */
  accumulateShareMax: 'accumulate_share_max',

  option() {
    return [
      {
        value: this.userCommentRandom,
        label: '从评论用户中随机抽取'
      },
      {
        value: this.userLikeRandom,
        label: '从点赞用户中随机抽取'
      },
      {
        value: this.userShareRandom,
        label: '从分享用户中随机抽取'
      },
      {
        value: this.relatedTopicsRandom,
        label: '从关联话题动态中随机抽取'
      },
      {
        value: this.accumulateLikesMax,
        label: '从累计点赞中抽取最多的'
      },
      {
        value: this.accumulateShareMax,
        label: '从累计分享中抽取最多的'
      }
    ]
  }
})

export const UpperList = Object.freeze({
  /**
   * 每人上限
   */
  person: 'person',
  /**
   * 每月上限
   */
  month: 'month',
  /**
   * 周期上限
   */
  week: 'week',

  option() {
    return [
      { label: '每人上限', value: this.person },
      { label: '每月上限', value: this.month },
      { label: '周期上限', value: this.week }
    ]
  }
})

export const UserOptions = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 手机号码
   */
  phoneNumber: 'phone_number',
  /**
   * 动态标题
   */
  content: 'content',

  option() {
    return [
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phoneNumber },
      { label: '动态标题', value: this.content }
    ]
  }
})

export const ExtractList = Object.freeze({
  /**
   * 指定名单
   */
  appoint: 'appoint',
  /**
   * 随机抽取
   */
  random: 'random',

  option() {
    return [
      {
        value: this.appoint,
        label: '指定名单'
      },
      {
        value: this.random,
        label: '随机抽取'
      }
    ]
  }
})

export const dynamicActionType = Object.freeze({
  /**
   * 日常：未关联话题
   */
  daily: 'daily',
  /**
   * 话题：关联话题
   */
  topic: 'topic',
  /**
   * 晒单：关联商品
   */
  bask: 'bask',

  options() {
    return [
      { label: '日常', value: this.daily },
      { label: '晒单', value: this.bask },
      { label: '话题', value: this.topic }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})
