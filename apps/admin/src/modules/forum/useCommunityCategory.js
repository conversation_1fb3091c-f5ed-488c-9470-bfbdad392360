import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { communityCategoriesApi } from './api'

export function useCommunityCategory(callback = null, filters = {}) {
  const { data: categories } = useApiRequest(() => {
    return communityCategoriesApi.list({ filters }).then(res => (callback ? callback(res) : res))
  })

  return {
    categories
  }
}

export function useCommunityCategoryOptions(filters) {
  const { categories: communityCategoryOptions } = useCommunityCategory(cates => useTransformOptions(cates, 'title', 'id'), filters)
  return {
    communityCategoryOptions
  }
}

export function useCommunityCategoryFilter(arr, value) {
  if (arr) {
    return arr.find(item => item.value == value) ?? {}
  }
}
