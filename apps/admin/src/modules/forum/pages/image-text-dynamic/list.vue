<template>
  <uc-layout-list title="图文动态">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.topics_id"
          class="w-200"
          placeholder="有奖话题"
          :options="awardInteractOptions"
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.forum_category_id"
          placeholder="图文分类"
          :options="communityCategoryOptions"
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.status"
          placeholder="全部状态"
          :options="imageTextStatusOptions"
          @change="onChangeImageTextStatus"
        />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="formState.created_at" :placeholder="['发布日期', '发布日期']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch"> 查询 </a-button>
        <a-button @click="handleResetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="动态标题/内容" ellipsis>
          <template #default="{ record }">
            <div class="flex relative">
              <uc-img-text :url="record.photo_urls" :tag="record.photo_urls?.length" />
              <div class="flex flex-dc hand text-ellipsis" @click="showDetail(record)">
                <div class="text-ellipsis">
                  <span class="color-85 fw-bold">「{{ dynamicActionType.filterValue(record.action_type).label }}」
                  </span>
                  <span class="color-primary text-ellipsis fw-bold">{{ record.title }}</span>
                </div>
                <span class="text-ellipsis">{{ record.content }}</span>
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="发布人" width="190px" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record?.user?.nickname }}
            </div>
            {{ record.created_at }}
          </template>
        </a-table-column>
        <a-table-column title="访问(人/次)" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.uv, false, false) }} /
            {{ $formatters.thousandSeparator(record.pv, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="分享(人/次)" width="110px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.us, false, false) }} /
            {{ $formatters.thousandSeparator(record.ps, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="评论(人/次)" width="110px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.comment_user_count, false, false) }} /
            {{ $formatters.thousandSeparator(record.comment_count, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="点赞" width="80px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.like_count, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="图文状态" width="100px">
          <template #default="{ record }">
            <a-select
              v-model:value="record.status"
              class="w-80"
              placeholder="图文状态"
              :options="record.statusOptions || []"
              :disabled="record.statusDisabled"
              @change="handleChangeStatus(record)"
            />
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-drawer
    v-model:visible="modalVisible"
    :title="currentRecord?.article?.user?.nickname"
    width="400"
    placement="right"
    :closable="false"
    :after-visible-change="afterVisibleChange"
  >
    <div class="dynamic-details">
      <div id="swiper" class="swiper hide relative m-b-14">
        <div class="swiper-wrapper">
          <div v-for="(banner, i) in currentRecord.article.photo_urls" :key="i" class="swiper-slide">
            <a-image :src="banner" style="width: 100%" />
          </div>
        </div>
        <div class="swiper-pagination"></div>
      </div>
      <div class="dynamic-details-content">
        <p class="fs-16 fw-bold color-85 text-ellipsis">
          {{ currentRecord?.article?.title }}
        </p>
        <div class="fs-14 color-65 m-b-20">
          <a-textarea
            v-model:value="currentRecord.article.content"
            class="article-content"
            :auto-size="{ minRows: 1, maxRows: 200 }"
            disabled
          />
        </div>
        <div v-if="currentRecord?.article?.topic_title" class="dynamic-details-content_word-subject m-b-20">
          <span class="color-primary">{{ currentRecord?.article?.topic_title }}</span>
        </div>
        <div class="dynamic-details-content_stats-info flex flex-sb color-45">
          <span>{{ currentRecord?.article?.created_at }}</span>
          <div class="color-45">
            <span class="m-r-10"><uc-ant-icon name="EyeOutlined" /> {{ currentRecord?.article?.pv }}</span>
            <span class="m-r-10"><uc-ant-icon name="LikeOutlined" /> {{ currentRecord?.article?.like_count }}</span>
            <span><uc-ant-icon name="ShareAltOutlined" /> {{ currentRecord?.article?.ps }}</span>
          </div>
        </div>
      </div>
      <!-- <a-divider />
      <div v-if="currentRecord?.comment?.items?.length" class="dynamic-details-comment">
        <p class="fs-16 fw-bold color-85">
          最新评论（{{ currentRecord.comment.total }}）
        </p>
        <comment :list="currentRecord.comment.items" :default-avatar="shopStyleConfig?.config?.logo_white" />
      </div> -->
    </div>
  </a-drawer>
</template>
<script setup>
import { watch, nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useApiRequest } from '@/composables/useApiRequest'
import { imageTextDynamicApi, awardInteractApi, interactCommentApi, shopStyleActions } from '../../api'
import { searchCondition, imageTextStatus, commentType, dynamicExamineStatus, dynamicActionType } from '../../enums'
import { useModalVisible } from '@/composables/useToggles'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import comment from '../../components/comment'
import { deepLoopHandle } from '@/utils/functions'
import Swiper from 'swiper'
import 'swiper/css'
import { useCommunityCategoryOptions } from '../../useCommunityCategory'

const imageTextStatusOptions = imageTextStatus.imageTextDynamicOptions()

const { communityCategoryOptions } = useCommunityCategoryOptions()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  imageTextDynamicApi.paginator({
    filters: useTransformQuery(formState.value, {
      title: 'like',
      topics_id: '=',
      forum_category_id: '=',
      status: '=',
      created_at: 'dateRange'
    }),
    relation_filters: {
      user: useTransformQuery(
        {
          nickname: formState.value.nickname,
          phone_number: formState.value.phone_number
        },
        {
          nickname: 'like',
          phone_number: '='
        }
      )
    },
    offset,
    limit,
    relations: ['user', 'productRegister', 'feedGoodsSpecs']
  }).then(res => {
    if (res.items.length) {
      res.items.forEach(i => {
        // 不包含话题和晒单 = 日常
        if (!i.topics_id && !i.feed_goods_specs.length) i.action_type = dynamicActionType.daily
        // 包含话题 = 话题
        if (i.topics_id) i.action_type = dynamicActionType.topic
        // 包含晒单 = 晒单
        if (i.feed_goods_specs.length) i.action_type = dynamicActionType.bask
        // 包含话题和晒单 = 晒单
        if (i.topics_id && i.feed_goods_specs.length) i.action_type = dynamicActionType.bask
      })
    }
    return res
  }),
  { immediate: true }
)

const { data: shopStyleConfig } = useApiRequest(() => {
  return shopStyleActions.get({}).then()
})

watch(
  () => data.value,
  v => {
    v.items.forEach(item => {
      if (!item.statusOptions) {
        item.statusOptions = cloneDeep(imageTextStatusOptions)
      }

      // examine_status
      if (item.examine_status !== dynamicExamineStatus.pass) {
        if(item.examine_status === dynamicExamineStatus.reject) {
          item.status = imageTextStatus.reject
        } else {
          item.status = imageTextStatus.examine
        }
        if (item.examine_status === dynamicExamineStatus.normal) {
          // item.statusDisabled = true // 微信审核中禁止修改 先注释
        }
      }

      // status select disabled
      switch (item.status) {
        case imageTextStatus.reject:
        case imageTextStatus.hidden:
        case imageTextStatus.essence:
          item.statusDisabled = true
          break
        case imageTextStatus.recommend:
        case imageTextStatus.normal:
        case imageTextStatus.bottom:
          item.statusOptions.find(op => op.value === imageTextStatus.examine).disabled = true
          item.statusOptions.find(op => op.value === imageTextStatus.hidden).disabled = true
          break
      }
    })
  },
  { immediate: true, deep: true }
)

const { data: awardInteractOptions } = useApiRequest(() => {
  return awardInteractApi.list({}).then(res => useTransformOptions(res, 'title', 'id'))
})

let conditionOptions = [...searchCondition.options(), { label: '动态标题', value: 'title' }]
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()

watch(conditionKey, () => (conditionValue.value = undefined))

const queryFormBasic = Object.freeze({
  nickname: undefined,
  phone_number: undefined,
  title: undefined
})

const { formState, onRestFormState, resetFormState } = useFormState({ ...queryFormBasic })

onRestFormState(() => setPage())

const onChangeImageTextStatus = () => {
  const { status } = formState.value
  if (status === imageTextStatus.examine) {
    // 待审
    Object.assign(formState.value, {
      examine_status: dynamicExamineStatus.normal,
      status: undefined
    })
  } else {
    // 其他状态
    Object.assign(formState.value, {
      examine_status: dynamicExamineStatus.pass
    })
  }
}

const handleSearch = () => {
  Object.assign(formState.value, queryFormBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

const handleResetFormState = () => {
  conditionValue.value = undefined
  resetFormState()
}

const handleDelete = async ({ id }) => {
  await imageTextDynamicApi.delete(id)
  message.success('删除完成')
  setPage()
}

const handleChangeStatus = async ({ id, status }) => {
  if (status === imageTextStatus.examine) return
  let examine_status = dynamicExamineStatus.pass
  if(status === imageTextStatus.reject) {
    examine_status = dynamicExamineStatus.reject
    status = imageTextStatus.normal
  }
  await imageTextDynamicApi.update(id, { status, examine_status })
  message.success('更新成功')
  setPage()
}

const currentRecord = ref({
  article: {},
  comment: []
})

const { modalVisible, setModalVisible } = useModalVisible()

const showDetail = record => {
  const { id, topics_id } = record

  // load comment
  interactCommentApi
    .paginator({
      relations: ['subComments', 'user'],
      filters: {
        biz_id: id,
        biz_type: commentType.feed
      },
      offset: 1,
      limit: 20
    })
    .then(res => {
      // handle data
      res.items.forEach(i => (i.level = 1))
      deepLoopHandle(res.items, item => {
        item.openMore = false
      })
      currentRecord.value.comment = res
      currentRecord.value.article = record
      if (topics_id) {
        currentRecord.value.article.topic_title = awardInteractOptions.value.find(op => op.value === topics_id).label
      }

      // show modal
      setModalVisible(true)

      nextTick(() => {
        new Swiper('#swiper', {
          autoplay: true,
          loop: true,
          observer: true,
          observeSlideChildren: true,
          autoHeight: true,
          pagination: {
            el: '.swiper-pagination'
          }
        })
      })
    })
}
</script>
<style lang="less" scoped>
.dynamic-details {
  &-content {
    &_word-subject {
      span {
        background: #f5f5f5;
        border-radius: 4px;
        padding: 2px 6px;
      }
    }
  }
  &-comment {
    :deep(.ant-comment-content-author-name) {
      width: 100%;
    }
  }
  .swiper {
    :deep(.ant-image) {
      width: 100%;
    }
  }
  .article-content {
    color: #333;
    background-color: #fff;
    border: none;
    padding: 0;
  }
}
</style>
