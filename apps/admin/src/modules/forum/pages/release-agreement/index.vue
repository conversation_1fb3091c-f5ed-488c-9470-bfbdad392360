<template>
  <div class="agreement flex flex-dc">
    <a-card class="g-mg-bt m-b-22">
      <a-form>
        <a-form-item class="m-b-0">
          <a-input
            v-model:value.trim="formState.value.title"
            placeholder="请输入协议名称，不超过20字"
            maxlength="20"
            allow-clear
          />
        </a-form-item>
      </a-form>
    </a-card>
    <uc-rich-text v-model="formState.value.content" placeholder="请输入协议内容" :height="600" />
    <div class="footer-box">
      <a-button type="primary" @click="onSubmit">
        保存
      </a-button>
    </div>
  </div>
</template>

<script setup>
import { nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { communityAgreementActions, communityAgreementUpdateActions } from '../../api'
import { message } from 'ant-design-vue'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  value: {}
})

const loadData = () => {
  communityAgreementActions.get().then(res => {
    setFormState(res)
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

setFormRules({
  value: {
    validator(_, value) {
      const { title, content } = value
      if (!title) return Promise.reject('请输入协议名称')
      if (!content) return Promise.reject('请输入协议内容')
      return Promise.resolve()
    }
  }
})

const onSubmit = async () => {
  if (!(await validateForm())) return
  useLoadingMessage(communityAgreementUpdateActions.post(formState.value)).then(() => {
    message.success('保存成功')
  })
}
</script>
<style scoped lang="less">
.agreement {
  height: 100%;
  padding-bottom: 50px;
  .footer-box {
    width: 100%;
    padding: 10px 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9;
    box-shadow: 0 0 8px #f0f1f2;
  }
  :deep(.m-rich-text) {
    padding-bottom: 80px;
  }
}
</style>
