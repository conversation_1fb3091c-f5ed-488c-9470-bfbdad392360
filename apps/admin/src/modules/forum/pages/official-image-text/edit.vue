<template>
  <uc-layout-form class="joinmember-edit" :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="图文封面" class="required">
            <uc-upload v-model:list="formState.photo_urls" upload-text=" " :max-length="1" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="图文分类" class="required">
            <a-select
              v-model:value="formState.forum_category_id"
              placeholder="请选择图文分类"
              :options="communityCategoryOptions"
            />
          </a-form-item>
          <a-form-item label="图文标题" class="required">
            <a-input v-model:value="formState.title" maxlength="30" placeholder="请输入图文标题，不超过30字" />
          </a-form-item>
          <a-form-item label="图文作者" class="required">
            <div class="flex">
              <a-input v-model:value="formState.author" class="w-150 m-r-10" placeholder="请输入图文作者" />
              <a-input v-model:value="formState.link" class="w-340" placeholder="请输入图文作者链接（非必填）" />
            </div>
          </a-form-item>
          <a-form-item label="图文摘要" class="required">
            <a-textarea
              v-model:value="formState.abstract"
              placeholder="请输入图文摘要，不超过200字"
              :auto-size="{ minRows: 5, maxRows: 10 }"
              maxlength="200"
            />
          </a-form-item>
          <a-form-item label="推荐商品">
            <a-textarea
              v-model:value="formState.recommend_products"
              placeholder="请输入推荐商品SKU，一行一个，不超过10个"
              :auto-size="{ minRows: 5, maxRows: 10 }"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.content" :disabled="isRead" placeholder="请输入图文内容…" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { cloneDeep, isEqual, uniqWith, debounce } from 'lodash'
import { officialImageTextApi } from '../../api'
import { imageTextStatus } from '../../enums'
import { useCommunityCategoryOptions } from '../../useCommunityCategory'

const { communityCategoryOptions } = useCommunityCategoryOptions()

const { id } = useRoute().params

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  officialImageTextApi
    .get(id, {})
    .then(async res => {
      setFormState(res)
    })
    .finally(hideLoading)
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  photo_urls: undefined,
  forum_category_id: undefined,
  title: undefined,
  author: undefined,
  link: undefined,
  abstract: undefined,
  recommend_products: undefined,
  content: undefined,
  status: imageTextStatus.hidden
})

setFormRules({
  photo_urls: { required: true, message: '请选择弹窗封面' },
  forum_category_id: { required: true, message: '请选择图文分类' },
  title: { required: true, message: '请输入图文标题' },
  author: { required: true, message: '请输入图文作者' },
  abstract: { required: true, message: '请输入图文摘要' },
  recommend_products: {
    validator(_, value) {
      if (!value) return Promise.resolve()
      let skuArr = uniqWith(value.split('\n'), isEqual)
      if (skuArr.length > 10) return Promise.reject('推荐商品SKU不超过10个')
      return Promise.resolve()
    }
  },
  content: { required: true, message: '请输入图文内容' }
})

const router = useRouter()

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) {
    return
  }

  const params = cloneDeep(formState.value)
  if (params.recommend_products) {
    params.recommend_products = uniqWith(params.recommend_products.split('\n'), isEqual).join('\n')
  }

  if (id) {
    await officialImageTextApi.update(id, params)
    message.success('编辑完成')
  } else {
    await officialImageTextApi.create(params)
    message.success('创建完成')
  }
  router.back()
}, 500)
</script>
<style scoped lang="less">
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}
</style>
