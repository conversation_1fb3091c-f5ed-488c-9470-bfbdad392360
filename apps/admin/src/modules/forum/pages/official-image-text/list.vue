<template>
  <uc-layout-list title="官方图文">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" placeholder="请输入图文标题" />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.forum_category_id"
          placeholder="图文分类"
          allow-clear
          :options="communityCategoryOptions"
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.status"
          placeholder="图文状态"
          allow-clear
          :options="imageTextStatus.options()"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="toAdd"> 新增官方图文 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="图文封面/标题" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_urls"
              :title="record.title"
              :subtit="useCommunityCategoryFilter(communityCategoryOptions, record.forum_category_id)?.label"
            />
          </template>
        </a-table-column>
        <a-table-column title="访问(人/次)" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.uv, false, false) }} /
            {{ $formatters.thousandSeparator(record.pv, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="分享(人/次)" width="110px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.us, false, false) }} /
            {{ $formatters.thousandSeparator(record.ps, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="评论(人/次)" width="110px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.comment_user_count, false, false) }} /
            {{ $formatters.thousandSeparator(record.comment_count, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="点赞" width="80px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.like_count, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="图文状态" width="100px">
          <template #default="{ record }">
            <a-select
              v-model:value="record.status"
              class="w-80"
              placeholder="请选择"
              :options="imageTextStatus.options()"
              @change="handleChangeStatus(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="140px">
          <template #default="{ record }">
            <a-button type="link" @click="copyLink(linkPath + record.id)"> 链接 </a-button>
            <a-button type="link" @click="toEdit(record)"> 编辑 </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="!record.can_delete"
              @confirm="handleDelete(record)"
            >
              <template #icon>
                <uc-ant-icon name="QuestionCircleOutlined" type="danger" />
              </template>
              <a-button type="link" class="danger" :disabled="record.pv"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { imageTextStatus } from '../../enums'
import { officialImageTextApi } from '../../api'
import { useCommunityCategoryOptions, useCommunityCategoryFilter } from '../../useCommunityCategory'

const { communityCategoryOptions } = useCommunityCategoryOptions()

const linkPath = Object.freeze('/pages/forum/detail/index?id=')

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  officialImageTextApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like',
      forum_category_id: '=',
      status: '='
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  forum_category_id: undefined,
  status: undefined
})

onRestFormState(() => setPage())

const router = useRouter()

const toAdd = () => router.push({ name: 'official-image-text-add' })
const toEdit = ({ id }) => router.push({ name: 'official-image-text-edit', params: { id } })

const handleDelete = async ({ id }) => {
  await officialImageTextApi.delete(id)
  message.success('删除完成')
  setPage()
}

const handleChangeStatus = async ({ id, status }) => {
  await officialImageTextApi.update(id, { status })
  message.success('更新成功')
  setPage()
}
</script>
