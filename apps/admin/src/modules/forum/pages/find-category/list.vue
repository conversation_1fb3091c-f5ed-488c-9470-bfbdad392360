<template>
  <uc-layout-list title="发现分类" class="flex">
    <template #extra>
      <a-button type="primary" @click="showAdd">
        新增发现分类
      </a-button>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="false" @change="setPage">
        <a-table-column title="分类名称" data-index="title" align="left" ellipsis />
        <a-table-column title="分类排序">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="w-80 input-center"
              :min="0"
              precision="0"
              @blur="handleChangeSort(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="是否显示">
          <template #default="{ record }">
            <a-switch
              v-model:checked="record.on_show"
              :un-checked-value="0"
              :checked-value="1"
              @change="handleShowStatus(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button type="link" @click="showEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>

  <a-modal :visible="modalVisible" title="新增发现分类" @cancel="setModalVisible(false)">
    <a-form :model="formState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
      <a-form-item required label="分类名称" class="required">
        <a-input v-model:value="formState.title" placeholder="请输入分类名称，不超过100字" maxlength="100" />
      </a-form-item>
      <a-form-item required label="分类排序" class="required">
        <a-input-number v-model:value="formState.sort" placeholder="请输入分类排序值（排序值越大排名越靠前）" min="0" max="9999" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleSubmit">
        确定
      </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { communityCategoriesApi } from '../../api'
import { debounce } from 'lodash'
import { useModalVisible } from '@/composables/useToggles'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  communityCategoriesApi.paginator({
    offset,
    limit: 999
  })
)

const handleChangeSort = async ({ id, sort }) => {
  await communityCategoriesApi.update(id, { sort })
  message.success('操作成功')
  setPage()
}

const handleShowStatus = async ({ id, on_show }) => {
  await communityCategoriesApi.update(id, { on_show })
  message.success('更新成功')
  setPage()
}

const handleDelete = async ({ id }) => {
  await communityCategoriesApi.delete(id)
  message.success('删除完成')
  setPage()
}

const { modalVisible, setModalVisible } = useModalVisible()

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  sort: undefined
})

setFormRules({
  title: { required: true, message: '请输入分类名称' },
  sort: { required: true, message: '请输入分类排序' }
})

const showAdd = () => {
  resetFormState()
  setModalVisible(true)
}

const showEdit = record => {
  setFormState(record)
  setModalVisible(true)
}

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) {
    return
  }
  const { id } = formState.value
  if (id) {
    await communityCategoriesApi.update(id, formState.value)
    message.success('编辑完成')
  } else {
    await communityCategoriesApi.create({ ...formState.value, on_show: 0 })
    message.success('创建完成')
  }
  setModalVisible(false)
  setPage()
}, 500)
</script>
