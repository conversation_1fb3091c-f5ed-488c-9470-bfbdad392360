<template>
  <uc-layout-list title="互动评论">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="handleSearch">
          查询
        </a-button>
        <a-button @click="handleResetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="评论内容/关联内容" data-index="title" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              <span v-if="record.comment" class="color-primary">@{{ record?.comment?.user?.nickname }}：</span>
              <span class="fw-bold">{{ record.content }}</span>
            </div>
            <div>
              {{ record?.feed?.title }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="评论人" width="180px" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record?.user?.nickname }}
            </div>
            {{ $formatters.numberEncryption(record?.user?.phone_number) }}
          </template>
        </a-table-column>
        <a-table-column title="评论时间" data-index="created_at" width="130px" />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { watch } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { interactCommentApi } from '../../api'
import { searchCondition } from '../../enums'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  interactCommentApi.paginator({
    filters: useTransformQuery(
      {
        nickname: formState.value.nickname,
        phone_number: formState.value.phone_number,
        content: formState.value.content
      },
      {
        content: 'like'
      }
    ),
    relation_filters: {
      user: useTransformQuery(
        { nickname: formState.value.nickname, phone_number: formState.value.phone_number },
        { nickname: 'like', phone_number: '=' }
      ),
      feed: useTransformQuery({ title: formState.value.relation }, { title: 'like' })
    },
    offset,
    limit,
    relations: ['user', 'comment', 'feed']
  })
)

let conditionOptions = [
  ...searchCondition.options(),
  { label: '评论内容', value: 'content' },
  { label: '关联互动', value: 'relation' }
]
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()

watch(conditionKey, () => (conditionValue.value = undefined))

const queryFormBasic = Object.freeze({
  nickname: undefined,
  phone_number: undefined,
  code: undefined,
  sku: undefined
})

const { formState, onRestFormState, resetFormState } = useFormState({ ...queryFormBasic })

onRestFormState(() => setPage())

const handleResetFormState = () => {
  conditionValue.value = undefined
  resetFormState()
}

const handleSearch = () => {
  Object.assign(formState.value, queryFormBasic, {
    [conditionKey.value]: conditionValue.value
  })
  setPage()
}

const handleDelete = async ({ id }) => {
  await interactCommentApi.delete(id)
  message.success('删除完成')
  setPage()
}
</script>
