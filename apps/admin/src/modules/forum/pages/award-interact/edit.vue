<template>
  <uc-layout-form class="award-interact" :is-save="saveBtnDisabled" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="话题信息">
          <a-form-item label="互动封面" class="required">
            <div class="flex">
              <uc-upload
                v-model:list="formState.photo_url"
                upload-text=" "
                :max-length="1"
                :disabled="isRead"
                show-label
                label-text="主图"
              />
              <uc-upload
                v-model:list="formState.bg_url"
                upload-text=" "
                :max-length="1"
                :disabled="isRead"
                show-label
                label-text="背景"
              />
            </div>
          </a-form-item>
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="id && !isNostart"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              placeholder="活动开始时间"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isRead"
              :disabled-date="disabledEndTime"
              placeholder="活动结束时间"
            />
          </a-form-item>
          <a-form-item label="互动类型" class="required">
            <a-select
              v-model:value="formState.type"
              placeholder="请选择互动类型"
              :options="interactType.options()"
              :disabled="id && !isNostart"
            />
          </a-form-item>
          <a-form-item label="互动话题" class="required">
            <a-input
              v-model:value="formState.title"
              maxlength="20"
              placeholder="请输入互动话题，不超过20字"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="互动简介" class="required">
            <a-textarea
              v-model:value="formState.desc"
              placeholder="请输入互动简介，不超过200字"
              maxlength="200"
              :auto-size="{ minRows: 5, maxRows: 10 }"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card :title="prizesTitle" class="prize-set">
          <a-form-item label="兑奖期限" class="required m-b-24">
            <a-input-number
              v-model:value="formState.limit_day"
              precision="0"
              min="0"
              placeholder="请输入活动发放礼品领奖截止天数（0表示不限制）"
              :disabled="isNormal || isRead"
            />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            :label="`奖项${ruleIndex + 1}`"
            class="required"
          >
            <div v-for="(item, index) in rule.award_settings" :key="index" class="flex">
              <!-- <uc-upload v-model:list="rule.photo_url" :max-length="1" :disabled="isNormal || isRead" /> -->
              <a-space direction="vertical">
                <a-space>
                  <a-select
                    v-model:value="item.type"
                    class="w-100 m-r-10"
                    placeholder="礼品类型"
                    allow-clear
                    :disabled="isNormal || isRead"
                    :options="giftType.options()"
                    @change="onChangeGiftType(item, index)"
                  />
                  <a-input-number
                    v-if="item.type === giftType.credit"
                    v-model:value="item.quantity"
                    class="w-220 m-r-10"
                    show-search
                    placeholder="请输入奖励积分"
                    :disabled="isNormal || isRead"
                  />
                  <a-select
                    v-else
                    v-model:value="item.option_id"
                    class="w-220 m-r-10"
                    placeholder="请选择奖励礼品"
                    :options="item.options"
                    allow-clear
                    show-search
                    :filter-option="false"
                    :not-found-content="null"
                    :disabled="isNormal || isRead"
                    @focus="handleShowGifts(item)"
                    @search="handleSearchGifts($event, item)"
                  />
                  <a-input-number
                    v-model:value="item.stock"
                    precision="0"
                    class="w-100"
                    placeholder="活动数量"
                    :disabled="isNormal || isRead"
                  />
                  <a-button
                    shape="circle"
                    size="small"
                    class="delete-btn m-l-8"
                    type="link"
                    :disabled="isNormal || isRead"
                  >
                    <template #icon>
                      <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemovePrize(ruleIndex)" />
                    </template>
                  </a-button>
                </a-space>
                <a-textarea
                  v-model:value="rule.roster"
                  style="max-height: 200px; min-height: 90px"
                  placeholder="请输入中奖手机号码，一行一个"
                  :disabled="nameListDisabled"
                  :rows="4"
                />
              </a-space>
            </div>
          </a-form-item>
          <a-button
            type="link"
            style="margin-left: 12.5%; height: 14px; line-height: 1"
            class="p-0"
            :disabled="isNormal || isRead"
            @click="onAddPrize"
          >
            添加奖励层级
          </a-button>
        </a-card>
      </uc-col>
    </uc-row>

    <uc-rich-text
      v-model="formState.content"
      :disabled="
        formState.status === TIME_STATUS_ENDED && formState.dispatch_status == interactDispatchStatus.completed
      "
    />
  </uc-layout-form>
</template>
<script setup>
import { computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { useTransformImg } from '@/composables/useTransformFormat'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, isEqual, uniqWith, debounce } from 'lodash'
import { awardInteractApi, dispatchNameListActions } from '../../api'
import { interactType, giftType, awardBizType, interactDispatchStatus } from '../../enums'
import { useAward } from '../../useAward'
import { usePrizeAwardUpdate, useTransformPrize } from '../../usePrizeAward'

const { onPrizeDefault, onChangeGiftType, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
const { transformPrizesRequest } = useTransformPrize()
const { batchTransformImg } = useTransformImg()
const { TIME_STATUS_NOSTART, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()

const router = useRouter()
const { id } = useRoute().params

let isNostart = ref(false)
let isEdit = ref(false)
let isRead = ref(false)
let isNormal = ref(false)
const imgskeys = {
  info: ['photo_url', 'bg_url'],
  rule: ['photo_url']
}

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  isEdit.value = true
  awardInteractApi
    .get(id, { relations: ['rules'] })
    .then(async res => {
      switch (res.status) {
        case TIME_STATUS_NOSTART:
          isNostart.value = true
          break
        case TIME_STATUS_NORMAL:
          isNormal.value = true
          break
        case TIME_STATUS_ENDED:
          isRead.value = true
          break
      }

      batchTransformImg(res, 'array', imgskeys.info)

      const showPrizePrm = []
      res.rules.forEach(rule => {
        batchTransformImg(rule, 'array', imgskeys.rule)
        rule.award_settings.forEach(prize => {
          showPrizePrm.push(handleAward(prize, { id: prize.option_id })) // 回显奖项
        })
      })

      await Promise.all(showPrizePrm)
      setFormState(res)
    })
    .finally(hideLoading)
}

const prizeDefault = Object.freeze({
  roster: undefined,
  photo_url: [],
  award_settings: [
    onPrizeDefault({
      type: giftType.coupon
    })
  ]
})

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  type: interactType.normal,
  bg_url: undefined,
  photo_url: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  desc: undefined,
  limit_day: undefined,
  rules: [cloneDeep(prizeDefault)],
  content: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  photo_url: { required: true, message: '请选择主图' },
  bg_url: { required: true, message: '请选择背景' },
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  type: { required: true, message: '请选择互动类型' },
  title: { required: true, message: '请输入互动话题' },
  desc: { required: true, message: '请输入互动简介' },
  limit_day: { required: true, message: '请输入兑奖期限' },
  rules: {
    validator(_, value) {
      let tip
      for (let i = 0; i < value.length; i++) {
        const { award_settings, roster, photo_url } = value[i]
        // if (!photo_url.length) {
        //   tip = `奖项${i + 1}：请上传奖项图片`
        //   break
        // }
        // 校验中奖名单
        if (!nameListDisabled.value) {
          if (!roster) {
            tip = `奖项${i + 1}：请输入中奖名单`
            break
          }
          const tempStock = award_settings.reduce((prev, item) => (prev + isNaN(+item.stock) ? 0 : +item.stock), 0)
          let rosterArr = uniqWith(roster.split('\n'), isEqual)
          if (rosterArr.length !== tempStock) {
            tip = `奖项${i + 1}：中奖人数与活动数量不一致`
            break
          }
        }

        for (const { type, option_id, quantity, stock } of award_settings) {
          if (!type) {
            tip = `奖项${i + 1}：请选择礼品类型`
            break
          }
          if (type === giftType.credit) {
            if (!quantity) {
              tip = `奖项${i + 1}：请输入奖励积分`
              break
            }
          } else {
            if (!option_id) {
              tip = `奖项${i + 1}：请选择奖励礼品`
              break
            }
          }
          if (!stock) {
            tip = `奖项${i + 1}：请输入活动数量`
            break
          }
        }
        if (tip) break
      }
      if (tip) return Promise.reject(tip)
      return Promise.resolve()
    }
  },
  content: { required: true, message: '请输入活动内容' }
})

let nameListDisabled = ref(true) // 中奖名单禁用
let saveBtnDisabled = ref(true) // 保存按钮禁用
const prizesTitle = computed(() => (nameListDisabled.value ? '奖项设置' : '中奖名单'))

watch(
  () => formState.value,
  v => {
    if (id) {
      const { status, dispatch_status } = v
      nameListDisabled.value = !(status === TIME_STATUS_ENDED && dispatch_status === interactDispatchStatus.normal)
      saveBtnDisabled.value =
        (status === TIME_STATUS_ENDED && dispatch_status === interactDispatchStatus.normal) ||
        status !== TIME_STATUS_ENDED
    }
  },
  { immediate: true, deep: true }
)

const onAddPrize = () => {
  if (formState.value.rules.length == 10) return message.info('最多可添加10个奖励层级')
  formState.value.rules.push(cloneDeep(prizeDefault))
}

const onRemovePrize = index => formState.value.rules.splice(index, 1)

const handleSendNameList = async () => {
  const dispatchParams = []
  formState.value.rules.forEach(rule => {
    dispatchParams.push({
      rule_id: rule.id,
      roster: uniqWith(rule.roster.split('\n'), isEqual).join('\n')
    })
  })

  await dispatchNameListActions(id).post({ items: dispatchParams })
  message.success('开奖完成')
  router.back()
}

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) {
    return
  }

  if (!nameListDisabled.value) {
    handleSendNameList()
  } else {
    const params = cloneDeep(formState.value)

    batchTransformImg(params, 'string', imgskeys.info)
    params.rules.forEach(rule => {
      batchTransformImg(rule, 'string', imgskeys.rule)
      transformPrizesRequest(rule)
    })

    id ? await awardInteractApi.replace(id, params) : await awardInteractApi.create(params)
    message.success('操作完成')
    router.back()
  }
}, 500)
</script>
<style scoped lang="less">
.prize-set {
  :deep(.ant-form-item) {
    margin-bottom: 10px;
  }
}
.delete-btn {
  line-height: 32px;
  padding-top: 2px;
  border: none;
  :deep(.anticon) {
    font-size: 20px;
  }
}
.delete-btn:disabled {
  :deep(.anticon) {
    color: #d9d9d9 !important;
  }
}
</style>
