<template>
  <a-space direction="vertical" :size="20">
    <a-card class="std-filter">
      <a-form layout="inline">
        <a-form-item name="title">
          <a-input-group compact>
            <a-select
              v-model:value="Condition.conditionKey"
              class="w-120"
              :options="skuOrTitleType.options()"
              @change="changeConditionKey"
            />
            <a-input v-model:value.trim="Condition.conditionValue" placeholder="请输入关键词" class="w-420" />
          </a-input-group>
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage"> 查询 </a-button>
          <a-button @click="resetCondition"> 重置 </a-button>
        </a-form-item>
      </a-form>
    </a-card>
    <a-card>
      <template #title> 商品库存 </template>
      <template #extra>
        <a-space>
          <div class="user-flex">
            <div v-if="!stockSyncStep.value.checked">
              <a :href="downloadStockApi" class="user-flex icon-margin">
                <uc-svg-icon name="download" size="22px" color="#409eff" />
                <span>下载模板</span>
              </a>
            </div>
            <div v-if="!stockSyncStep.value.checked">
              <a-upload
                :action="importSTockApi"
                :before-upload="beforeUpload"
                :on-error="errorUpload"
                :show-upload-list="false"
                @change="handleChange"
              >
                <a href="javascript:;" class="user-flex icon-margin">
                  <uc-svg-icon name="tolead" size="22px" color="#409eff" />
                  <span>导入库存</span>
                </a>
              </a-upload>
            </div>
            <div>
              <a href="javascript:;" class="user-flex">
                <a-switch v-model:checked="stockSyncStep.value.checked" @change="updateStockSync" />
                <span class="m-l-10">{{ stockSyncStep.value.checked ? '开启库存同步' : '关闭库存同步' }}</span>
              </a>
            </div>
          </div>
          <a-button :href="handleExport()">导出</a-button>
        </a-space>
      </template>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="商品名称/商品规格" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :title="record?.goods?.title"
              :label="record.sku"
              symbol=":&nbsp"
              :subtit="assembleAttrs(record?.spec?.attrs)"
            />
          </template>
        </a-table-column>
        <a-table-column
          title="库存数"
          :align="stockSyncStep.value.checked ? 'right' : 'center'"
          :width="stockSyncStep.value.checked ? '150px' : '200px'"
        >
          <template #default="{ record }">
            <span v-if="stockSyncStep.value.checked">{{
              $formatters.thousandSeparator(record.stock, false, false)
            }}</span>
            <a-input
              v-else
              v-model:value="record.stock"
              style="width: 100px"
              class="t-center"
              placeholder="库存数"
              @blur="handleUpdateStock(record)"
            />
          </template>
        </a-table-column>
        <a-table-column title="可售" width="120px" align="right">
          <template #default="{ record }">
            {{ record.stock - record.hold }}
          </template>
        </a-table-column>
        <a-table-column title="占用" data-index="hold" width="120px" align="right" />
      </a-table>
    </a-card>
  </a-space>
</template>
<script setup>
import { nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { message } from 'ant-design-vue'
import { skuOrTitleType } from '../enums'
import {
  downloadStockApi,
  importSTockApi,
  excelExportUrl,
  goodsStocksApi,
  goodStockSyncApi,
  goodStockSyncUpdateApi
} from '../api'

const stockSyncStep = ref({
  value: { checked: true }
})

const loadStockSync = () => {
  goodStockSyncApi.get().then(res => {
    stockSyncStep.value = res
  })
}

const updateStockSync = async () => {
  await goodStockSyncUpdateApi.post(stockSyncStep.value)
  message.success('操作成功')
}

nextTick(() => {
  loadStockSync()
})

const {
  formState: Condition,
  resetFormState: resetCondition,
  onRestFormState
} = useFormState({
  conditionKey: 'sku',
  conditionValue: undefined
})

const changeConditionKey = type => (Condition.value.conditionValue = undefined)

// 组装attrs--规格
const assembleAttrs = (attrs = []) => {
  let str = ''
  attrs.forEach(item => {
    str += `/${item.value}`
  })
  return str.slice(1)
}

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  goodsStocksApi.paginator({
    filters: useTransformQuery(Condition.value.conditionKey == 'sku' ? { sku: Condition.value.conditionValue } : {}, {
      sku: 'like'
    }),
    relation_filters: {
      goods:
        Condition.value.conditionKey == 'title'
          ? useTransformQuery({ title: Condition.value.conditionValue }, { title: 'like' })
          : {}
    },
    relations: ['goods', 'spec'],
    offset,
    limit
  })
)

onRestFormState(() => {
  setPage()
})

const handleUpdateStock = async ({ id, stock }) => {
  if (stock === '') {
    return
  }

  await goodsStocksApi.update(id, { stock })
  message.success('商品库存更新成功')
  setPage()
}

const handleExport = () => {
  let query = ''
  switch (Condition.value.conditionKey) {
    case 'sku':
      Condition.value.conditionValue ? (query = `filters={"sku":"%${Condition.value.conditionValue}%"}`) : ''
      break
    case 'title':
      Condition.value.conditionValue
        ? (query = `relation_filters={ "goods": { "title": "%${Condition.value.conditionValue}%" } }`)
        : ''
      break
    default:
      break
  }
  let excelExportUrlQuery = encodeURI(`${excelExportUrl}&${query}`)
  return excelExportUrlQuery
}

const handleChange = ({ file: { status, response } }) => {
  switch (status) {
    case 'uploading':
      return
    case 'error':
      message.error(response?.tips?.goodsStockArray[0] ?? '上传文件失败')
      return
    case 'done':
      if (response.data) {
        message.success('库存导入成功')
        setPage()
      }
      break
    default:
      break
  }
}
</script>
<style scoped lang="less">
.user-flex {
  display: flex;
  align-items: center;
  .u-svg {
    margin-right: 10px;
  }
}
.icon-margin {
  margin-right: 30px;
  span {
    margin-left: 5px;
  }
}
</style>
