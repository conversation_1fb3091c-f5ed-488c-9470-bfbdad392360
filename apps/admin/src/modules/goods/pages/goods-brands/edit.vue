<template>
  <uc-layout-form @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基础信息">
          <a-form-item label="品牌logo">
            <uc-upload v-model:list="formState.logo" upload-text=" " :max-length="1" />
          </a-form-item>
          <a-form-item label="上级品牌" name="parent_id">
            <a-tree-select
              v-model:value="formState.parent_id"
              placeholder="请选择上级品牌（若为一级品牌可不选）"
              :tree-data="brandTree"
              allow-clear
            />
          </a-form-item>
          <a-form-item label="品牌名称" class="required">
            <a-input v-model:value.trim="formState.name" placeholder="请输入品牌名称，不超过15字" :maxlength="15" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.desc" placeholder="请输入品牌简介" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { cloneDeep } from 'lodash'
import { useBrands } from '../../useBrand'

import { goodsBrandsApi } from '../../api'
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  name: undefined,
  logo: undefined,
  desc: ''
})

// 商品品牌
const { brandTree } = useBrands()
const router = useRouter()
const { id } = useRoute().params

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  goodsBrandsApi
    .get(id)
    .then(res => {
      res.logo = res.logo?.length > 0 ? [res.logo] : undefined
      setFormState(res)
    })
    .finally(() => {
      hideLoading()
    })
}

setFormRules({
  name: { required: true, message: '请输入品牌名称' }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  params.logo = params.logo?.length > 0 ? params.logo[0] : undefined
  id ? await goodsBrandsApi.replace(id, params) : await goodsBrandsApi.create(params)

  message.success('操作成功')
  router.back()
}
</script>

<style lang="less" scoped></style>
