<template>
  <uc-layout-list title="商品品牌">
    <template #filter>
      <a-form-item name="name">
        <a-input v-model:value="formState.name" placeholder="请输入品牌名称" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增品牌
      </a-button>
    </template>
    <template #list>
      <a-table row-key="id" :data-source="brandTree" :loading="loading" :pagination="false">
        <a-table-column title="logo" width="240px" class="first-cell">
          <template #default="{ record }">
            <div class="m-title-box">
              <img :src="record.logo" class="m-img" />
            </div>
          </template>
        </a-table-column>

        <a-table-column title="品牌名称" ellipsis>
          <template #default="{ record }">
            <span>{{ record.name }}</span>
          </template>
        </a-table-column>

        <a-table-column title="商品" ellipsis>
          <template #default="{ record }">
            {{ record.goods_count }}
          </template>
        </a-table-column>

        <a-table-column width="150px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="record.goods_count > 0">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { goodsBrandsApi } from '../../api'
import { handleTree, sumAttr } from '@/utils/functions'

const { formState, onRestFormState, resetFormState } = useFormState({
  name: undefined
})

const { data: brands, setPage, loading } = useApiRequest(() =>
  goodsBrandsApi.list({
    filters: useTransformQuery(
      formState,
      { name: 'like' }
    ),
    relations: ['goods_count'],
  })
)

const brandTree = computed(() => handleTree(brands.value ?? [], 'id', 'parent_id').map(c => {
  sumAttr(c, 'goods_count')
  return c
}))

onRestFormState(() => setPage())

const router = useRouter()

const onAdd = () => router.push({ name: 'goods-brands-add' })

const onEdit = ({ id }) => router.push({ name: 'goods-brands-edit', params: { id } })

const handleDelete = async ({ id }) => {
  await goodsBrandsApi.delete(id)
  message.success('操作成功')
  setPage()
}
</script>

<style lang="less" scoped>
.m-title-box {
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  margin-right: 8px;

  .m-img {
    width: 50px;
  }
}
:deep(.first-cell) {
  display: flex;
  justify-content: flex-start;
  align-items: center;
}
:deep(.logo-box .ant-image) {
  background-color: transparent !important;
}

:deep(.logo-box .ant-image .ant-image-img) {
  object-fit: cover !important;
}
</style>
