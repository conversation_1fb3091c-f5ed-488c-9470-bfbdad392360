<template>
  <div class="m-template p-b-50">
    <a-space direction="vertical" :size="20" class="w-fill">
      <a-card class="g-mg-bt">
        <a-form>
          <a-form-item class="m-b-0">
            <a-input v-model:value.trim="formState.title" placeholder="请输入商详模板名称" allow-clear />
          </a-form-item>
        </a-form>
      </a-card>
      <uc-rich-text v-model="formState.head_blocks" placeholder="请输入头部模版内容" :height="600" />
      <uc-rich-text v-model="formState.tail_blocks" :index="1" placeholder="请输入尾部模版内容" :height="600" />
    </a-space>
    <div class="footer-box">
      <a-space :size="10">
        <a-button @click="onCancel">
          取消
        </a-button>
        <a-button type="primary" @click="onSubmit">
          保存
        </a-button>
      </a-space>
    </div>
  </div>
</template>
<script setup>
import { useRoute, useRouter } from "vue-router";
import { message } from "ant-design-vue";
import { useFormState } from "@/composables/useFormState";
import { goodsTemplatesApi } from "../../api"

const router = useRouter();
const { id } = useRoute().params;

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  head_blocks: undefined,
  tail_blocks: undefined
});

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  goodsTemplatesApi
    .get(id, {})
    .then((goodsTemplates) => setFormState(goodsTemplates))
    .finally(hideLoading)
}

setFormRules({
  title: { required: true, message: '请输入商详模板名称' },
})

const onCancel = () => {
  router.back()
}

const onSubmit = async () => {
  if (!(await validateForm())) {
    return
  }
  if (id) {
    await goodsTemplatesApi.update(id, formState.value)
    message.success('商品模板编辑完成')
  } else {
    await goodsTemplatesApi.create(formState.value)
    message.success('商品模板创建完成')
  }
  router.back()
};

</script>
<style lang="less" scoped>
.m-template {
  position: relative;
  .footer-box {
    width: 100%;
    padding: 10px 15px;
    background: #fff;
    display: flex;
    justify-content: flex-end;
    position: fixed;
    bottom: 0;
    right: 0;
    z-index: 9;
    box-shadow: 0 0 8px #f0f1f2;
  }
}
</style>
