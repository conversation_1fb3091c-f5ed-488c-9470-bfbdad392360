<template>
  <uc-layout-list title="商详模版" class="flex">
    <template #extra>
      <a-button type="primary" @click="onCreate">
        新增商详模版
      </a-button>
    </template>
    <template #list>
      <a-table row-key="id" :pagination="false" :data-source="data.items ?? data" :loading="loading">
        <a-table-column title="模版名称" data-index="title" ellipsis />
        <a-table-column title="默认模版" width="150px">
          <template #default="{ record }">
            <a-radio :checked="record.is_default" @focus="handleFocus(record.id, !record.is_default)" />
          </template>
        </a-table-column>
        <a-table-column width="110px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.goods_count"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <template #icon>
                <uc-ant-icon name="QuestionCircleOutlined" type="danger" />
              </template>
              <a-button type="link" class="danger" :disabled="record.goods_count">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { goodsTemplatesApi } from '../../api'
import { message } from 'ant-design-vue'

const router = useRouter()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  goodsTemplatesApi.list({
    relations: ['goods_count']
  })
)

const handleFocus = async (id, checked) => {
  if (!checked) return
  await goodsTemplatesApi.update(id, { is_default: checked })
  setPage()
}

const onCreate = () => {
  router.push({
    name: 'goods-template-add'
  })
}

const onEdit = ({ id }) => {
  router.push({
    name: 'goods-template-edit',
    params: {
      id
    }
  })
}

const handleDelete = ({ id }) => {
  goodsTemplatesApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}
</script>
<style scoped lang="less"></style>
