<template>
  <a-modal
    :visible="modalVisible"
    :title="formState.id ? '编辑商品分组' : '新增商品分组'"
    @ok="handleCreate"
    @cancel="setModalVisible(false)"
  >
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item label="分组图片" name="photo_url">
        <uc-upload
          upload-text=" "
          :list="formState.photo_url ? [formState.photo_url] : []"
          :max-length="1"
          multiple
          @update:list="data => (formState.photo_url = data[0])"
        />
      </a-form-item>
      <a-form-item label="上级分组" name="parent_id">
        <a-tree-select
          v-model:value="formState.parent_id"
          placeholder="请选择上级分组（若为一级分组可不选）"
          :tree-data="groups"
          :replace-fields="{ key: 'id', value: 'id' }"
          :disabled="formState.parent_id == 0"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="分组名称" name="title" class="required">
        <a-input v-model:value.trim="formState.title" placeholder="请输入分组名称，不超过10字" :maxlength="10" />
      </a-form-item>
      <a-form-item label="分组排序" name="sort" class="required">
        <a-input-number
          v-model:value="formState.sort"
          :min="0"
          :max="9999"
          placeholder="请输入分组排序值（排序值越大排名越靠前）"
          :formatter="$formatters.number"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>

  <uc-layout-list title="商品分组" class="m-group flex">
    <template #extra>
      <a-button type="primary" @click="onCreate">
        新增分组
      </a-button>
    </template>
    <template #list>
      <a-table row-key="id" :pagination="false" :data-source="groups" :loading="groupLoading">
        <a-table-column title="分组名称" ellipsis class="m-title">
          <template #default="{ record }">
            <div class="m-title-box">
              <img :src="record.photo_url" class="m-img" />
            </div>
            <span>{{ record.title }}</span>
          </template>
        </a-table-column>
        <a-table-column title="商品" ellipsis>
          <template #default="{ record }">
            {{ record.goods_count }}
          </template>
        </a-table-column>
        <a-table-column title="分组排序" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="sort w-80"
              :formatter="$formatters.number"
              :min="0"
              :max="9999"
              @blur="handleUpdate(record.id, { sort: record.sort })"
            />
          </template>
        </a-table-column>
        <a-table-column title="是否显示">
          <template #default="{ record }">
            <a-switch
              v-model:checked="record.is_hidden"
              :un-checked-value="1"
              :checked-value="0"
              @change="handleUpdate(record.id, { is_hidden: $event }, { title: record.title })"
            />
          </template>
        </a-table-column>
        <a-table-column width="150px" title="操作">
          <template #default="{ record }">
            <!--            <a-button type="link" @click="copyLinkByRoute('goodsList', { id: getLinkPath(record) })">-->
            <!--              链接-->
            <!--            </a-button>-->
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="record.goods_count > 0"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button
                type="link"
                class="danger"
                :disabled="record.goods_count > 0 || (record.children && record.children.length > 0)"
              >
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { useGroups } from '../../useGroup'
import { message } from 'ant-design-vue'
import { goodsGroupsApi } from '../../api'

const { groups, refreshGroups, groupLoading } = useGroups()

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  sort: undefined
})

const { modalVisible, setModalVisible } = useModalVisible()

watch(modalVisible, v => !v && resetFormState())
const getLinkPath = computed(() => item => {
  const ids = item.children?.map(({ id }) => id) ?? []
  ids.unshift(item.id)
  return ids.join()
})
const onCreate = () => {
  setModalVisible(true)
}
const onEdit = props => {
  setFormState(props) || setModalVisible(true)
}

setFormRules({
  title: {
    required: true,
    message: '请填写商品分组名称'
  },
  sort: {
    required: true,
    message: '请填写商品分组排序'
  }
})

//  处理分组创建/更新
const { loading, setLoading } = useLoading()
const handleCreate = async () => {
  setLoading(true)

  if (!(await validateForm())) {
    setLoading(false)
    return
  }

  try {
    const { id } = formState.value
    if (id) {
      if (id == formState.value.parent_id) {
        message.warning('不能选择自己为上级分组')
        return
      }
      // 判断parent_id--为undefined传0
      const parent_id = formState.value.parent_id
      await goodsGroupsApi.update(id, { ...formState.value, parent_id: parent_id ? parent_id : 0 })
      message.success('商品分组更新完成')
    } else {
      await goodsGroupsApi.create(formState.value)
      message.success('商品分组创建完成')
    }
    setModalVisible(false)
    refreshGroups()
  } finally {
    setLoading(false)
  }
}

//  处理分组更新
const handleUpdate = async (id, props, { title } = {}) => {
  await goodsGroupsApi.update(id, props)
  if (title) {
    message.success(`修改商品分组(${title})为${props.is_hidden ? '隐藏' : '显示'}`)
  } else {
    message.success('修改成功')
  }
}

const handleDelete = ({ id }) => {
  goodsGroupsApi.delete(id).then(res => {
    message.success('删除成功')
    refreshGroups()
  })
}
</script>
<style scoped lang="less">
.m-group {
  .sort {
    :deep(.ant-input-number-input) {
      text-align: center !important;
    }
  }

  :deep(.ant-table-row-cell-ellipsis.m-title) {
    display: flex;
    align-items: center;
  }

  .m-title-box {
    height: 50px;
    display: flex;
    align-items: center;
    background-color: #f5f5f5;
    margin-right: 8px;

    .m-img {
      width: 50px;
    }
  }
}
</style>
