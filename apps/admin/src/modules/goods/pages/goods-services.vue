<template>
  <a-modal :visible="modalVisible" :title="formState.id ? '编辑商品服务' : '新增商品服务'" @ok="handleCreate" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item label="服务名称" name="title" class="required">
        <a-input v-model:value.trim="formState.title" placeholder="请输入服务名称，不超过10字" :maxlength="10" />
      </a-form-item>
      <a-form-item label="服务排序" name="sort" class="required">
        <a-input-number v-model:value.trim="formState.sort" :min="0" :max="9999" placeholder="请输入分类排序值（排序值越大排名越靠前）" :formatter="$formatters.number" />
      </a-form-item>
      <a-form-item required label="服务说明" class="required">
        <a-textarea v-model:value="formState.desc" placeholder="请输入服务说明，一行一个，不超过200字" :auto-size="{ minRows: 5, maxRows: 10 }" :maxlength="200" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>
  <uc-layout-list title="商品服务" class="m-serve flex">
    <template #extra>
      <a-button type="primary" @click="onCreate">
        新增商品服务
      </a-button>
    </template>
    <template #list>
      <a-table row-key="id" :pagination="false" :data-source="serve" :loading="serveLoading">
        <a-table-column title="服务名称" data-index="title" ellipsis width="200px" />
        <a-table-column title="服务说明" data-index="desc" ellipsis />
        <a-table-column title="服务排序" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="sort w-80"
              :formatter="$formatters.number"
              :min="0"
              :max="9999"
              @blur="handleUpdate(record.id, { sort: record.sort })"
            />
          </template>
        </a-table-column>
        <a-table-column width="110px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from "@/composables/useFormState"
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { message } from "ant-design-vue"
import { goodsServicesApi } from "../api"
import { useServe } from '../useServe'

const { serve, refreshServe, serveLoading } = useServe()

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  sort: undefined,
  desc: undefined
})

const { modalVisible, setModalVisible } = useModalVisible()

watch(modalVisible, (v) => !v && resetFormState())

const onCreate = () => setModalVisible(true)
const onEdit = (props) => setFormState(props) || setModalVisible(true)

setFormRules({
  title: {
    required: true,
    message: '请填写服务分类名称'
  },
  sort: {
    required: true,
    message: '请填写服务分类排序'
  },
  desc: {
    required: true,
    message: '请填写服务说明'
  },
})

//  处理分类创建/更新
const { loading, setLoading } = useLoading()
const handleCreate = async () => {
  setLoading(true)

  if (!(await validateForm())) {
    setLoading(false)
    return
  }

  try {
    const { id } = formState.value

    if (id) {
      await goodsServicesApi.update(id, formState.value)
      message.success('商品服务更新完成')
    } else {
      await goodsServicesApi.create({ ...formState.value })
      message.success('商品服务创建完成')
    }
    setModalVisible(false)
    refreshServe()
  } finally {
    setLoading(false)
  }
}

//  处理分类更新
const handleUpdate = async (id, props) => {
  await goodsServicesApi.update(id, props)
  message.success("修改成功")
}

const handleDelete = ({id}) => {
  goodsServicesApi.delete(id).then(res => {
    message.success("删除成功")
    refreshServe()
  })
}

</script>
<style scoped lang="less">
.m-serve {
  .sort {
    :deep(.ant-input-number-input) {
      text-align: center !important;
    }
  }
}
</style>
