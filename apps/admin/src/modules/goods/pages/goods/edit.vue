<template>
  <uc-layout-form @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="商品类型" name="type" class="required">
            <a-select
              v-model:value="formState.type"
              placeholder="请选择商品类型"
              class="w-500"
              :options="goodsType.options()"
            />
          </a-form-item>
          <a-form-item label="商品分类" name="category_id" class="required">
            <a-cascader
              :value="dispose(formState.category_id, categories)"
              placeholder="请选择商品分类"
              class="w-500"
              :options="categories"
              :field-names="{ label: 'title', value: 'id' }"
              change-on-select
              @change="onCategoryChange"
            />
          </a-form-item>
          <a-form-item label="商品分组" name="category_id">
            <a-tree-select
              v-model:value="formState.group_ids"
              multiple
              placeholder="请选择商品分组"
              class="w-500"
              tree-node-filter-prop="title"
              show-search
              :tree-data="groups"
            />
          </a-form-item>
          <a-form-item label="商品品牌" name="brand_id">
            <a-cascader
              :value="dispose(formState.brand_id, brandTree)"
              placeholder="请选择商品品牌"
              class="w-500"
              :options="brandTree"
              change-on-select
              @change="onBrandChange"
            />
          </a-form-item>
          <a-form-item label="商品名称" name="title" class="required">
            <a-input v-model:value="formState.title" placeholder="请输入商品名称，不超过100字" :maxlength="100" class="w-500" />
          </a-form-item>
          <a-form-item label="标准名称" name="standard_title">
            <a-input
              v-model:value="formState.standard_title"
              placeholder="请输入标准名称，不超过100字"
              :maxlength="100"
              class="w-500"
            />
          </a-form-item>
          <a-form-item label="商品卖点" name="selling_point">
            <a-input
              v-model:value="formState.selling_point"
              placeholder="请输入商品卖点，不超过30字"
              :maxlength="30"
              class="w-500"
            />
          </a-form-item>

          <a-form-item label="商品SPU" name="spu">
            <a-input v-model:value="formState.spu" placeholder="请输入商品SPU，不超过30字" :maxlength="30" class="w-500" />
          </a-form-item>

          <a-form-item label="商品服务" name="service" class="required">
            <a-select
              v-model:value="formState.service"
              mode="multiple"
              placeholder="请选择商品服务（最多可选5个服务）"
              :options="serveOptions"
              class="w-500"
              @change="handleServe"
            />
          </a-form-item>
          <a-form-item label="商品场景标签" name="service">
            <a-select
              v-model:value="formState.tags"
              mode="tags"
              show-search
              placeholder="请选择商品场景标签"
              :options="goodsTagsOptions"
              class="w-500"
              @search="handleSearch"
            />
          </a-form-item>
          <a-form-item label="排序" name="sort">
            <a-input-number v-model:value="formState.sort" placeholder="请输入排序" :maxlength="9" class="w-500" />
          </a-form-item>
          <a-form-item label="上架状态" name="on_sale">
            <a-switch v-model:checked="formState.on_sale" :un-checked-value="0" :checked-value="1" />
          </a-form-item>
          <a-form-item label="是否允许分享" name="is_share">
            <a-radio-group v-model:value="formState.is_share">
              <a-radio :value="1">允许</a-radio>
              <a-radio :value="0">不允许</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="商品相册" class="h-fill">
          <a-form-item label="商品相册" name="photo_urls" class="required">
            <uc-upload
              v-model:list="formState.photo_urls"
              upload-text=" "
              isdrag
              show-label
              :max-length="9"
              multiple
            />
          </a-form-item>
          <a-form-item label="主图视频" name="video">
            <div class="flex">
              <uc-upload
                type="video"
                upload-text=" "
                :list="formState.video_url ? [formState.video_url] : []"
                :max-length="1"
                @update:list="data => (formState.video_url = data[0])"
              />
              <a-textarea
                v-model:value="formState.video_cover"
                placeholder="添加视频可提升成交转化，有利于获取更多新流量时长 9-30 秒，宽高比 16:9，视频不能超过100M，支持mp4视频格式"
                style="height: 104px !important"
              />
            </div>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="商品规格">
          <a-form-item label="默认规格" name="default_sku" class="required">
            <a-input
              v-model:value="formState.default_sku"
              placeholder="请输入默认商品SKU"
              class="w-500"
              @keyup="$event => {
                formState.spec = $formatters.replaceChinese($event.target.value)
              }
              "
            />
          </a-form-item>
          <a-form-item label="选择规格" name="select_spec" class="required">
            <a-checkbox-group
              :value="formState.specs[0].attrs.map(({ name }) => name)"
              :options="goodsAttrOptions"
              @change="handleAttrs"
            />
          </a-form-item>
          <a-form-item label="商品规格" name="specs" class="required commodity-spec">
            <div v-for="(item, index) in formState.specs" :key="index" class="m-b-10">
              <a-space :size="10" wrap class="m-b-0">
                <a-checkbox :checked="!item.is_hidden" @change="onChangeSpecShow(item)" />
                <a-input
                  v-model:value="item.sku"
                  placeholder="商品SKU"
                  class="w-160"
                  :disabled="item.disabled"
                  @keyup="$event => {
                    item.sku = $formatters.replaceChinese($event.target.value)
                  }
                  "
                />
                <a-input v-model:value="item.standard_name" placeholder="标准规格名称" class="w-160" />
                <!-- 根据上面规格循环展示的输入框 -->
                <template v-for="(item2, index2) in item.attrs" :key="index2">
                  <a-input v-model:value="item2.value" :placeholder="item2.name" class="w-150" />
                </template>
                <a-space :size="10">
                  <a-input-number v-model:value="item.price" placeholder="销售价" :precision="2" class="w-120" :min="0" />
                  <a-input-number v-model:value="item.limit" placeholder="限购数" :precision="0" class="w-80" :min="0" />
                  <!-- <a-input-number v-model:value="item.vip_price" placeholder="VIP价" :precision="2" class="w-120 m-l-10 m-r-10" :min="0" />
              <a-input-number v-model:value="item.inside_price" placeholder="内购价" :precision="2" class="w-120" :min="0" /> -->
                </a-space>

                <uc-upload
                  :list="item.photo_url ? [item.photo_url] : []"
                  class="upload-position"
                  :max-length="1"
                  :size="'small'"
                  title="规格图片"
                  @update:list="data => (item.photo_url = data[0])"
                />
                <a-button
                  shape="circle"
                  size="small"
                  class="delete-btn"
                  type="link"
                  :disabled="formState.specs.length == 1 || item.disabled"
                >
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveSpec(index)" />
                  </template>
                </a-button>
              </a-space>
              <a-space :size="10">
                <a-select
                  v-model:value="item.user_tags_range"
                  mode="multiple"
                  show-search
                  :filter-option="userTagFilterOption"
                  allow-clear
                  placeholder="用户标签限制"
                  :options="userTagOptions"
                  class="w-500 m-t-10"
                />
              </a-space>
            </div>
            <a-button type="link" class="p-0" @click="onAddSpec"> 添加一个规格 </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="销售设置">
          <a-form-item label="显示状态" name="is_hidden" class="required">
            <a-radio-group
              :value="formState.is_hidden"
              :options="showStateList.options()"
              @change="formState.is_hidden = !formState.is_hidden - 0"
            />
          </a-form-item>
          <a-form-item label="快递运费" name="freight_id" class="required">
            <a-select
              v-model:value="formState.freight_id"
              placeholder="请选择运费模版"
              :options="freightOption"
              class="w-500"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <div class="rich-text">
      <uc-rich-text v-model="formState.desc" :height="300" toolbar-width="calc(100% - 320px)" placeholder="请输入商品介绍" />
      <a-select
        v-model:value="formState.template_id"
        placeholder="请选择商品详情模版"
        :options="goodsTemplatesOption"
        class="w-300 rich-text-select"
      />
    </div>
  </uc-layout-form>
</template>
<script setup>
import { ref, watch, getCurrentInstance } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { goodsApi, goodsSpecAttrsApi, goodsTemplatesApi, freightApi } from '../../api'
import { showStateList, goodsType } from '../../enums'
import { useCategories } from '../../useCategory'
import { useBrands } from '../../useBrand'
import { useServeOptions } from '../../useServe'
import { useGoodsTagsOptions } from '../../useGoodsTags'
import { cloneDeep } from 'lodash'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { getParentLabelList } from '@/utils/functions'
import { useUserTag } from '@/modules/member/useUserTag'
import { debounce } from 'lodash'
import { useGroups } from '../../useGroup'

const { userTagOptions, userTagFilterOption } = useUserTag()

const { id } = useRoute().params

// 品牌筛选
const filterBrandOption = (input, option) => option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0

// 金额处理 (/100 *100)
const { appContext } = getCurrentInstance()
const thousandSeparator = appContext.config.globalProperties.$formatters.thousandSeparator
const priceToInteger = appContext.config.globalProperties.$formatters.priceToInteger

// 初始值--商品规格
const initCommoditySpec = {
  is_hidden: 0,
  sku: undefined,
  attrs: [],
  price: undefined,
  limit: 1,
  // vip_price: undefined,
  // inside_price: undefined,
  photo_url: undefined,
  disabled: false,
  user_tags_range: []
}

// 商品分类
const { categories } = useCategories()

const { groups } = useGroups()
setTimeout(() => {
  console.log(groups)
}, 5000)

// 商品品牌
const { brandTree } = useBrands()

// 商品服务
const { serveOptions } = useServeOptions()

// 商品场景标签
const { goodsTagsOptions, goodsTagsSearch } = useGoodsTagsOptions()

watch(
  serveOptions,
  newValue => {
    if (newValue) {
      handleServe(formState.value.service)
    }
  },
  { immediate: true }
)

const onChangeSpecShow = item => {
  if (item.sku == formState.value.default_sku) return message.info('不可取消默认规格')
  item.is_hidden = +!item.is_hidden
}

// 商品规格 商品详情模板 运费模板
const goodsAttrOptions = ref([]),
  goodsTemplatesOption = ref([]),
  freightOption = ref([])
const initOptions = () => {
  goodsSpecAttrsApi.get().then(res => {
    goodsAttrOptions.value = useTransformOptions(res, 'title', 'title')
  })
  goodsTemplatesApi.list().then(res => {
    goodsTemplatesOption.value = useTransformOptions(res, 'title', 'id')
    formState.value.template_id = res.find(item => item.is_default == 1)?.id
  })
  freightApi.get().then(res => {
    freightOption.value = useTransformOptions(res, 'title', 'id')
    if (!id) formState.value.freight_id = res.find(item => item.is_default == 1)?.id
  })
}

const router = useRouter()
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  type: 'entity',
  category_id: undefined,
  brand_id: undefined,
  title: undefined,
  standard_title: undefined,
  selling_point: undefined,
  spu: undefined,
  service: [],
  tags: [],
  photo_urls: [],
  video_url: '',
  video_cover: undefined,
  default_sku: undefined,
  // select_spec: [],
  specs: [cloneDeep(initCommoditySpec)],
  is_hidden: 1,
  freight_id: undefined, //默认选择默认运费模板
  desc: undefined,
  template_id: undefined,
  sort: 0,
  on_sale: 0,
  is_share: 1,
  group_ids: []
})

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  goodsApi
    .get(id, { relations: ['specs', 'groups'] })
    .then(goods => {
      goods.specs.forEach(item => {
        item.user_tags_range = item.user_tags_range ? item.user_tags_range : []
      })
      goods.tags = goods.tags || []

      goods.group_ids = []
      goods.groups.forEach(item => {
        goods.group_ids.push(item.id)
      })

      setFormState(goods)
      initOptions()
      addField(formState.value.specs)
    })
    .finally(hideLoading)
} else {
  initOptions()
}
// 添加禁用
const addField = specs => {
  specs.forEach(item => {
    item.price /= 100
    // item.vip_price = thousandSeparator(item.vip_price)
    // item.inside_price = thousandSeparator(item.inside_price)
    item.disabled = true
  })
}

// 自定义商品规格验证
const validator = (_, value) => {
  const { attrs } = value[0]
  if (attrs.length == 0) {
    return Promise.reject('请选择规格')
  }
  const ignoreKeys = ['is_hidden', 'disabled', 'user_tags_range', 'stock', 'standard_name']
  for (const item of value) {
    for (const key in item) {
      if (ignoreKeys.indexOf(key) >= 0) {
        continue
      }
      // 排除is_hidden字段和disabled字段
      if (key == 'attrs') {
        //attrs字段
        for (const attrItem of item[key]) {
          if (!attrItem.value && attrItem.value !== 0) {
            console.log('attrs', attrItem)
            return Promise.reject('请补充商品规格')
          }
        }
      } else {
        if (!item[key] && item[key] !== 0) {
          console.log('key', key, item[key])
          return Promise.reject('请补充商品规格')
        }
      }
    }
  }
  return Promise.resolve()
}

setFormRules({
  type: { required: true, message: '请选择商品类型' },
  category_id: { required: true, message: '请选择商品分类' },
  title: { required: true, message: '请选择商品名称' },
  service: { required: true, message: '请选择商品服务', type: 'array' },
  photo_urls: { required: true, message: '请上传商品相册', type: 'array' },
  default_sku: { required: true, message: '请选择默认规格' },
  specs: { validator: validator, trigger: 'blur' },
  is_hidden: { required: true, message: '请选择显示状态' },
  freight_id: { required: true, message: '快递运费' }
})

// 处理商品分类id--用于展示
const dispose = (data, list) => {
  if (!data) {
    return
  }
  if (list) {
    return getParentLabelList(list, data, 'id')
  }
}
// 改变商品分类
const onCategoryChange = value => {
  formState.value.category_id = value[value.length - 1]
}

// 改变商品品牌
const onBrandChange = value => {
  formState.value.brand_id = value.length ? value[value.length - 1] : null
}

// 取value
const getOptionsValue = options => {
  return options.map(({ name }) => name)
}

// 改变商品规格
const handleAttrs = attrsArr => {
  // 至少一个 最多两个规格
  if (attrsArr.length < 1) {
    return message.info('至少选择一个规格')
  } else {
    if (attrsArr.length > formState.value.specs[0].attrs.length) {
      // 增加
      if (attrsArr.length == 1) {
        formState.value.specs.forEach(item => {
          item.attrs.push({ name: attrsArr[0], value: undefined })
        })
      } else {
        attrsArr.forEach(item => {
          if (!getOptionsValue(formState.value.specs[0].attrs).includes(item)) {
            // 不存在就添加
            formState.value.specs.forEach(item2 => {
              item2.attrs.push({ name: item, value: undefined })
            })
          }
        })
      }
    } else {
      // 减少
      getOptionsValue(formState.value.specs[0].attrs).forEach((item, index) => {
        if (!attrsArr.includes(item)) {
          // 不存在就添加
          formState.value.specs.forEach(item2 => {
            item2.attrs.splice(index, 1)
          })
        }
      })
    }
  }
}

// 改变商品服务选项
const handleServe = value => {
  if (value.length >= 5) {
    serveOptions.value.forEach(item => {
      if (value.includes(item.value)) {
        item.disabled = false
      } else {
        item.disabled = true
      }
    })
  } else {
    serveOptions.value.forEach(item => {
      item.disabled = false
    })
  }
}

// 商品场景标签选择
const handleSearch = debounce((val) => {
  goodsTagsSearch(val)
}, 300)

// 删除规格
const onRemoveSpec = index => formState.value.specs.splice(index, 1)

// 格式化attrs--商品规格
const formattingAttrs = attrs => {
  return attrs.map(({ name }) => {
    return {
      name,
      value: undefined
    }
  })
}

// 添加规格
const onAddSpec = () =>
  formState.value.specs.push(
    cloneDeep({ ...initCommoditySpec, attrs: formattingAttrs(formState.value.specs[0].attrs) })
  )

// 组合参数
const combinedParams = () => {
  const { specs } = cloneDeep(formState.value)
  specs.forEach(item => {
    item.price = priceToInteger(item.price)
    // item.vip_price = priceToInteger(item.vip_price)
    // item.inside_price = priceToInteger(item.inside_price)
  })
  return { specs }
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = { ...formState.value, ...combinedParams() }
  id ? await goodsApi.replace(id, params) : await goodsApi.create(params)
  message.success(`保存成功：${formState.value.title}`)
  router.back()
}
</script>
<style scoped lang="less">
.flex {
  textarea {
    height: 104px !important;
    min-height: 104px !important;
    max-height: 104px !important;
  }
}

.commodity-spec {
  .upload-position {
    :deep(.ant-upload-picture-card-wrapper) {
      width: 32px;
      height: 32px;
    }

    :deep(.ant-upload) {
      margin: 0 !important;
    }
  }
}

.delete-btn {
  padding-top: 2px;
  border: none;

  :deep(.anticon) {
    font-size: 20px;
  }
}

.delete-btn:disabled {
  :deep(.anticon) {
    color: #d9d9d9 !important;
  }
}

.rich-text {
  position: relative;

  &-select {
    position: absolute;
    top: 5px;
    right: 20px;
    z-index: 6;
  }
}
</style>
