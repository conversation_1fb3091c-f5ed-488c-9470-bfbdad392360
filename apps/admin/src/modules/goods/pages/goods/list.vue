<template>
  <uc-layout-list title="全部商品">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            :options="skuOrTitleType.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键词" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-if="categories && categories.length"
          v-model:value="formState.category_id"
          placeholder="商品分类"
          :options="categories"
          :field-names="{ label: 'title', value: 'id' }"
          show-search
          change-on-select
        />
      </a-form-item>
      <a-form-item>
        <a-tree-select
          v-model:value="formState.group_id"
          placeholder="商品分组"
          class="w-250"
          tree-node-filter-prop="title"
          show-search
          :tree-data="groups"
        />
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-model:value="formState.brand_id"
          placeholder="商品品牌"
          :options="brandTree"
          show-search
          change-on-select
        />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.on_sale" placeholder="上架状态" :options="commodityOnState.options()" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增商品
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="商品名称/分类" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-bind="record"
              :url="record.photo_urls[0]"
              :title="record.title"
              :label="record.id"
              symbol=":&nbsp"
              :subtit="getCategory(categories, record.category_id)"
            />
          </template>
        </a-table-column>
        <a-table-column title="商品品牌" align="center">
          <template #default="{ record }">
            {{ record.brand?.name || '-' }}
          </template>
        </a-table-column>
        <a-table-column title="最低价" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.floor_price) }}
          </template>
        </a-table-column>
        <a-table-column title="可售/已售" width="150px" align="right">
          <template #default="{ record }">
            {{ record.surplus_num + '/' + record.sale_num }}
          </template>
        </a-table-column>
        <a-table-column title="排序" width="120px" align="center">
          <template #default="{ record }">
            <a-input
              v-model:value="record.sort"
              style="width: 80px"
              class="t-center"
              placeholder="排序"
              @blur="handleUpdate(record.id, { sort: record.sort - 0 })"
            />
          </template>
        </a-table-column>
        <a-table-column title="上架状态" width="120px">
          <template #default="{ record }">
            <a-switch
              v-model:checked="record.on_sale"
              :un-checked-value="0"
              :checked-value="1"
              @change="handleUpdate(record.id, { on_sale: $event - 0 })"
            />
          </template>
        </a-table-column>
        <a-table-column width="150px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="copyLinkByRoute('goodsDetail', { id: record.id })">
              链接
            </a-button>
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { message } from 'ant-design-vue'
import { getCategory, useCategories } from '../../useCategory'
import { useBrands } from '../../useBrand'
import { goodsApi } from '../../api'
import { skuOrTitleType, commodityOnState } from '../../enums'
import { useGroups } from '../../useGroup'

const linkPath = Object.freeze(`/goods/pages/standard/details/index?id=`)
const router = useRouter()

// 商品分类
const { categories } = useCategories()

const { groups } = useGroups()

const { brandTree } = useBrands()

const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: 'sku',
  conditionValue: undefined,
  category_id: [],
  group_id: undefined,
  brand_id: [],
})

const changeConditionKey = () => {
  formState.value.conditionValue = undefined
}

const getSku = () =>
  formState.value.conditionKey == 'sku' && formState.value.conditionValue
    ? { specs: useTransformQuery({ sku: formState.value.conditionValue }, { sku: 'like' }) }
    : {}

const getCategoryId = () =>
  formState.value.category_id.length
    ? { category_id: formState.value.category_id[formState.value.category_id.length - 1] }
    : {}

const getBrandId = () =>
  formState.value.brand_id.length
    ? { brand_id: formState.value.brand_id[formState.value.brand_id.length - 1] }
    : {}

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  goodsApi.paginator({
    filters: useTransformQuery(
      {
        ...getCategoryId(),
        ...getBrandId(),
        on_sale: formState.value.on_sale,
        title: formState.value.conditionKey == 'title' ? formState.value.conditionValue : undefined,
        group_id: formState.value.group_id
      },
      { title: 'like' }
    ),
    relation_filters: { ...getSku() },
    offset,
    limit,
    relations: ['brand']
  })
)

onRestFormState(() => setPage())

//  处理上架状态更新
const handleUpdate = async (id, props) => {
  await goodsApi.update(id, props)
  message.success('修改成功')
}

const onAdd = () => {
  router.push({
    name: 'goods-add'
  })
}

const onEdit = ({ id }) => {
  router.push({
    name: 'goods-edit',
    params: { id }
  })
}

const handleDelete = ({ id }) => {
  goodsApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}
</script>
<style scoped lang="less"></style>
