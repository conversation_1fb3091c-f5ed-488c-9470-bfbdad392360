export default {
  path: 'goods',
  meta: {
    title: '商品',
    antIcon: 'ShoppingOutlined'
  },
  children: [
    {
      path: 'goods-categories',
      name: 'goods-categories',
      meta: {
        title: '商品分类'
      },
      component: () => import('./pages/goods-categories')
    },
    {
      path: 'goods-groups',
      name: 'goods-groups',
      meta: {
        title: '商品分组'
      },
      component: () => import('./pages/goods-groups')
    },
    {
      path: 'goods-brands',
      name: 'goods-brands',
      meta: {
        title: '商品品牌',
        keepAlive: true
      },
      component: () => import('./pages/goods-brands/list')
    },
    {
      path: 'goods-brands-add',
      name: 'goods-brands-add',
      meta: {
        title: '添加商品品牌'
      },
      hidden: true,
      component: () => import('./pages/goods-brands/edit')
    },
    {
      path: 'goods-brands-edit/:id',
      name: 'goods-brands-edit',
      meta: {
        title: '编辑商品品牌'
      },
      hidden: true,
      component: () => import('./pages/goods-brands/edit')
    },
    {
      path: 'goods-services',
      name: 'goods-services',
      meta: {
        title: '商品服务'
      },
      component: () => import('./pages/goods-services')
    },
    {
      path: 'goods-stock',
      name: 'goods-stock',
      meta: {
        title: '商品库存'
      },
      component: () => import('./pages/goods-stock')
    },
    {
      path: 'goods-template',
      name: 'goods-template',
      meta: {
        title: '商详模板'
      },
      component: () => import('./pages/goods-template/list')
    },
    {
      path: 'goods-template-add',
      name: 'goods-template-add',
      meta: {
        title: '新增商详模板'
      },
      component: () => import('./pages/goods-template/edit'),
      hidden: true
    },
    {
      path: 'goods-template-edit/:id',
      name: 'goods-template-edit',
      meta: {
        title: '编辑商详模板'
      },
      component: () => import('./pages/goods-template/edit'),
      hidden: true
    },
    {
      path: 'goods',
      name: 'goods',
      meta: {
        title: '全部商品',
        keepAlive: true
      },
      component: () => import('./pages/goods/list')
    },
    {
      path: 'goods-add',
      name: 'goods-add',
      meta: {
        title: '新增全部商品'
      },
      component: () => import('./pages/goods/edit'),
      hidden: true
    },
    {
      path: 'goods-edit/:id',
      name: 'goods-edit',
      meta: {
        title: '编辑全部商品'
      },
      component: () => import('./pages/goods/edit'),
      hidden: true
    },
  ]
}
