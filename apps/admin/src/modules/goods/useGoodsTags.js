import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { goodsTagsApi } from './api'
import { ref } from 'vue'

export function useGoodsTagsOptions(filters = {}) {
  const goodsTagsOptions = ref()
  const { loading, setLoading } = useLoading()

  const goodsTagsSearch = (val = '') => {
    let searchFilters = {}
    if(val) {
      searchFilters = { filters: {name: `%${val}%`} }
    } else {
      searchFilters = filters
    }
    const { getData } = useApiRequest(() => {
      goodsTagsOptions.value = []
      setLoading(true)
      return goodsTagsApi.list({
        ...searchFilters
      })
        .then((cates) => {
          goodsTagsOptions.value = useTransformOptions(cates, 'name', 'name')
        }).finally(() => setLoading(false))
    }, false)
    getData()
  }

  goodsTagsSearch()

  return {
    goodsTagsSearch,
    goodsTagsOptions,
    loading
  }
}
