import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { goodsServicesApi } from './api'

export function useServe(callback = null, filters = {}) {
  const { loading, setLoading } = useLoading()
  const { data: serve, getData: refreshServe } = useApiRequest(() => {
    setLoading(true)
    return goodsServicesApi.list({
      ...filters
    })
      .then((cates) => (callback ? callback(cates) : cates))
      .finally(() => setLoading(false))
  })

  return {
    serveLoading: loading,
    serve,
    refreshServe
  }
}

export function useServeOptions(filters) {
  const { serve: serveOptions } = useServe((serve) => useTransformOptions(serve, 'title', 'id'), filters)
  
  return {
    serveOptions
  }
}
