import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { goodsGroupsApi } from './api'
import { handleTree, getParentLabelList, sumAttr } from '@/utils/functions'

// 组合分组数据[筛选二级数据]
const setChildren = groups => {
  if (groups) {
    return handleTree(groups.map(group => {
      group.value = group.key = group.id
      return group
    }), 'id', 'parent_id').map(c => {
      sumAttr(c, 'goods_count')
      return c
    })
  }
}

export function useGroups(filters = {}) {
  const { loading, setLoading } = useLoading()
  const { data: groups, getData: refreshGroups } = useApiRequest(() => {
    setLoading(true)
    // 获取全部数据
    return goodsGroupsApi
      .list({
        ...filters,
        relations: ['goods_count']
      })
      .then(groups => setChildren(groups))
      .finally(() => setLoading(false))
  })
  return {
    groupLoading: loading,
    groups,
    refreshGroups
  }
}

// 展示分组
export const getGroup = (arr, groupId) => {
  const list = getParentLabelList(arr, groupId)
  return list ? list.join('/') : ''
}
