import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { goodsCategoriesApi } from './api'
import { handleTree, getParentLabelList, sumAttr } from '@/utils/functions'

//组合分类数据[筛选二级数据]
const setChildren = categories => {
  if (categories) {
    return handleTree(categories, 'id', 'parent_id').map(c => {
      sumAttr(c, 'goods_count')
      return c
    })
  }
}

export function useCategories(filters = {}) {
  const { loading, setLoading } = useLoading()
  const { data: categories, getData: refreshCategories } = useApiRequest(() => {
    setLoading(true)
    // 获取全部数据
    return goodsCategoriesApi
      .list({
        ...filters,
        relations: ['goods_count']
      })
      .then(cates => setChildren(cates))
      .finally(() => setLoading(false))
  })
  return {
    categoryLoading: loading,
    categories,
    refreshCategories
  }
}

// 展示分类
export const getCategory = (arr, categoryId) => {
  const list = getParentLabelList(arr, categoryId)
  return list ? list.join('/') : ''
}
