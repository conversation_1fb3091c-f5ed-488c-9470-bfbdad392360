import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

export const downloadStockApi = `${import.meta.env.VITE_API_BASE_URL}/goods/stocks/excel/download-template?token=${
  state.token
}` // 下载模板
export const importSTockApi = `${import.meta.env.VITE_API_BASE_URL}/goods/stocks/excel/import?token=${state.token}` // 导入
export const excelExportUrl = `${import.meta.env.VITE_API_BASE_URL}/goods/stocks/excel/export?token=${state.token}` //导出

export const goodsCategoriesApi = apiFactory.restful('/goods/categories') // 商品分类
export const goodsServicesApi = apiFactory.restful('/goods/services') // 商品服务
export const goodsApi = apiFactory.restful('/goods/goods') // 全部商品
export const goodsSpecAttrsApi = apiFactory.command('/goods/spec-attrs') // 商品规格模板
export const goodsTemplatesApi = apiFactory.restful('/goods/templates') // 商品模板 /goods/stocks
export const goodsStocksApi = apiFactory.restful('/goods/stocks') // 商品库存

export const freightApi = apiFactory.command('/setting/freights') //运费模板

export const goodStockSyncApi = apiFactory.command('/setting/settings/sync-goods-stock') // 库存同步
export const goodStockSyncUpdateApi = apiFactory.command('/setting/settings/sync-goods-stock/update') // 库存同步

export const goodsBrandsApi = apiFactory.restful('/goods/brands') // 商品品牌
export const goodsTagsApi = apiFactory.restful('/goods/tags') // 商品场景标签
export const goodsGroupsApi = apiFactory.restful('/goods/groups') // 商品分组
