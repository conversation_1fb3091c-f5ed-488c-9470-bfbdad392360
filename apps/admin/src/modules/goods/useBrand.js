import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { goodsBrandsApi } from './api'
import { ref } from 'vue'
import { handleTree } from '@/utils/functions'

export function useBrands(filters = {}) {
  const options = ref([])
  const tree = ref([])
  const { loading, setLoading } = useLoading()
  const { data: brands, getData: refreshBrands } = useApiRequest(() => {
    setLoading(true)
    // 获取全部数据
    return goodsBrandsApi.list({
      ...filters
    })
    .then(res=>{
      res.forEach(item => {
        options.value.push({
          value: item.id,
          label: item.name,
          id: item.id,
          parent_id: item.parent_id
        })
      })
      tree.value = handleTree(options.value, 'value', 'parent_id')
    })
    .finally(() => setLoading(false))
  })

  return {
    brandLoading: loading,
    brands,
    brandOptions: options,
    brandTree: tree,
    refreshBrands
  }
}