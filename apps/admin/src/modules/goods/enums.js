/**
 * sku or 商品名称
 */
export const skuOrTitleType = Object.freeze({
  /**
   * sku
   */
  sku: 'sku',

  /**
   * 商品名称
   */
  title: 'title',

  options() {
    return [
      { label: '商品sku', value: this.sku },
      { label: '商品名称', value: this.title }
    ]
  }
})

/**
 * 商品上架状态
 */
export const commodityOnState = Object.freeze({
  /**
   * 上架
   */
  putaway: 1,

  /**
   * 下架
   */
  soldOut: 0,

  options() {
    return [
      { label: '上架', value: this.putaway },
      { label: '下架', value: this.soldOut }
    ]
  }
})

/**
 * 商品售后服务
 */
export const afterSalesList = Object.freeze({
  /**
   * 退款
   */
  refund: 'refund',

  /**
   * 退货
   */
  return: 'return',

  options() {
    return [
      { label: '退款', value: this.refund },
      { label: '退货', value: this.return }
    ]
  }
})

/**
 * 商品显示状态
 */
export const showStateList = Object.freeze({
  /**
   * 显示
   */
  show: 0,

  /**
   * 隐藏
   */
  hide: 1,

  options() {
    return [
      { label: '显示', value: this.show },
      { label: '隐藏', value: this.hide }
    ]
  }
})

/**
 * 商品类型
 */
export const goodsType = Object.freeze({
  /**
   * 实物商品
   */
  entity: 'entity',

  /**
   * 虚拟商品
   */
  invented: 'invented',

  /**
   * 定制商品
   */
  custom: 'custom',

  options() {
    return [
      { label: '实物商品', value: this.entity },
      { label: '虚拟商品', value: this.invented },
      { label: '定制商品', value: this.custom }
    ]
  }
})
