<template>
  <!-- 登录 -->
  <div class="m-login w-vw h-vh flex flex-center">
    <div class="m-img-text flex hide bgc-white">
      <div class="lf-img flex flex-center">
        <img :src="assets.decorate" />
      </div>
      <div class="rt-txt">
        <div class="u-hd flex flex-sb">
          <h3 class="u-tit">
            {{ updatePasswordForce ? '设置密码' : loginType === 'scan' ? '扫码登录' : '密码登录' }}
          </h3>
          <div class="u-btn" @click="onLoginType">
            <span>{{ loginType === 'scan' ? '密码登录' : '扫码登录' }}</span>
            <uc-ant-icon name="CaretRightOutlined" />
          </div>
        </div>
        <!-- 设置密码 -->
        <div v-if="updatePasswordForce" class="u-bd-password">
          <a-form>
            <a-form-item>
              <a-input-password v-model:value.trime="pwdFormState.new_password" placeholder="请输入8-20位密码">
                <template #addonBefore>
                  <uc-ant-icon name="LockOutlined" />
                </template>
              </a-input-password>
            </a-form-item>
            <a-form-item>
              <a-input-password v-model:value.trime="pwdFormState.password_again" placeholder="请再次输入新密码">
                <template #addonBefore>
                  <uc-ant-icon name="LockOutlined" />
                </template>
              </a-input-password>
            </a-form-item>
            <a-form-item name="button">
              <a-button type="primary" block :disabled="isLogining" :loading="isLogining" @click="handleSubmit">
                保存
              </a-button>
            </a-form-item>
          </a-form>
        </div>
        <!-- 扫码 -->
        <div v-else-if="loginType === 'scan'" class="u-bd-scan flex flex-sc">
          暂未开放
        </div>
        <!-- 登录 -->
        <div v-else class="u-bd-password">
          <a-form>
            <a-form-item name="username">
              <a-input v-model:value.trim="formState.username" placeholder="请输入登录账号" @press-enter="handleLogin">
                <template #addonBefore>
                  <uc-ant-icon name="UserOutlined" />
                </template>
              </a-input>
            </a-form-item>
            <a-form-item name="password">
              <a-input-password
                v-model:value.trim="formState.password"
                placeholder="请输入8-20位密码"
                @press-enter="handleLogin"
              >
                <template #addonBefore>
                  <uc-ant-icon name="LockOutlined" />
                </template>
              </a-input-password>
            </a-form-item>
            <a-form-item name="button">
              <a-button type="primary" block :disabled="isLogining" :loading="isLogining" @click="handleLogin">
                登录{{ isLogining ? '中' : '' }}
              </a-button>
            </a-form-item>
          </a-form>
        </div>
      </div>
    </div>
    <p class="u-desc w-fill flex flex-center color-white">
      Copyright © 2019-present 京ICP备10009123号-19 北京兴长信达科技发展有限公司
    </p>
  </div>
</template>
<script setup>
import assets from '../assets.config'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { loginApi, shopStyleActions } from '../api'
import { editPasswordApi, forceEditPasswordApi } from '@/modules/auth/api'
import { useStore } from '@/store/auth'
import { useShop } from '@/store/shop'
import { cloneDeep } from 'lodash'
import { Base64 } from 'js-base64'
import { encrypt } from '@/utils/functions.js'

const { setUserInfo, setToken } = useStore()
const router = useRouter()
const loginType = ref('password')
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  username: '',
  password: ''
})

const updatePasswordForce = ref(false)

setFormRules({
  username: { required: true, message: '请输入登录账号' },
  password: { required: true, message: '请输入登录密码' },
})

const isLogining = ref(false) // 是否登录中

const onLoginType = () => {
  updatePasswordForce.value = false
  loginType.value = loginType.value === 'scan' ? 'password' : 'scan'
}
// 点击登录按钮
const handleLogin = async () => {
  if (!(await validateForm()) || isLogining.value) return

  isLogining.value = true

  // 加密
  //进行AES加密
  const loginForm = cloneDeep(formState.value)
  loginForm.username = encrypt(loginForm.username, publicKey.value)
  loginForm.password = encrypt(loginForm.password, publicKey.value)

  loginApi
    .post({ ...loginForm, grant_type: 'password' })
    .then((data) => {
      if (data.password_expired) {
        updatePasswordForce.value = true
        message.error(data.password_expired_remark)
      } else {
        setUserInfo(data)
        setToken(data.token)
        setShopInfo()
        router.push({ name: import.meta.env.VITE_DEFAULT_ROUTE || 'admin_home' })
        message.success('登录成功')
      }
    })
    .finally(() => {
      isLogining.value = false
    })
}

const publicKey = ref('')

const getPublicKey = () => {
  loginApi
    .get()
    .then((data) => {
      publicKey.value = Base64.decode(data)
    })
}

onMounted(getPublicKey)

const { setShop } = useShop()
const setShopInfo = () => {
  shopStyleActions.get().then((data) => {
    const { config } = data
    if (config) {
      setShop(config)
    }
  })
}

// 设置密码
const { formState: pwdFormState, resetFormState: resetPwdFormState, setFormRules: setPwdFormRules, validateForm: validatePwdForm } = useFormState({
  new_password: undefined,
  password_again: undefined
})

const newPasswordValidator = (rule, value) => {
  const preg = new RegExp(/^(?=.*[A-Z])(?=.*[a-z])(?=.*\d)(?=.*[.@#$%^&+=!])/)

  if (!value) {
    return Promise.reject('请输入新设密码')
  } else if(value === formState.value.password) {
    return Promise.reject('密码未改变')
  } else if(value.length < 8) {
    return Promise.reject('新设密码长度必须大于等于8位')
  } else if(preg.test(value) === false) {
    return Promise.reject('新设密码必须包含大小写字母，数字，和特殊字符（.@#$%^&+=!）')
  }

  return Promise.resolve(true)
}

const passwordAgainValidator = (rule, value) => {
  if(!value) {
    return Promise.reject('请再次输入新设密码')
  } else if(value !== pwdFormState.value.new_password) {
    return Promise.reject('两次输入的密码不一致')
  }
  return Promise.resolve(true)
}

setPwdFormRules({
  new_password: { validator: newPasswordValidator },
  password_again: { validator: passwordAgainValidator }
})

const handleSubmit = async () => {
  if (!(await validateForm()) || !(await validatePwdForm()) || isLogining.value) return

  isLogining.value = true
    // 加密
  //进行AES加密
  const loginForm = cloneDeep({ ...formState.value, ...pwdFormState.value })
  loginForm.old_password = encrypt(loginForm.password, publicKey.value)
  loginForm.password = encrypt(loginForm.new_password, publicKey.value)
  loginForm.password_again = encrypt(loginForm.password_again, publicKey.value)

  if(updatePasswordForce.value) {
    loginForm.username = encrypt(loginForm.username, publicKey.value)
    await forceEditPasswordApi.post(loginForm)
  } else {
    await editPasswordApi.post(loginForm)
  }

  message.success('密码设置成功')

  loginApi
    .post({ username: loginForm.username, password: loginForm.password, grant_type: 'password' })
    .then((data) => {
      if (data.password_expired) {
        updatePasswordForce.value = true
        message.error(data.password_expired_remark)
      } else {
        setUserInfo(data)
        setToken(data.token)
        setShopInfo()
        isLogining.value = false
        router.push({ name: import.meta.env.VITE_DEFAULT_ROUTE || 'admin_home' })
      }
    })
}
</script>
<style scoped lang="less">
.m-login {
  @primary: #1890ff;
  background: @primary;

  // 固定定位
  .position(@left, @height) {
    position: fixed;
    left: @left;
    height: @height;
  }

  .u-img-logo {
    .position(30px, 50px);
    top: 30px;
  }

  .m-img-text {
    width: 680px;
    height: 316px;
    border-radius: 10px;

    .lf-img {
      width: 54%;
      background: #60b0ff;

      img {
        width: 70%;
      }
    }

    .rt-txt {
      width: 46%;
      padding: 20px;
      padding-top: 44px;

      .u-hd {
        margin-bottom: 20px;
        align-items: flex-end;
        line-height: 1;

        .u-tit {
          margin-bottom: 0;
          color: @primary;
          font-size: 24px;
          font-weight: bold;
        }

        .u-btn {
          font-size: 14px;
          color: #666;
          cursor: pointer;

          :deep(.cur-icon) {
            opacity: 0.7;
          }
        }
      }

      .u-bd-scan {}

      .u-bd-password {
        margin-top: 40px;

        :deep(.ant-form-item) {
          .ant-btn {
            &[disabled] {
              background: #b3b3b3;
              color: @color-white;
            }
          }
        }
      }
    }
  }

  .u-desc {
    .position(0, 60px);
    bottom: 0;
    font-size: 14px;
  }
}
</style>
