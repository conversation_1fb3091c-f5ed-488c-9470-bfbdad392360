import { orderAttributesBizType, orderAttributesType } from '@/modules/order/enums'

const getAttrValue = list => {
  return list.reduce((sum, item) => sum + (Number(item.value) || 0), 0)
}
const getAttrTitle = list => {
  return Array.from(new Set(list.map(item => item.title).filter(item => item))).join('+')
}

export const useTransformAttr = shop_order_attributes => {
  const couponPromotions = [],
    freightPromotions = [],
    salesPromotions = [],
    activityPromotions = []
  shop_order_attributes.forEach(item => {
    const { biz_type, type } = item
    if (biz_type === orderAttributesBizType.coupon && type !== orderAttributesType.freight) {
      couponPromotions.push(item)
    } else if (
      (biz_type === orderAttributesBizType.promotion && type === orderAttributesType.salesFreight) ||
      (biz_type === orderAttributesBizType.coupon && type === orderAttributesType.freight)
    ) {
      freightPromotions.push(item)
    } else if (
      (biz_type === orderAttributesBizType.promotion && type !== orderAttributesType.salesFreight) ||
      biz_type === orderAttributesBizType.distribution
    ) {
      salesPromotions.push(item)
    } else if (biz_type === orderAttributesBizType.group) {
      activityPromotions.push(item)
    } else if (biz_type === orderAttributesBizType.gift_box) {
      activityPromotions.push(item)
    } else if (biz_type === orderAttributesBizType.gift_card) {
      activityPromotions.push(item)
    } else if (biz_type === orderAttributesBizType.internal_purchase) {
      activityPromotions.push(item)
    }
  })
  // 优惠券
  const couponInfo = {
    coupon_discount_amount: getAttrValue(couponPromotions),
    coupon_discount_title: getAttrTitle(couponPromotions)
  }
  // 运费
  const freightInfo = {
    freight_discount: getAttrValue(freightPromotions),
    freight_title: getAttrTitle(freightPromotions)
  }
  // 促销
  const salesInfo = {
    promotion_discount_amount: getAttrValue(salesPromotions),
    promotion_discount_title: getAttrTitle(salesPromotions)
  }
  // 活动
  const actInfo = {
    activity_discount_amount: getAttrValue(activityPromotions),
    activity_discount_title: getAttrTitle(activityPromotions)
  }
  return {
    ...couponInfo,
    ...freightInfo,
    ...salesInfo,
    ...actInfo
  }
}
