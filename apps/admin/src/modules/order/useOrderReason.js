import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { orderSettingApi } from './api'

export function useReason(callback = null, type = 'refund') { // 退货退款理由
  const { loading, setLoading } = useLoading()
  const { data: reason, getData: refreshReason } = useApiRequest(() => {
    setLoading(true)
    return orderSettingApi.get()
      .then(({ value }) => type == 'refund' ? (callback ? callback(value.refund_reason) : value.refund_reason) : (callback ? callback(value.return_reason) : value.return_reason))
      .finally(() => setLoading(false))
  })

  return {
    reasonLoading: loading,
    reason,
    refreshReason
  }
}

function useTransOptions(arr) { // 格式化配置项
  return arr.split('\n').map((item, index) => {
    return {
      label: item,
      value: index
    }
  })
}

export function useRefundReasonOptions() { // 退款配置项
  const { reason: refundReasonOptions } = useReason((reason) => useTransOptions(reason))

  return {
    refundReasonOptions
  }
}

export function useReturnReasonOptions() { // 退货配置项
  const { reason: returnReasonOptions } = useReason((reason) => useTransOptions(reason),'return')

  return {
    returnReasonOptions
  }
}

export function useReasonFilter(arr, value) {
  if (arr) {
    return arr.find(item => item.value == value)?.label
  }
}