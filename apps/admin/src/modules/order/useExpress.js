import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { expressCodeApi } from './api'

export async function useExpress(callback = null) {
  const companies = await expressCodeApi.list()
  return callback(companies)
}

export async function useExpressOptions() {
  const companyOptions = await useExpress((companies) => {
    let options = []
    Object.keys(companies).forEach(key => {
      options.push({ label: companies[key], value: key })
    })
    return options
  })
  return { companyOptions }
}
