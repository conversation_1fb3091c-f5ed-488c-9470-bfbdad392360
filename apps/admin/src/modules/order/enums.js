/**
 * 订单搜索条件
 */
export const orderCondition = Object.freeze({
  /**
   * 订单编号
   */
  orderCode: 'sn',
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 手机号码
   */
  phoneNumber: 'phone_number',
  /**
   * 礼品编码
   */
  giftCode: 'code',
  /**
   * 礼品名称
   */
  giftName: 'title',

  //shop newly
  /**
   * 商品SKU
   */
  productSku: 'sku',
  /**
   * 商品名称
   */
  productName: 'goods_title',
  /**
   * 会员昵称
   */
  memberName: 'nickname',
  /**
   * 会员手机
   */
  memberPhone: 'user_phone_number',
  /**
   * 收货人姓名
   */
  receiverName: 'consignee',
  /**
   * 收货人手机
   */
  receiverPhone: 'phone_number',
  /**
   * 服务单号
   */
  saleSn: 'sale_sn',
  /**
   * 渠道追踪码
   */
  trackCode: 'track_code',
  /**
   *
   * 交易流水号
   */
  tradeNumber: 'trade_number',

  giftOptions() {
    return [
      { label: '订单编号', value: this.orderCode },
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phoneNumber },
      { label: '礼品编码', value: this.giftCode },
      { label: '礼品名称', value: this.giftName }
    ]
  },

  shopOptions() {
    return [
      { label: '订单编号', value: this.orderCode },
      { label: '商品SKU', value: this.productSku },
      { label: '商品名称', value: this.productName },
      { label: '会员昵称', value: this.memberName },
      { label: '会员手机', value: this.memberPhone },
      { label: '收货人姓名', value: this.receiverName },
      { label: '收货人手机', value: this.receiverPhone },
      { label: '渠道追踪码', value: this.trackCode },
      { label: '交易流水号', value: this.tradeNumber }
    ]
  },

  commentOptions() {
    return [
      { label: '用户昵称', value: this.nickname },
      { label: '商品SKU', value: this.productSku },
      { label: '商品名称', value: this.productName }
    ]
  },

  refundApplyOptions() {
    return [
      { label: '订单编号', value: this.orderCode },
      { label: '服务单号', value: this.saleSn },
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phoneNumber }
    ]
  }
})

/* 支付状态 */
export const orderPayStatus = Object.freeze({
  /**
   * 未支付
   */
  unpaid: 'unpaid',

  /**
   * 已支付
   */
  paid: 'paid',

  /**
   * 已退款
   */
  refund: 'refund',

  options() {
    return [
      { label: '未支付', value: 'unpaid' },
      { label: '已支付', value: 'paid' },
      { label: '已退款', value: 'refund' }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 购物订单类型 */
export const shopOrderType = Object.freeze({
  //普通订单、特价秒杀、限时折扣、满减促销、多人拼团、砍价助力
  /**
   * 普通订单
   */
  normal: 'normal',

  options() {
    return [
      { label: '普通订单', value: this.normal },
      { label: '特价秒杀', value: this.normal },
      { label: '限时折扣', value: this.normal },
      { label: '满减促销', value: this.normal },
      { label: '多人拼团', value: this.normal },
      { label: '砍价助力', value: this.normal }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 订单状态 */
export const orderStatus = Object.freeze({
  /**
   * 待支付
   */
  unpaid: 'unpaid',
  /**
   * 待发货
   */
  unshipped: 'unshipped',
  /**
   * 待收货
   */
  shipped: 'shipped',
  /**
   * 已完成
   */
  completed: 'completed',
  /**
   * 已取消
   */
  cancelled: 'cancelled',
  /**
   * 已提货
   */
  picked: 'picked',
  /**
   * 待提货
   */
  unpicked: 'unpicked',

  options() {
    return [
      // { label: '待支付', value: this.unpaid, color: '#F5222D' },
      { label: '待发货', value: this.unshipped, color: '#1890FF' },
      { label: '待收货', value: this.shipped, color: '#FA8C16' },
      { label: '待提货', value: this.unpicked, color: '#FA8C16' },
      { label: '已完成', value: this.completed, color: '#52C41A' },
      { label: '已提货', value: this.picked, color: '#52C41A' },
      { label: '已取消', value: this.cancelled, color: '#d9d9d9' }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  },
  giftOptions() {
    return [
      { label: '待发货', value: this.unshipped, color: '#1890FF' },
      { label: '待收货', value: this.shipped, color: '#FA8C16' },
      { label: '待提货', value: this.unpicked, color: '#FA8C16' },
      { label: '已完成', value: this.completed, color: '#52C41A' },
      { label: '已提货', value: this.picked, color: '#52C41A' },
      { label: '已取消', value: this.cancelled, color: '#d9d9d9' }
    ]
  },
  giftFilterValue(value) {
    return this.giftOptions().find(op => op.value == value) ?? {}
  },
  shopOptions() {
    return [
      { label: '待支付', value: this.unpaid, color: '#F5222D' },
      { label: '待发货', value: this.unshipped, color: '#1890FF' },
      { label: '待收货', value: this.shipped, color: '#FA8C16' },
      { label: '待提货', value: this.unpicked, color: '#FA8C16' },
      { label: '已完成', value: this.completed, color: '#52C41A' },
      { label: '已提货', value: this.picked, color: '#52C41A' },
      { label: '已取消', value: this.cancelled, color: '#d9d9d9' }
    ]
  },
  customOptions() {
    return [
      { label: '待发货', value: this.unshipped, color: '#1890FF' },
      { label: '待收货', value: this.shipped, color: '#FA8C16' },
      { label: '已完成', value: this.completed, color: '#52C41A' },
      { label: '已取消', value: this.cancelled, color: '#d9d9d9' }
    ]
  },
  shopFilterValue(value) {
    return this.shopOptions().find(op => op.value == value) ?? {}
  }
})

/* 订单来源 */
export const orderSource = Object.freeze({
  /**
   * 积分商城
   */
  credit: 'credit_good',
  /**
   * 试用活动
   */
  trial: 'trial',
  /**
   * 抽奖活动
   */
  draw: 'draw_activity',
  /**
   * 签到活动
   */
  sign: 'sign_activity',
  /**
   * 邀请活动
   */
  invite: 'invite_activity',
  /**
   * 注册活动
   */
  register: 'register_activity',

  options() {
    return [
      { label: '积分商城', value: this.credit },
      { label: '抽奖活动', value: this.draw },
      { label: '签到活动', value: this.sign },
      { label: '邀请活动', value: this.invite },
      { label: '注册活动', value: this.register }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 快递公司 */
export const expressType = Object.freeze({
  /**
   * 韵达快递
   */
  YunDa: 'yunda_express',
  /**
   * 顺丰快递
   */
  SF: 'sf_express',
  /**
   * 中通快递
   */
  ZTO: 'zto_express',

  options() {
    return [
      { label: '韵达快递', value: this.YunDa },
      { label: '顺丰快递', value: this.SF },
      { label: '中通快递', value: this.ZTO }
    ]
  }
})

/* 退款状态 */
export const refundStatus = Object.freeze({
  /**
   * 待处理
   */
  pending: 'pending',

  /**
   * 已退款
   */
  paid: 'refund',

  /**
   * 已拒绝
   */
  refund: 'refuse',

  /**
   * 已关闭
   */
  close: 'close',

  options() {
    return [
      { label: '待处理', value: this.pending, color: '#1890FF' },
      { label: '已退款', value: this.refund, color: '#52C41A' },
      { label: '已拒绝', value: this.refuse, color: '#FF4D4F' },
      { label: '已关闭', value: this.close, color: '#d9d9d9' }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/**
 * 商品评论公开状态
 */
export const commentIsHiddenState = Object.freeze({
  /**
   * 上架
   */
  show: 0,

  /**
   * 下架
   */
  isHidden: 1,

  options() {
    return [
      { label: '公开', value: this.show },
      { label: '隐藏', value: this.isHidden }
    ]
  }
})

/* 订单类型 */
export const orderType = Object.freeze({
  /**
   * 普通订单
   */
  normal: 'normal',
  /**
   * 定制订单
   */
  custom: 'custom',
  /**
   * 满减促销
   */
  cash: 'cash',
  /**
   * 限时折扣
   */
  discount: 'discount',
  /**
   * 砍价助力
   */
  bargain: 'bargain',
  /**
   * 特价秒杀
   */
  seckill: 'seckill',
  /**
   * 新品试用
   */
  trial: 'trial',
  /**
   * 尖货抽签
   */
  top_goods: 'top_goods',
  /**
   * 加价换购
   */
  markup: 'markup',
  /**
   * 赠品促销
   */
  gift: 'gift',
  /**
   * 多人拼团
   */
  group: 'group',
  /**
   * 运费促销
   */
  sales_freight: 'sales_freight',
  /**
   * 活动发券
   */
  coupon: 'coupon',
  /**
   * 团购
   */
  wholesale: 'wholesale',
  /**
   * 现场活动
   */
  site: 'site',
  /**
   * 定制礼盒活动
   */
  gift_box: 'gift_box',
  /**
   * 礼品卡
   */
  gift_card: 'gift_card',
  /**
   * 内购订单
   */
  internal_purchase: 'internal_purchase',

  options() {
    return [
      { label: '普通订单', value: this.normal },
      { label: '定制订单', value: this.custom },
      { label: '活动发券', value: this.coupon },
      { label: '满减促销', value: this.cash },
      { label: '满赠促销', value: this.gift },
      { label: '限时折扣', value: this.discount },
      { label: '砍价助力', value: this.bargain },
      { label: '特价秒杀', value: this.seckill },
      { label: '加价换购', value: this.markup },
      { label: '员工内购', value: this.internal_purchase },
      { label: '尖货抽签', value: this.top_goods },
      { label: '运费促销', value: this.sales_freight },
      { label: '多人拼团', value: this.group },
      { label: '新品试用', value: this.trial },
      { label: '团购批发', value: this.wholesale },
      { label: '现场活动', value: this.site },
      { label: '定制礼盒', value: this.gift_box },
      { label: '礼品卡', value: this.gift_card }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  },
  filterValues(values) {
    if (!values) return []
    return this.options().filter(op => values.includes(op.value)) ?? []
  }
})

/* 支付方式 */
export const payMethod = Object.freeze({
  /**
   * 微信支付
   */
  wechatPayJsapi: 'wechat-pay-jsapi',

  options() {
    return [{ label: '微信支付', value: this.wechatPayJsapi }]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 售后状态 */
export const afterSaleType = Object.freeze({
  /**
   * 待处理
   */
  normal: 'normal',
  /**
   * 待退款
   */
  refundWait: 'refund_wait',
  /**
   * 待签收
   */
  receivingWait: 'receiving_wait',
  /**
   * 待退货
   */
  returnWait: 'return_wait',
  /**
   * 已退款
   */
  completed: 'completed',
  /**
   * 已拒绝
   */
  refuse: 'refuse',
  /**
   * 已关闭
   */
  closed: 'closed',

  applyOptions() {
    return [
      { label: '待处理', value: this.normal, color: '#1890FF' },
      { label: '已退款', value: this.completed, color: '#52C41A' },
      { label: '已拒绝', value: this.refuse, color: '#FF4D4F' },
      { label: '已关闭', value: this.closed, color: 'rgb(217, 217, 217)' }
    ]
  },
  applyFilterValue(value) {
    return this.applyOptions().find(op => op.value == value) ?? {}
  },
  cargoOption() {
    return [
      { label: '待处理', value: this.normal, color: '#1890FF' },
      { label: '待退款', value: this.refundWait, color: '#1890FF' },
      { label: '待签收', value: this.receivingWait, color: '#1890FF' },
      { label: '待退货', value: this.returnWait, color: '#FA8C16' },
      { label: '已退款', value: this.completed, color: '#52C41A' },
      { label: '已拒绝', value: this.refuse, color: '#FF4D4F' },
      { label: '已关闭', value: this.closed, color: 'rgb(217, 217, 217)' }
    ]
  },
  cargoFilterValue(value) {
    return this.cargoOption().find(op => op.value == value) ?? {}
  }
})

/* 定制订单 */
export const customStatus = Object.freeze({
  /* 合成中 */
  normal: 'normal',
  /* 合成完成 */
  completed: 'completed',
  // 是否完成
  isCompleted(value) {
    return value === this.completed
  }
})

/* 订单组合状态 */
export const orderCombStatus = Object.freeze({
  /**
   * 未支付
   */
  unpaid: 'unpaid',
  /**
   * 未发货
   */
  unshipped: 'unshipped',
  /**
   * 未自提
   */
  unpicked: 'unpicked',
  /**
   * 已发货
   */
  shipped: 'shipped',
  /**
   * 已取消
   */
  cancelled: 'cancelled',
  /**
   * 已完成
   */
  completed: 'completed',
  /**
   * 已自提
   */
  picked: 'picked',
  /**
   * 已退款
   */
  refund: 'refund',

  options() {
    return [
      { label: '未支付', value: this.unpaid },
      { label: '未发货', value: this.unshipped },
      { label: '未自提', value: this.unpicked },
      { label: '已发货', value: this.shipped },
      { label: '已取消', value: this.cancelled },
      { label: '已完成', value: this.completed },
      { label: '已自提', value: this.picked },
      { label: '已退款', value: this.refund }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

/* 发货方式 */
export const orderShippingMethod = Object.freeze({
  /**
   * 邮寄
   */
  express: 'express',
  /**
   * 自提
   */
  pick_up: 'pick_up',

  options() {
    return [
      { label: '邮寄', value: this.express },
      { label: '自提', value: this.pick_up }
    ]
  },
  filterValue(value) {
    return this.options().find(op => op.value == value) ?? {}
  }
})

export const orderAttributesBizType = {
  coupon: 'coupon',
  promotion: 'promotion',
  group: 'group',
  distribution: 'distribution',
  gift_box: 'gift_box',
  gift_card: 'gift_card',
  internal_purchase: 'internal_purchase'
}
export const orderAttributesType = {
  cash: 'cash',
  salesFreight: 'sales_freight',
  freight: 'freight',
  distribution: 'distribution'
}
