export default {
  path: 'order',
  meta: {
    title: '订单',
    antIcon: 'ProfileOutlined'
  },
  children: [
    {
      path: 'entity-list',
      name: 'entity-list',
      meta: {
        title: '实物订单',
        keepAlive: true
      },
      component: () => import('./pages/gift-order/entity/list.vue')
    },
    {
      path: 'entity-detail/:id',
      name: 'entity-detail',
      meta: {
        title: '实物订单详情'
      },
      component: () => import('./pages/gift-order/entity/details.vue'),
      hidden: true
    },
    {
      path: 'invented-list',
      name: 'invented-list',
      meta: {
        title: '虚拟订单',
        keepAlive: true
      },
      component: () => import('./pages/gift-order/invented/list.vue')
    },
    {
      path: 'invented-detail/:id',
      name: 'invented-detail',
      meta: {
        title: '虚拟订单详情'
      },
      component: () => import('./pages/gift-order/invented/details.vue'),
      hidden: true
    },
    {
      path: 'shop-list',
      name: 'shop-list',
      meta: {
        title: '购物订单',
        keepAlive: true
      },
      component: () => import('./pages/shop-order/shop/list.vue')
    },
    {
      path: 'shop-detail/:id',
      name: 'shop-detail',
      meta: {
        title: '购物订单详情'
      },
      component: () => import('./pages/shop-order/shop/details.vue'),
      hidden: true
    },
    {
      path: 'shop-preview/:id/:sku/:sn',
      name: 'shop-preview',
      meta: {
        title: '定制预览',
        useLayout: false
      },
      component: () => import('./pages/shop-order/shop/preview.vue'),
      hidden: true
    },
    {
      path: 'refund-apply-list',
      name: 'refund-apply-list',
      meta: {
        title: '退款申请',
        keepAlive: true
      },
      component: () => import('./pages/shop-order/refund-apply/list.vue')
    },
    {
      path: 'refund-apply-detail/:id',
      name: 'refund-apply-detail',
      meta: {
        title: '退款申请详情'
      },
      component: () => import('./pages/shop-order/refund-apply/details.vue'),
      hidden: true
    },
    {
      path: 'sales-return-list',
      name: 'sales-return-list',
      meta: {
        title: '退货申请',
        keepAlive: true
      },
      component: () => import('./pages/shop-order/sales-return/list.vue')
    },
    {
      path: 'sales-return-detail/:id',
      name: 'sales-return-detail',
      meta: {
        title: '退货申请详情'
      },
      component: () => import('./pages/shop-order/sales-return/details.vue'),
      hidden: true
    },
    {
      path: 'comment-list',
      name: 'comment-list',
      meta: {
        title: '订单评论',
        keepAlive: true
      },
      component: () => import('./pages/shop-order/comment/list.vue'),
      hidden: false
    }
  ]
}
