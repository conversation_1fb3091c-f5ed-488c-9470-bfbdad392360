<template>
  <div class="shop-details">
    <a-space direction="vertical" size="large" class="flex">
      <a-card title="基本信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="订单编号">
            {{ document.sn || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="下单用户">
            {{ document.nickname || '-' }}({{ $formatters.numberEncryption(document.user?.phone_number) || '' }})
          </a-descriptions-item>
          <a-descriptions-item label="下单时间">
            {{ document.created_at || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="订单来源">
            {{ orderSource.filterValue(document.source)?.label || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="积分消耗">
            {{ document.credit_total || 0 }}
          </a-descriptions-item>
          <a-descriptions-item label="订单状态">
            {{ orderStatus.filterValue(document.shipping_status)?.label }}
          </a-descriptions-item>
          <a-descriptions-item label="充值账户">
            {{ document.account || document.phone_number || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card title="兑换礼品">
        <a-table :data-source="document.gift?[document.gift]:[]" row-key="id" :pagination="false">
          <a-table-column title="礼品名称/规格" ellipsis>
            <template #default="{ record }">
              <uc-img-text
                :url="record.photo_urls[0]"
                :title="record.title"
                :label="GiftType.filterValue(record.type)"
                symbol="&nbsp/&nbsp"
                :subtit="useCategoryFilter(categoryOptions, record.category_id)"
              />
            </template>
          </a-table-column>
          <a-table-column title="消耗积分" width="120px">
            <template #default>
              {{ document.credit_total }}
            </template>
          </a-table-column>
          <a-table-column title="数量" width="120px">
            <template #default>
              {{ document.quantity }}
            </template>
          </a-table-column>
        </a-table>
      </a-card>
    </a-space>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { inventedOrdersApi } from "../../../api"
import { shopOrderType, orderStatus, orderSource } from '../../../enums'
import { useCategoryOptions, useCategoryFilter } from "../../../../gift/useCategory"
import { GiftType } from '../../../../gift/enums'

const { categoryOptions } = useCategoryOptions()

const router = useRouter()

const { id } = useRoute().params

let document = ref({})
if (id) {
  const hideLoading = message.loading('正在加载数据...')
  inventedOrdersApi.get(id, { relations: ["gift",'user'] }).then(res => {
    document.value = res
  }).finally(hideLoading)
}else{
  router.back()
}

</script>
<style scoped lang="less">
</style>
