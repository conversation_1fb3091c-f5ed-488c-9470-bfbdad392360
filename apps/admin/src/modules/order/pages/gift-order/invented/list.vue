<template>
  <div class="shop-list">
    <uc-layout-list title="虚拟订单">
      <template #filter>
        <a-form-item>
          <a-input-group compact>
            <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
          </a-input-group>
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.source"
            placeholder="订单来源"
            allow-clear
            :options="orderSource.options()"
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.shipping_status"
            placeholder="订单状态"
            allow-clear
            :options="orderStatus.options()"
          />
        </a-form-item>
        <a-form-item>
          <a-range-picker v-model:value="formState.created_at" :placeholder="['下单开始时间', '下单结束时间']" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage"> 查询 </a-button>
          <a-button @click="handleResetFormState"> 重置 </a-button>
        </a-form-item>
      </template>
      <template #extra>
        <a-button @click="handleExport"> 导出 </a-button>
      </template>
      <template #list>
        <a-table
          :data-source="data.items"
          row-key="id"
          :loading="loading"
          :pagination="stdPagination(data)"
          @change="setPage"
        >
          <a-table-column title="订单编号/来源" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis color-primary" style="cursor: pointer" @click="toDetails(record)">
                {{ record.sn }}
              </div>
              <div class="text-ellipsis">
                {{ orderSource.filterValue(record.source)?.label }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="购买商品" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis">{{ record.gift_code }} / x{{ record.quantity }} / {{ record.gift_title }}</div>
            </template>
          </a-table-column>
          <a-table-column title="消耗积分" data-index="credit_total" width="120px" />
          <a-table-column title="用户昵称/下单时间" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis">
                {{ record.nickname }}
              </div>
              <div class="text-ellipsis">
                {{ record?.created_at }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="订单状态" width="120px">
            <template #default="{ record }">
              <a-badge
                :text="orderStatus.filterValue(record.shipping_status).label"
                :color="orderStatus.filterValue(record.shipping_status).color"
              />
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
  </div>
</template>
<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { inventedOrdersApi } from '../../../api'
import { useStore } from '@/store/auth'
import { orderCondition, shopOrderType, orderStatus, orderPayStatus, orderSource } from '../../../enums'

const conditionOptions = orderCondition.giftOptions()
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))

const filterQuery = (orderOrGift = false) => {
  if (orderOrGift) {
    // order订单
    if ((conditionKey.value == 'sn' || conditionKey.value == 'nickname') && conditionValue.value) {
      return { [conditionKey.value]: conditionValue.value }
    } else {
      return {}
    }
  } else {
    // 礼品
    if ((conditionKey.value == 'code' || conditionKey.value == 'title') && conditionValue.value) {
      return { gift: { [conditionKey.value]: `%${conditionValue.value}%` } }
    } else if (conditionKey.value == 'phone_number' && conditionValue.value) {
      return { user: { [conditionKey.value]: `${conditionValue.value}` } }
    } else {
      return {}
    }
  }
}

const { data, setPage, loading } = usePaginatorApiRequest(
  (
    { offset, limit } // 表格
  ) =>
    inventedOrdersApi.paginator({
      filters: useTransformQuery(
        { ...formState.value, type: 'invented', ...filterQuery(true) },
        {
          created_at: 'dateRange',
          [conditionKey.value]: 'like'
        }
      ),
      relation_filters: { ...filterQuery() },
      offset,
      limit,
      relations: ['gift']
    })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  // 搜索表单
})

const handleResetFormState = () => {
  resetFormState()
  conditionKey.value = conditionOptions[0].value
  conditionValue.value = undefined
  setPage()
}

const router = useRouter()
const toDetails = ({ id }) => router.push({ name: 'invented-detail', params: { id } }) // 跳转详情页

const { state } = useStore()

const handleExport = () => {
  let exportGiftOrderApi = `${import.meta.env.VITE_API_BASE_URL}/gift-order/invented-orders/excel/export?`
  let query = JSON.stringify(
    useTransformQuery(
      { ...formState.value, type: 'invented', ...filterQuery(true) },
      {
        created_at: 'dateRange',
        [conditionKey.value]: 'like'
      }
    )
  )
  let relation_filters = JSON.stringify({ ...filterQuery() })
  let excelExportUrl = encodeURI(
    `${exportGiftOrderApi}token=${state.token}&filters=${query}&relation_filters=${relation_filters}`
  )
  window.open(excelExportUrl)
}
</script>
<style lang="less" scoped>
.express-number-box {
  :deep(.ant-input-number-handler-wrap) {
    display: none;
  }
}
</style>
