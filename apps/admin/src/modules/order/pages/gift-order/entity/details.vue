<template>
  <a-card title="订单信息" width="100%">
    <a-descriptions class="m-l-40">
      <a-descriptions-item label="订单编号">
        <div class="p-l-10">
          {{ data.sn || '-' }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="下单用户">
        <div class="p-l-10">
          {{ data.nickname || '-' }} ({{ $formatters.numberEncryption(data.phone_number) || '-' }})
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="下单时间">
        <div class="p-l-10">
          {{ data.created_at || '-' }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="订单来源">
        <div class="p-l-10">
          {{ orderSource.filterValue(data.source)?.label || '-' }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="积分消耗">
        <div class="p-l-10">
          {{ data.credit_total || 0 }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item label="订单状态">
        <div class="p-l-10">
          {{ orderStatus.giftFilterValue(data.combine_status).label }}
        </div>
      </a-descriptions-item>
      <!-- 实物订单 -->
      <a-descriptions-item v-if="data.type == 'entity' && data.shipping_status != 'unshipped'" label="快递公司">
        <div class="p-l-10">
          {{ data.express_name || '-' }}
        </div>
      </a-descriptions-item>
      <a-descriptions-item v-if="data.type == 'entity' && data.shipping_status != 'unshipped'" label="快递单号">
        <div class="p-l-10">
          {{ data.express_number || '-' }}
        </div>
      </a-descriptions-item>
      <!-- 虚拟订单 -->
      <a-descriptions-item v-if="data.type != 'entity'" label="充值账户">
        <div class="p-l-10">
          100000
        </div>
      </a-descriptions-item>
      <a-descriptions-item v-if="data.type == 'entity' && data.status == 'cancelled'" label="取消说明">
        <div class="p-l-10">
          {{ `${data.admin.name}-${data.reason}` }}
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
  <a-card title="兑换礼品" width="100%" class="m-t-20">
    <a-table :data-source="tableData" row-key="id" :pagination="false" @change="setPage">
      <a-table-column title="话题封面/名称" ellipsis>
        <template #default="{ record }">
          <uc-img-text :url="record.gift_photo" :title="record.gift_title" :subtit="`${record.gift_code}：${record.gift_attr}`" />
        </template>
      </a-table-column>
      <a-table-column title="消耗积分" width="120px">
        <template #default="{ record }">
          {{ record.credit_total || 0 }}
        </template>
      </a-table-column>
      <a-table-column title="数量" width="120px">
        <template #default="{ record }">
          {{ record.quantity || '-' }}
        </template>
      </a-table-column>
    </a-table>
  </a-card>
  <a-card v-if="data.type == 'entity'" title="收货人信息" width="100%" class="m-t-20">
    <a-descriptions class="m-l-40">
      <a-descriptions-item label=" 收货人">
        <div class="p-l-24">
          {{ data.consignee || '-' }} | {{ data.phone_number || '-' }}
        </div>
      </a-descriptions-item>
    </a-descriptions>
    <a-descriptions class="m-l-40">
      <a-descriptions-item label="收货地址">
        <div class="p-l-10">
          {{ data.province }}{{ data.city }}{{ data.county }}{{ data.address }}
        </div>
      </a-descriptions-item>
    </a-descriptions>
  </a-card>
</template>
<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { entityOrdersApi } from '../../../api'
import { orderSource , orderStatus } from '../../../enums'

const router = useRouter()

const { id } = useRoute().params

let tableData = reactive([])
let data = ref({})
if (id) {
  const hideLoading = message.loading('正在加载数据...')
  entityOrdersApi
    .get(id, { relations: ['gift','admin'] })
    .then(res => {
      tableData.push(res)
      data.value = res
    })
    .finally(hideLoading)
} else {
  router.back()
}
</script>
