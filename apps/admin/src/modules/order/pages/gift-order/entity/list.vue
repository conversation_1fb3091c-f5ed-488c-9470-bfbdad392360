<template>
  <a-modal v-model:visible="modalVisible" title="发货" width="600px" @ok="onSubmit" @cancel="handleCancel">
    <a-form :model="formStateSendGoods">
      <a-form-item label="订单编号" name="sn" class="required">
        <a-input v-model:value.trim="formStateSendGoods.sn" placeholder="请输入订单编号" disabled />
      </a-form-item>
      <a-form-item label="快递公司" name="express_code" class="required">
        <a-select
          v-model:value="formStateSendGoods.express_code"
          placeholder="请选择快递公司"
          allow-clear
          :options="expressCode"
        />
      </a-form-item>
      <a-form-item label="快递单号" name="express_number" class="required">
        <a-input v-model:value.trim="formStateSendGoods.express_number" placeholder="请输入快递单号" />
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal :visible="modalCancelVisible" title="取消订单" @ok="onCancelSubmit" @cancel="cancelOrderHandleCancel">
    <a-form :model="formStateCancelOrder">
      <a-form-item label="订单编号" name="sn" class="required">
        <a-input v-model:value.trim="formStateCancelOrder.sn" placeholder="请输入订单编号" disabled />
      </a-form-item>
      <a-form-item label="退还积分" name="return_credit" class="required">
        <a-input
          v-model:value.trim="formStateCancelOrder.return_credit"
          :placeholder="`可退积分${formStateCancelOrder.credit_total}`"
        />
      </a-form-item>
      <a-form-item label="取消原因" name="cancel_reason" class="required">
        <a-textarea
          v-model:value.trim="formStateCancelOrder.reason"
          maxlength="200"
          :auto-size="{ minRows: 4 }"
          placeholder="请输入取消原因，不超过200字"
        />
      </a-form-item>
    </a-form>
  </a-modal>
  <uc-layout-list title="实物订单">
    <template #filter>
      <a-form-item name="订单编号">
        <a-input-group compact>
          <a-select v-model:value="vselect" :options="orderCondition.giftOptions()" @change="handOrderOption" />
          <a-input v-model:value="formState.conditionKey" placeholder="请输入关键词" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.source"
          placeholder="订单来源"
          allow-clear
          :options="orderSource.options()"
        />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.combine_status"
          placeholder="订单状态"
          allow-clear
          :options="orderStatus.giftOptions()"
        />
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.created_at"
          :placeholder="['下单开始时间', '下单结束时间']"
          @change="changeRangePicker"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button @click="handleExport"> 导出 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="订单编号/下单时间" width="200px" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis color-primary" style="cursor: pointer" @click="toDetails(record)">
              {{ record.sn }}
            </div>
            <div class="text-ellipsis">
              {{ record.created_at }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="兑换礼品" ellipsis>
          <template #default="{ record }">
            <div v-if="record.gift" class="text-ellipsis">
              {{ record.gift.code }} / x{{ record.quantity }} / {{ record.gift.title }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="消耗积分" data-index="credit_total" width="120px" />
        <a-table-column title="用户昵称/手机号码" width="200px">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.nickname }}
            </div>
            <div>{{ $formatters.numberEncryption(record.phone_number) }}</div>
          </template>
        </a-table-column>
        <a-table-column title="订单状态" width="120px">
          <template #default="{ record }">
            <a-badge
              :text="orderStatus.giftFilterValue(record.combine_status).label"
              :color="orderStatus.giftFilterValue(record.combine_status).color"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ record }">
            <a-button
              type="link"
              :disabled="record.status == 'cancelled' || record.shipping_status != 'unshipped'"
              @click="onShipped(record, { shipping_status: 'confirmed' })"
            >
              发货
            </a-button>
            <a-button
              class="danger"
              type="link"
              :disabled="record.status == 'cancelled' || record.shipping_status != 'unshipped'"
              @click="onCancel(record)"
            >
              取消
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { orderStatus, orderSource, orderCondition } from '../../../enums'
import { entityOrdersApi, entityOrdersShipApi, expressCodeApi, entityOrdersCancelAction } from '../../../api'
import { forEach } from 'lodash'
import { useStore } from '@/store/auth'

const router = useRouter()
const orderType = ref('entity')
const visible = ref(false)

const vselect = ref('sn')
const { formState, resetFormState, onRestFormState, setFormRules, validateForm } = useFormState({
  conditionKey: undefined,
  combine_status: undefined,
  created_at: undefined,
  source: undefined
})

const status = ref()

let selectOrderVal = ref('sn')
let selectGiftVal = ref(undefined)

const aid = ref('')

const filterParams = () => {
  if (selectOrderVal.value) {
    return { [selectOrderVal.value]: formState.value.conditionKey }
  }
  return {}
}

const expressCode = ref()

expressCodeApi.list().then(data => {
  let map = []
  forEach(data, function (v, key) {
    map.push({
      label: v,
      value: key
    })
  })
  expressCode.value = map
})

const { modalVisible, setModalVisible } = useModalVisible()
const { modalVisible: modalCancelVisible, setModalVisible: setModaCancellVisible } = useModalVisible()

const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  entityOrdersApi.paginator({
    filters: useTransformQuery(
      { ...formState.value, type: orderType.value, ...filterParams() },
      { [selectOrderVal.value]: 'like', created_at: 'dateRange' }
    ),
    relation_filters: selectGiftVal.value
      ? { gift: { [selectGiftVal.value]: `%${formState.value.conditionKey || ''}%` } }
      : null,
    offset,
    limit,
    relations: ['gift']
  })
)

const changeRangePicker = () => {
  if (!formState.value.created_at.length) {
    Object.assign(formState.value, {
      created_at: undefined
    })
  }
}

onRestFormState(setPage)

const getKey = type => {
  selectOrderVal.value = undefined
  selectGiftVal.value = undefined
  switch (vselect.value) {
    case 'sn':
      selectOrderVal.value = 'sn'
      break
    case 'nickname':
      selectOrderVal.value = 'nickname'
      break
    case 'phone_number':
      selectOrderVal.value = 'phone_number'
      break
    case 'code':
      selectGiftVal.value = 'code'
      break
    case 'title':
      selectGiftVal.value = 'title'
      break
  }
}

const handOrderOption = value => {
  formState.value.conditionKey = undefined
  getKey()
}

const {
  formState: formStateSendGoods,
  resetFormState: resetFormStateSendGoods,
  setFormRules: setFormRulesSendGoods,
  validateForm: validateFormSendGoods
} = useFormState({
  express_code: undefined,
  express_number: undefined
})

const {
  formState: formStateCancelOrder,
  resetFormState: resetFormStateCancelOrder,
  setFormRules: setFormRulesCancelOrder,
  validateForm: validateFormCancelOrder
} = useFormState({
  return_credit: undefined,
  reason: undefined
})

setFormRulesSendGoods({
  express_code: {
    required: true,
    message: '请选择快递公司'
  },
  express_number: {
    required: true,
    message: '请输入快递编号'
  }
})

setFormRulesCancelOrder({
  return_credit: {
    required: true,
    message: '请输入退还积分'
  },
  reason: {
    required: true,
    message: '请输入取消原因'
  }
})

const onCancelSubmit = async () => {
  if (!(await validateFormCancelOrder())) return

  await entityOrdersCancelAction(aid.value).post(formStateCancelOrder.value)
  message.success('取消成功')
  refresh()
  cancelOrderHandleCancel()
}

const onSubmit = async () => {
  if (!(await validateFormSendGoods())) return

  await entityOrdersShipApi(aid.value).post(formStateSendGoods.value)
  message.success('发货成功')
  refresh()
  handleCancel()
}

const cancelOrderHandleCancel = () => {
  resetFormStateCancelOrder()
  setModaCancellVisible(false)
}

const handleCancel = () => {
  resetFormStateSendGoods()
  setModalVisible(false)
}

const { state } = useStore()

const handleExport = () => {
  let excelExportUrl = `${import.meta.env.VITE_API_BASE_URL}/gift-order/entity-orders/excel/export?`
  let query = ''
  let filterTime = useTransformQuery({ created_at: formState.value.created_at }, { created_at: 'dateRange' })
  formState.value.conditionKey ? (query += `"${selectOrderVal.value}":"%${formState.value.conditionKey}%",`) : ''
  formState.value.combine_status ? (query += `"combine_status":"${formState.value.combine_status}",`) : ''
  formState.value.source ? (query += `"source":"${formState.value.source}",`) : ''
  formState.value.created_at ? (query += `"created_at":"${filterTime.created_at}",`) : ''
  let dd = query.split('')
  if (dd[dd.length - 1] == ',') {
    dd.splice(dd.length - 1, 1)
    query = dd.join('')
  }
  excelExportUrl = encodeURI(`${excelExportUrl}token=${state.token}&filters={${query}}`)
  window.open(excelExportUrl)
}

const toDetails = ({ id }) => {
  router.push({
    name: 'entity-detail',
    params: {
      id
    }
  })
}

const onShipped = async ({ id }) => {
  setModalVisible(true)
  aid.value = id
  if (id) {
    const res = await entityOrdersApi.get(id, {})
    formStateSendGoods.value.sn = res.sn
  }
}

const onCancel = async ({ id }) => {
  setModaCancellVisible(true)
  aid.value = id
  if (id) {
    const res = await entityOrdersApi.get(id, {})
    formStateCancelOrder.value.sn = res.sn
    formStateCancelOrder.value.credit_total = res.credit_total
  }
}
</script>
<style scoped lang="less"></style>
