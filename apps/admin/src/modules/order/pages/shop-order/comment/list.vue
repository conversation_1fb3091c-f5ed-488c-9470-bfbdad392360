<template>
  <uc-layout-list title="订单评价列表">
    <template #filter>
      <a-form-item>
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.is_hidden"
          placeholder="公开状态"
          allow-clear
          :options="commentIsHiddenState.options()"
        />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="formState.created_at" :placeholder="['评论开始时间', '评论结束时间']" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="handleResetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="评分" width="250px">
          <template #default="{ record }">
            <div class="flex flex-dc">
              <div class="flex row">
                <div class="margin-right-sm">
                  商品:
                </div>
                <a-rate
                  :value="Number(record.product_score)"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
              <div class="flex row">
                <div class="margin-right-sm">
                  物流:
                </div>
                <a-rate
                  :value="Number(record.logistics_score)"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
              <div class="flex row">
                <div class="margin-right-sm">
                  服务:
                </div>
                <a-rate
                  :value="Number(record.service_score)"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
            </div>
          </template>
        </a-table-column>
        <a-table-column title="评价内容" width="300px">
          <template #default="{ record }">
            <div class="comment-cell-table">
              <div>
                <div
                  v-if="record.photo_urls"
                  class="flex margin-right-sm"
                  style="width:100%"
                >
                  <div class="photo-view">
                    <div v-if="record.photo_urls&&record.photo_urls.length>0">
                      <a-image-preview-group>
                        <div v-for="(item,index) in record.photo_urls" :key="index">
                          <a-image
                            v-if="index===0"
                            class="proveiw-img"
                            :src="item"
                            :preview="record.id"
                          />
                        </div>
                      </a-image-preview-group>
                    </div>
                    <img
                      v-else
                      class="proveiw-img"
                      :src="assets.noData"
                    />
                    <div v-if="record.photo_urls.length>0" class="text-info">
                      +{{ record.photo_urls.length }}
                      <img
                        :src="assets.horn"
                        class="text-info-horn"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div>
                <div class="color-85 font-weight comment-contents">
                  {{ record.contents }}
                </div>
                <text-overflow-tooltip class="color-25" solt="solt">
                  {{ record.sku }}<span style="padding:0 16px">|</span> {{ record.goods?.title }}
                </text-overflow-tooltip>
              </div>
            </div>
          </template>
        </a-table-column>

        <a-table-column title="评价用户" width="210px">
          <template #default="{ record }">
            <div class="flex flex-dc">
              <div
                class="text-1-line color-primary hand"
                style="line-height:30px"
                @click="toUserDetails(record)"
              >
                {{ record.nickname }}{{ buildUserPhone(record) }}
              </div>
              <div
                class="text-1-line"
                style="line-height:30px"
              >
                {{ record.created_at }}
              </div>
            </div>
          </template>
        </a-table-column>

        <a-table-column title="公开状态" width="100px">
          <template #default="{ record }">
            <a-switch
              v-model:checked="record.is_hidden"
              :un-checked-value="1"
              :checked-value="0"
              @change="handleUpdate(record.id, { is_hidden: $event })"
            />
          </template>
        </a-table-column>

        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="text color-primary" @click="look(record)">
              查看
            </a-button>
            <a-button
              type="text"
              :disabled="isDisabled(record).isDisabled"
              :class="isDisabled(record).class"
              @click="reply(record)"
            >
              回复
            </a-button>
            <a-button
              type="text"
              :class="disableOrder(record).class"
              :disabled="disableOrder(record).disable"
              @click="toDetails(record)"
            >
              订单
            </a-button>
          </template>
        </a-table-column>
      </a-table>
      <!---弹出框-->
      <a-modal v-model:visible="showDialog" width="840px" title="评价回复" :footer="null" class="product-dialog last">
        <a-form
          class="dialog-form"
          :model="editItem"
          label-width="100px"
          :rules="rules"
        >
          <a-form-item>
            <div class="flex flex-dc">
              <div class="flex row">
                <a-avatar
                  style="width: 30px;height: 30px;margin-right:10px"
                  :src="editItem.user.avatar"
                />

                <span
                  style="font-size: 16px;margin-right:20px;color:rgba(0, 0, 0, 0.85);"
                  class="nickName"
                >{{ editItem.user.nickname }}</span>
                <span
                  style="font-size: 14px;color:rgba(0, 0, 0, 0.45);"
                  class="nickName"
                >{{ editItem.created_at }}</span>
              </div>
              <div
                class="flex row"
                style="color:rgba(0, 0, 0, 0.45);padding-top:10px"
              >
                <div class="margin-right-sm">
                  商品评分:
                </div>
                <a-rate
                  :value="Number(editItem.product_score)"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                />

                <span style="color:rgba(0, 0, 0, 0.2); margin-right:4px">|</span>
                <div class="margin-right-sm">
                  物流服务:
                </div>
                <a-rate
                  :value="Number(editItem.logistics_score)"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                />
                <span style="color:rgba(0, 0, 0, 0.2);margin-right:4px">|</span>
                <div class="margin-right-sm">
                  服务态度:
                </div>
                <a-rate
                  :value="Number(editItem.service_score)"
                  disabled
                  text-color="#ff9900"
                  score-template="{value}"
                />
              </div>
              <div
                style="font-size:14px;color:rgb(0, 0, 0,0.65);padding-top:10px"
              >
                {{ buildContents(editItem.contents) }}
              </div>
              <div
                v-if="editItem.photo_urls&&editItem.photo_urls[0]"
                class="flex flex-row"
                style="padding: 5px 0 "
              >
                <a-image-preview-group>
                  <a-image
                    v-for="(item, index) of editItem.photo_urls"
                    :key="index"
                    class="imgCenter"
                    style="width: 100px;height: 80px;margin-left:3px"
                    :src="item"
                  />
                </a-image-preview-group>
              </div>
              <div style="font-size: 12px;color: rgba(0, 0, 0, 0.447058823529412);">
                {{ editItem.goods_spec_attrs }} | {{
                  editItem.goods && editItem.goods.title ?
                    editItem.goods.title :
                    ""
                }}
              </div>
              <div
                v-if="editItem.reply"
                class="flex row last"
                style="margin-top:20px;margin-bottom:7px"
              >
                <img
                  :src="assets.voucher"
                  height="17"
                  width="20"
                  class="margin-right-sm"
                />
                <span style="color:#1890FF">客服回复</span>
              </div>
            </div>
          </a-form-item>
          <a-form-item name="reply" class="last">
            <div v-if="editType" class="comment-textarea content-list">
              <a-textarea
                v-model:value="form.reply"
                class="comment-textarea"
                maxlength="300"
                :rows="6"
                show-word-limit
              />
            </div>
            <div v-else>
              {{ editItem.reply }}
            </div>
          </a-form-item>
        </a-form>
        <span class="dialog-footer">
          <a-button @click="() => showDialog = false">取消</a-button>
          <a-button v-if="editType" type="primary" @click="submitForm">确定</a-button>
        </span>
      </a-modal>
    </template>
  </uc-layout-list>
</template>

<script setup>
import {ref, computed, watch} from 'vue'
import {useRouter} from 'vue-router'
import {useFormState} from '@/composables/useFormState'
import {shopOrderItemCommentsApi} from '../../../api'
import {usePaginatorApiRequest} from "@/composables/useApiRequest";
import {useTransformQuery} from "@/composables/useTransformQuery";
import {message} from "ant-design-vue";
import {commentIsHiddenState, orderCondition} from "@/modules/order/enums";

// 关键字搜索
const conditionOptions = orderCondition.commentOptions()
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()
watch(conditionKey, () => conditionValue.value = undefined)
const getConditionValue = (isFilters = true) => {
  if (conditionValue.value) {
    let query = {}
    if (isFilters) {
      switch (conditionKey.value) {
        case 'sku':
          query = {sku: `%${conditionValue.value}%`}
          break;
        case 'nickname': //用户
          query = {nickname: `%${conditionValue.value}%`}
          break;
        case 'goods_title': //用户
          query = {item: {goods_title: `%${conditionValue.value}%`}}
          break;
      }
    } else {
      return {}
    }
    return query
  }
  return {}
}


const router = useRouter()
const status = ref()
let editType = ref(false)
let showDialog = ref(false)
let rules = ref({})
let editItem = ref({
  nick_name: null,
  product_score: null,
  logistics_score: null,
  service_score: null,
  contents: null,
  reply:
    "感谢您对产品的喜爱，关于系统问题我们会尽快给技术人员反馈，争取优化，为您带来一个更好的服务体验，您的满意将是我们最大的动力，我们也会持续为您提供优质的服务和产品，祝您生活愉快~~",
  spec: {name: null},
  goods: {title: null},
  user: {avatar: null, nickname: null},
  id: null
})

let form = ref({
  reply:
    "感谢您对产品的喜爱，关于系统问题我们会尽快给技术人员反馈，争取优化，为您带来一个更好的服务体验，您的满意将是我们最大的动力，我们也会持续为您提供优质的服务和产品，祝您生活愉快~~",
})

const {formState, onRestFormState, resetFormState} = useFormState({ // 搜索表单
})

onRestFormState(() => setPage())

const {data, setPage, loading, refresh} = usePaginatorApiRequest(({offset, limit}) =>
  shopOrderItemCommentsApi.paginator({
    filters: useTransformQuery(
      {...formState.value, ...getConditionValue()},
      {created_at: 'dateRange'}
    ),
    relation_filters: {...getConditionValue()},
    offset,
    limit,
    relations: ['item', 'spec', 'goods', 'user']
  })
)

const preview = () => {
  return "liushuwu" + Math.ceil(Math.random() * 10000);
}

const buildUserPhone = value => {
  var data = "";
  if (value.user && value.user.phone_number) {
    var reg = /(\d{3})\d{4}(\d{4})/;
    value.user.phone_number = value.user.phone_number.replace(reg, "$1****$2")
    data = `(${value.user.phone_number})`;
  }
  return data;
}

const isDisabled = value => {
  var data = []
  if (value.reply) {
    data.isDisabled = true
    data.class = "disabled-red"
  } else {
    data.isDisabled = false
    data.class = "color-primary"
  }
  return data
}

const disableOrder = value => {
  const data = {}
  const {order_id} = value
  if (!order_id) {
    data.class = "disabled-red"
    data.disable = true
    return data
  } else {
    data.class = "color-primary"
    data.disable = false
  }
  return data
}

//  处理分类更新
const handleUpdate = async (id, props) => {
  await shopOrderItemCommentsApi.update(id, props)
  message.success("修改成功")
}

const look = value => {
  editItem.value = {...value};

  console.log(editItem.value);
  editType.value = false;
  showDialog.value = true;
}

const handleResetFormState = () => { // 重置
  resetFormState()
  conditionKey.value = conditionOptions[0].value
  conditionValue.value = undefined
  setPage()
}

const buildContents = value => {
  var data = "";
  if (value) {
    data = value.replace(/<\/?p[^>]*>/gi, "");
  }
  return data;
}

// 跳转订单详情
const toDetails = ({order_id}) => router.push({name: 'shop-detail', params: {id: order_id}})

// 跳转用户详情
const toUserDetails = ({account_id, user_id}) => account_id && user_id && router.push({
  name: 'user-details',
  params: {id: account_id, user_id}
})

const reply = value => {
  editItem.value = {...value};
  editItem.value.reply =
    "感谢您对产品的喜爱，关于系统问题我们会尽快给技术人员反馈，争取优化，为您带来一个更好的服务体验，您的满意将是我们最大的动力，我们也会持续为您提供优质的服务和产品，祝您生活愉快~~";
  editType.value = true;
  showDialog.value = true;
}

const submitForm = async () => {
  await shopOrderItemCommentsApi.update(editItem.value.id, {reply: form.value.reply})
  showDialog.value = false
  message.success("回复成功")
  refresh()
}


</script>
<style lang="less">
.row {
  flex-direction: row;
  align-items: center;
}

.imgCenter {
  object-fit: cover;
}

.nickName {
  line-height: 30px !important;
}

.bread-crumb-container {
  padding: 16px 32px;
}

.flex-center {
  display: flex;
  align-items: center;
}

/deep/ .a-textarea__inner .a-textarea__inner {
  height: 160px !important;
}

.proveiw-img {
  width: 50px;
  height: 50px;
  object-fit: cover;
}

.margin-right-sm {
  margin-right: 10px;
}

.margin-top-sm {
  margin-top: 10px;
}

.margin-bottom-sm {
  margin-bottom: 10px;
}

.a-form-item__content {
  margin-left: 0 !important;
  line-height: 24px !important;
  color: rgba(0, 0, 0, 0.65) !important;
}

.refund {
  .refund-header {
    margin: 12px 0;

    .filter-form {
      .a-input__icon {
        color: rgba(144, 144, 152) !important;
      }

      // 编号输入框
      .search-content-form-item {
        .a-input-group__prepend {
          // background: #fff;
          .a-select {
            width: 100px !important;
            color: #000;

            .a-input__inner {
              padding-left: 12px;
            }
          }
        }
      }

      // 售后状态选择栏
      .sales-status-form-item {
        .a-form-item__content {
          width: 135px;
        }
      }
    }
  }

  .card-title {
    padding: 20px;
    background: #fff;
    color: rgba(29, 29, 29);
    font-size: 18px;
  }

  .refund-content {
    .a-table {
      thead {
        tr {
          th {
            background: rgba(239, 239, 239);
            color: rgba(38, 38, 38);
          }
        }
      }

      .refund-status-item {
        .refund-status-round {
          display: inline-block;
          width: 10px;
          height: 10px;
          border-radius: 50%;
          margin-right: 6px;
        }
      }

      .refund-info {
        .refund-price {
          color: rgba(240, 29, 51);
        }
      }
    }
  }
}

.flex-row {
  .a-textarea__inner {
    height: 80px !important;
  }
}

.content-list {
  .a-textarea__inner {
    line-height: 1.7 !important;
  }
}

.last {
  .a-form-item {
    margin-bottom: 0 !important;
  }
}

.photo-view {
  width: 50px;
  height: 50px;
  position: relative;
}

.text-info {
  position: absolute;
  left: -6px;
  top: 0;
  width: 30px;
  height: 15px;
  color: #fff;
  font-size: 12px;
  line-height: 15px;
  text-align: center;
  background: #409eff;
  z-index: 9;

  > img {
    vertical-align: middle;
    border-style: none;
  }
}

.text-info-horn {
  position: absolute;
  left: 0;
  bottom: -6px;
  width: 6px;
  height: 6px;
}

.text-one-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  width: 100%;
  text-overflow: ellipsis;
}

.comment-cell-table {
  display: flex;
  align-items: center;
}

.comment-contents {
  margin-bottom: 4px;
}
</style>
<style scoped lang="less">
</style>
