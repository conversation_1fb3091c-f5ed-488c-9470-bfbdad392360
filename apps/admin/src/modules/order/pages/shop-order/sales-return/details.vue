<template>
  <div class="shop-details">
    <a-space direction="vertical" size="large" class="flex">
      <a-card title="退货信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="售后单号">
            {{ document.sn || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="订单编号">
            <span style="color:#1890ff;cursor: pointer;" @click="toDetails(document.order)">{{ document?.order?.sn ||
              '-' }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="申请时间">
            {{ document.created_at || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="申请用户">
            {{ document?.user?.nickname || '-' }}({{ $formatters.numberEncryption(document?.user?.phone_number) || '-'
            }})
          </a-descriptions-item>
          <a-descriptions-item label="退款金额">
            {{ $formatters.thousandSeparator(document.refund_amount) || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="申请状态">
            {{ afterSaleType.cargoFilterValue(document.combine_status)?.label || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="申请原因">
            {{ document.reason || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="审核结果">
            {{ (document.result || '-') }}
          </a-descriptions-item>
          <a-descriptions-item label="退回信息">
            {{ (document.express_code ? document.express_code + ' | ' + document.express_number : '-') }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions v-if="document?.platform_refund_success !== null" class="p-l-40">
          <a-descriptions-item label="平台退款结果">
            <a-tag :color="document?.platform_refund_success ? 'green' : 'red'">
              退款{{ document?.platform_refund_success ? '成功' : '失败' }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="平台退款反馈">
            {{ document?.platform_refund_message || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card title="商品信息">
        <a-table :data-source="document.after_sale_items ?? []" row-key="id" :pagination="false">
          <a-table-column title="商品信息" ellipsis>
            <template #default="{ record }">
              <uc-img-text
                v-if="record.goods_title"
                :title="record.goods_title"
                :url="record.goods_spec_photo"
                :label="record.sku"
                symbol=":&nbsp"
                :subtit="record.goods_spec_attrs"
              />
            </template>
          </a-table-column>

          <a-table-column title="单价" width="120px" align="right">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.goods_spec_price) }}
            </template>
          </a-table-column>
          <a-table-column title="数量" data-index="quantity" width="120px" />
          <a-table-column title="优惠" width="120px">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.discount_total) }}
            </template>
          </a-table-column>
          <a-table-column title="实付" width="120px">
            <template #default="{ record }">
              <span class="color-red">{{ $formatters.thousandSeparator(record.total - record.discount_total) }}</span>
            </template>
          </a-table-column>
        </a-table>
      </a-card>
      <a-card title="收货人信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="收货人">
            {{ document?.order?.consignee || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="收货地址">
            {{ document?.order?.province || '-' }}{{ document?.order?.city || '-' }}{{ document?.order?.county || '-'
            }}{{
              document?.order?.address || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="买家备注">
            {{ document?.order?.remark || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
    </a-space>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { afterSaleOrdersApi } from "../../../api"
import { afterSaleType } from '../../../enums'

const router = useRouter()

const { id } = useRoute().params

if (!id) router.back()

let document = ref({})

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  afterSaleOrdersApi.get(id, {
    relations: ['order', 'afterSaleItems', 'user'], filters: {
      type: 'return'
    }
  }).then(res => {
    document.value = res
  }).finally(hideLoading)
}

// 跳转订单详情
const toDetails = ({ id }) => router.push({ name: 'shop-detail', params: { id } })
</script>
