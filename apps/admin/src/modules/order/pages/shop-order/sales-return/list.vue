<template>
  <div class="shop-list">
    <uc-layout-list title="订单列表">
      <template #filter>
        <a-form-item>
          <a-input-group compact>
            <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
          </a-input-group>
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.combine_status"
            placeholder="售后状态"
            :options="afterSaleType.cargoOption()"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.reason"
            placeholder="申请退货理由"
            class="w-200"
            :options="returnReasonOptions"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-range-picker v-model:value="formState.created_at" :placeholder="['申请开始时间', '申请结束时间']" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage"> 查询 </a-button>
          <a-button @click="handleResetFormState"> 重置 </a-button>
        </a-form-item>
      </template>
      <template #list>
        <a-table
          :data-source="data.items"
          row-key="id"
          :loading="loading"
          :pagination="stdPagination(data)"
          @change="setPage"
        >
          <a-table-column title="服务单号/订单编号" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis" style="color: #1890ff; cursor: pointer" @click="toDetails(record)">
                {{ record.sn }}
              </div>
              <div class="text-ellipsis">
                {{ record?.order?.sn }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="退款商品" ellipsis>
            <template #default="{ record }">
              <div v-for="(item, index) in record.after_sale_items" :key="index" class="text-ellipsis">
                {{ item?.sku }} | x{{ item?.quantity }} | {{ item?.goods_title }}
              </div>
              <div class="text-ellipsis color-red">退款理由: {{ record.reason }}</div>
            </template>
          </a-table-column>
          <a-table-column title="退款金额" width="120px" align="right">
            <template #default="{ record }">
              <span class="color-red">{{ $formatters.thousandSeparator(record.refund_amount) }}</span>
            </template>
          </a-table-column>
          <a-table-column title="申请人/申请时间" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis">
                {{ record?.user?.nickname }}
              </div>
              <div>{{ record?.created_at }}</div>
            </template>
          </a-table-column>
          <a-table-column title="订单状态" width="120px">
            <template #default="{ record }">
              <a-badge
                :text="afterSaleType.cargoFilterValue(record.combine_status).label"
                :color="afterSaleType.cargoFilterValue(record.combine_status).color"
              />
            </template>
          </a-table-column>
          <!-- 待处理/待退款状态为审核，其他状态禁用审核操作 -->
          <a-table-column title="操作" width="140px">
            <template #default="{ record }">
              <a-button
                v-if="record.combine_status == afterSaleType.returnWait"
                type="link"
                @click="showReturnModal(record)"
              >
                客户退货
              </a-button>
              <a-button
                v-if="record.combine_status != afterSaleType.receivingWait"
                type="link"
                :disabled="record.combine_status != afterSaleType.normal && record.combine_status != afterSaleType.refundWait
                "
                @click="showModalDeliverGoods(record)"
              >
                退款
              </a-button>
              <a-button
                v-if="record.combine_status == afterSaleType.receivingWait"
                type="link"
                @click="showModalDeliverGoods(record)"
              >
                签收
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>

    <a-modal :visible="returnModalVisible" title="客户退货信息录入" @cancel="onCancel">
      <a-form :model="returnModelFormState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
        <a-form-item label="服务单号" class="required">
          <a-input v-model:value="returnModelFormState.sn" placeholder="请输入服务单号" disabled />
        </a-form-item>

        <a-form-item label="快递编码" class="required">
          <a-input v-model:value="returnModelFormState.express_code" placeholder="请输入快递公司编码" />
        </a-form-item>

        <a-form-item label="快递单号" class="required">
          <a-input v-model:value="returnModelFormState.express_number" placeholder="请输入快递单号" />
        </a-form-item>

        <a-form-item label="操作说明" class="required">
          <a-textarea
            v-model:value="returnModelFormState.result"
            placeholder="请输入操作备注说明，不超过200字"
            :rows="5"
            :maxlength="200"
          />
        </a-form-item>
      </a-form>

      <template #footer>
        <a-button @click="hideReturnModal()">取消</a-button>
        <a-button type="primary" :loading="loading" @click="handleReturn()">确认</a-button>
      </template>
    </a-modal>

    <a-modal :visible="modalVisible" :title="`${operateStatus(modelFormState.combine_status)}审核`" @cancel="onCancel">
      <a-form :model="modelFormState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
        <a-form-item label="服务单号" class="required">
          <a-input v-model:value="modelFormState.sn" placeholder="请输入服务单号" disabled />
        </a-form-item>
        <a-form-item label="退款金额" class="required">
          <a-input
            :value="$formatters.thousandSeparator(modelFormState.refund_amount)"
            disabled
            placeholder="请输入退款金额"
          />
        </a-form-item>
        <a-form-item label="操作说明" class="required">
          <a-textarea
            v-model:value="modelFormState.result"
            :placeholder="modelFormState.combine_status == afterSaleType.refundWait
              ? '请输入操作备注说明，不超过200字\n请确认此单无异议，同意后退款金额将原路退回到申请人账户中'
              : '请输入操作备注说明，不超过200字'
            "
            :rows="5"
            :maxlength="200"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button type="primary" danger @click="handleRefund('refused')">
          {{ `拒绝${operateStatus(modelFormState.combine_status)}` }}
        </a-button>
        <a-button type="primary" :loading="submitLoading" @click="handleRefund('agreed')">
          {{ `同意${operateStatus(modelFormState.combine_status)}` }}
        </a-button>
      </template>
    </a-modal>
  </div>
</template>
<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { afterSaleOrdersApi } from '../../../api'
import { orderCondition, orderStatus, afterSaleType } from '../../../enums'
import { useReturnReasonOptions, useReasonFilter } from '../../../useOrderReason'

// 关键字搜索
const conditionOptions = orderCondition.refundApplyOptions()
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))
const getConditionValue = (isFilters = true) => {
  if (conditionValue.value) {
    let query = {}
    if (isFilters && conditionKey.value == orderCondition.saleSn) {
      // 售后
      query = { sn: conditionValue.value }
    } else if (!isFilters && conditionKey.value != orderCondition.saleSn) {
      switch (conditionKey.value) {
        case orderCondition.orderCode:
          query = { order: { sn: `%${conditionValue.value}%` } }
          break
        case 'nickname': //用户
          query = { user: { nickname: `%${conditionValue.value}%` } }
          break
        case 'phone_number': //用户
          query = { user: { phone_number: `${conditionValue.value}` } }
          break
      }
    } else {
      return {}
    }
    return query
  }
  return {}
}

// 退货录入
const returnModalVisible = ref(false)
const {
  formState: returnModelFormState,
  setFormState: returnModelSetFormState,
  resetFormState: returnModelResetFormState,
  setFormRules: returnModelSetFormRules,
  validateForm: returnModelValidateForm
} = useFormState({
  sn: undefined,
  express_code: undefined,
  express_number: undefined,
  result: undefined
}) // 对话框

returnModelSetFormRules({
  express_code: {
    required: true,
    message: '请输入快递公司编码'
  },
  express_number: {
    required: true,
    message: '请输入快递单号'
  },
  result: {
    required: true,
    message: '请输入操作备注说明'
  },
})


const showReturnModal = (record) => {
  returnModelSetFormState({
    id: record.id,
    sn: record.sn,
    express_code: 'DHL',
    result: '客户已寄回包裹'
  })
  returnModalVisible.value = true
}

const hideReturnModal = () => {
  returnModalVisible.value = false
  returnModelResetFormState()
}

const handleReturn = debounce(async () => {
  const { id, result } = returnModelFormState.value

  if (!(await returnModelValidateForm())) {
    return
  }

  await afterSaleOrdersApi.update(id, {
    result,
    action: 'returnd',
    express_code: returnModelFormState.value.express_code,
    express_number: returnModelFormState.value.express_number
  })

  message.success('操作成功')
  hideReturnModal()
  setPage()
}, 500)

// 退货理由
const { returnReasonOptions } = useReturnReasonOptions()
const getReturnReason = () => {
  return { reason: useReasonFilter(returnReasonOptions.value, formState.value.reason) }
}

const { data, setPage, loading } = usePaginatorApiRequest(
  (
    { offset, limit } // 表格
  ) =>
    afterSaleOrdersApi.paginator({
      filters: {
        ...useTransformQuery(
          { ...formState.value, ...getReturnReason(), ...getConditionValue() },
          {
            created_at: 'dateRange',
            sn: 'like'
          }
        ),
        type: 'return'
      },
      relation_filters: { ...getConditionValue(false) },
      offset,
      limit,
      relations: ['order', 'afterSaleItems', 'user']
    })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  // 表单
})

const handleResetFormState = () => {
  // 重置
  resetFormState()
  conditionKey.value = conditionOptions[0].value
  conditionValue.value = undefined
  setPage()
}

const router = useRouter()
const toDetails = ({ id }) => router.push({ name: 'sales-return-detail', params: { id } }) // 跳转详情

// 操作状态--退款，退货，签收
const operateStatus = type => {
  switch (type) {
    case afterSaleType.normal:
      return '退货'
    case afterSaleType.refundWait:
      return '退款'
    case afterSaleType.receivingWait:
      return '签收'
    default:
      break
  }
}

const {
  formState: modelFormState,
  setFormState: modelSetFormState,
  resetFormState: modelResetFormState,
  setFormRules: modelSetFormRules,
  validateForm: modelValidateForm
} = useFormState({
  sn: undefined,
  refund_amount: undefined,
  result: undefined
}) // 对话框
const { modalVisible, setModalVisible } = useModalVisible()

const showModalDeliverGoods = record => {
  //打开对话框
  modelSetFormState(record)
  modelFormState.value.result = undefined
  setModalVisible(true)
}

// 设置表单校验规则
modelSetFormRules({
  sn: {
    required: true,
    message: '请输入服务单号'
  },
  refund_amount: {
    required: true,
    message: '请输入退款金额'
  },
  result: {
    required: true,
    message: '请输入操作备注说明'
  }
})

// 同意退款-拒绝退款-签收
const submitLoading = ref(false)
const handleRefund = debounce(async action => {
  submitLoading.value = true
  try {
    const { id, result } = modelFormState.value

    if (!(await modelValidateForm())) {
      return
    }
    await afterSaleOrdersApi.update(id, { action, result })

    message.success('操作完成')
    onCancel()
    setPage()
  } finally {
    submitLoading.value = false
  }
}, 500)

const onCancel = () => {
  setModalVisible(false)
  modelResetFormState()
}

const handleExport = () => { }
</script>
<style lang="less" scoped>
.express-number-box {
  :deep(.ant-input-number-handler-wrap) {
    display: none;
  }
}
</style>
