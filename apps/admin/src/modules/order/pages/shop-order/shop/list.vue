<template>
  <div class="shop-list">
    <uc-layout-list :title="`购物订单${activity_title ? '-' + activity_title : ''}`">
      <template #filter>
        <a-form-item>
          <a-input-group compact>
            <a-select
              v-model:value="conditionKey"
              placeholder="请选择"
              :options="conditionOptions"
              @change="conditionKeyChange"
            />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
          </a-input-group>
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.type"
            placeholder="订单类型"
            :options="orderType.options()"
            allow-clear
            :disabled="queryDisable"
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.combine_status"
            placeholder="订单状态"
            :options="orderStatus.shopOptions()"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.pay_status"
            placeholder="支付状态"
            :options="orderPayStatus.options()"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.shipping_method"
            placeholder="发货方式"
            :options="orderShippingMethod.options()"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-range-picker v-model:value="formState.created_at" :placeholder="['下单开始时间', '下单结束时间']" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage"> 查询 </a-button>
          <a-button @click="handleResetFormState"> 重置 </a-button>
        </a-form-item>
      </template>
      <template #extra>
        <export-optional-fields
          placement="bottomRight"
          :fields="data.export_fields"
          :search="searchFields"
          @ok="handleExport"
        />
      </template>
      <template #list>
        <a-table
          :data-source="data.items"
          row-key="id"
          :loading="loading"
          :pagination="stdPagination(data)"
          @change="setPage"
        >
          <a-table-column title="订单编号/下单时间" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis color-primary" style="cursor: pointer" @click="toDetails(record)">
                {{ record.sn }}
              </div>
              <div>{{ record?.created_at }}</div>
            </template>
          </a-table-column>
          <a-table-column title="购买商品" ellipsis>
            <template #default="{ record }">
              <a-space>
                <a-image
                  v-for="(item, index) in record.items.slice(0, 5)"
                  :key="index"
                  :width="40"
                  :height="40"
                  :src="item.goods_spec_photo"
                  class="cursor-pointer"
                />
                <div v-if="record.items.length > 5">···</div>
              </a-space>
            </template>
          </a-table-column>
          <a-table-column title="应付款" width="120px" align="right">
            <template #default="{ record }">
              <span class="color-red">{{
                $formatters.thousandSeparator(
                  record.total_amount + record.freight_amount - record.discount_amount - record.freight_discount
                )
              }}</span>
            </template>
          </a-table-column>
          <a-table-column title="下单用户" width="200px" ellipsis>
            <template #default="{ record }">
              <uc-avatar :src="record.user?.avatar" :nickname="record.user?.nickname" tooltip />
            </template>
          </a-table-column>
          <a-table-column title="订单状态" width="120px">
            <template #default="{ record }">
              <a-badge
                :text="orderStatus.shopFilterValue(record.combine_status).label"
                :color="orderStatus.shopFilterValue(record.combine_status).color"
              />
            </template>
          </a-table-column>

          <a-table-column title="操作" width="180px">
            <template #default="{ record }">
              <!-- <a-button
                type="link"
                :disabled="record.combine_status != orderStatus.unshipped || record.pay_status != orderPayStatus.paid"
                @click="showModalDeliverGoods(record)"
              >
                发货
              </a-button> -->
              <div>
                <template
                  v-if="record.combine_status == orderStatus.unshipped && record.pay_status == orderPayStatus.paid"
                >
                  <a-button
                    v-if="record.after_sales.find(x => x.type == 'refund' && !refundCloseStatus.includes(x.status))"
                    disabled
                    type="link"
                    href="https://example.com"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    退款申请中
                  </a-button>
                  <a-button v-else type="link" @click="showRefundModal(record, 'refund')">申请退款</a-button>
                </template>

                <template
                  v-if="(record.combine_status == orderStatus.shipped || record.combine_status == orderStatus.completed) && record.pay_status == orderPayStatus.paid"
                >
                  <a-button
                    v-if="record.after_sales.find(x => x.type == 'return' && x.status != 'completed')"
                    disabled
                    type="link"
                  >
                    退货申请中
                  </a-button>
                  <a-button v-else-if="record.allow_returned" type="link" @click="showRefundModal(record, 'return')">
                    申请退货
                  </a-button>
                </template>

                <a-button v-if="isCustom(record)" type="link" :disabled="record.is_commit !== 1" @click="onLook(record)">
                  查看
                </a-button>

                <a-button type="link" :disabled="cancelDisabled(record)" @click="showCancelModal(record)">
                  取消
                </a-button>
              </div>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
    <a-modal :visible="modalVisible" title="订单发货" @cancel="onCancel">
      <a-form :model="formModalState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
        <a-form-item label="订单编号" class="required">
          <a-input v-model:value="formModalState.sn" placeholder="请输入" disabled />
        </a-form-item>
        <a-form-item label="发货商品" class="required">
          <a-select v-model:value="formModalState.sku" mode="multiple" placeholder="请选择发货商品">
            <a-select-option v-for="(item, index) in formModalState.items" :key="index" :value="item.sku">
              {{ item.sku }} - {{ item.goods_spec_attrs }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="快递公司" class="required" name="express_code">
          <a-select
            v-model:value="formModalState.express_code"
            placeholder="请选择快递公司"
            allow-clear
            :options="expressCode"
          />
        </a-form-item>
        <a-form-item label="快递单号" name="express_number" class="required express-number-box">
          <a-input-number v-model:value="formModalState.express_number" placeholder="请输入快递单号" />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="onCancel"> 取消 </a-button>
        <a-button type="primary" :loading="loading" @click="handleSubmit"> 保存 </a-button>
      </template>
    </a-modal>
    <a-modal :visible="cancelModalVisible" title="取消订单" @cancel="setCancelModalVisible(false)">
      <a-form :model="formModalStateCancel" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
        <a-form-item label="订单编号" class="required">
          <a-input v-model:value="formModalStateCancel.sn" placeholder="请输入" disabled />
        </a-form-item>
        <a-form-item label="退款金额" class="required">
          <a-input-number
            v-model:value="formModalStateCancel.refund_amount"
            :class="formModalStateCancel.exceedWechatTerm ? 'placeholder-red' : ''"
            :placeholder="formModalStateCancel.placeholder"
            :disabled="formModalStateCancel.exceedWechatTerm"
            precision="2"
          />
        </a-form-item>
        <a-form-item label="取消原因" class="required">
          <a-textarea
            v-model:value="formModalStateCancel.reason"
            placeholder="请输入取消订单原因，不超过200字"
            :rows="4"
            :max-length="200"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="setCancelModalVisible(false)"> 取消 </a-button>
        <a-button type="primary" @click="handleCancelSubmit"> 确定 </a-button>
      </template>
    </a-modal>
    <a-modal :visible="refundModalVisible" title="申请退款/退货" width="800px" @cancel="setRefundModalVisible(false)">
      <a-form :model="formModalStateCancel" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
        <a-form-item label="订单编号" class="required">
          <a-input v-model:value="formModalStateCancel.sn" placeholder="请输入" disabled />
        </a-form-item>
        <a-form-item v-if="refundOrderItemOptions.length > 0" label="退货商品" required>
          <a-checkbox-group v-model:value="formModalStateCancel.items" :options="refundOrderItemOptions" />
        </a-form-item>
        <a-form-item label="原因" class="required">
          <a-textarea
            v-model:value="formModalStateCancel.reason"
            placeholder="请输入退款/退货原因，不超过200字"
            :rows="4"
            :max-length="200"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button @click="setRefundModalVisible(false)"> 取消 </a-button>
        <a-button type="primary" @click="handleRefundSubmit"> 确定 </a-button>
      </template>
    </a-modal>
  </div>
</template>
<script setup>
import { computed, onMounted, reactive, ref, toRaw, watchEffect } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import formatters from '@/utils/formatters'
import { cloneDeep, pick } from 'lodash'
import { shopOrdersApi, expressCodeApi, shippedApi, exportShopOrderApi, shopOrdersCancelAction, afterSaleOrdersApi } from '../../../api'
import { orderCondition, shopOrderType, orderStatus, orderPayStatus, orderType, orderShippingMethod, afterSaleType } from '../../../enums'
import moment from 'moment'
import ExportOptionalFields from '@/components/export-optional-fields/index.vue'
import { orderSettingApi } from '../../../../setting/api'

const activity_id = ref(undefined)
const activity_title = ref(undefined)
const user_id = ref(undefined)
const track_code = ref(undefined)
const queryDisable = ref(false)

// 条件查询
const conditionOptions = orderCondition.shopOptions()
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()

// 退款状态
const refundCloseStatus = ['completed', 'closed', 'refund_fail']

// 基本查询参数
const conditionBasic = {}
const queryFormBasic = {
  type: undefined, // 订单类型
  combine_status: undefined, // 订单状态
  pay_status: undefined, // 支付状态
  shipping_method: undefined, // 发货方式
  created_at: undefined
}
conditionOptions.forEach(op => (conditionBasic[op.value] = undefined))

const { formState, setFormState, onRestFormState, resetFormState } = useFormState(cloneDeep(queryFormBasic))

const setTrackCode = ()=>{
  if (track_code.value) {
    conditionKey.value = orderCondition.trackCode
    formState.value.track_code = track_code.value
    conditionValue.value = track_code.value
  }
}
setTrackCode()

// 导出显示查询字段
const searchFields = ref()
const searchChangeLabel = (options, value) => {
  if (!value) {
    return value
  }
  for (let index = 0; index < options.length; index++) {
    if (options[index].value === value) {
      return options[index].label
    }
  }
  return;
}

const route = useRoute()
const alreadyCache = ref(false)
watch(() => route.query, (query) => {
  if(route.name === 'shop-list') {
    if(alreadyCache.value) {
      if((activity_id.value && !query.activity_id) || (query.activity_id && !activity_id.value)) {
        page.value = 1
      }
    }
    activity_id.value = query.activity_id
    activity_title.value = query.activity_title
    user_id.value = query.user_id
    track_code.value = query.track_code
    formState.value.type = query.activity_type
    queryDisable.value = Boolean(activity_id.value)
    setTrackCode()
    if(alreadyCache.value) {
      refresh()
    }
    alreadyCache.value = true
  }
}, {
  immediate: true
})

watchEffect(() => {

  const searchFormQuery = useTransformQuery({
    created_at: formState.value.created_at
  });
  let created_at = '--';
  if (searchFormQuery.created_at) {
    created_at = searchFormQuery.created_at[0].startOf('day').format('YYYY-MM-DD HH:mm:ss') + ' - '
      + searchFormQuery.created_at[1].endOf('day').format('YYYY-MM-DD HH:mm:ss')
  }

  searchFields.value = [
    {
      label: searchChangeLabel(conditionOptions, conditionKey.value),
      value: conditionValue.value
    },
    {
      label: '订单类型',
      value: searchChangeLabel(orderType.options(), formState.value.type)
    }, // 订单类型
    {
      label: '订单状态',
      value: searchChangeLabel(orderStatus.shopOptions(), formState.value.combine_status)
    }, // 订单状态
    {
      label: '支付状态',
      value: searchChangeLabel(orderPayStatus.options(), formState.value.pay_status)
    }, // 支付状态
    {
      label: '发货方式',
      value: searchChangeLabel(orderShippingMethod.options(), formState.value.shipping_method)
    }, // 发货方式
    {
      label: '订单时间',
      value: created_at
    }
  ]
})

// 重置
const handleResetFormState = () => {
  Object.assign(formState.value, conditionBasic, queryFormBasic)
  conditionValue.value = undefined
  conditionKey.value = conditionOptions[0].value
  setPage()
}

const getFilters = () => {
  Object.assign(formState.value, conditionBasic, {
    [conditionKey.value]: conditionValue.value
  })
  let relationFilters = {}
  let itemRelationFilters = useTransformQuery(
    {
      sku: formState.value.sku,
      goods_title: formState.value.goods_title
    },
    {
      sku: 'like',
      goods_title: 'like'
    }
  );
  if (Object.values(itemRelationFilters).filter(filterValue => filterValue).length) {
    console.log(itemRelationFilters)
    relationFilters['items'] = itemRelationFilters
  }
  let userRelationFilters = useTransformQuery(
    {
      nickname: formState.value.nickname,
      phone_number: formState.value.user_phone_number
    },
    {
      nickname: 'like',
      phone_number: '='
    }
  )

  if (Object.values(userRelationFilters).filter(filterValue => filterValue).length) {
    relationFilters['user'] = userRelationFilters
  }

  let billRelationFilters = useTransformQuery(
    {
      trade_number: formState.value.trade_number,
    },
    {
      trade_number: 'like'
    }
  )

  if (Object.values(billRelationFilters).filter(filterValue => filterValue).length) {
    relationFilters['bill'] = billRelationFilters
  }

  return {
    filters: useTransformQuery(
      {
        sn: formState.value.sn,
        phone_number: formState.value.phone_number,
        consignee: formState.value.consignee,
        track_code: formState.value.track_code,
        type: formState.value.type,
        user_id: user_id.value,
        activity_id: activity_id.value,
        combine_status: formState.value.combine_status,
        pay_status:
          formState.value.pay_status === orderPayStatus.paid ? `!${orderPayStatus.unpaid}` : formState.value.pay_status,
        shipping_method: formState.value.shipping_method,
        created_at: formState.value.created_at
      },
      {
        sn: 'like',
        consignee: 'like',
        track_code: 'like',
        created_at: 'dateRange'
      }
    ),
    relation_filters: relationFilters,
  }
}

const { data, setPage, refresh, loading, page } = usePaginatorApiRequest(({ offset, limit }) =>
  shopOrdersApi.paginator({
    ...getFilters(),
    offset,
    limit,
    relations: ['items', 'user', 'spec', 'afterSales']
  })
)

// 快递
const expressCode = ref()
expressCodeApi.list().then(data => {
  expressCode.value = Object.keys(data).map(key => {
    return {
      label: data[key],
      value: key
    }
  })
})

const router = useRouter()
const toDetails = ({ id }) => router.push({ name: 'shop-detail', params: { id } })

const conditionKeyChange = () => conditionValue.value = undefined

const handleExport = (fields, token) => {
  const query = getFilters()
  let excelExportUrl = `&filters=${JSON.stringify(query.filters)}&relation_filters=${JSON.stringify(query.relation_filters)}`

  if (fields) {
    excelExportUrl += `&export_fields=${fields}`
  }

  window.open(encodeURI(`${exportShopOrderApi}?token=${token}${excelExportUrl}`))
}

// 发货块

const {
  formState: formModalState,
  setFormState: setFormModalState,
  resetFormState: resetFormModalState,
  setFormRules,
  validateForm: validateModalForm
} = useFormState({
  id: undefined,
  sku: [],
  express_code: undefined,
  express_number: undefined
})
const { modalVisible, setModalVisible } = useModalVisible()

const showModalDeliverGoods = record => {
  const { sn, id, items } = record
  setFormModalState({
    sn,
    id,
    items,
    sku: [],
    express_code: undefined,
    express_number: undefined
  })
  setModalVisible(true)
}

const filterProducts = sku => {
  let groupSku = sku.map(item => {
    return {
      label: item.sku + item.goods_spec_attrs,
      value: item.sku
    }
  })
  groupSku.push({ label: '全部商品', value: 'all' })
  formModalState.value.sku = ['all']
  return groupSku
}

setFormRules({
  sku: { required: true, message: '请选择发货商品', type: 'array' },
  express_code: { required: true, message: '请选择快递公司' },
  express_number: { required: true, message: '请输入快递单号' }
})

const handleSubmit = async () => {
  if (!(await validateModalForm())) {
    return
  }

  let params = cloneDeep(formModalState.value)
  delete params.items

  await shippedApi(params.id).post(params)
  message.success('发货成功')
  onCancel()
  setPage()
}

const onCancel = () => {
  resetFormModalState()
  setModalVisible(false)
}

// 取消订单块

const {
  formState: formModalStateCancel,
  setFormState: setFormModalStateCancel,
  resetFormState: resetFormModalStateCancel,
  setFormRules: setFormRulesCancel,
  validateForm: validateFormCancel
} = useFormState({
  sn: undefined,
  refund_amount: undefined,
  reason: undefined,
  items: []
})


setFormRulesCancel({
  refund_amount: {
    validator(_, value) {
      if (value > formatters.thousandSeparator(formModalStateCancel.value.paid_amount, false)) {
        return Promise.reject('退款金额不可大于当前订单实际支付金额')
      }
      return Promise.resolve()
    }
  },
  reason: { required: true, message: '请输入取消原因' }
})

const { modalVisible: cancelModalVisible, setModalVisible: setCancelModalVisible } = useModalVisible()

const showCancelModal = record => {
  resetFormModalStateCancel()
  Object.assign(record, {
    reason: undefined,
    refund_amount: record.paid_amount / 100
  })

  // 退款金额占位符
  if (!record.freight_amount || record.freight_amount - record.freight_discount === 0) {
    record.placeholder = `可退款金额${formatters.thousandSeparator(record.paid_amount)}元（不含运费）`
  } else {
    record.placeholder = `可退款金额${formatters.thousandSeparator(
      record.paid_amount
    )}元（含运费${formatters.thousandSeparator(record.freight_amount - record.freight_discount)}）`
  }

  // 订单距当前时间已超过微信退款期限时
  let yearStart = moment().startOf('year').format('YYYY-MM-DD')
  let yearEnded = moment().endOf('year').format('YYYY-MM-DD')
  record.exceedWechatTerm = true // orderdays > yearDays

  setFormModalStateCancel(record)
  setCancelModalVisible(true)
}

const handleCancelSubmit = async () => {
  if (!(await validateFormCancel())) {
    return
  }

  let cancelOrder = cloneDeep(formModalStateCancel.value)
  const { id, sn, reason, paid_amount } = cancelOrder
  let params = {
    sn,
    reason,
    refund_amount: paid_amount
  }

  await shopOrdersCancelAction(id).post(params)
  message.success('取消成功')
  setCancelModalVisible(false)
  setPage()
}

const { modalVisible: refundModalVisible, setModalVisible: setRefundModalVisible } = useModalVisible()
const refundType = ref(null)
const currentRefundOrder = ref(null)

const showRefundModal = (record, type) => {
  resetFormModalStateCancel()
  currentRefundOrder.value = record
  Object.assign(record, {
    reason: type == 'refund' ? '客户期望退款' : '客户期望退货'
  })
  refundType.value = type
  setFormModalStateCancel({
    ...toRaw(record),
    items: record.items.filter(item => item.status == 'normal').map((item) => item.id)
  })
  setRefundModalVisible(true)
}

const refundOrderItemOptions = computed(() => {
  if (!currentRefundOrder.value || refundType.value == 'refund') {
    return []
  }
  const order = currentRefundOrder.value
  return order.items.filter(item => item.status == 'normal').map((item) => {
    return {
      label: `${item.goods_title}(${item.goods_spec_attrs})`,
      value: item.id
    }
  })
})

const handleRefundSubmit = async () => {
  let cancelOrder = cloneDeep(formModalStateCancel.value)
  const { id, reason, items } = cancelOrder
  const payload = { id: id, reason, type: refundType.value }
  if (items.length == 0) {
    return message.error('请选择退款商品')
  }
  payload.items = toRaw(currentRefundOrder.value.items).filter(x => items.includes(x.id)).map(x => ({
    id: x.id,
    sku: x.sku,
    quantity: x.quantity,
    amount: x.total - x.discount_total
  }))
  console.log('payload', payload)
  await afterSaleOrdersApi.create(payload)
  setRefundModalVisible(false)
  if (refundType.value == 'refund') {
    message.success('退款申请创建成功')
    router.push({ name: 'refund-apply-list' })
  } else {
    message.success('退货申请创建成功')
    router.push({ name: 'sales-return-list' })
  }
}

const orderConfig = ref()

onMounted(() => {
  orderSettingApi.get({}).then((data) => {
    orderConfig.value = data
  })
})

const cancelDisabled = ({ combine_status, shipping_method, shipped_at }) => {

  // 已自提退款期限
  if (shipping_method === orderShippingMethod.pick_up) {
    return combine_status === orderStatus.picked ?
      diffDays(shipped_at) >= orderConfig.value?.value?.pick_up_return_timeout
      :
      combine_status !== orderStatus.unpicked
  } else {
    return combine_status !== orderStatus.unshipped
  }
}


const isCustom = computed(() => (item)=> Boolean(item.items.find(item => item.goods_type === orderType.custom)))

const diffDays = (startDatetime) => {
  var startDate = Date.parse(startDatetime);
  var endDate = (new Date()).getTime();
  var days = Math.round((endDate - startDate) / (1 * 24 * 60 * 60 * 1000));
  return days;
}

const onLook = (item)=> {
  window.open(`https://www.kanzheli.cn/fj/c/5?tid=${item.sn}&mobile=${item.phone_number}`, '_blank', 'noopener,noreferrer');
}
</script>
<style lang="less" scoped>
.express-number-box {
  :deep(.ant-input-number-handler-wrap) {
    display: none;
  }
}

:deep(.placeholder-red) {
  input::placeholder {
    color: #f5222d;
  }
}

.flags {
  .anticon-flag {
    margin-right: 10px;
  }
}
</style>
