<template>
  <div id="water-flow" class="water-flow flex flex-sc relative">
    <template v-for="(colList, colIndex) in waterFlowData.list" :key="colIndex">
      <div v-if="colList.length" class="water-flow-col flex flex-dc flex-1 w-300 w-300-400">
        <div v-for="(row, rowIndex) in colList" :key="rowIndex" class="water-flow-row w-fill p-b-10 relative">
          <img :src="row.produce_url" class="w-fill" />
          <div v-if="row.quantity > 1" class="tag absolute flex flex-center color-white bgc-red">
            {{ row.quantity }}
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
export default {
  name: 'WaterFlow'
}
</script>

<script setup>
const props = defineProps({
  col: {
    // 几列
    type: Number,
    required: true
  },
  list: {
    // 数据源
    type: Array,
    required: true
  }
})

const waterFlowData = ref({
  // 重排后的二维数组
  list: [],
  heightList: []
})

// 初始化
const init = async () => {
  const list = []
  const heightList = []
  for (let i = 0; i < props.col; i++) {
    list[i] = []
    heightList[i] = []
  }
  const tempList = []
  const tasks = props.list.map(async item => await getImgInfo(item.produce_url))
  Promise.all(tasks).then(hGroups => {
    hGroups.forEach((val, i) => {
      const minI = getMinIndex(heightList) // 获取最短列索引
      list[minI].push(props.list[i])
      heightList[minI].push(val)
    })
    waterFlowData.value = { list, heightList }
  })
}
watch(() => props, init, { deep: true })
// 获取最短列索引---最短的列
const getMinIndex = heightList => {
  let minIndex = 0
  let preHeightSum

  heightList.forEach((hArray, index) => {
    const sums = getSum(hArray, index)
    if (preHeightSum === undefined || sums < preHeightSum) {
      preHeightSum = sums
      minIndex = index
    }
  })
  return minIndex
}

// 求和
const getSum = arr => arr.reduce((prev, item) => prev + item, 0)

// 获取图片等比例的高(图片高*200/图片宽)
const getImgInfo = imgUrl =>
  new Promise(resolve => {
    const img = new Image()
    img.src = imgUrl
    img.onload = () => {
      resolve((img.height / img.width) * 200)
    }
  })

onMounted(init)
</script>
<style scoped lang="less">
.water-flow {
  &-col {
    & + .water-flow-col {
      padding-left: 10px;
    }
  }
  &-row {
    .tag {
      top: 0;
      left: 0;
      width: 30px;
      height: 20px;
    }
  }
  .w-300-400 {
    min-width: 300px;
    max-width: 400px;
  }
}
</style>
