<template>
  <div class="my-steps">
    <a-steps v-if="data && data.length" progress-dot :current="0" direction="vertical">
      <a-step v-for="item in data" :key="item.time" :title="item.time" :description="item.message" />
    </a-steps>
    <div v-else>
      暂无物流信息
    </div>
  </div>
</template>

<script setup>
defineProps({
  data: {
    type: Array,
    default: () => []
  }
})
</script>

<style lang="less" scoped>
.my-steps {
  :deep(.ant-steps-dot .ant-steps-item-content) {
    width: auto;
  }
}
</style>
