<template>
  <div class="shop-details">
    <a-space direction="vertical" size="large" class="flex">
      <a-card title="基本信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="订单编号">
            {{ document.sn || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="下单用户">
            {{ document?.user?.nickname || '-' }}({{
              $formatters.numberEncryption(document?.user?.phone_number) || '-'
            }})
          </a-descriptions-item>
          <a-descriptions-item label="订单时间">
            {{ document.created_at || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="订单类型">
            <span v-for="(item, index) in orderType.filterValues(document.type)" :key="item.value">{{
              `${index != 0 ? '/' : ''}` + item.label
            }}</span>
          </a-descriptions-item>
          <a-descriptions-item label="订单状态">
            {{ orderStatus.shopFilterValue(document.combine_status)?.label }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card v-if="document.bill && document?.pay_status != orderPayStatus.unpaid" title="支付信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="支付金额">
            {{ $formatters.thousandSeparator(document.paid_amount) }}
          </a-descriptions-item>
          <a-descriptions-item label="支付方式">
            {{ payMethod.filterValue(document?.bill?.payment_code).label || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="支付时间">
            {{ document.paid_at || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="支付状态">
            {{ orderPayStatus.filterValue(document.pay_status).label }}
          </a-descriptions-item>
          <a-descriptions-item label="交易流水号">
            {{ document?.bill?.trade_number || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card title="商品信息">
        <a-table :data-source="document.items ?? []" row-key="id" :pagination="false">
          <a-table-column title="商品信息" ellipsis>
            <template #default="{ record }">
              <uc-img-text
                :title="record.goods_title"
                :url="record.goods_spec_photo"
                :label="record.sku"
                symbol=":&nbsp"
                :subtit="record.goods_spec_attrs"
              />
            </template>
          </a-table-column>
          <a-table-column v-if="showExpress" title="快递信息" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis">
                {{ record.express_name }}
              </div>
              <div class="text-ellipsis">
                {{ record.express_number }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="单价" width="100px" align="right">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.goods_spec_price) }}
            </template>
          </a-table-column>
          <a-table-column title="数量" data-index="quantity" width="80px" />
          <a-table-column title="优惠" data-index="discount_total" width="100px">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.discount_total) }}
            </template>
          </a-table-column>
          <a-table-column title="小计" width="100px">
            <template #default="{ record }">
              <span class="color-red">{{ $formatters.thousandSeparator(record.total - record.discount_total) }}</span>
            </template>
          </a-table-column>
        </a-table>
        <div class="flex flex-rr">
          <div class="flex flex-dc p-t-20 t-right">
            <p>
              商品总金额：
              <span class="color-primary">¥{{ $formatters.thousandSeparator(document.total_amount) }}</span> + 运费：
              <span class="color-primary">¥{{ $formatters.thousandSeparator(document.freight_amount) }}</span> =
              订单金额：
              <span class="color-primary">¥{{ $formatters.thousandSeparator(document.total_amount + document.freight_amount) }}</span>
            </p>
            <p>
              -活动优惠<span v-if="document.activity_discount_title"> ({{ document.activity_discount_title }})</span>：
              <span class="color-danger">
                ¥{{ $formatters.thousandSeparator(document.activity_discount_amount) }}
              </span>
            </p>
            <p>
              -促销优惠<span v-if="document.promotion_discount_title"> ({{ document.promotion_discount_title }})</span>：
              <span class="color-danger">
                ¥{{ $formatters.thousandSeparator(document.promotion_discount_amount) }}
              </span>
            </p>
            <p>
              -优惠券<span v-if="document.coupon_discount_title"> ({{ document.coupon_discount_title }})</span>：
              <span class="color-danger"> ¥{{ $formatters.thousandSeparator(document.coupon_discount_amount) }} </span>
            </p>
            <p>
              -运费优惠<span v-if="document.freight_discount_title"> ({{ document.freight_discount_title }})</span>：
              <span class="color-danger">¥{{ $formatters.thousandSeparator(document.freight_discount) }}</span>
            </p>
            <p>
              = 应付金额：
              <span class="color-danger">¥{{ $formatters.thousandSeparator(document.payable_amount) }}</span>
            </p>
            <p>
              实付金额：
              <span class="color-danger">¥{{ $formatters.thousandSeparator(document.paid_amount) }}</span>
            </p>
          </div>
        </div>
      </a-card>
      <a-card title="收货人信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="收货人">
            {{ document.consignee || '-' }} / {{ document?.phone_number || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="收货地址">
            {{ document.province || '-' }}{{ document.city || '-' }}{{ document.county || '-'
            }}{{ document.address || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="发货方式">
            {{ orderShippingMethod.filterValue(document.shipping_method).label || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="买家备注">
            {{ document.remark || '这个用户有点懒，什么备注都没有留下～' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>

      <a-card title="渠道信息">
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="渠道分组">
            {{ document?.track_code?.group || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="渠道分类">
            {{ document?.track_code?.category || '-' }}
          </a-descriptions-item>
        </a-descriptions>
        <a-descriptions class="p-l-40">
          <a-descriptions-item label="渠道名称">
            {{ document?.track_code?.title || '-' }}
          </a-descriptions-item>
        </a-descriptions>
      </a-card>
      <a-card
        v-for="track in document.tracks"
        :key="track.id"
        :title="`物流信息：${track.express_name} | ${track.express_number}`"
      >
        <Steps :data="track.traces" />
      </a-card>
    </a-space>
  </div>
</template>
<script setup>
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { useRoute, useRouter } from 'vue-router'
import { shopOrdersApi } from '../../../api'
import { shopOrderType, orderStatus, orderPayStatus, orderType, payMethod, orderShippingMethod } from '../../../enums'
import { useExpressOptions } from '../../../useExpress'
import Steps from './steps.vue'
import { useTransformAttr } from '@/modules/order/useTransform'

const router = useRouter()

const { id } = useRoute().params

const document = ref({})

const showExpress = ref(false)
if (id) {
  const hideLoading = message.loading('正在加载数据...')
  shopOrdersApi
    .get(id, { relations: ['items', 'user', 'bill', 'trackCode', 'tracks', 'shopOrderAttributes'] })
    .then(async res => {
      const { companyOptions } = await useExpressOptions()

    res.items.forEach(item => {
      if(item.express_code) {
        item.express_name = companyOptions.find(({value})=>value == item.express_code)?.label
      }
    })
    const tracks = []
    res.tracks.forEach(item => {
      if(!tracks.some(it => it.express_code === item.express_code)) {
        item.express_name = companyOptions.find(({value})=>value == item.express_code)?.label
        item.traces.sort((p, n) => {
          if(p.time < n.time) return 1
          if(p.time > n.time) return -1
          return 0
        })
        tracks.push(item)
      }
    })
    res.tracks = tracks

      let deliverGoods = res.items.filter(i => i.express_code)
      document.value = { ...res, ...useTransformAttr(res.shop_order_attributes) }
      showExpress.value = deliverGoods.length ? true : false
    })
    .finally(hideLoading)
} else {
  router.back()
}
</script>
<style scoped lang="less"></style>
