<template>
  <!-- 定制预览 -->
  <div class="bgc-default w-vw h-vh flex flex-dc">
    <a-card :bordered="false" class="w-vw">
      <div class="flex flex-sb flex-cc">
        <div class="flex flex-1">
          <img :src="productInfo.goods_spec_photo" mode="fill" class="w-100 m-r-10" />
          <div class="flex flex flex-dc flex-sb flex-1 w-200">
            <h3 class="w-fill fs-20 fw-bold text-ellipsis color-85 m-b-0">
              {{ productInfo.goods_title }}
            </h3>
            <div class="color-65">
              <p class="m-b-0">
                商品SKU：{{ productInfo.sku }} ｜ 商品规格：{{ productInfo.goods_spec_attrs }} ｜ 购买数量：{{
                  productInfo.quantity
                }}
              </p>
              <p class="m-b-0">
                订单编号：{{ document.sn }} ｜ 定制编号：{{ customData.sn }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </a-card>
    <div class="img-flow p-24 overflow-auto">
      <water-flow v-if="customStatus.isCompleted(customData.status)" :col="col" :list="customData.items" />
      <a-empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" class="p-t-80">
        <template #description>
          定制设计图片正在合成中...
        </template>
      </a-empty>
    </div>
  </div>
</template>
<script setup>
import { message, Empty } from 'ant-design-vue'
import { shopOrdersApi, customOrderApi } from '../../../api'
import WaterFlow from './water-flow.vue'
import { customStatus } from '../../../enums'

const router = useRouter()
const { id, sku, sn } = useRoute().params

const document = ref({}) // 页面数据
const productInfo = computed(() => document.value?.items?.find(item => item.sku === sku) ?? {}) // 商品信息

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  shopOrdersApi
    .get(id, { relations: ['items'] })
    .then(res => {
      document.value = res
    })
    .finally(hideLoading)
} else {
  router.back()
}

const customData = ref({}) // 定制数据
if (sn) {
  customOrderApi.get({ filters: { trade_sn: sn }, relations: ['items'] }).then(res => {
    customData.value = res[0]
  })
} else {
  router.back()
}
const countCol = () => Math.floor((window.innerWidth - 48) / 300) // 计算列数
const col = ref(countCol()) // 列数

// 绑定窗口变化事件,不做防抖
window.addEventListener('resize', () => {
  col.value = countCol()
})
</script>
