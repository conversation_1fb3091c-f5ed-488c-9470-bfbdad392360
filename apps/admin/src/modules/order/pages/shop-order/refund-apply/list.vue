<template>
  <div class="shop-list">
    <uc-layout-list title="订单列表">
      <template #filter>
        <a-form-item>
          <a-input-group compact>
            <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
            <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
          </a-input-group>
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.combine_status"
            placeholder="售后状态"
            :options="afterSaleType.applyOptions()"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-select
            v-model:value="formState.reason"
            placeholder="申请退款理由"
            class="w-200"
            :options="refundReasonOptions"
            allow-clear
          />
        </a-form-item>
        <a-form-item>
          <a-range-picker v-model:value="formState.created_at" :placeholder="['申请开始时间', '申请结束时间']" />
        </a-form-item>
        <a-form-item>
          <a-button type="primary" @click="setPage">
            查询
          </a-button>
          <a-button @click="handleResetFormState">
            重置
          </a-button>
        </a-form-item>
      </template>
      <template #list>
        <a-table
          :data-source="data.items"
          row-key="id"
          :loading="loading"
          :pagination="stdPagination(data)"
          @change="setPage"
        >
          <a-table-column title="服务单号/订单编号" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis" style="color: #1890ff; cursor: pointer" @click="toDetails(record)">
                {{ record.sn }}
              </div>
              <div class="text-ellipsis">
                {{ record?.order?.sn }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="退款商品" ellipsis>
            <template #default="{ record }">
              <div v-for="(item, index) in record.after_sale_items" :key="index" class="text-ellipsis">
                {{ item?.sku }} | x{{ item?.quantity }} | {{ item?.goods_title }}
              </div>
              <div class="text-ellipsis color-red">
                退款理由: {{ record.reason }}
              </div>
            </template>
          </a-table-column>
          <a-table-column title="退款金额" width="120px" align="right">
            <template #default="{ record }">
              <span class="color-red">{{ $formatters.thousandSeparator(record.refund_amount) }}</span>
            </template>
          </a-table-column>
          <a-table-column title="申请人/申请时间" width="200px" ellipsis>
            <template #default="{ record }">
              <div class="text-ellipsis">
                {{ record?.user?.nickname }}
              </div>
              <div>{{ record?.created_at }}</div>
            </template>
          </a-table-column>
          <a-table-column title="订单状态" width="120px">
            <template #default="{ record }">
              <a-badge
                :text="afterSaleType.applyFilterValue(record.combine_status).label"
                :color="afterSaleType.applyFilterValue(record.combine_status).color"
              />
            </template>
          </a-table-column>
          <!-- 除待处理以外状态的审核操作禁用 -->
          <a-table-column title="操作" width="70px">
            <template #default="{ record }">
              <a-button
                type="link"
                :disabled="record.combine_status != afterSaleType.normal"
                @click="showModalDeliverGoods(record)"
              >
                退款
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </template>
    </uc-layout-list>
    <a-modal :visible="modalVisible" title="退款审核" @cancel="onCancel">
      <a-form :model="modelFormState" :label-col="modalLabelCol" :wrapper-col="modalWrapperCol">
        <a-form-item label="服务单号" class="required">
          <a-input v-model:value="modelFormState.sn" placeholder="请输入服务单号" disabled />
        </a-form-item>
        <a-form-item label="退款金额" class="required">
          <a-input
            :value="`${$formatters.thousandSeparator(modelFormState.refund_amount)}${modelFormState.include_freight
              ? '(含运费' +
                $formatters.thousandSeparator(
                  modelFormState?.order?.freight_amount - modelFormState?.order?.freight_discount
                ) +
                '元)'
              : ''
            }`"
            disabled
            placeholder="请输入退款金额"
          />
        </a-form-item>
        <a-form-item label="操作说明" class="required">
          <a-textarea
            v-model:value="modelFormState.result"
            :placeholder="'请输入操作备注说明，不超过200字\n请确认此单无异议，同意后退款金额将原路退回到申请人账户中'"
            :rows="5"
            :maxlength="200"
          />
        </a-form-item>
      </a-form>
      <template #footer>
        <a-button type="primary" danger @click="handleRefund('refused')">
          拒绝退款
        </a-button>
        <a-button type="primary" :loading="refundLoading" @click="handleRefund('agreed')">
          同意退款
        </a-button>
      </template>
    </a-modal>
  </div>
</template>
<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep, debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useModalVisible } from '@/composables/useToggles'
import { afterSaleOrdersApi } from '../../../api'
import { orderCondition, afterSaleType } from '../../../enums'
import { useRefundReasonOptions, useReasonFilter } from '../../../useOrderReason'

// 关键字搜索
const conditionOptions = orderCondition.refundApplyOptions()
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))
const getConditionValue = (isFilters = true) => {
  if (conditionValue.value) {
    let query = {}
    if (isFilters && conditionKey.value == orderCondition.saleSn) {
      // 售后
      query = { sn: conditionValue.value }
    } else if (!isFilters && conditionKey.value != orderCondition.saleSn) {
      switch (conditionKey.value) {
        case orderCondition.orderCode:
          query = { order: { sn: `%${conditionValue.value}%` } }
          break
        case 'nickname': //用户
          query = { user: { nickname: `%${conditionValue.value}%` } }
          break
        case 'phone_number': //用户
          query = { user: { phone_number: `${conditionValue.value}` } }
          break
      }
    } else {
      return {}
    }
    return query
  }
  return {}
}

// 退款理由
const { refundReasonOptions } = useRefundReasonOptions()
const getRefundReason = () => {
  return { reason: useReasonFilter(refundReasonOptions.value, formState.value.reason) }
}

const { data, setPage, loading } = usePaginatorApiRequest(
  (
    { offset, limit } // 表格
  ) =>
    afterSaleOrdersApi.paginator({
      filters: {
        ...useTransformQuery(
          { ...formState.value, ...getRefundReason(), ...getConditionValue() },
          {
            created_at: 'dateRange',
            sn: 'like'
          }
        ),
        type: 'refund'
      },
      relation_filters: { ...getConditionValue(false) },
      offset,
      limit,
      relations: ['order', 'afterSaleItems', 'user']
    })
)

const { formState, onRestFormState, resetFormState } = useFormState({})

const handleResetFormState = () => {
  resetFormState()
  conditionKey.value = conditionOptions[0].value
  conditionValue.value = undefined
  setPage()
}

const router = useRouter()
const toDetails = ({ id }) => router.push({ name: 'refund-apply-detail', params: { id } })

const {
  formState: modelFormState,
  setFormState: modelSetFormState,
  resetFormState: modelResetFormState,
  setFormRules: modelSetFormRules,
  validateForm: modelValidateForm
} = useFormState({
  sn: undefined,
  refund_amount: undefined,
  result: undefined
})

const { modalVisible, setModalVisible } = useModalVisible()

const showModalDeliverGoods = record => {
  modelSetFormState(record)
  setModalVisible(true)
}

modelSetFormRules({
  sn: {
    required: true,
    message: '请输入服务单号'
  },
  refund_amount: {
    required: true,
    message: '请输入退款金额'
  },
  result: {
    required: true,
    message: '请输入操作备注说明'
  }
})

const refundLoading = ref(false)

const handleRefund = debounce(async action => {
  refundLoading.value = true
  try {

    if (!(await modelValidateForm())) {
      return
    }

    let params = cloneDeep(modelFormState.value)
    console.log('params', params)
    await afterSaleOrdersApi.update(params.id, { action, result: params.result })

    message.success('审核成功')
    onCancel()
    setPage()
  } finally {
    refundLoading.value = false
  }
}, 500)

const onCancel = () => {
  setModalVisible(false)
  modelResetFormState()
}

// 退款金额累加
const getFreight = arr => {
  return arr.reduce((a, b) => (+a.refund_total || +a) + +b.refund_total, 0)
}
</script>
<style lang="less" scoped>
.express-number-box {
  :deep(.ant-input-number-handler-wrap) {
    display: none;
  }
}
</style>
