import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

export const exportGiftOrderApi = `${import.meta.env.VITE_API_BASE_URL}/gift-order/excel/export?token=${state.token}` // 虚拟订单导出
export const entityOrdersShipApi = id => apiFactory.command(`/gift-order/entity-orders/${id}/shipped`) // 实物订单发货

export const expressCodeApi = apiFactory.restful(`/gift-order/express-code`)
export const shopOrdersApi = apiFactory.restful(`/shop-order/shop-orders`)
export const shopOrdersCancelAction = id => apiFactory.command(`/shop-order/shop-orders/${id}/cancel`)
export const entityOrdersCancelAction = id => apiFactory.command(`/gift-order/entity-orders/${id}/cancel`)

export const shippedApi = id => apiFactory.command(`/shop-order/shop-orders/${id}/shipped`) // 购物订单-发货
export const exportShopOrderApi = `${import.meta.env.VITE_API_BASE_URL}/shop-order/shop-orders/excel/export` // 购物订单-导出

export const afterSaleOrdersApi = apiFactory.restful(`/shop-order/after-sale-orders`) // 售后-退货退款
export const orderSettingApi = apiFactory.command('/setting/settings/order') //订单设置-退货退款理由
export const entityOrdersApi = apiFactory.restful('/gift-order/entity-orders') // 实物订单
export const inventedOrdersApi = apiFactory.restful('/gift-order/invented-orders') // 虚拟订单
export const customOrderApi = apiFactory.command('/custom/custom-orders') // 定制订单
export const shopOrderItemCommentsApi = apiFactory.restful('/shop-order/shop-order-item-comments') //订单评论
