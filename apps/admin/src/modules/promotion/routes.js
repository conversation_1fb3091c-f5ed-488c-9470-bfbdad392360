export default {
  path: 'promotion',
  meta: {
    title: '促销',
    antIcon: 'AppstoreOutlined'
  },
  children: [
    {
      path: 'activity-summary-list',
      name: 'activity-summary-list',
      meta: {
        title: '活动汇总'
      },
      component: () => import('./pages/activity-summary/list')
    },
    {
      path: 'promotion-coupon-list',
      name: 'promotion-coupon-list',
      meta: {
        title: '优惠卷',
        keepAlive: true
      },
      component: () => import('./pages/coupon/list')
    },
    {
      path: 'promotion-coupon-add',
      name: 'promotion-coupon-add',
      meta: {
        title: '新增优惠卷',
        activeMenu: 'promotion-coupon-list'
      },
      hidden: true,
      component: () => import('./pages/coupon/edit')
    },
    {
      path: 'promotion-coupon-edit/:id',
      name: 'promotion-coupon-edit',
      meta: {
        title: '编辑优惠卷'
      },
      hidden: true,
      component: () => import('./pages/coupon/edit')
    },
    {
      path: 'promotion-coupon-data/:id',
      name: 'promotion-coupon-data',
      meta: {
        title: '优惠卷数据统计'
      },
      hidden: true,
      component: () => import('./pages/coupon/data')
    },
    {
      path: 'promotion-activity-dispatch-coupon',
      name: 'promotion-activity-dispatch-coupon',
      meta: {
        title: '活动发券',
        keepAlive: true
      },
      component: () => import('./pages/activity-dispatch-coupon/list')
    },
    {
      path: 'promotion-activity-dispatch-coupon-code/:id',
      name: 'promotion-activity-dispatch-coupon-code',
      meta: {
        title: '活动发券'
      },
      hidden: true,
      component: () => import('./pages/activity-dispatch-coupon/code')
    },
    {
      path: 'promotion-activity-dispatch-coupon-add',
      name: 'promotion-activity-dispatch-coupon-add',
      meta: {
        title: '新增活动发券'
      },
      hidden: true,
      component: () => import('./pages/activity-dispatch-coupon/edit')
    },
    {
      path: 'promotion-activity-dispatch-coupon-edit/:id',
      name: 'promotion-activity-dispatch-coupon-edit',
      meta: {
        title: '编辑活动发券'
      },
      hidden: true,
      component: () => import('./pages/activity-dispatch-coupon/edit')
    },
    {
      path: 'promotion-activity-dispatch-coupon-detail/:id',
      name: 'promotion-activity-dispatch-coupon-detail',
      meta: {
        title: '活动发券详情'
      },
      hidden: true,
      component: () => import('./pages/activity-dispatch-coupon/edit')
    },
    {
      path: 'promotion-directional-dispatch-coupon',
      name: 'promotion-directional-dispatch-coupon',
      meta: {
        title: '定向发券',
        keepAlive: true
      },
      component: () => import('./pages/directional-dispatch-coupon/list')
    },
    {
      path: 'promotion-directional-dispatch-coupon-add',
      name: 'promotion-directional-dispatch-coupon-add',
      meta: {
        title: '新增定向发券'
      },
      hidden: true,
      component: () => import('./pages/directional-dispatch-coupon/edit')
    },
    {
      path: 'promotion-directional-dispatch-coupon-detail/:id',
      name: 'promotion-directional-dispatch-coupon-detail',
      meta: {
        title: '定向发券详情'
      },
      hidden: true,
      component: () => import('./pages/directional-dispatch-coupon/edit')
    },
    {
      path: 'full-minus-list',
      name: 'full-minus-list',
      meta: {
        title: '满减促销',
        keepAlive: true
      },
      component: () => import('./pages/full-minus/list')
    },
    {
      path: 'full-minus-add',
      name: 'full-minus-add',
      meta: {
        title: '新增满减促销'
      },
      hidden: true,
      component: () => import('./pages/full-minus/edit')
    },
    {
      path: 'full-minus-edit/:id',
      name: 'full-minus-edit',
      meta: {
        title: '编辑满减促销'
      },
      hidden: true,
      component: () => import('./pages/full-minus/edit')
    },
    {
      path: 'limited-time-discount-list',
      name: 'limited-time-discount-list',
      meta: {
        title: '限时折扣',
        keepAlive: true
      },
      component: () => import('./pages/limited-time-discount/list')
    },
    {
      path: 'limited-time-discount-add',
      name: 'limited-time-discount-add',
      meta: {
        title: '新增限时折扣'
      },
      hidden: true,
      component: () => import('./pages/limited-time-discount/edit')
    },
    {
      path: 'limited-time-discount-edit/:id',
      name: 'limited-time-discount-edit',
      meta: {
        title: '编辑限时折扣'
      },
      hidden: true,
      component: () => import('./pages/limited-time-discount/edit')
    },
    {
      path: 'gift-list',
      name: 'promotion-gift-list',
      meta: {
        title: '满赠促销',
        keepAlive: true
      },
      component: () => import('./pages/gift/list.vue')
    },
    {
      path: 'gift-add',
      name: 'promotion-gift-add',
      meta: {
        title: '新增满赠促销'
      },
      hidden: true,
      component: () => import('./pages/gift/edit.vue')
    },
    {
      path: 'gift-edit/:id',
      name: 'promotion-gift-edit',
      meta: {
        title: '编辑满赠促销'
      },
      hidden: true,
      component: () => import('./pages/gift/edit.vue')
    },
    {
      path: 'sales-freight-list',
      name: 'sales-freight-list',
      meta: {
        title: '运费促销',
        keepAlive: true
      },
      component: () => import('./pages/sales-freight/list')
    },
    {
      path: 'sales-freight-add',
      name: 'sales-freight-add',
      meta: {
        title: '新增运费促销'
      },
      hidden: true,
      component: () => import('./pages/sales-freight/edit')
    },
    {
      path: 'sales-freight-edit/:id',
      name: 'sales-freight-edit',
      meta: {
        title: '编辑运费促销'
      },
      hidden: true,
      component: () => import('./pages/sales-freight/edit')
    },
    {
      path: 'promotion-bargain-list',
      name: 'promotion-bargain-list',
      meta: {
        title: '砍价助力',
        keepAlive: true
      },
      component: () => import('./pages/bargain/list')
    },
    {
      path: 'promotion-bargain-add',
      name: 'promotion-bargain-add',
      meta: {
        title: '新增砍价助力'
      },
      hidden: true,
      component: () => import('./pages/bargain/edit')
    },
    {
      path: 'promotion-bargain-edit/:id',
      name: 'promotion-bargain-edit',
      meta: {
        title: '编辑砍价'
      },
      hidden: true,
      component: () => import('./pages/bargain/edit')
    },
    {
      path: 'promotion-bargain-data/:id',
      name: 'promotion-bargain-data',
      meta: {
        title: '砍价数据'
      },
      hidden: true,
      component: () => import('./pages/bargain/data')
    },
    {
      path: 'seckill-list',
      name: 'seckill-list',
      meta: {
        title: '特价秒杀',
        keepAlive: true
      },
      component: () => import('./pages/seckill/list')
    },
    {
      path: 'seckill-add',
      name: 'seckill-add',
      meta: {
        title: '新增特价秒杀'
      },
      hidden: true,
      component: () => import('./pages/seckill/edit')
    },
    {
      path: 'seckill-edit/:id/:readonly',
      name: 'seckill-edit',
      meta: {
        title: '编辑特价秒杀'
      },
      hidden: true,
      component: () => import('./pages/seckill/edit')
    },
    {
      path: 'mark-up-list',
      name: 'mark-up-list',
      meta: {
        title: '加价换购',
        keepAlive: true
      },
      component: () => import('./pages/mark-up/list')
    },
    {
      path: 'mark-up-add',
      name: 'mark-up-add',
      meta: {
        title: '新增加价换购'
      },
      hidden: true,
      component: () => import('./pages/mark-up/edit')
    },
    {
      path: 'mark-up-edit/:id',
      name: 'mark-up-edit',
      meta: {
        title: '编辑加价换购'
      },
      hidden: true,
      component: () => import('./pages/mark-up/edit')
    },
    {
      path: 'top-goods-list',
      name: 'top-goods-list',
      meta: {
        title: '尖货抽签',
        keepAlive: true
      },
      component: () => import('./pages/top-goods/list')
    },
    {
      path: 'top-goods-add',
      name: 'top-goods-add',
      meta: {
        title: '新增尖货抽签'
      },
      hidden: true,
      component: () => import('./pages/top-goods/edit')
    },
    {
      path: 'top-goods-edit/:id',
      name: 'top-goods-edit',
      meta: {
        title: '编辑尖货抽签'
      },
      hidden: true,
      component: () => import('./pages/top-goods/edit')
    },
    {
      path: 'promotion-group-data/:id',
      name: 'promotion-group-data',
      meta: {
        title: '拼团数据'
      },
      hidden: true,
      component: () => import('./pages/group/data')
    },
    {
      path: 'promotion-group-list',
      name: 'promotion-group-list',
      meta: {
        title: '多人拼团',
        keepAlive: true
      },
      component: () => import('./pages/group/list')
    },
    {
      path: 'promotion-group-add',
      name: 'promotion-group-add',
      meta: {
        title: '新增多人拼团'
      },
      hidden: true,
      component: () => import('./pages/group/edit')
    },
    {
      path: 'promotion-group-edit/:id',
      name: 'promotion-group-edit',
      meta: {
        title: '编辑多人拼团'
      },
      hidden: true,
      component: () => import('./pages/group/edit')
    },
    {
      path: 'promotion-wholesale-list',
      name: 'promotion-wholesale-list',
      meta: {
        title: '团购批发',
        keepAlive: true
      },
      component: () => import('./pages/wholesale/list')
    },
    {
      path: 'promotion-wholesale-add',
      name: 'promotion-wholesale-add',
      meta: {
        title: '新增团购批发'
      },
      hidden: true,
      component: () => import('./pages/wholesale/edit')
    },
    {
      path: 'promotion-wholesale-edit/:id',
      name: 'promotion-wholesale-edit',
      meta: {
        title: '编辑团购批发'
      },
      hidden: true,
      component: () => import('./pages/wholesale/edit')
    },
    {
      path: 'promotion-wholesale-user-list/:id',
      name: 'promotion-wholesale-user-list',
      meta: {
        title: '用户管理'
      },
      hidden: true,
      component: () => import('./pages/wholesale/user/list')
    },
    {
      path: 'promotion-site-list',
      name: 'promotion-site-list',
      meta: {
        title: '现场活动',
        keepAlive: true
      },
      component: () => import('./pages/site/list')
    },
    {
      path: 'promotion-site-add',
      name: 'promotion-site-add',
      meta: {
        title: '新增现场活动'
      },
      hidden: true,
      component: () => import('./pages/site/edit')
    },
    {
      path: 'promotion-site-edit/:id',
      name: 'promotion-site-edit',
      meta: {
        title: '编辑现场活动'
      },
      hidden: true,
      component: () => import('./pages/site/edit')
    },
    {
      path: 'promotion-site-goods/:act_id',
      name: 'promotion-site-goods',
      meta: {
        title: '编辑现场活动商品'
      },
      hidden: true,
      component: () => import('./pages/site/goods')
    },
    {
      path: 'promotion-site-position/:act_id',
      name: 'promotion-site-position',
      meta: {
        title: '编辑现场活动点位'
      },
      hidden: true,
      component: () => import('./pages/site/position')
    },
    {
      path: 'promotion-gift-box',
      name: 'promotion-gift-box',
      meta: {
        title: '定制礼盒',
        keepAlive: true
      },
      component: () => import('./pages/gift-box/list')
    },
    {
      path: 'promotion-gift-box-add',
      name: 'promotion-gift-box-add',
      meta: {
        title: '新增定制礼盒'
      },
      hidden: true,
      component: () => import('./pages/gift-box/edit')
    },
    {
      path: 'promotion-gift-box-edit/:id',
      name: 'promotion-gift-box-edit',
      meta: {
        title: '编辑定制礼盒'
      },
      hidden: true,
      component: () => import('./pages/gift-box/edit')
    },
    {
      path: 'promotion-gift-card-list',
      name: 'promotion-gift-card-list',
      meta: {
        title: '礼品卡',
        keepAlive: true
      },
      component: () => import('./pages/gift-card/list')
    },
    {
      path: 'promotion-gift-card-add',
      name: 'promotion-gift-card-add',
      meta: {
        title: '新增礼品卡'
      },
      hidden: true,
      component: () => import('./pages/gift-card/edit')
    },
    {
      path: 'promotion-gift-card-edit/:id',
      name: 'promotion-gift-card-edit',
      meta: {
        title: '编辑礼品卡'
      },
      hidden: true,
      component: () => import('./pages/gift-card/edit')
    },
    {
      path: 'promotion-gift-card-code-list',
      name: 'promotion-gift-card-code-list',
      meta: {
        title: '礼品卡-卡号'
      },
      hidden: true,
      component: () => import('./pages/gift-card/code-list')
    },
  ]
}
