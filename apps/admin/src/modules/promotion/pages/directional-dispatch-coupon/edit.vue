<template>
  <uc-layout-form :is-save="!isAllDisabled" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="发放主题" name="title" class="required">
            <a-input
              v-model:value.trim="formState.title"
              class="input-width"
              placeholder="请输入发放主题，不超过20字"
              :maxlength="20"
              :disabled="isAllDisabled"
            />
          </a-form-item>

          <a-form-item label="发放类型" name="type" class="required">
            <a-select
              v-model:value="formState.type"
              placeholder="请选择发放类型"
              :options="dispatchCouponsType.options()"
              :disabled="isAllDisabled"
            />
          </a-form-item>

          <a-form-item
            v-if="formState.type === dispatchCouponsType.phone"
            label="用户发放"
            name="user_range"
            class="required"
          >
            <a-textarea
              v-model:value="formState.user_range"
              placeholder="请输入手机号码，一行一个，手机号码不可重复"
              :auto-size="{ minRows: 6, maxRows: 6 }"
              :disabled="isAllDisabled"
            />
          </a-form-item>
          <a-form-item
            v-if="formState.type === dispatchCouponsType.tags"
            label="标签发放"
            name="user_tags_range"
            class="required"
          >
            <a-select
              v-model:value="formState.user_tags_range"
              mode="multiple"
              show-search
              :filter-option="userTagFilterOption"
              allow-clear
              placeholder="请选择用户标签"
              :options="userTagOptions"
              :disabled="isAllDisabled"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="发放优惠券" class="h-fill">
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '定向发券'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex }"
            class="m-b-10"
          >
            <div v-for="(item, index) in rule.award_settings" :key="index">
              <a-space :size="10" wrap class="m-b-0">
                <!-- 获取全部为过期优惠券，按创建时间倒序，选项为券码-优惠券名称 -->
                <a-select
                  v-model:value="item.option_id"
                  placeholder="请选择优惠券"
                  :options="couponDateOptions"
                  class="w-500"
                  :disabled="isAllDisabled"
                />
                <a-input-number
                  v-model:value="item.quantity"
                  placeholder="每人发放数量"
                  :min="1"
                  :disabled="isAllDisabled"
                />
                <a-button
                  shape="circle"
                  size="small"
                  class="delete-btn"
                  type="link"
                  :disabled="formState.rules.length == 1 || isAllDisabled"
                >
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveSend(ruleIndex)" />
                  </template>
                </a-button>
              </a-space>
            </div>
          </a-form-item>
          <a-form-item label=" " :colon="false">
            <a-button type="link" class="p-0" :disabled="isAllDisabled" @click="onAddRule"> 添加发放层级 </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { directionalCouponApi, awardOptionsApi } from '../../api'
import { useCouponDateOptions } from '../../useCoupon'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { cloneDeep } from 'lodash'
import { dispatchCouponsType } from '../../enums'
import { useUserTag } from '@/modules/member/useUserTag'

const { userTagOptions, userTagFilterOption } = useUserTag()

const { id } = useRoute().params
const { phone } = useRoute().query
const isAllDisabled = ref(false)
isAllDisabled.value = !!id

const couponDateOptions = ref([])

awardOptionsApi.list({ filters: { type: 'coupon' } }).then(data => {
  couponDateOptions.value = useTransformOptions(data, 'title', 'id', ['type', 'photo_url'])
})

// 默认定向发券
const defaultSendStamps = { option_id: undefined, type: 'coupon', quantity: undefined }

const router = useRouter()

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  type: dispatchCouponsType.phone,
  user_range: phone ? phone : undefined,
  user_tags_range: undefined,
  rules: [{ award_settings: [cloneDeep(defaultSendStamps)] }]
})

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  directionalCouponApi
    .get(id, { relations: ['rules'] })
    .then(data => {
      setFormState(data)
    })
    .finally(hideLoading)
}

// 自定义定向发券验证
const rulesValidate = (_, value) => {
  for (const { award_settings } of value) {
    for (const { option_id, quantity } of award_settings) {
      if (!option_id) {
        return Promise.reject('请选择优惠券')
      }
      if (!quantity) {
        return Promise.reject('请输入发放数量')
      }
    }
  }
  return Promise.resolve()
}

// 自定义发放用户验证
const SendUserValidate = (_, value) => {
  if (formState.value.type !== dispatchCouponsType.phone) {
    return Promise.resolve()
  }
  if (!value) {
    return Promise.reject('请输入发放用户')
  }
  const arr = value.split('\n').map(value => value.trim())
  const resArr = [...new Set(arr)]
  if (arr.length !== resArr.length) {
    return Promise.reject('手机号码不可重复')
  }
  return Promise.resolve()
}

// 自定义标签验证
const SendTagsValidate = (_, value) => {
  if (formState.value.type !== dispatchCouponsType.tags) {
    return Promise.resolve()
  }
  if (!value || value.length === 0) {
    return Promise.reject('请选择用户标签')
  }
  return Promise.resolve()
}

setFormRules({
  title: { required: true, message: '请输入发放主题' },
  user_range: { validator: SendUserValidate, trigger: 'blur' },
  user_tags_range: { validator: SendTagsValidate, trigger: 'blur' },
  rules: { validator: rulesValidate, trigger: 'blur' }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  params.rules.forEach(rule => {
    rule.award_settings.forEach(item => {
      item.stock = item.quantity
    })
  })

  await directionalCouponApi.create(params)
  message.success('定向发券创建完成')
  router.back()
}

const onRemoveSend = index => formState.value.rules.splice(index, 1)

const onAddRule = () => {
  if (formState.value.rules.length == 5) return message.error('最多可添加5个层级')
  formState.value.rules.push({ award_settings: [cloneDeep(defaultSendStamps)] })
}
</script>
<style scoped lang="less">
.delete-btn {
  padding-top: 2px;
  border: none;

  :deep(.anticon) {
    font-size: 20px;
  }
}

.delete-btn:disabled {
  :deep(.anticon) {
    color: #d9d9d9 !important;
  }
}
</style>
