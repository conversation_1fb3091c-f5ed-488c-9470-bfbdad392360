<template>
  <uc-layout-list title="定向发券">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            :options="searchType.options()"
            @change="() => (formState.conditionValue = undefined)"
          />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键词" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增定向发券
      </a-button>
    </template>
    <template #list>
      <a-table
        row-key="id"
        :data-source="data.items"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="发放主题/用户" ellipsis>
          <template #default="{ record }">
            <uc-img-text :title="record.title" :subtit="record.type ===dispatchCouponsType.phone ? record.users : record.tags" />
          </template>
        </a-table-column>
        <a-table-column title="类型" width="120px" data-index="type" align="center">
          <template #default="{ record }">
            {{ dispatchCouponsType.filter(record.type).label }}
          </template>
        </a-table-column>
        <a-table-column title="发放" width="120px" data-index="send" align="right" />
        <a-table-column title="核销" width="120px" data-index="used" align="right" />
        <a-table-column title="过期" width="120px" data-index="expired" align="right" />
        <a-table-column title="创建时间" data-index="created_at" width="150px" />
        <a-table-column title="操作" width="70px">
          <template #default="{ record }">
            <a-button type="link" @click="onDetail(record)">
              查看
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { watch } from 'vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { directionalCouponApi } from '../../api'
import { searchType, dispatchCouponsType } from '../../enums'

const router = useRouter()

const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: searchType.userRange,
  conditionValue: undefined
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  directionalCouponApi.paginator({
    filters: useTransformQuery(
      { [formState.value.conditionKey]: formState.value.conditionValue },
      { [formState.value.conditionKey]: 'like' }
    ),
    offset,
    limit
  })
)

watch(
  () => data.value,
  v => {
    if (v && v.items.length) {
      v.items.forEach(item => {
        if(item.type === dispatchCouponsType.phone) {
          item.users = item.user_range.split('\n').toString()
        }
      })
    }
  },
  { immediate: true, deep: true }
)

onRestFormState(() => setPage())
// 新增
const onAdd = () => {
  router.push({
    name: 'promotion-directional-dispatch-coupon-add'
  })
}
const onDetail = ({ id }) => {
  router.push({
    name: 'promotion-directional-dispatch-coupon-detail',
    params: { id }
  })
}
</script>
