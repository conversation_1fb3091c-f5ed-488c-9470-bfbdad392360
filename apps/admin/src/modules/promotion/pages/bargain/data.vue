<template>
  <a-space :size="20" direction="vertical">
    <a-row :gutter="18">
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            访客 {{ $formatters.thousandSeparator(statsData.uv, false, false) }}
          </div>
          <div class="color-45">
            <a-space :size="10">
              <span>会员：{{ $formatters.thousandSeparator(statsData.register_user_count, false, false) }} </span>
            </a-space>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            参与 {{ $formatters.thousandSeparator(statsData.join_user_count, false, false) }}
          </div>
          <div class="color-45">
            <a-space :size="10">
              <span>发起：{{ $formatters.thousandSeparator(statsData.bargain_user_count, false, false) }}</span>
              <span>助力：{{ $formatters.thousandSeparator(statsData.help_user_count, false, false) }} </span>
            </a-space>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            砍价 {{ $formatters.thousandSeparator(statsData.bargain_count, false, false) }}
          </div>
          <div class="color-45">
            <a-space :size="10">
              <span>成功：{{ $formatters.thousandSeparator(statsData.complete_count, false, false) }}</span>
              <span>失败：{{ $formatters.thousandSeparator(statsData.fail_count, false, false) }} </span>
            </a-space>
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            订单 {{ $formatters.thousandSeparator(statsData.order_count, false, false) }}
          </div>
          <div class="color-45">
            <a-space :size="10">
              <span>支付：{{ $formatters.thousandSeparator(statsData.paid_count, false, false) }}</span>
              <span>支付金额：{{ $formatters.thousandSeparator(statsData.paid_amount) }} </span>
            </a-space>
          </div>
        </div>
      </a-col>
    </a-row>
    <a-card title="砍价记录">
      <template #extra>
        <a-space :size="10">
          <a-input-group compact>
            <a-select
              v-model:value="formState.type"
              class="w-120"
              placeholder="请选择"
              :options="userModeList.options()"
              @change="setPage()"
            />
            <a-input
              v-model:value.trim="formState.keyword"
              placeholder="请输入关键字"
              class="w-300"
              @press-enter="setPage()"
              @blur="setPage()"
            />
          </a-input-group>
          <a-select
            v-model:value="formState.bargain_status"
            allow-clear
            class="w-120"
            placeholder="助力状态"
            :options="helpStatusList.options()"
            @change="setPage()"
          />
          <a-select
            v-model:value="formState.pay_status"
            allow-clear
            class="w-120"
            placeholder="支付状态"
            :options="payStatusList.options()"
            @change="setPage()"
          />
        </a-space>
      </template>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="商品信息" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.goods.photo_urls[0]"
              :title="record.goods.title"
              :subtit="`${record.sku}：${record.goods_spec.attrs.reduce(
                (prev, item, i) => prev + (i ? '/' : '') + item.value,
                ''
              )}`"
              :status="helpStatusList.filter(record.bargain_status)"
            />
          </template>
        </a-table-column>
        <a-table-column title="发起人" width="160px" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.user?.nickname }}
            </div>
            <div>{{ $formatters.numberEncryption(record.user?.phone_number) }}</div>
          </template>
        </a-table-column>
        <a-table-column title="有效时间" width="200px">
          <template #default="{ record }">
            {{ record.start_time }} <br />至 {{ record.end_time }}
          </template>
        </a-table-column>
        <a-table-column title="助力" width="100px">
          <template #default="{ record }">
            {{ record.help_count }}
          </template>
        </a-table-column>
        <a-table-column title="砍后价" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.cur_price) }}
          </template>
        </a-table-column>
        <a-table-column title="支付状态" width="120px">
          <template #default="{ record }">
            <a-badge
              :color="payStatusList.filter(record.pay_status).color"
              :text="payStatusList.filter(record.pay_status).label"
            />
          </template>
        </a-table-column>
      </a-table>
    </a-card>
  </a-space>
</template>
<script setup>
import { useRoute } from 'vue-router'
import { debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { bargainDataApi, bargainRecordApi } from '../../api'
import { userModeList, helpStatusList, payStatusList } from '../../enums'

const { id } = useRoute().params
const statsData = ref({
  join_user_count: 0,
  bargain_user_count: 0,
  help_user_count: 0,
  bargain_count: 0,
  complete_count: 0,
  fail_count: 0,
  order_count: 0,
  paid_count: 0,
  paid_amount: 0,
  uv: 0,
  register_user_count: 0
})
const loadDataStats = async () => {
  statsData.value = await bargainDataApi(id).get({})
}
useLoadingMessage(loadDataStats(), {
  loadingText: '正在加载数据...'
})

const { formState } = useFormState({
  type: userModeList.phone_number,
  keyword: undefined,
  bargain_status: undefined,
  pay_status: undefined
})
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  bargainRecordApi.paginator({
    offset,
    limit,
    filters: {
      activity_id: id,
      bargain_status: formState.value.bargain_status,
      pay_status: formState.value.pay_status
    },
    relations: ['user', 'goods', 'goodsSpec'],
    sorts: ['-created_at'],
    relation_filters: {
      user: useTransformQuery({ [formState.value.type]: formState.value.keyword }, { nickname: 'like' })
    }
  })
)
setPage()
</script>
