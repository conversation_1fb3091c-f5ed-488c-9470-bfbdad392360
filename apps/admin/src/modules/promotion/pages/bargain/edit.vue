<template>
  <!-- 砍价编辑 -->
  <uc-layout-form :is-save="!isReadonly" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              class="w-240"
              :disabled="isReadonly || isStarted"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
              class="w-240"
              :disabled="isReadonly"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value.trim="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isReadonly"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              placeholder="请输入活动标签，不超过10字"
              :maxlength="10"
              :disabled="isReadonly"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="有效时间" class="required">
            <a-input-number
              v-model:value="formState.effect_time"
              :min="1"
              placeholder="请输入有效时间（用户需在限定时间内完成砍价，否则砍价失败）"
              :formatter="$formatters.number"
              :disabled="isReadonly"
            />
          </a-form-item>
          <a-form-item label="发起次数" class="required">
            <a-input-number
              v-model:value="formState.max_bargain_times"
              placeholder="请输入本场活动发起次数（以砍价成功并完成支付计算）"
              :min="0"
              :formatter="$formatters.naturalNumber"
              :disabled="isStarted || isReadonly"
            />
          </a-form-item>
          <a-form-item label="助力资格" class="required">
            <a-space :size="10">
              <a-select
                v-model:value="formState.help_range_type"
                :disabled="isStarted || isReadonly"
                :options="helpList.options()"
                class="w-150"
                @change="onChangeHelpType"
              />
              <a-input-number
                v-model:value="formState.max_help_times"
                placeholder="请输入助力次数"
                :min="1"
                class="w-340"
                :formatter="$formatters.number"
                :disabled="formState.help_range_type === helpList.new_user || isStarted || isReadonly"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="减免运费" class="required">
            <a-space :size="10">
              <a-select
                v-model:value="formState.is_freight_free"
                :disabled="isStarted || isReadonly"
                :options="feeList.options()"
                class="w-500"
              />
            </a-space>
          </a-form-item>
        </a-card>
      </uc-col>
      <a-card title="活动商品">
        <template #extra>
          <a-button type="primary" :disabled="isStarted || isReadonly" @click="onAddGoods"> 添加商品 </a-button>
        </template>
        <a-table :data-source="formState.goods" row-key="id" :pagination="false">
          <a-table-column title="商品名称/规格名称" ellipsis>
            <template #default="{ record }">
              <uc-img-text
                :url="record.photo_url"
                :title="record.title"
                :subtit="`${record.sku}：${record.attrs.reduce(
                  (prev, item, i) => prev + (i ? '/' : '') + item.value,
                  ''
                )}`"
              />
            </template>
          </a-table-column>
          <a-table-column title="销售价" width="120px" align="right">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.price) }}
            </template>
          </a-table-column>
          <a-table-column title="助力价" width="120px" align="right">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.min_price) }}
            </template>
          </a-table-column>
          <a-table-column title="活动数量" width="100px">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.setting_stock, false, false) }}
            </template>
          </a-table-column>
          <a-table-column title="助力人数" width="100px">
            <template #default="{ record }">
              {{ record.cur_buy_user_count }} - {{ record.min_price_user_count }}
            </template>
          </a-table-column>
          <a-table-column title="操作" width="110px">
            <template #default="{ index }">
              <a-button type="link" class="danger" :disabled="isStarted || isReadonly" @click="onRemoveGoods(index)">
                移除
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </a-card>
      <uc-col>
        <a-card title="页面设置">
          <a-form-item label="皮肤模式" class="required">
            <a-space size="0">
              <a-select
                v-model:value="formState.page_setting.skin_mode"
                :options="skinMode.option()"
                :disabled="isStarted"
                class="w-310"
              />
              <a-button type="link" @click="imgRef.$el.nextSibling.click()"> 效果图预览 </a-button>
              <div v-show="false">
                <a-image ref="imgRef" :src="assets.previewImg" />
              </div>
            </a-space>
          </a-form-item>
          <a-form-item label="颜色配置" class="required">
            <a-space :size="10" wrap>
              <a-input
                v-model:value.trim="formState.page_setting.color.subject"
                placeholder="主题色，如#000000"
                class="w-150"
                :disabled="isReadonly"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.sub"
                placeholder="辅助色，如#000000"
                class="w-150"
                :disabled="isReadonly"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.bg"
                placeholder="背景色，如#000000"
                class="w-150"
                :disabled="isReadonly"
              />
              <a-input
                v-model:value.trim="formState.page_setting.color.title"
                placeholder="标题色，如#000000"
                class="w-150"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" class="required">
            <a-space :size="10">
              <uc-upload
                v-for="(item, index) in pageConfig"
                :key="index"
                v-model:list="item.value"
                upload-text=" "
                show-label
                :label-text="item.label"
                :max-length="1"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="弹窗配置" class="required">
            <a-space :size="10" wrap>
              <uc-upload
                v-for="(item, index) in modalConfig"
                :key="index"
                v-model:list="item.value"
                upload-text=" "
                show-label
                :label-text="item.label"
                :max-length="1"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="分享设置" class="h-fill">
          <a-form-item label="分享海报" class="required">
            <a-space :size="10">
              <uc-upload
                v-for="(item, index) in shareConfig"
                :key="index"
                v-model:list="item.value"
                upload-text=" "
                show-label
                :label-text="item.label"
                :max-length="1"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="分享文案" class="required">
            <a-input
              v-model:value="formState.share_setting.share_text"
              placeholder="请输入分享文案，不超过20字"
              :maxlength="20"
              :disabled="isReadonly"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <uc-rich-text v-model="formState.desc" :disabled="isReadonly" />
  </uc-layout-form>
  <!-- 添加商品模态框 -->
  <a-modal title="添加商品" ok-text="保存" :visible="modalVisible" @cancel="modalVisible = false" @ok="onModalSubmit">
    <a-form>
      <a-form-item label="商品SKU" class="required">
        <input-sku
          v-if="modalVisible"
          v-model="modalFormState.sku"
          @change-sku="$event => (modalFormState.price = $event)"
        />
      </a-form-item>
      <a-form-item label="商品原价" class="required">
        <a-input-number v-model:value.trim="modalFormState.price" disabled placeholder="商品原价" />
      </a-form-item>
      <a-form-item label="助力价格" class="required">
        <a-input-number v-model:value.trim="modalFormState.min_price" placeholder="请输入好友助力最低价格" />
      </a-form-item>
      <a-form-item label="活动数量" class="required">
        <a-input-number
          v-model:value="modalFormState.setting_stock"
          placeholder="请输入活动数量"
          :min="1"
          :formatter="$formatters.number"
        />
      </a-form-item>
      <a-form-item label="助力人数" class="required">
        <a-input-number
          v-model:value="modalFormState.min_price_user_count"
          placeholder="请输入砍至最低价需助力人数，至少2人"
          :min="2"
          :formatter="$formatters.number"
        />
      </a-form-item>
      <a-form-item label="当前价买" class="required">
        <a-input-number
          v-model:value="modalFormState.cur_buy_user_count"
          placeholder="请输入当前价购买需助力人数，至少1人"
          :min="1"
          :formatter="$formatters.number"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { priceToInt } from '@/composables/useTransformFormat'
import { generateRandom } from '@/utils/functions'
import { colorVerify } from '@/utils/index'
import { bargainApi, bargainGoodsApi, goodsSpecsApi } from '../../api'
import { helpList, skinMode, feeList } from '../../enums'
import { useTransformBargain } from '../../useTransform'
import assets from '../../assets.config'
import InputSku from '../../components/input-sku'

const { statusList, TIME_STATUS_NOSTART, TIME_STATUS_ENDED } = useTimeStatus()

const tableLoading = ref(true)
const imgRef = ref(null)

const { pageConfig, modalConfig, shareConfig, transformToShow, transformToRequest } = useTransformBargain()
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  //
  effect_time: null,
  max_bargain_times: null,
  help_range_type: helpList.new_user,
  max_help_times: 1,
  is_freight_free: feeList.fee,
  //
  goods: [],
  page_setting: {
    skin_mode: 'default',
    color: {
      subject: '',
      sub: '',
      bg: '',
      title: ''
    }
  },
  share_setting: {
    share_text: ''
  },
  desc: ''
})
const router = useRouter()
const { id } = useRoute().params
const isReadonly = ref(false)
const isStarted = ref(false)

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  bargainApi
    .get(id, { relations: ['goods'] })
    .then(res => {
      isStarted.value = res.status !== TIME_STATUS_NOSTART
      isReadonly.value = res.status === TIME_STATUS_ENDED
      setFormState(transformToShow(res))
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

const validatorGoods = (rule, value) => {
  if (!value.length) return Promise.reject('请添加活动商品')
  return Promise.resolve(true)
}
const validatorPage = (rule, value) => {
  if (!colorVerify(value.color.subject)) {
    return Promise.reject('请输入正确主题色')
  } else if (!colorVerify(value.color.sub)) {
    return Promise.reject('请输入正确辅助色')
  } else if (!colorVerify(value.color.bg)) {
    return Promise.reject('请输入正确背景色')
  } else if (!colorVerify(value.color.title)) {
    return Promise.reject('请输入正确标题色')
  }
  const pageItem = pageConfig.value.concat(modalConfig.value).find(item => !item.value.length)
  if (pageItem) return Promise.reject(`请上传${pageItem.label}图`)
  return Promise.resolve(true)
}
const validatorShare = (rule, value) => {
  if (!value.share_text) return Promise.reject('请输入分享文案')

  const shareItem = shareConfig.value.find(item => !item.value.length)
  if (shareItem) return Promise.reject(`请上传${shareItem.label}图`)

  return Promise.resolve(true)
}

setFormRules({
  start_time: { required: true, message: '请选择开始时间' },
  end_time: { required: true, message: '请选择结束时间' },
  title: { required: true, message: '请输入活动名称' },
  effect_time: { required: true, message: '请输入有效时间' },
  max_bargain_times: { required: true, message: '请输入本场活动发起次数' },
  max_help_times: { required: true, message: '请输入助力次数' },
  goods: { validator: validatorGoods },
  page_setting: { validator: validatorPage },
  share_setting: { validator: validatorShare },
  desc: { required: true, message: '请输入活动说明' }
})

// 助力资格类型改变时
const onChangeHelpType = () => {
  formState.value.max_help_times = formState.value.help_range_type === helpList.new_user ? 1 : ''
}

const modalVisible = ref(false)
const onAddGoods = () => {
  modalResetFormState()
  modalVisible.value = true
}
const onRemoveGoods = index => {
  formState.value.goods.splice(index, 1)
}

const {
  formState: modalFormState,
  resetFormState: modalResetFormState,
  setFormState: modalSetFormState,
  setFormRules: modalSetFormRules,
  validateForm: modalValidateForm
} = useFormState({
  sku: '',
  price: '',
  min_price: '',
  setting_stock: '',
  min_price_user_count: '',
  cur_buy_user_count: ''
})

const validatorGoodsSku = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入商品SKU')
  } else if (formState.value.goods.some(item => item.sku?.toLowerCase() === value?.toLowerCase())) {
    return Promise.reject('该商品SKU已存在')
  }
  return Promise.resolve(true)
}

const validatorMinPrice = (rule, value) => {
  if (!value && value !== 0) {
    return Promise.reject('请输入助力价格')
  } else if (value >= modalFormState.value.price) {
    return Promise.reject('助力价格不能高于或等于原价')
  }
  return Promise.resolve(true)
}

const validatorCurBuy = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入当前价买')
  } else if (value > modalFormState.value.min_price_user_count) {
    return Promise.reject('当前价买不能高于助力人数')
  }
  return Promise.resolve(true)
}

modalSetFormRules({
  sku: { validator: validatorGoodsSku },
  price: { required: true, message: '请输入商品原价' },
  min_price: { validator: validatorMinPrice },
  setting_stock: { required: true, message: '请输入活动数量' },
  min_price_user_count: { required: true, message: '请输入助力人数' },
  cur_buy_user_count: { validator: validatorCurBuy }
})

let modalLocked = false
// 请输入当前价购买需助力人数
const onModalSubmit = async () => {
  if (!(await modalValidateForm()) || modalLocked) return
  modalLocked = true
  const item = cloneDeep(modalFormState.value)
  const priceFields = ['price', 'min_price']
  priceFields.forEach(key => {
    item[key] = priceToInt(item[key])
  })
  item.id = item.id || generateRandom()
  goodsSpecsApi
    .list({ filters: { sku: item.sku }, relations: ['goods', 'goods_spec', 'stock'] })
    .then(data => {
      const goods_spec = data[0]
      if (!goods_spec) {
        message.error('商品SKU不存在')
      } else if (goods_spec.price !== item.price) {
        message.error('商品原价错误')
      } else if (item.setting_stock > goods_spec.stock.stock) {
        message.error('库存不足')
      } else {
        item.attrs = goods_spec.attrs
        item.photo_url = goods_spec.photo_url //goods.photo_urls[0]
        item.title = goods_spec.goods.title
        item.sku = goods_spec.sku
        formState.value.goods.unshift(item)
        modalVisible.value = false
      }
    })
    .finally(() => {
      modalLocked = false
    })
}

const handleSubmit = async () => {
  if (!(await validateForm())) return

  const params = transformToRequest(formState.value)
  id ? await bargainApi.replace(id, params) : await bargainApi.create(params)

  message.success('操作成功')
  router.back()
}
</script>
