<template>
  <uc-layout-form :is-save="!statusInfo.isEnded" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动海报">
            <uc-upload
              upload-text=" "
              :list="formState.photo_url ? [formState.photo_url] : []"
              :max-length="1"
              :disabled="statusInfo.isEnded"
              @update:list="data => (formState.photo_url = data[0])"
            />
          </a-form-item>
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              placeholder="开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              class="w-240"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="statusInfo.isEnded"
              placeholder="结束时间"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="statusInfo.isEnded"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value="formState.tag"
              maxlength="10"
              placeholder="请输入活动标签，不超过10字"
              :disabled="statusInfo.isEnded"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则" class="h-fill">
          <a-form-item label="赠品条件" class="required">
            <a-input-group compact>
              <a-select
                v-model:value="formState.condition_type"
                placeholder="请选择"
                :options="couponThreshold.options()"
                class="m-r-10 w-120"
                :disabled="statusInfo.isNormal || statusInfo.isEnded"
              />
              <a-input-number
                v-if="formState.condition_type == couponThreshold.amount"
                v-model:value.trim="formState.condition"
                class="w-240"
                :min="0.01"
                placeholder="请输入商品总金额"
                :disabled="statusInfo.isNormal || statusInfo.isEnded"
              />
              <a-input-number
                v-else
                v-model:value.trim="formState.condition"
                class="w-240"
                :min="1"
                :formatter="$formatters.naturalNumber"
                placeholder="请输入商品总件数"
                :disabled="statusInfo.isNormal || statusInfo.isEnded"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="适用范围" class="required">
            <a-select
              v-model:value="formState.range.type"
              placeholder="请选择适用范围"
              :options="productRange.options()"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              @change="initproductRangeProduct"
            />
          </a-form-item>
          <a-form-item
            v-if="productRange.filterCategories(formState.range.type)"
            :label="productRange.filter(formState.range.type)"
            class="required"
          >
            <a-tree-select
              v-model:value="formState.range.config"
              placeholder="请选择商品分类"
              :tree-data="categories"
              :replace-fields="{ key: 'id', value: 'id' }"
              multiple
              allow-clear
            />
          </a-form-item>
          <a-form-item
            v-if="productRange.filterGroup(formState.range.type)"
            :label="productRange.filter(formState.range.type)"
            class="required"
          >
            <a-tree-select
              v-model:value="formState.range.config"
              placeholder="请选择商品分组"
              :tree-data="groups"
              :replace-fields="{ key: 'id', value: 'id' }"
              multiple
              allow-clear
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="是否叠加" name="is_repeated">
            <a-radio-group v-model:value="formState.is_repeated">
              <a-radio :value="0">不叠加</a-radio>
              <a-radio :value="1">叠加</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <apply-sku-goods
      v-if="productRange.filterSku(formState.range.type)"
      v-model:list="formState.range.config"
      :title="productRange.filter(formState.range.type)"
      :disabled="statusInfo.isEnded"
    />
    <apply-spu-goods
      v-if="productRange.filterSpu(formState.range.type)"
      v-model:list="formState.range.config"
      :title="productRange.filter(formState.range.type)"
      :disabled="statusInfo.isEnded"
    />
    <a-card title="赠品">
      <template #extra>
        <a-button type="primary" :disabled="statusInfo.isNormal || statusInfo.isEnded" @click="onAddGoods">
          添加赠品
        </a-button>
      </template>
      <a-table :data-source="formState.goods" row-key="sku" :pagination="false">
        <a-table-column title="商品名称/规格名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record?.photo_url"
              :title="`${record?.goods?.title || record?.goods_title}`"
              :subtit="`${record?.sku}：${record?.attrs[0].value || record?.attrs}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="销售金额" width="150px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record?.price) }}
          </template>
        </a-table-column>
        <a-table-column title="活动数量" width="150px">
          <template #default="{ record }">
            <a-input-number
              v-model:value.trim="record.setting_stock"
              :min="0"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              placeholder="请输入活动数量"
            />
          </template>
        </a-table-column>
        <a-table-column title="赠送数量" width="150px">
          <template #default="{ record }">
            <a-input-number
              v-model:value.trim="record.quantity"
              :min="0"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              placeholder="请输入赠送数量"
            />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="70px">
          <template #default="{ index }">
            <a-button
              type="link"
              class="danger"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              @click="onRemoveGoods(index)"
            >
              移除
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-card>
    <select-sku-goods v-model:visible="modalVisible" :mode="goodsSelectType.single" @ok="onSkuSelectSubmit" />
    <uc-rich-text v-model="formState.desc" placeholder="请输入活动说明" :disabled="statusInfo.isEnded" :height="300" />
  </uc-layout-form>
</template>
<script setup>
import { toRaw } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useModalVisible } from '@/composables/useToggles'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { cloneDeep, debounce } from 'lodash'
import { giftApi, goodsSpecsApi } from '../../api'
import { couponThreshold, productRange, goodsSelectType } from '../../enums'
import formatters from '@/utils/formatters'
import { useMarkUpEdit } from '../../useTransform'
import { useCategories } from '@/modules/goods/useCategory'
import { useGroups } from '@/modules/goods/useGroup'

const { categories } = useCategories()
const { groups } = useGroups()

const { statusFilter, TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()
const { transformToShow, transformToRequest } = useMarkUpEdit()

const statusInfo = ref({
  isNostart: true
  // isNostart
  // isNormal
  // isEnded
})
const router = useRouter()
const { id } = useRoute().params

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  giftApi
    .get(id, { relations: ['prizes', 'goods', 'range'] })
    .then(async res => {
      statusInfo.value = statusFilter(res.status)
      setFormState(transformToShow(res))
    })
    .finally(hideLoading)
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  photo_url: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  is_repeated: 0,

  condition_type: couponThreshold.amount,
  condition: undefined,

  range: {
    type: productRange.all,
    biz_type: 'gift',
    config: []
  },

  goods: [],
  desc: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  condition: {
    validator: (rule, value) => {
      const { condition_type } = formState.value
      if (!value) {
        switch (condition_type) {
          case couponThreshold.amount:
            return Promise.reject('请输入商品总金额')
          case couponThreshold.number:
            return Promise.reject('请输入商品总件数')
        }
      }

      return Promise.resolve()
    }
  },
  goods: {
    validator: (rule, value) => {
      if (!value.length) return Promise.reject('请添加赠品')
      return Promise.resolve()
    }
  },
  desc: { required: true, message: '请输入活动说明' }
})

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) return

  // handle params
  const params = transformToRequest(formState.value)

  if (id) {
    await giftApi.replace(id, params)
    message.success('编辑完成')
  } else {
    await giftApi.create(params)
    message.success('创建完成')
  }
  router.back()
}, 500)

const { modalVisible, setModalVisible } = useModalVisible()
const onAddGoods = () => {
  setModalVisible(true)
}

const onRemoveGoods = index => formState.value.goods.splice(index, 1)

const onSkuSelectSubmit = ([goods]) => {
  if (formState.value.goods.some(item => item.sku?.toLowerCase() === goods?.sku?.toLowerCase())) {
    message.warning('该商品SKU已存在')
    return
  }

  const goods_spec = {
    ...toRaw(goods),
    setting_stock: 0,
    quantity: 0
  }
  const item = cloneDeep(goods_spec)
  delete item.stock
  formState.value.goods.unshift(item)
}
</script>
