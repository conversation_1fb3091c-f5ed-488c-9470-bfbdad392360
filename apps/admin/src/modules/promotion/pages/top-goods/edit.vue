<template>
  <uc-layout-form :is-save="!statusInfo.isEnded" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动海报">
            <uc-upload
              :list="formState.poster ? [formState.poster] : []"
              :max-length="1"
              upload-text=" "
              :disabled="statusInfo.isEnded"
              @update:list="data => (formState.poster = data[0])"
            />
          </a-form-item>
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              placeholder="活动开始时间"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="statusInfo.isEnded"
              :disabled-date="disabledEndTime"
              placeholder="活动结束时间"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="statusInfo.isEnded"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value="formState.tag"
              maxlength="10"
              placeholder="请输入活动标签，不超过10字"
              :disabled="statusInfo.isEnded"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则" class="h-fill">
          <a-form-item label="中签上限" class="required">
            <a-input-number
              v-model:value="formState.draw_limit_value"
              :min="1"
              precision="0"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              placeholder="请输入本场活动中签次数上限"
            />
          </a-form-item>
          <a-form-item label="签码上限" class="required">
            <a-input-number
              v-model:value="formState.code_limit_value"
              :min="0"
              precision="0"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              placeholder="请输入个人签码上限"
            />
          </a-form-item>
          <a-form-item label="助力资格" class="required">
            <div class="flex flex-sb" style="width: 500px">
              <a-select
                v-model:value="formState.user_type"
                placeholder="请选择助力资格"
                class="m-r-10 w-150"
                :options="helpQualified.options()"
                :disabled="statusInfo.isNormal || statusInfo.isEnded"
                @change="onChangeUserType"
              />
              <a-input-number
                v-model:value="formState.help_limit_value"
                class="flex flex-1"
                :min="1"
                placeholder="请输入助力次数"
                :disabled="formState.user_type === helpQualified.new_user || !statusInfo.isNostart"
              />
            </div>
          </a-form-item>
          <a-form-item label="开奖时间" class="required">
            <a-date-picker
              v-model:value="formState.publish_time"
              style="width: 500px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择抽签结果公布时间"
              :disabled="statusInfo.isEnded"
              :disabled-date="$event => disabledEndTime($event, formState.end_time, true)"
              :disabled-time="$event => disabledEndTime($event, formState.end_time, true)"
            />
          </a-form-item>
          <a-form-item label="购买期限" class="required">
            <a-input-number
              v-model:value="formState.buy_limit_day"
              :min="1"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              placeholder="请输入中奖结果公布后截止天数"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <a-card title="抽签商品">
      <template #extra>
        <a-button type="primary" :disabled="statusInfo.isNormal || statusInfo.isEnded" @click="onAddGoods">
          添加商品
        </a-button>
      </template>
      <a-table :data-source="formState.goods" row-key="sku" :pagination="false">
        <a-table-column title="商品名称/规格名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.spec.photo_url"
              :title="`${record.goods.title}`"
              :subtit="`${record.sku}：${record.spec.attrs[0].value}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="销售价" width="150px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.spec.price) }}
          </template>
        </a-table-column>
        <a-table-column title="中签价" width="150px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.activity_price) }}
          </template>
        </a-table-column>
        <a-table-column title="限量" width="120px">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.setting_stock, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="70px">
          <template #default="{ index }">
            <a-button
              type="link"
              class="danger"
              :disabled="statusInfo.isNormal || statusInfo.isEnded"
              @click="onRemoveGoods(index)"
            >
              移除
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-card>
    <uc-rich-text v-model="formState.desc" :disabled="statusInfo.isEnded" />
  </uc-layout-form>
  <a-modal title="新增抽签商品" ok-text="保存" :visible="modalVisible" @cancel="modalVisible = false" @ok="onModalSubmit">
    <a-form>
      <a-form-item label="商品SKU" class="required">
        <input-sku v-if="modalVisible" v-model="modalFormState.sku" />
      </a-form-item>
      <a-form-item label="中签价格" class="required">
        <a-input-number v-model:value.trim="modalFormState.activity_price" placeholder="请输入换购价格，不可高于销售价格" />
      </a-form-item>
      <a-form-item label="活动限量" class="required">
        <a-input-number v-model:value="modalFormState.setting_stock" placeholder="请输入活动限量" :min="1" precision="0" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { generateRandom } from '@/utils/functions'
import { validateColor } from '@/utils/validates'
import formatters from '@/utils/formatters'
import { topGoodsApi, goodsSpecsApi } from '../../api'
import { helpList, helpQualified } from '../../enums'
import { useTransformBargain } from '../../useTransform'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'
import InputSku from '../../components/input-sku'

const { statusFilter, TIME_STATUS_NOSTART } = useTimeStatus()
const statusInfo = ref({
  isNostart: true
  // isNostart
  // isNormal
  // isEnded
})

const router = useRouter()
const { id } = useRoute().params

const tableLoading = ref(true)

const { pageConfig, modalConfig, shareConfig } = useTransformBargain()

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  poster: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,

  draw_limit_value: undefined,
  code_limit_value: undefined,
  user_type: helpQualified.all,
  help_limit_value: '',
  publish_time: undefined,
  buy_limit_day: undefined,

  goods: [],
  desc: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  topGoodsApi
    .get(id, { relations: ['goods'] })
    .then(res => {
      statusInfo.value = statusFilter(res.status)
      setFormState(res)
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const validatorGoods = (_, value) => {
  if (!value.length) return Promise.reject('请添加活动商品')
  return Promise.resolve(true)
}

const validatorPage = (_, value) => {
  if (!validateColor(value.color.subject)) {
    return Promise.reject('请输入正确主题色')
  } else if (!validateColor(value.color.sub)) {
    return Promise.reject('请输入正确辅助色')
  } else if (!validateColor(value.color.bg)) {
    return Promise.reject('请输入正确背景色')
  } else if (!validateColor(value.color.title)) {
    return Promise.reject('请输入正确标题色')
  }
  const pageItem = pageConfig.value.concat(modalConfig.value).find(item => !item.value.length)
  if (pageItem) return Promise.reject(`请上传${pageItem.label}图`)
  return Promise.resolve(true)
}

const validatorShare = (_, value) => {
  if (!value.share_text) return Promise.reject('请输入分享文案')

  const shareItem = shareConfig.value.find(item => !item.value.length)
  if (shareItem) return Promise.reject(`请上传${shareItem.label}图`)

  return Promise.resolve(true)
}

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },

  draw_limit_value: { required: true, message: '请输入中签上限' },
  code_limit_value: { required: true, message: '请输入签码上限' },
  user_type: { required: true, message: '请选择助力资格' },
  help_limit_value: { required: true, message: '请输入助力次数' },
  // help_limit_value: {
  //   validator(_, value) {
  //     if (!value) return Promise.reject('请输入助力次数')
  //     return Promise.resolve()
  //   }
  // },
  publish_time: { required: true, message: '请选择开奖时间' },
  buy_limit_day: { required: true, message: '请输入购买期限' },
  goods: { validator: validatorGoods },
  desc: { required: true, message: '请输入活动说明' }
})

const onChangeUserType = () => {
  if (formState.value.user_type === helpQualified.new_user) {
    formState.value.help_limit_value = 1
  } else {
    formState.value.help_limit_value = undefined
  }
}

const modalVisible = ref(false)
const onAddGoods = () => {
  modalResetFormState()
  modalVisible.value = true
}
const onRemoveGoods = index => {
  formState.value.goods.splice(index, 1)
}

const {
  formState: modalFormState,
  resetFormState: modalResetFormState,
  setFormState: modalSetFormState,
  setFormRules: modalSetFormRules,
  validateForm: modalValidateForm
} = useFormState({
  sku: undefined,
  activity_price: undefined,
  setting_stock: undefined
})

const validatorGoodsSku = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入商品SKU')
  } else if (formState.value.goods.some(item => item.sku?.toLowerCase() === value?.toLowerCase())) {
    return Promise.reject('该商品SKU已存在')
  }
  return Promise.resolve(true)
}

const validatorCurBuy = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入当前价买')
  } else if (value > modalFormState.value.min_price_user_count) {
    return Promise.reject('当前价买不能高于助力人数')
  }
  return Promise.resolve(true)
}

modalSetFormRules({
  sku: { validator: validatorGoodsSku },
  activity_price: { required: true, message: '请输入中签价格' },
  setting_stock: { required: true, message: '请输入活动限量' }
})
const skuOptions = ref([])
const handleSearch = sku => {
  goodsSpecsApi
    .paginator({
      filters: useTransformQuery({ sku }, { sku: 'like' }),
      relation_filters: { goods: { on_sale: 1 } },
      limit: 20,
      offset: 1
    })
    .then(data => {
      skuOptions.value = useTransformOptions(data.items, 'sku', 'sku')
    })
}

let modalLocked = false

const onModalSubmit = async () => {
  if (!(await modalValidateForm()) || modalLocked) return
  modalLocked = true

  const item = cloneDeep(modalFormState.value)
  goodsSpecsApi
    .list({ filters: { sku: item.sku }, relations: ['goods', 'goods_spec', 'stock'] })
    .then(data => {
      const goods_spec = data[0]
      if (!goods_spec) return message.error('商品SKU不存在')
      if (item.quantity > goods_spec.stock.stock) return message.error('商库存不足品SKU不存在')

      const { goods_id, price } = goods_spec
      const activity_price = formatters.priceToInteger(item.activity_price)
      if (price < activity_price) return message.error('中签价格需小于销售价')
      Object.assign(item, {
        spec: goods_spec,
        goods: goods_spec.goods,
        goods_id,
        activity_price
      })
      formState.value.goods.unshift(item)
      modalVisible.value = false
    })
    .finally(() => {
      modalLocked = false
    })
}

const handleSubmit = async () => {
  let params = formState.value
  if (!(await validateForm())) return
  id ? await topGoodsApi.replace(id, params) : await topGoodsApi.create(params)
  message.success('操作成功')
  router.back()
}
</script>
