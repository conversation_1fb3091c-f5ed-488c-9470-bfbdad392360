<template>
  <uc-layout-list title="尖货抽签">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item name="productType">
        <a-select v-model:value="formState.status" placeholder="活动状态" allow-clear :options="statusList" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="toAdd">
        新增尖货抽签
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称/时间" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :title="record.title"
              :subtit="`${record.start_time} ~ ${record.end_time}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="UV/PV" width="150px" ellipsis align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.uv, false, false) }} /
            {{ $formatters.thousandSeparator(record.pv, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="抽签人/数" width="150px" ellipsis align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.draw_user_count, false, false) }} /
            {{ $formatters.thousandSeparator(record.draw_count, false, false) }}
          </template>
        </a-table-column>
        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" @click="copyLink(linkPath + record.id)">
              链接
            </a-button>
            <a-button v-if="record.status != TIME_STATUS_ENDED" type="link" @click="toEdit(record)">
              编辑
            </a-button>
            <a-button v-if="record.status == TIME_STATUS_ENDED" type="link" @click="toEdit(record)">
              查看
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="!record.can_delete"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter, useRoute } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { topGoodsApi } from '../../api'

const { activity_id } = useRoute().query
const linkPath = Object.freeze(`/draw-lot/pages/standard/list/index?id=`)

const { statusList, statusFilter, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  topGoodsApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  status: undefined,
  id: activity_id
})

onRestFormState(() => setPage())

const router = useRouter()

const toAdd = () => router.push({ name: 'top-goods-add' })
const toEdit = ({ id }) => router.push({ name: 'top-goods-edit', params: { id } })

const handleDelete = async ({ id }) => {
  await topGoodsApi.delete(id)
  message.success('删除完成')
  setPage()
}
</script>
