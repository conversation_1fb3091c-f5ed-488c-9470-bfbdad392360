<template>
  <uc-layout-list title="特价秒杀">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="活动状态" :options="statusList" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd">
        新增特价秒杀
      </a-button>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="活动名称/时间" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.title }}
            </div>
            <div class="text-ellipsis">
              {{ record.start_time }} ~ {{ record.end_time }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="UV/PV" width="120px" align="right">
          <template #default="{ record }">
            {{ record.uv }} / {{ record.pv }}
          </template>
        </a-table-column>
        <a-table-column title="下单人数/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.order_statistics.user }} / {{ record.order_statistics.number }}/
            {{ $formatters.thousandSeparator(record.order_statistics.amount) }}
          </template>
        </a-table-column>

        <a-table-column title="支付人数/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.pay_statistics.user }} / {{ record.pay_statistics.number }} /
            {{ $formatters.thousandSeparator(record.pay_statistics.amount) }}
          </template>
        </a-table-column>

        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="140px">
          <template #default="{ record }">
            <a-button type="link" class="link" @click="copyLink(linkPath + record.id)">
              链接
            </a-button>
            <a-button v-if="record.status !== TIME_STATUS_ENDED" type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-button v-if="record.status === TIME_STATUS_ENDED" type="link" @click="onLook(record)">
              查看
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { seckillApi } from '../../api'
import { useTimeStatus } from '@/composables/useTimeStatus'

const { activity_id } = useRoute().query
const linkPath = Object.freeze('/seckill/pages/standard/list/index?id=')

const { statusList, statusFilter, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus() // 时间状态
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  seckillApi.paginator({
    filters: useTransformQuery(formState.value, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  status: undefined,
  id: activity_id
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await seckillApi.delete(id)
  message.success('操作成功')
  setPage()
}

const router = useRouter()

const onAdd = () => router.push({ name: 'seckill-add' })

const onEdit = ({ id }) => router.push({ name: 'seckill-edit', params: { id, readonly: 0 } })

const onLook = ({ id }) => router.push({ name: 'seckill-edit', params: { id, readonly: 1 } })
</script>
