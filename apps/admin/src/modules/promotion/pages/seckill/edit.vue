<template>
  <!-- 特价秒杀编辑 -->
  <uc-layout-form :is-save="!isReadonly" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动海报">
            <uc-upload v-model:list="formState.poster" upload-text=" " :max-length="1" :disabled="isRead" />
          </a-form-item>

          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              class="w-240"
              :disabled="isReadonly || isStarted"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
              class="w-240"
              :disabled="isReadonly"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value.trim="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isReadonly"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              placeholder="请输入活动标签，不超过10字"
              :maxlength="10"
              :disabled="isReadonly"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则" class="h-fill">
          <a-form-item label="本次限购" class="required">
            <a-input-number
              v-model:value="formState.limit_value"
              placeholder="请输入本场秒杀限购数量"
              :min="1"
              :formatter="$formatters.number"
              :disabled="isStarted || isReadonly"
            />
          </a-form-item>
          <a-form-item label="减免运费" class="required">
            <a-space :size="10">
              <a-select
                v-model:value="formState.is_free_freight"
                :disabled="isStarted || isReadonly"
                :options="feeList.options()"
                class="w-500"
              />
            </a-space>
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <a-card title="秒杀商品">
      <template #extra>
        <a-button type="primary" :disabled="isStarted || isReadonly" @click="onAddGoods"> 添加商品 </a-button>
      </template>
      <a-table :data-source="formState.goods" row-key="id" :pagination="false">
        <a-table-column title="商品名称/规格名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_url"
              :title="record.title"
              :subtit="`${record.sku}：${record.attrs.reduce(
                (prev, item, i) => prev + (i ? '/' : '') + item.value,
                ''
              )}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="销售价" width="150px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.price) }}
          </template>
        </a-table-column>
        <a-table-column title="秒杀价" width="150px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.activity_price) }}
          </template>
        </a-table-column>
        <a-table-column title="限量" width="150px">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.setting_stock, false, false) }}
          </template>
        </a-table-column>

        <a-table-column title="操作" width="70px">
          <template #default="{ index }">
            <a-button type="link" class="danger" :disabled="isStarted || isReadonly" @click="onRemoveGoods(index)">
              移除
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-card>
    <uc-rich-text v-model="formState.desc" :disabled="isReadonly" />
  </uc-layout-form>
  <!-- 添加商品模态框 -->
  <a-modal title="添加商品" ok-text="保存" :visible="modalVisible" @cancel="modalVisible = false" @ok="onModalSubmit">
    <a-form>
      <a-form-item label="商品SKU" class="required">
        <input-sku v-if="modalVisible" v-model="modalFormState.sku" />
      </a-form-item>
      <a-form-item label="秒杀价格" class="required">
        <a-input-number v-model:value.trim="modalFormState.activity_price" placeholder="请输入秒杀价格" />
      </a-form-item>
      <a-form-item label="活动数量" class="required">
        <a-input-number
          v-model:value="modalFormState.setting_stock"
          placeholder="请输入活动数量"
          :min="1"
          :formatter="$formatters.number"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { priceToInt } from '@/composables/useTransformFormat'
import { generateRandom } from '@/utils/functions'
import { seckillApi, goodsSpecsApi } from '../../api'
import { feeList } from '../../enums'
import { useTransformSeckill } from '../../useTransform'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'
import InputSku from '../../components/input-sku'

const { statusList, TIME_STATUS_NOSTART } = useTimeStatus()

const tableLoading = ref(true)

const { transformRequest } = useTransformSeckill()
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  poster: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  limit_value: '',
  is_free_freight: feeList.fee,
  goods: [],
  desc: ''
})

const router = useRouter()
const { id, readonly } = useRoute().params
const isReadonly = ref(Boolean(+readonly))
const isStarted = ref(false)

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  seckillApi
    .get(id, { relations: ['rule', 'goods'] })
    .then(res => {
      isStarted.value = res.status !== TIME_STATUS_NOSTART
      res.poster = [res.poster]
      res.goods.forEach(item => {
        item['title'] = item.goods.title
        item['photo_url'] = item.spec.photo_url
        item['attrs'] = item.spec.attrs
        item['price'] = item.spec.price
      })
      setFormState(res)
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const skuOptions = ref([])
const handleSearch = sku => {
  goodsSpecsApi
    .paginator({
      filters: useTransformQuery({ sku }, { sku: 'like' }),
      relation_filters: { goods: { on_sale: 1 } },
      limit: 20,
      offset: 1
    })
    .then(data => {
      skuOptions.value = useTransformOptions(data.items, 'sku', 'sku')
    })
}

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)
const validatorRule = (rule, value) => {
  if (!value.limit_value) {
    return Promise.reject('请输入本场限购')
  }
  return Promise.resolve(true)
}
const validatorGoods = (rule, value) => {
  if (!value.length) return Promise.reject('请添加秒杀商品')
  return Promise.resolve(true)
}

setFormRules({
  start_time: { required: true, message: '请选择开始时间' },
  end_time: { required: true, message: '请选择结束时间' },
  title: { required: true, message: '请输入活动名称' },
  rule: { validator: validatorRule },
  goods: { validator: validatorGoods },
  desc: { required: true, message: '请输入活动说明' }
})

const modalVisible = ref(false)
const onAddGoods = () => {
  modalResetFormState()
  modalVisible.value = true
}
const onRemoveGoods = index => {
  formState.value.goods.splice(index, 1)
}

const {
  formState: modalFormState,
  resetFormState: modalResetFormState,
  setFormState: modalSetFormState,
  setFormRules: modalSetFormRules,
  validateForm: modalValidateForm
} = useFormState({
  sku: undefined,
  activity_price: '',
  setting_stock: ''
})

const validatorGoodsSku = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入商品SKU')
  } else if (formState.value.goods.some(item => item.sku?.toLowerCase() === value?.toLowerCase())) {
    return Promise.reject('该商品SKU已存在')
  }
  return Promise.resolve(true)
}

// 校验秒杀价格
const validatorActivityPrice = (_, value) => {
  if (!value && value != 0) {
    return Promise.reject(`请输入商品秒杀价`)
  }
  let y = String(value).indexOf('.') + 1 //获取小数点的位置
  // 有小数点
  if (y > 0) {
    let count = String(value).length - y //获取小数点后的个数
    if (count > 2) {
      return Promise.reject(`秒杀价只能保留二位小数点`)
    }
  }

  return Promise.resolve()
}

modalSetFormRules({
  sku: { validator: validatorGoodsSku },
  activity_price: { validator: validatorActivityPrice },
  setting_stock: { required: true, message: '请输入活动数量' }
})

let modalLocked = false
const onModalSubmit = async () => {
  if (!(await modalValidateForm()) || modalLocked) return
  modalLocked = true
  const item = cloneDeep(modalFormState.value)
  const priceFields = ['activity_price']
  priceFields.forEach(key => {
    item[key] = priceToInt(item[key])
  })
  item.id = item.id || generateRandom()
  goodsSpecsApi
    .list({ filters: { sku: item.sku }, relations: ['goods', 'goods_spec', 'stock'] })
    .then(data => {
      const goods_spec = data[0]
      if (!goods_spec) {
        message.error('商品SKU不存在')
      } else if (goods_spec.price / 100 < item.activity_price / 100) {
        message.error(`秒杀价格不能超过商品原价:${goods_spec.price / 100}`)
      } else {
        item.goods_id = goods_spec.goods_id
        item.attrs = goods_spec.attrs
        item.price = goods_spec.price
        item.photo_url = goods_spec.photo_url //goods.photo_urls[0]
        item.title = goods_spec.goods.title
        item.sku = goods_spec.sku
        formState.value.goods.unshift(item)
        modalVisible.value = false
      }
    })
    .finally(() => {
      modalLocked = false
    })
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = transformRequest(formState.value)
  params.poster = params.poster[0]
  id ? await seckillApi.replace(id, params) : await seckillApi.create(params)

  message.success('操作成功')
  router.back()
}
</script>
