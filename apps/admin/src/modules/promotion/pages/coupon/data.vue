<template>
  <div class="coupon-data">
    <div class="coupon-data-stats">
      <a-row :gutter="18">
        <a-col :span="6">
          <div class="col-wrap bgc-white">
            <span class="stats-count">{{
              $formatters.thousandSeparator(statsData.user_count || 0, false, false)
            }}</span>
            <span class="stats-label">发放人数</span>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="col-wrap bgc-white">
            <span class="stats-count">{{ $formatters.thousandSeparator(statsData.count || 0, false, false) }}</span>
            <span class="stats-label">发放张数</span>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="col-wrap bgc-white">
            <span class="stats-count">{{ $formatters.thousandSeparator(statsData.used || 0, false, false) }}</span>
            <span class="stats-label">核销张数</span>
          </div>
        </a-col>
        <a-col :span="6">
          <div class="col-wrap bgc-white">
            <span class="stats-count">{{ $formatters.thousandSeparator(statsData.convert_ratio || 0, true, false) }}%</span>
            <span class="stats-label">使用转化率</span>
          </div>
        </a-col>
      </a-row>
    </div>
    <a-card title="发放记录">
      <template #extra>
        <div class="flex">
          <a-input-group compact class="flex m-r-16">
            <a-select v-model:value="conditionKey" style="width: 100px" placeholder="请选择" :options="conditionOptions" />
            <a-input v-model:value.trim="conditionValue" style="width: 300px" placeholder="请输入关键字" @blur="handleSearch" />
          </a-input-group>
          <a-select
            v-model:value="formState.status"
            allow-clear
            style="width: 100px"
            class="select-width"
            placeholder="持券状态"
            :options="couponStatus.options()"
            @change="handleSearch"
          />
        </div>
      </template>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="优惠券码" data-index="sn" ellipsis />
        <a-table-column title="用户昵称" ellipsis>
          <template #default="{ record }">
            {{ record?.user?.nickname }}
          </template>
        </a-table-column>
        <a-table-column title="手机号码" ellipsis>
          <template #default="{ record }">
            {{ $formatters.numberEncryption(record?.user?.phone_number) }}
          </template>
        </a-table-column>
        <a-table-column title="发放时间" data-index="created_at" ellipsis />
        <a-table-column title="持券状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="couponStatus.filter(record.status).colorType" :text="couponStatus.filter(record.status).label" />
          </template>
        </a-table-column>
      </a-table>
    </a-card>
  </div>
</template>
<script setup>
import { nextTick } from 'vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useRoute } from 'vue-router'
import { couponStatsApi } from '../../api'
import { couponStatus } from '../../enums'
import { debounce } from 'lodash'

const { id } = useRoute().params

const conditionBasic = Object.freeze({ nickname: undefined, phone_number: undefined })
const conditionOptions = [
  { label: '用户昵称', value: 'nickname' },
  { label: '手机号码', value: 'phone_number' }
]
const conditionKey = ref(conditionOptions[0].value)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))

const handleSearch = debounce(() => {
  // Object.assign(formState.value, conditionBasic, {
  //   [conditionKey.value]: conditionValue.value
  // })
  setPage()
}, 400)

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  couponStatsApi.paginator({
    filters: useTransformQuery(
      {
        coupon_id: id,
        ...formState.value,
      },
      {
        coupon_id: '=',
        status: '='
      }
    ),
    relation_filters: { user: useTransformQuery({ [conditionKey.value]: conditionValue.value || undefined }, { nickname: 'like' }) },
    offset,
    limit,
    relations: ['user']
  })
)

const statsData = ref({})

const loadDataStats = () => {
  couponStatsApi.get(id, {}).then((res) => {
    statsData.value = res
  })
}

nextTick(() => {
  useLoadingMessage(loadDataStats(), {
    loadingText: '正在加载数据…'
  })
})

const { formState, onRestFormState, resetFormState } = useFormState({
  type: undefined
})

onRestFormState(() => setPage())
</script>
<style scoped lang="less">
.coupon-data {
  &-stats {
    border: 1px solid #f0f0f0;
    margin-bottom: 20px;
    .col-wrap {
      display: flex;
      flex-direction: column;
      padding: 16px 0;
      span {
        text-align: center;
      }
      .stats-count {
        font-size: 20px;
        color: #333;
      }
      .stats-label {
        font-size: 14px;
        color: #999;
      }
    }
  }
  .select-width {
    :deep(.ant-select-selector) {
      width: 100px;
    }
  }
}
</style>
