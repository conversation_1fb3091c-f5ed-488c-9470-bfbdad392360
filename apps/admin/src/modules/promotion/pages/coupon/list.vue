<template>
  <uc-layout-list title="优惠券">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入优惠券名称" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.type" placeholder="优惠卷类型" :options="couponType.options()" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="toAdd">
        新增优惠卷
      </a-button>
    </template>
    <template #list>
      <a-table :data-source="data.items" row-key="id" :loading="loading" :pagination="stdPagination(data)" @change="setPage">
        <a-table-column title="优惠券编码/名称">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.code }}/{{ record.title }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="发放" data-index="send" width="120px" align="right" />
        <a-table-column title="核销" data-index="used" width="120px" align="right" />
        <a-table-column title="过期" data-index="expired" width="120px" align="right" />
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" @click="toData(record)">
              数据
            </a-button>
            <a-button type="link" @click="toEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter } from "vue-router"
import { message } from "ant-design-vue";
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { couponApi } from '../../api'
import { couponType } from '../../enums'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit}) =>
    couponApi.paginator({
        filters: useTransformQuery(formState.value,{
            title: 'like',
            type: '=',
        }),
        offset,
        limit,
        relations: ['spec']
    })
)

const { formState, onRestFormState, resetFormState } = useFormState({
    title: undefined,
    type: undefined,
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
    await couponApi.delete(id)
    message.success('删除完成')
    setPage()
}

const router = useRouter()
const toAdd = () => router.push({name: 'promotion-coupon-add'})
const toEdit = ({ id }) => router.push({name: 'promotion-coupon-edit',params: { id }})
const toData = ({ id }) => router.push({name: 'promotion-coupon-data',params: { id }})

</script>