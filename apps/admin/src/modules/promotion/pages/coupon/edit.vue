<template>
  <uc-layout-form @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="优惠券图片" name="title">
            <uc-upload
              v-model:list="formState.photo_url"
              :max-length="1"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="优惠类型" class="required">
            <a-select
              v-model:value="formState.type"
              placeholder="请选择"
              :options="couponType.options()"
              :disabled="isEdit"
              @change="onChangeCouponType"
            />
          </a-form-item>
          <a-form-item label="优惠名称" class="required">
            <a-input v-model:value="formState.title" placeholder="请输入请输入优惠券名称，不超过20字" maxlength="20" />
          </a-form-item>
          <a-form-item label="使用说明" class="required">
            <a-textarea
              v-model:value="formState.desc"
              placeholder="请输入优惠券使用说明，不超过200字"
              maxlength="200"
              :auto-size="{ minRows: 4 }"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="使用规则" class="h-fill">
          <a-form-item v-if="formState.type != couponType.gift" label="优惠额度" class="required">
            <a-input-number
              v-if="formState.type == couponType.cash"
              v-model:value="formState.rule.value"
              placeholder="请输入优惠金额"
              :disabled="isEdit"
            />
            <a-input-number
              v-if="formState.type == couponType.discount"
              v-model:value="formState.rule.value"
              placeholder="请输入优惠折扣，0.1～9.9之间的小数"
              :min="0.1"
              :max="9.9"
              :precision="1"
              :disabled="isEdit"
            />
            <a-input-number
              v-if="formState.type == couponType.exchange"
              v-model:value="formState.rule.value"
              placeholder="请输入可兑换数量"
              :min="1"
              :disabled="isEdit"
            />
            <a-input-number
              v-if="formState.type == couponType.freight"
              v-model:value="formState.rule.value"
              placeholder="请输入优惠金额，0表示免邮"
              :min="0"
              :disabled="isEdit"
            />
          </a-form-item>
          <a-form-item label="使用门槛" class="required">
            <a-input-group compact>
              <a-select
                v-model:value="formState.rule.condition_type"
                placeholder="请选择"
                :options="couponThreshold.options()"
                class="w-120 m-r-10"
                :disabled="isEdit"
                @change="onChangeCouponThreshold"
              />
              <a-input
                v-if="formState.rule.condition_type == couponThreshold.amount"
                v-model:value.trim="formState.rule.condition"
                class="w-370"
                placeholder="请输入商品总金额"
                :disabled="isEdit"
              />
              <a-input-number
                v-else
                v-model:value.trim="formState.rule.condition"
                class="w-370"
                placeholder="请输入商品总数量"
                :min="1"
                :disabled="isEdit"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="券有效期" class="required flex">
            <a-select
              v-model:value="formState.valid_date_type"
              style="width: 120px"
              placeholder="请选择"
              :options="couponTerm.options()"
              class="m-r-10"
              :disabled="isEdit"
              @change="onChangeCouponTerm"
            />
            <span v-if="formState.valid_date_type == couponTerm.fixed">
              <a-date-picker
                v-model:value="formState.start_time"
                :disabled="isEdit"
                class="w-175 min-w-175"
                show-time
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择开始时间"
              />
              <span class="data_time-gap">~</span>
              <a-date-picker
                v-model:value="formState.end_time"
                class="w-175 min-w-175"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                show-time
                placeholder="请选择结束时间"
              />
            </span>
            <span v-else>
              <a-input-number
                v-model:value="formState.receive_date"
                class="w-370"
                :min="0"
                :disabled="isEdit"
                placeholder="请输入有效天数，0表示不限制"
              />
            </span>
          </a-form-item>
          <a-form-item label="适用商品" class="required">
            <a-select
              v-model:value="formState.range.type"
              placeholder="请选择适用范围"
              :options="productRange.options()"
              :disabled="isEdit || couponType.disabled(formState.type)"
              @change="initCouponRangeProduct"
            />
          </a-form-item>
          <a-form-item
            v-if="productRange.filterCategories(formState.range.type)"
            :label="productRange.filter(formState.range.type)"
            class="required"
          >
            <a-tree-select
              v-model:value="formState.range.config"
              placeholder="请选择商品分类"
              :tree-data="categories"
              :replace-fields="{ key: 'id', value: 'id' }"
              multiple
              allow-clear
            />
          </a-form-item>
          <a-form-item
            v-if="productRange.filterGroup(formState.range.type)"
            :label="productRange.filter(formState.range.type)"
            class="required"
          >
            <a-tree-select
              v-model:value="formState.range.config"
              placeholder="请选择商品分组"
              :tree-data="groups"
              :replace-fields="{ key: 'id', value: 'id' }"
              multiple
              allow-clear
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <a-card v-if="formState.type == couponType.gift" title="赠品">
      <template #extra>
        <a-button type="primary" :disabled="isEdit" @click="onAddGoods"> 添加赠品 </a-button>
      </template>
      <a-table :data-source="formState.goods" row-key="sku" :pagination="false">
        <a-table-column title="商品名称/规格名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record?.photo_url"
              :title="`${record?.goods?.title || record?.goods_title}`"
              :subtit="`${record?.sku}：${record?.attrs[0].value || record?.attrs}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="销售金额" width="150px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record?.price) }}
          </template>
        </a-table-column>
        <a-table-column title="赠送数量" width="150px">
          <template #default="{ record }">
            <a-input-number v-model:value.trim="record.quantity" :min="0" :disabled="isEdit" placeholder="请输入赠送数量" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="70px">
          <template #default="{ index }">
            <a-button type="link" class="danger" :disabled="isEdit" @click="onRemoveGoods(index)"> 移除 </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-card>
    <apply-sku-goods
      v-if="productRange.filterSku(formState.range.type)"
      v-model:list="formState.range.config"
      :title="productRange.filter(formState.range.type)"
    />
    <apply-spu-goods
      v-if="productRange.filterSpu(formState.range.type)"
      v-model:list="formState.range.config"
      :title="productRange.filter(formState.range.type)"
    />
    <select-sku-goods v-model:visible="modalVisible" :mode="goodsSelectType.single" @ok="onSkuSelectSubmit" />
  </uc-layout-form>
</template>
<script setup>
import { onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { couponType, couponThreshold, couponTerm, productRange, goodsSelectType } from '../../enums'
import { cloneDeep, pull } from 'lodash'
import { couponApi } from '../../api'
import { useCategories } from '@/modules/goods/useCategory'
import { useModalVisible } from '@/composables/useToggles'
import { useGroups } from '@/modules/goods/useGroup'

const router = useRouter()

const { id } = useRoute().params

const isEdit = ref(false)

const { categories } = useCategories()
const { groups } = useGroups()

const init = () => {
  if (id) {
    isEdit.value = true
    const hideLoading = message.loading('正在加载数据...')
    couponApi
      .get(id, { relations: ['range', 'rule', 'goods'] })
      .then(res => {
        const { type, range, rule } = res
        if(rule.condition_type === couponThreshold.amount || rule.condition_type === couponThreshold.checkoutAmount) {
          rule.condition /= 100
        }
        // 优惠券类型
        switch (type) {
          case couponType.cash:
          case couponType.freight:
            res.rule.value = res.rule.value / 100
            break
          case couponType.discount:
            res.rule.value = res.rule.value / 10
            break
        }

        res.photo_url = res.photo_url ? [res.photo_url] : []
        // 商品数据
        formState.value.range.type = res.range.type
        formState.value.range.config = res.range.config
        // 商品范围
        setFormState(res)
      })
      .finally(hideLoading)
  }
}

onMounted(init)

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  type: couponType.cash,
  desc: undefined,

  valid_date_type: couponTerm.receive,
  start_time: undefined,
  end_time: undefined,
  receive_date: undefined,
  goods: [],
  range: {
    type: productRange.all,
    biz_id: undefined, // 优惠券id
    biz_type: 'coupon', // coupon
    config: [] // 范围商品
  },
  rule: {
    biz_id: undefined, // 优惠券id
    biz_type: 'coupon',
    value: undefined, // 优惠额度 折扣券需要单独处理
    condition_type: couponThreshold.amount, // 优惠条件类型
    condition: undefined // 优惠条件
  },
  photo_url: []
})

setFormRules({
  title: { required: true, message: '请输入优惠券名称' },
  type: { required: true, message: '请选择优惠券类型' },
  desc: { required: true, message: '请输入优惠券使用说明' },
  rule: {
    validator(_, value) {
      const { value: quota, condition_type, condition } = value
      // 使用规则
      if (quota === undefined && formState.type != couponType.gift) return Promise.reject('请输入优惠额度')
      if (!condition_type) return Promise.reject('请选择使用门槛')
      if (condition === undefined) {
        if (condition_type == couponThreshold.amount) return Promise.reject('请输入商品总金额')
        if (condition_type == couponThreshold.number) return Promise.reject('请输入商品总件数')
      }

      // 使用规则-券有效期
      const { valid_date_type, receive_date, start_time, end_time } = formState.value

      if (!valid_date_type) return Promise.reject('请选择券有效期')
      if (valid_date_type) {
        if (valid_date_type == couponTerm.fixed && !start_time && !end_time)
          return Promise.reject('请选择券有效期时间范围')
        if (valid_date_type == couponTerm.receive && !receive_date && receive_date !== 0)
          return Promise.reject('请输入券有效天数')
      }
      return Promise.resolve()
    }
  },
  range: {
    validator(_, value) {
      const { type, config } = value

      if (productRange.filterAll(type) && !config.length) return Promise.reject('请选择范围商品')

      return Promise.resolve()
    }
  }
})

// 初始化当前优惠使用范围的商品
const initCouponRangeProduct = () => {
  formState.value.range.config = []
}

// 改变使用期限
const onChangeCouponTerm = () => {
  Object.assign(formState.value, {
    start_time: undefined,
    end_time: undefined,
    receive_date: undefined
  })
}

// 改变使用门槛
const onChangeCouponThreshold = () => {
  Object.assign(formState.value.rule, {
    condition: undefined
  })
}

// 改变优惠券类型
const onChangeCouponType = () => {
  // 清空适用商品
  formState.value.range.config = []

  // 修改其他状态默认值
  switch (formState.value.type) {
    case couponType.discount:
    case couponType.freight:
    case couponType.cash:
      formState.value.range.type = productRange.all
      formState.value.rule.condition_type = couponThreshold.amount
      formState.value.valid_date_type = couponTerm.receive
      break
    case couponType.exchange:
      formState.value.range.type = productRange.sku_include
      formState.value.rule.condition_type = couponThreshold.amount
      formState.value.valid_date_type = couponTerm.receive
      break
  }
}

const handleSubmit = async () => {
  // if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  // 优惠券类型
  switch (params.type) {
    case couponType.cash:
    case couponType.freight:
      params.rule.value = params.rule.value * 100
      break
    case couponType.discount:
      params.rule.value = params.rule.value * 10
      break
    case couponType.gift:
      params.coupon_goods = params.goods
      params.rule.value = 0
      delete params.goods
      break
  }
  params.rule.type = params.type
  if(params.rule.condition_type === couponThreshold.amount || params.rule.condition_type === couponThreshold.checkoutAmount) {
    params.rule.condition *= 100
  }
  params.photo_url = params.photo_url[0] ?? null
  if (id) {
    await couponApi.update(id, params)
    message.success('编辑完成')
  } else {
    await couponApi.create(params)
    message.success('创建完成')
  }
  router.back()
}

// 添加赠品
const { modalVisible, setModalVisible } = useModalVisible()
const onAddGoods = () => {
  setModalVisible(true)
}

const onRemoveGoods = index => formState.value.goods.splice(index, 1)

const onSkuSelectSubmit = ([goods]) => {
  if (formState.value.goods.some(item => item.sku?.toLowerCase() === goods?.sku?.toLowerCase())) {
    message.warning('该商品SKU已存在')
    return
  }

  const goods_spec = {
    ...toRaw(goods),
    quantity: 0
  }
  const item = cloneDeep(goods_spec)
  formState.value.goods.unshift(item)
}
</script>
<style scoped lang="less">
.input-addon {
  position: relative;
  padding: 0 11px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s;
}

.data_time-gap {
  width: 20px;
  line-height: 32px;
  text-align: center;
  display: inline-block;
}
</style>
