<template>
  <!-- 砍价编辑 -->
  <uc-layout-form :is-save="!isReadonly" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息" class="h-fill">
          <a-form-item label="活动时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="请选择开始时间"
              class="w-240"
              :disabled="isReadonly || isStarted"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="请选择结束时间"
              class="w-240"
              :disabled="isReadonly"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input
              v-model:value.trim="formState.title"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="isReadonly"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              placeholder="请输入活动标签，不超过10字"
              :maxlength="10"
              :disabled="isReadonly"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="成团人数" class="required">
            <a-input-number
              v-model:value="formState.people_total"
              :min="3"
              placeholder="请输入成团人数（拼团成功需购买的人数，需大于2人）"
              :disabled="isReadonly || isStarted"
            />
          </a-form-item>
          <a-form-item label="成团时间" class="required">
            <a-input-number
              v-model:value="formState.time"
              :min="0"
              placeholder="请输入成团时间（用户需要在限定时间内成团，否则拼团失败，单位小时）"
              :disabled="isReadonly || isStarted"
            />
          </a-form-item>
          <a-form-item label="开团次数" class="required">
            <a-input-number
              v-model:value="formState.group_number"
              :min="0"
              placeholder="请输入开团次数（0表示不限制，已成功拼团计算次数）"
              :disabled="isReadonly || isStarted"
            />
          </a-form-item>
          <a-form-item label="参团次数" class="required">
            <a-input-number
              v-model:value="formState.join_number"
              :min="0"
              placeholder="请输入参团次数（0表示不限制，已成功拼团计算次数）"
              :disabled="isReadonly || isStarted"
            />
          </a-form-item>
          <a-form-item label="参团人数" class="required">
            <a-input-number
              v-model:value="formState.group_people"
              :min="0"
              placeholder="请输入参团人数（前端显示在初始参团人数的基础上累加）"
              :disabled="isReadonly"
            />
          </a-form-item>
          <a-form-item label="模拟成团">
            <a-input-number
              v-model:value="formState.simulation"
              :min="0"
              placeholder="请输入模拟成团人数"
              :disabled="isReadonly || isStarted"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <a-card title="拼团商品">
        <template #extra>
          <a-button type="primary" :disabled="isReadonly || isStarted" @click="onAddGoods"> 添加商品 </a-button>
        </template>
        <a-table :data-source="formState.goods" row-key="sku" :pagination="false">
          <a-table-column title="商品名称/规格名称" ellipsis>
            <template #default="{ record }">
              <uc-img-text
                :url="record.photo_url"
                :title="record.title"
                :subtit="`${record.sku}：${record.attrs.reduce(
                  (prev, item, i) => prev + (i ? '/' : '') + item.value,
                  ''
                )}`"
              />
            </template>
          </a-table-column>
          <a-table-column title="商品库存" width="120px">
            <template #default="{ record }">
              {{ record.stock }}
            </template>
          </a-table-column>
          <a-table-column title="商品价格" width="120px" align="right">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.price) }}
            </template>
          </a-table-column>
          <a-table-column title="活动数量" width="140px">
            <template #default="{ record }">
              <a-input-number v-model:value="record.setting_stock" :min="0" :disabled="isReadonly || isStarted" />
            </template>
          </a-table-column>
          <a-table-column title="拼团价格" width="140px" align="right">
            <template #default="{ record }">
              <a-input-number
                v-model:value.trim="record.group_price"
                :min="0"
                :max="Number(record.price / 100)"
                :disabled="isReadonly || isStarted"
                @blur="changeGroupPrice(record)"
              />
            </template>
          </a-table-column>
          <a-table-column title="操作" width="110px">
            <template #default="{ index }">
              <a-button type="link" class="danger" :disabled="isReadonly || isStarted" @click="onRemoveGoods(index)">
                移除
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </a-card>
      <uc-col>
        <a-card title="分享设置" class="h-fill">
          <a-form-item label="分享海报" class="required">
            <a-space :size="10">
              <uc-upload
                v-for="(item, index) in shareConfig"
                :key="index"
                v-model:list="item.value"
                upload-text=" "
                show-label
                :label-text="item.label"
                :max-length="1"
                :disabled="isReadonly"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="分享文案" class="required">
            <a-input
              v-model:value="formState.share_setting.share_text"
              placeholder="请输入分享文案，不超过20字"
              :maxlength="20"
              :disabled="isReadonly"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.desc" :disabled="isReadonly" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
  <select-spu-goods v-model:visible="modalVisible" :mode="goodsSelectType.single" @ok="onModalSubmit" />
</template>
<script setup>
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { priceToInt } from '@/composables/useTransformFormat'
import { generateRandom } from '@/utils/functions'
import { colorVerify } from '@/utils/index'
import formatters from '@/utils/formatters'

import { groupApi, bargainGoodsApi, goodsApi, goodsSpecsApi } from '../../api'
import { helpList, skinMode, feeList, productRange, goodsSelectType } from '../../enums'
import { useTransformBargain } from '../../useTransform'
import assets from '../../assets.config'

const { statusList, TIME_STATUS_NOSTART, TIME_STATUS_ENDED } = useTimeStatus()

const tableLoading = ref(true)
const imgRef = ref(null)

const { pageConfig, modalConfig, shareConfig, transformToShow, transformToRequest } = useTransformBargain()
const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  //
  people_total: undefined,
  time: undefined,
  group_number: undefined,
  join_number: undefined,
  group_people: undefined,
  simulation: undefined,
  //
  goods: [],
  page_setting: {
    skin_mode: 'default',
    color: {
      subject: '',
      sub: '',
      bg: '',
      title: ''
    }
  },
  share_setting: {
    share_text: ''
  },
  desc: ''
})
const router = useRouter()
const { id } = useRoute().params
const isReadonly = ref(false)
const isStarted = ref(false)

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  groupApi
    .get(id, { relations: ['goods'] })
    .then(res => {
      isStarted.value = res.status !== TIME_STATUS_NOSTART
      isReadonly.value = res.status === TIME_STATUS_ENDED
      res = transformToShow(res)
      res.goods = res.goods.map(item => {
        const { attrs, photo_url, price } = item.spec
        const { title } = item.goods
        return {
          ...item,
          group_price: item.group_price / 100,
          // 仅作页面展示用字段
          photo_url,
          title,
          attrs,
          price,
        }
      })
      setFormState(res)
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

const validatorGoods = (rule, value) => {
  if (!value.length) return Promise.reject('请添加活动商品')
  return Promise.resolve(true)
}
const validatorPage = (rule, value) => {
  if (!colorVerify(value.color.subject)) {
    return Promise.reject('请输入正确主题色')
  } else if (!colorVerify(value.color.sub)) {
    return Promise.reject('请输入正确辅助色')
  } else if (!colorVerify(value.color.bg)) {
    return Promise.reject('请输入正确背景色')
  } else if (!colorVerify(value.color.title)) {
    return Promise.reject('请输入正确标题色')
  }
  const pageItem = pageConfig.value.concat(modalConfig.value).find(item => !item.value.length)
  if (pageItem) return Promise.reject(`请上传${pageItem.label}图`)
  return Promise.resolve(true)
}
const validatorShare = (rule, value) => {
  if (!value.share_text) return Promise.reject('请输入分享文案')

  const shareItem = shareConfig.value.find(item => !item.value.length)
  if (shareItem) return Promise.reject(`请上传${shareItem.label}图`)

  return Promise.resolve(true)
}

setFormRules({
  start_time: { required: true, message: '请选择开始时间' },
  end_time: { required: true, message: '请选择结束时间' },
  title: { required: true, message: '请输入活动名称' },
  people_total: { required: true, message: '请输入成团人数' },
  time: { required: true, message: '请输入成团时间' },
  group_number: { required: true, message: '请输入开团次数' },
  join_number: { required: true, message: '请输入参团次数' },
  group_people: { required: true, message: '请输入参团人数' },
  goods: { validator: validatorGoods },
  share_setting: { validator: validatorShare },
  desc: { required: true, message: '请输入活动说明' }
})

const modalVisible = ref(false)
const onAddGoods = () => {
  modalResetFormState()
  modalVisible.value = true
}
const onRemoveGoods = index => {
  formState.value.goods.splice(index, 1)
}

const {
  formState: modalFormState,
  resetFormState: modalResetFormState,
  setFormState: modalSetFormState,
  setFormRules: modalSetFormRules,
  validateForm: modalValidateForm
} = useFormState({
  sku: '',
  price: '',
  min_price: '',
  setting_stock: '',
  min_price_user_count: '',
  cur_buy_user_count: ''
})

const validatorGoodsSku = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入商品SKU')
  } else if (formState.value.goods.some(item => item.sku?.toLowerCase() === value?.toLowerCase())) {
    return Promise.reject('该商品SKU已存在')
  }
  return Promise.resolve(true)
}

const validatorMinPrice = (rule, value) => {
  if (!value && value !== 0) {
    return Promise.reject('请输入助力价格')
  } else if (value >= modalFormState.value.price) {
    return Promise.reject('助力价格不能高于或等于原价')
  }
  return Promise.resolve(true)
}

const validatorCurBuy = (rule, value) => {
  if (!value) {
    return Promise.reject('请输入当前价买')
  } else if (value > modalFormState.value.min_price_user_count) {
    return Promise.reject('当前价买不能高于助力人数')
  }
  return Promise.resolve(true)
}

modalSetFormRules({
  sku: { validator: validatorGoodsSku },
  price: { required: true, message: '请输入商品原价' },
  min_price: { validator: validatorMinPrice },
  setting_stock: { required: true, message: '请输入活动数量' },
  min_price_user_count: { required: true, message: '请输入助力人数' },
  cur_buy_user_count: { validator: validatorCurBuy }
})

const changeGroupPrice = row => {
  if (Number(row.price) < formatters.priceToInteger(row.group_price)) {
    message.error('拼团价格不能高于商品价格')
  } else if (Number(row.group_price) < 0) {
    message.error('拼团价格必须大于0')
  }
}

const onModalSubmit = async ([goods]) => {
  goodsSpecsApi.list({ filters: { goods_id: goods.id }, relations: ['stock', 'goods'] }).then(res => {
    modalVisible.value = false
    formState.value.goods = res.map(item => {
      return {
        // group_goods表所需字段
        goods_id: item.goods_id,
        sku: item.sku,
        group_price: item.price / 100,
        setting_stock: item.stock.ava_stock,
        stock: item.stock.ava_stock,
        // 仅作页面展示用字段
        photo_url: item.photo_url,
        title: item.goods.title,
        attrs: item.attrs,
        price: item.price
      }
    })
  })
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(transformToRequest(formState.value))
  params.goods = params.goods.map(item => {
    item.group_price = formatters.priceToInteger(item.group_price)
    delete item.stock
    return item
  })
  id ? await groupApi.replace(id, params) : await groupApi.create(params)

  message.success('操作成功')
  router.back()
}
</script>
