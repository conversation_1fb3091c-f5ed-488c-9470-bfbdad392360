<template>
  <a-space :size="20" direction="vertical">
    <a-row :gutter="18">
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            正在拼团 {{ $formatters.thousandSeparator(statsData.normal_group_buy_count, false, false) }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            拼团成功 {{ $formatters.thousandSeparator(statsData.success_group_buy_count, false, false) }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            拼团失败 {{ $formatters.thousandSeparator(statsData.fail_group_buy_count, false, false) }}
          </div>
        </div>
      </a-col>
      <a-col :span="6">
        <div class="bgc-white p-20">
          <div class="color-85">
            支付订单 {{ $formatters.thousandSeparator(statsData.paid_order_count, false, false) }}
          </div>
        </div>
      </a-col>
    </a-row>
    <a-card title="活动趋势">
      <div ref="echartsContainer" style="width: 100%; height: 400px;"></div>
    </a-card>
    <a-card title="拼团记录">
      <template #extra>
        <a-space :size="10">
          <a-input-group compact>
            <a-select
              v-model:value="formState.type"
              class="w-120"
              placeholder="请选择"
              :options="userModeList.options()"
              @change="setPage()"
            />
            <a-input
              v-model:value.trim="formState.keyword"
              placeholder="请输入关键字"
              class="w-300"
              @press-enter="setPage()"
              @blur="setPage()"
            />
          </a-input-group>
          <a-select
            v-model:value="formState.status"
            allow-clear
            class="w-120"
            placeholder="拼团状态"
            :options="groupStatusList.options()"
            @change="setPage()"
          />
          <a-select
            v-model:value="formState.pay_status"
            allow-clear
            class="w-120"
            placeholder="支付状态"
            :options="payStatusList.options()"
            @change="setPage()"
          />
        </a-space>
      </template>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="商品信息" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.goods.spec.photo_url"
              :title="record.goods.goods.title"
              :subtit="`${record.goods.sku}：${record.goods.spec.attrs.reduce(
                (prev, item, i) => prev + (i ? '/' : '') + item.value,
                ''
              )}`"
              :status="groupStatusList.filter(record.status)"
            />
          </template>
        </a-table-column>
        <a-table-column title="发起人" width="250px" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.user.nickname }}
            </div>
            <div>{{ $formatters.numberEncryption(record.user.phone_number) }}</div>
          </template>
        </a-table-column>
        <a-table-column title="有效时间" width="200px">
          <template #default="{ record }">
            {{ record.activity.start_time }} <br />至 {{ record.activity.end_time }}
          </template>
        </a-table-column>
        <a-table-column title="支付状态" width="120px">
          <template #default="{ record }">
            <a-badge
              :color="payStatusList.filter(record.order.pay_status).color"
              :text="payStatusList.filter(record.order.pay_status).label"
            />
          </template>
        </a-table-column>
      </a-table>
    </a-card>
  </a-space>
</template>
<script setup>
import { useRoute } from 'vue-router'
import { debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { groupDataApi, groupRecordApi } from '../../api'
import { userModeList, groupStatusList, payStatusList } from '../../enums'
import * as echarts from 'echarts';

const { id } = useRoute().params
const statsData = ref({
  join_user_count: 0,
  bargain_user_count: 0,
  help_user_count: 0,
  bargain_count: 0,
  complete_count: 0,
  fail_count: 0,
  order_count: 0,
  paid_count: 0,
  paid_amount: 0,
  uv: 0,
  register_account_count: 0,
  register_user_count: 0
})

const echartsContainer = ref(null);

const initEcharts = () => {
  const { table } = statsData.value
  const myChart = echarts.init(echartsContainer.value, null, );
  const series = []
  table.forEach(item => {
    series.push({
      data: [item.success_group_buy_data, item.fail_group_buy_data],
      type: 'line',
      stack: 'x',
    })
  })
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: 0,
      right: 0,
      bottom: 0,
      containLabel: true // 如果包含了坐标轴的标签和标题，将其设置为 true
    },
    xAxis: {
      data: table.map(item=> item.date)
    },
    toolbox: {
      feature: {
        saveAsImage: {}
      }
    },
    yAxis: {},
    series: [
      {
        data: table.map(item=> item.success_group_buy_data),
        type: 'line',
        name: '拼团成功',
      },
      {
        data: table.map(item=> item.fail_group_buy_data),
        type: 'line',
        name: '拼团失败',
      }
    ]
  };
  myChart.setOption(option);
}
const loadDataStats = async () => {
  statsData.value = await groupDataApi.list({ filters: { activity_id: id }, relations: ['activity']})
  initEcharts()
}
useLoadingMessage(loadDataStats(), {
  loadingText: '正在加载数据...'
})

const { formState } = useFormState({
  type: userModeList.phone_number,
  keyword: undefined,
  status: undefined,
  pay_status: undefined
})
const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  groupRecordApi.paginator({
    offset,
    limit,
    filters: {
      activity_id: id,
      status: formState.value.status,
      pay_status: formState.value.pay_status
    },
    relations: ['goods', 'order' ,'records', 'activity','user'],
    sorts: ['-created_at'],
    relation_filters: {
      user: useTransformQuery({ [formState.value.type]: formState.value.keyword }, { nickname: 'like' })
    }
  })
)
setPage()
</script>
