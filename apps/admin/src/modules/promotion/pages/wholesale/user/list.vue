<template>
  <uc-layout-list title="参与用户列表">
    <template #extra>
      <a-upload
        :action="wholesaleUserImportApi(id)"
        :show-upload-list="false"
        @change="handleChange"
      >
        <a href="javascript:;" class="user-flex icon-margin m-r-10">
          <a-button type="primary">
            导入
          </a-button>
        </a>
      </a-upload>
      <a-button @click="exportTemplate">
        导出模板
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="手机号码" ellipsis>
          <template #default="{ record }">
            {{ record.phone_number }}
          </template>
        </a-table-column>

        <a-table-column title="过期时间" ellipsis>
          <template #default="{ record }">
            {{ record.expired_at }}
          </template>
        </a-table-column>

        <a-table-column title="导入时间" ellipsis>
          <template #default="{ record }">
            {{ record.created_at }}
          </template>
        </a-table-column>

        <a-table-column title="下单数量" align="right" ellipsis>
          <template #default="{ record }">
            <span v-if="!record.order_statistics?.number"> {{ record.order_statistics?.number }}</span>
            <router-link
              v-else
              :to="{
                name: 'shop-list',
                query: {
                  activity_id: id,
                  activity_type: orderType.wholesale,
                  activity_title: title,
                  user_id: record.user_id
                }
              }"
            >
              {{ record.order_statistics?.number }}
            </router-link>
          </template>
        </a-table-column>

        <a-table-column title="下单金额" ellipsis align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.order_statistics?.amount) }}
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { message } from 'ant-design-vue'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { wholesaleUsersApi, wholesaleUserTemplateApi, wholesaleUserImportApi } from '../../../api'
import {useRoute} from 'vue-router'
import {orderType} from '@/modules/order/enums'

const {id} = useRoute().params
const {title} = useRoute().query

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  return wholesaleUsersApi.paginator({
    filters: {
      activity_id: id
    },
    offset,
    limit
  })
})

const exportTemplate = () => window.open(wholesaleUserTemplateApi)

const handleChange = ({ file: { status, response } }) => {
  switch (status) {
    case 'uploading':
      return
    case 'error':
      message.error(response?.data?.message ?? '上传文件失败')
      return
    case 'done':
      if (response.data) {
        message.success('导入成功')
        setPage()
      }
      break
    default:
      break
  }
}
</script>

<style lang="scss" scoped>

</style>
