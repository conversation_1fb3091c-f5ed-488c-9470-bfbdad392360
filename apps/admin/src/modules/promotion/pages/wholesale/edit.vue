<template>
  <!-- 砍价编辑 -->
  <uc-layout-form :is-save="!isReadonly" @submit="handleSubmit">
    <a-card title="基本信息">
      <a-form-item label="活动海报">
        <uc-upload v-model:list="formState.banner_url" upload-text=" " :max-length="1" :disabled="isRead" />
      </a-form-item>

      <a-form-item label="活动时间">
        <a-date-picker
          v-model:value="formState.start_time"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择开始时间"
          class="w-240"
          :disabled="isReadonly || isStarted"
          :disabled-date="disabledStartTime"
          :disabled-time="disabledStartTime"
        />
        <span class="separator">~</span>
        <a-date-picker
          v-model:value="formState.end_time"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          show-time
          placeholder="请选择结束时间"
          class="w-240"
          :disabled="isReadonly"
          :disabled-date="disabledEndTime"
        />
      </a-form-item>
      <a-form-item label="活动名称" class="required">
        <a-input
          v-model:value.trim="formState.title"
          placeholder="请输入活动名称，不超过100字"
          :maxlength="100"
          :disabled="isReadonly"
        />
      </a-form-item>
      <a-form-item label="限制人群" class="required">
        <a-select v-model:value="formState.user_source" placeholder="请选择限制人群" :options="userSource.options()" />
      </a-form-item>

      <a-form-item v-if="formState.user_source === userSource.from_api" label="API地址" class="required">
        <a-input
          v-model:value.trim="formState.user_source_api"
          placeholder="请输入验证API地址"
          :maxlength="30"
          :disabled="isReadonly"
        />
      </a-form-item>


      <a-form-item label="浮窗配置">
        <div v-for="item,index in formState.popup" :key="index" class="popup-item">
          <uc-jump v-model:value="item.jump" :ignore-jumps="[jumpType.share]" />
          <uc-upload
            v-model:list="item.image"
            upload-text=" "
            show-label
            label-text="图片"
            :max-length="1"
            :disabled="isRead"
            class="m-t-10"
          />
          <a-button shape="circle" size="small" class="delete-btn popup-item-close" type="link">
            <template #icon>
              <uc-ant-icon name="CloseCircleFilled" type="danger" @click="removePopup(index)" />
            </template>
          </a-button>
        </div>

        <a-button type="link" class="p-0" @click="addPopup()">添加浮窗配置</a-button>
      </a-form-item>
    </a-card>

    <a-card title="批发规格">
      <template #extra>
        <a-button type="primary" :disabled="isReadonly" @click="onAddSkuGoods">
          添加规格
        </a-button>
        <a-button type="primary" :disabled="isReadonly" style="margin-left: 10px;" @click="onAddGoods">
          添加商品
        </a-button>
      </template>
      <a-table :data-source="formState.goods" row-key="sku" :pagination="false">
        <a-table-column title="商品名称/规格名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_url"
              :title="record.goods_title"
              :subtit="`${record.sku}: ${Array.isArray(record.attrs) ? record.attrs.reduce(
                (prev, item, i) => prev + (i ? '/' : '') + item.value,
                ''
              ) : record.attrs}`"
            />
          </template>
        </a-table-column>
        <a-table-column title="商品价格" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.price) }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ index }">
            <a-button type="link" class="danger" :disabled="isReadonly" @click="onRemoveGoods(index)">
              移除
            </a-button>
          </template>
        </a-table-column>
        <template #expandedRowRender="{ record }">
          <div class="sub-table">
            <a-table
              v-if="record.price_levels.length"
              :data-source="record.price_levels"
              row-key="id"
              :pagination="false"
            >
              <a-table-column title="起批数量" width="180px">
                <template #default="{ record: level }">
                  <a-input-number v-model:value.trim="level.quantity" min="1" placeholder="请输入起批数量" />
                </template>
              </a-table-column>
              <a-table-column title="单价" width="180px" align="right">
                <template #default="{ record: level }">
                  <a-input-number v-model:value.trim="level.price" min="0.01" placeholder="请输入单价" />
                </template>
              </a-table-column>
              <a-table-column title="操作" width="60px">
                <template #default="{ record: level, index }">
                  <a-button
                    type="link"
                    class="danger"
                    :disabled="level.id > 0 && isReadonly"
                    style="padding: 0;"
                    @click="deletePriceLevels(record.price_levels, index)"
                  >
                    移除
                  </a-button>
                </template>
              </a-table-column>
              <a-table-column />
            </a-table>

            <a-button style="margin: 10px 0 0 15px;" @click="addPriceLevels(record)">
              添加
            </a-button>
          </div>
        </template>
      </a-table>
    </a-card>
    <uc-rich-text v-model="formState.desc" :disabled="isReadonly" />
  </uc-layout-form>
  <select-spu-goods v-model:visible="modalVisible" :mode="goodsSelectType.multiple" @ok="onModalSubmit" />
  <select-sku-goods
    v-model:visible="modalSkuVisible"
    :scroll="{ y: 600 }"
    :mode="goodsSelectType.multiple"
    @ok="onModalSkuSubmit"
  />
</template>
<script setup>
import { ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'

import { wholesaleApi, goodsSpecsApi } from '../../api'
import { goodsSelectType, userSource } from '../../enums'
import { jumpType } from '@/enums/jump' 

const { TIME_STATUS_NOSTART, TIME_STATUS_ENDED } = useTimeStatus()

const tableLoading = ref(true)
const expandedRowKeys = ref([])


const { formState, setFormState, setFormRules, validateForm } = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  user_source: undefined,
  user_source_api: undefined,
  banner_url: undefined,
  //
  goods: [],
  desc: undefined,
  popup: []
})
const router = useRouter()
const { id } = useRoute().params
const isReadonly = ref(false)
const isStarted = ref(false)

const amountConversion = (goods, show = true) => {

  if (goods) {
    goods.forEach(good => {
      good.price_levels.forEach(level => {
        level.price = show ? level.price / 100 : level.price * 100
      })
    })
  }

  return goods
}


if (id) {
  const hideLoading = message.loading('正在加载数据...')

  wholesaleApi
    .get(id, { relations: ['goods'] })
    .then(res => {
      isStarted.value = res.status !== TIME_STATUS_NOSTART
      isReadonly.value = res.status === TIME_STATUS_ENDED

      res.goods.forEach(item => {
        expandedRowKeys.value.push(item.id)

      })

      res.goods = amountConversion(res.goods)

      res.banner_url = res.banner_url ? [res.banner_url] : undefined

      if(!res.popup) {
        res.popup = []
      }

      res.popup.forEach(item => {
        item.image = [item.image]
      })

      setFormState(res)
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

const validatorGoods = (rule, value) => {
  if (!value.length) {
    return Promise.reject('请添加活动商品')
  } else if (value.some(item => {
    return item.price_levels.length === 0
  })) {
    return Promise.reject('请设置起批价格和数量')
  }
  return Promise.resolve(true)
}

setFormRules({
  title: { required: true, message: '请输入活动名称' },
  popup: {
    validator(_, values) {
      for (const value of values) {
        const { jump, image } = value
        let tip

        // check settinf jump
        switch (jump.jump_type) {
          case jumpType.webview:
            if (!jump.jump_link) tip = '请输入网页URL'
            break
          case jumpType.page:
            if (!jump.jump_link) tip = '请输入链接URL'
            break
          case jumpType.miniprogram:
            if (!jump.appid) {
              tip = '请输入小程序appid'
              break
            }
            if (!jump.jump_link) {
              tip = '请输入链接URL'
              break
            }
        }
        if (tip) return Promise.reject(tip)
      }
      return Promise.resolve()
    }
  },
  user_source: { required: true, message: '请选择用户来源' },
  goods: { validator: validatorGoods },
  desc: { required: true, message: '请输入活动说明' }
})


const modalVisible = ref(false)
const modalSkuVisible = ref(false)
const onAddGoods = () => {
  modalVisible.value = true
}
const onAddSkuGoods = () => {
  modalSkuVisible.value = true
}

const onRemoveGoods = index => {
  formState.value.goods.splice(index, 1)
}

const onModalSubmit = async (goodsList) => {
  goodsList.forEach(async (goods) => {
    const res = await goodsSpecsApi.list({ filters: { goods_id: goods.id, }, relations: ['stock',] })
    const specList = res.map(item => {
      return {
        goods_title: goods.title,
        photo_url: item.photo_url,
        attrs: item.attrs,
        price: item.price,
        sku: item.sku,
        price_levels: []
      }
    })

    specList.forEach(spec => {
      // 去重
      if (!formState.value.goods.some(item => {
        return item.sku == spec.sku
      })) {
        formState.value.goods.push(spec)
      }
    })
  })
  modalVisible.value = false
}

const onModalSkuSubmit = async (specs) => {
  specs.forEach(spec => {
    spec = {
      goods_title: spec.goods.title,
      photo_url: spec.photo_url,
      attrs: spec.attrs,
      price: spec.price,
      sku: spec.sku,
      price_levels: []
    }
    // 去重
    if (!formState.value.goods.some(item => {
      return item.sku == spec.sku
    })) {
      formState.value.goods.push(spec)
    }
  })
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  params.banner_url = params.banner_url?.length > 0 ? params.banner_url[0] : ''
  // 规格转换分
  params.goods = amountConversion(params.goods, false)

  params.popup.forEach(item => {
    item.image = item.image[0]
  })

  id ? await wholesaleApi.replace(id, params) : await wholesaleApi.create(params)

  message.success('操作成功')
  router.back()
}

const addPriceLevels = (record) => {
  record.price_levels.push({
    id: 0,
    price: record.price / 100,
    quantity: 1,
  })
}

const deletePriceLevels = (price_levels, index) => {
  price_levels.splice(index, 1)
}

const addPopup = () => {
  formState.value.popup.push({
    image: [],
    jump: {
      jump_type: jumpType.page,
      jump_link: undefined,
      appid: undefined
    }
  })
}
const removePopup = (index) => {
  formState.value.popup.splice(index, 1)
}
</script>

<style scoped>
.sub-table {
  background-color: #fff;
  padding: 10px 10px;
}

:deep(.sub-table .ant-table-thead > tr > th) {
  background: #fff;
}
.popup-item {
  position: relative;
}
.popup-item-close{
  position: absolute;
  left: 114px;
  top: 50%;
}
.delete-btn {
  padding-top: 2px;
  border: none;

  :deep(.anticon) {
    font-size: 20px;
  }
}
</style>
