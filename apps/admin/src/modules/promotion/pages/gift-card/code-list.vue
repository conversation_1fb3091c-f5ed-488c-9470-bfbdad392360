<template>
  <uc-layout-list :title="title">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            :options="giftCardCodeSearch.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键词" />
        </a-input-group>
      </a-form-item>

      <a-form-item>
        <a-range-picker
          v-model:value="formState.created_at"
          show-time
          value-format="YYYY-MM-DD HH:mm:ss"
          separator="-"
          :placeholder="['创建时间', '创建时间']" 
        />
      </a-form-item>

      <a-form-item>
        <a-select
          v-model:value="formState.exchanged"
          placeholder="是否兑换"
          :options="giftCardCodeExchanged.options()"
          allow-clear
        />
      </a-form-item>

      <a-form-item>
        <a-select
          v-model:value="formState.status"
          placeholder="是否使用"
          :options="giftCardCodeStatus.options()"
          allow-clear
        />
      </a-form-item>

      <a-form-item>
        <a-range-picker
          v-model:value="formState.exchanged_at"
          show-time
          value-format="YYYY-MM-DD HH:mm:ss"
          separator="-"
          :placeholder="['兑换时间', '兑换时间']" 
        />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button>
        <router-link to="promotion-gift-card-list">返回</router-link>
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="卡号" width="120px" ellipsis align="center">
          <template #default="{ record }">
            {{ record.code }}
          </template>
        </a-table-column>

        <a-table-column title="有效期" width="220px" ellipsis align="left">
          <template #default="{ record }">
            <uc-img-text
              :subtit="record.start_time + ' ~ ' + (record.end_time ?? '无限制')"
            />
          </template>
        </a-table-column>

        <a-table-column title="是否兑换" width="90px" ellipsis align="center">
          <template #default="{ record }">
            {{ giftCardCodeExchanged.filter(record.exchanged).label }}
          </template>
        </a-table-column>

        <a-table-column title="是否使用" width="90px" ellipsis align="center">
          <template #default="{ record }">
            {{ giftCardCodeStatus.filter(record.status).label }}
          </template>
        </a-table-column>

        <a-table-column title="用户信息" width="120px" ellipsis align="center">
          <template #default="{ record }">
            <div v-if="!record.user">-</div>
            <div v-else>
              <div>{{ record.user?.nickname }}</div>
              <div>{{ record.user?.phone_number }}</div>
            </div>
          </template>
        </a-table-column>

        <a-table-column title="兑换时间" width="160px" align="center">
          <template #default="{ record }">
            {{ record.exchanged_at || '-' }}
          </template>
        </a-table-column>

        <a-table-column title="使用时间" width="160px" align="center">
          <template #default="{ record }">
            {{ record.used_at || '-' }}  
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { giftCardCodeSearch, giftCardCodeExchanged, giftCardCodeStatus } from '../../enums'
import { giftCardCodeApi } from '../../api'

import { useRoute } from 'vue-router'
const route = useRoute()

const {id, title, exchanged, status} = route.query
const currentExchanged = exchanged == giftCardCodeExchanged.yes || exchanged == giftCardCodeExchanged.no ? parseInt(exchanged) : undefined

const getNickname = () =>
  formState.value.conditionKey == giftCardCodeSearch.nickname && formState.value.conditionValue
    ? { user: useTransformQuery({ nickname: formState.value.conditionValue }, { nickname: 'like' }) }
    : {}

const getPhoneNumber = () =>
  formState.value.conditionKey == giftCardCodeSearch.phone_number && formState.value.conditionValue
    ? { user: useTransformQuery({ phone_number: formState.value.conditionValue }, { phone_number: '=' }) }
    : {}

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  giftCardCodeApi.paginator({
    filters: useTransformQuery(
      {
        code: formState.value.conditionKey == giftCardCodeSearch.code ? formState.value.conditionValue : undefined,
        created_at: formState.value.created_at,
        exchanged_at: formState.value.exchanged_at,
        activity_id: formState.value.activity_id,
        exchanged: formState.value.exchanged,
        status: formState.value.status
      },
      { 
        code: '=',
        activity_id: '=',
        created_at: 'dateRange',
        exchanged_at: 'dateRange',
        exchanged: '=',
        status: '=',
      }
    ),
    relation_filters: { ...getNickname(), ...getPhoneNumber() },
    relations: ['user'],
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  conditionKey: 'code',
  conditionValue: undefined,
  activity_id: id ?? undefined,
  created_at: undefined,
  exchanged_at: undefined,
  exchanged: currentExchanged,
  status: status ?? undefined,
})

const changeConditionKey = () => {
  formState.value.conditionValue = undefined
}

onRestFormState(() => setPage())

</script>
