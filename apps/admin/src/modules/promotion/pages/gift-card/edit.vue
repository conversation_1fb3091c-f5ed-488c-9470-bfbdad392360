<template>
  <!-- 砍价编辑 -->
  <uc-layout-form @submit="handleSubmit">
    <a-card title="基本信息">
      <a-form-item label="卡片图片" class="required">
        <uc-upload v-model:list="formState.photo_url" upload-text=" " :max-length="1" :disabled="isRead" />
      </a-form-item>
      
      <a-form-item label="卡片名称" class="required">
        <a-input
          v-model:value.trim="formState.title"
          placeholder="请输入卡片名称，不超过30字"
          :maxlength="30"
        />
      </a-form-item>

      <a-form-item label="卡片有效期" class="required flex">
        <a-select
          v-model:value="formState.valid_date_type"
          style="width: 120px"
          placeholder="请选择"
          :options="validDateType.options()"
          class="m-r-10"
          :disabled="isEdit"
          @change="onChangeValidDateType"
        />
        <span v-if="formState.valid_date_type == validDateType.fixed">
          <a-date-picker
            v-model:value="formState.start_time"
            :disabled="isEdit"
            class="w-180 min-w-175"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择开始时间"
          />
          <span class="data_time-gap">~</span>
          <a-date-picker
            v-model:value="formState.end_time"
            class="w-180 min-w-175"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            show-time
            placeholder="请选择结束时间"
          />
        </span>
        <span v-else>
          <a-input-number
            v-model:value="formState.receive_date"
            class="w-370"
            :min="0"
            :disabled="isEdit"
            placeholder="请输入有效天数，0表示不限制"
          />
        </span>
      </a-form-item>
    </a-card>

    <a-card title="可兑换商品">
      <template #extra>
        <a-button type="primary" @click="onAddSkuGoods()">
          添加商品
        </a-button>
      </template>
      <a-table
        :data-source="formState.goods"
        row-key="sku"
        :pagination="false"
      >
        <a-table-column title="商品封面/商品名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_url"
              :title="record.goods_title"
            />
          </template>
        </a-table-column>
        <a-table-column title="商品规格" width="250px">
          <template #default="{ record }">
            {{ `${record.sku}: ${Array.isArray(record.attrs) ? record.attrs.reduce(
              (prev, item, i) => prev + (i ? '/' : '') + item.value,
              ''
            ) : record.attrs}` }}
          </template>
        </a-table-column>
        <a-table-column title="SKU" width="120px">
          <template #default="{ record }">
            {{ record.sku }}
          </template>
        </a-table-column>
        <a-table-column title="销售价格" width="150px">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.price) }}
          </template>
        </a-table-column>
        <a-table-column title="可兑换数量" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.quantity"
              :min="1"
              :max="record.stock"
              :disabled="record.stock <= 0"
            />
          </template>
        </a-table-column>
        <a-table-column title="库存" width="120px">
          <template #default="{ record }">
            {{ record.stock }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="80px">
          <template #default="{ index }">
            <a-button type="link" class="danger" @click="onRemoveGoods(index)">
              移除
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-card>

    <uc-rich-text v-model="formState.desc" />
  </uc-layout-form>
  <select-sku-goods v-model:visible="modalSkuVisible" :scroll="{y:600}" :mode="goodsSelectType.multiple" @ok="onModalSkuSubmit" />
</template>
<script setup>
import { ref } from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {message} from 'ant-design-vue'
import {cloneDeep} from 'lodash'
import {useFormState} from '@/composables/useFormState'

import { giftCardApi } from '../../api'
import { goodsSelectType, validDateType } from '../../enums'

const tableLoading = ref(true)

const {formState, setFormState, setFormRules, validateForm} = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  photo_url: undefined,
  valid_date_type: validDateType.receive,
  receive_date: undefined,
  goods: [],
  desc: undefined
})
const router = useRouter()
const {id} = useRoute().params

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  giftCardApi
    .get(id, {relations: ['goods']})
    .then(res => {
      res.photo_url = res.photo_url ? [res.photo_url] : undefined
      setFormState(res)
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const validatorGoods = (rule, value) => {
  if(!value || value.length < 1) {
    return Promise.reject('请选择商品')
  }

  return Promise.resolve(true)
}

setFormRules({
  photo_url: {required: true, message: '请上传卡片图片'},
  title: {required: true, message: '请输入活动名称'},
  goods: {validator: validatorGoods},
  receive_date: {
    validator(_, value) {
      // 有效期
      const { valid_date_type, receive_date, start_time, end_time } = formState.value

      if (!valid_date_type) return Promise.reject('请选择卡片有效期')
      if (valid_date_type) {
        if (valid_date_type == validDateType.fixed && !start_time && !end_time)
          return Promise.reject('请选择卡片有效期时间范围')
        if (valid_date_type == validDateType.receive && !receive_date && receive_date !== 0)
          return Promise.reject('请输入卡片有效天数')
      }
      return Promise.resolve()
    }
  },
  desc: {required: true, message: '请输入活动说明'},
})


const modalSkuVisible = ref(false)

const onAddSkuGoods = () => {
  modalSkuVisible.value = true
}

// 商品
const onRemoveGoods = index => {
  formState.value.goods.splice(index, 1)
}

const onModalSkuSubmit = async(specs) => {
  for(const spec of specs) {
    const item = {
      goods_title: spec.goods.title,
      photo_url: spec.photo_url,
      attrs: spec.attrs,
      price: spec.price,
      goods_id: spec.goods_id,
      quantity: 1,
      sku: spec.sku,
      stock: Math.max(0, spec.stock.stock - spec.stock.hold)
    }
    formState.value.goods.push(item)
  }
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  params.photo_url = params.photo_url?.length > 0 ? params.photo_url[0] : ''
  id ? await giftCardApi.replace(id, params) : await giftCardApi.create(params)
  message.success('操作成功')
  router.back()
}
</script>

<style scoped>
.sub-table{
  background-color: #fff;
  padding: 10px 10px;
}
:deep(.sub-table .ant-table-thead > tr > th){
  background: #fff;
}
.category-wrapper{
  margin-top: 20px;
}
.category-wrapper:first-child{
  margin-top: 0;
}
.category-wrapper__head{
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
