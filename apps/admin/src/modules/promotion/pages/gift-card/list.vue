<template>
  <uc-layout-list title="礼品卡">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" placeholder="请输入礼品卡名称" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary">
        <router-link to="promotion-gift-card-add">添加礼品卡</router-link>
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="卡片信息" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_url"
              :title="record.title"
            />
          </template>
        </a-table-column>

        <a-table-column title="有效期类型" width="220px" ellipsis align="left">
          <template #default="{ record }">
            <div>{{ validDateType.filter(record.valid_date_type).label }}</div>
            <div v-if="record.valid_date_type == validDateType.fixed">
              <div>开始时间：{{ record.start_time }}</div>
              <div>结束时间：{{ record.end_time }}</div>
            </div>
            <div v-else>
              {{ `领取后${record.receive_date}天有效` }}
            </div>
          </template>
        </a-table-column>

        <a-table-column title="卡片总数" width="120px" ellipsis align="right">
          <template #default="{ record }">
            <router-link :to="{ name: 'promotion-gift-card-code-list', query: { id: record.id, title: record.title } }">
              {{ record.code_total }}
            </router-link>
          </template>
        </a-table-column>

        <a-table-column title="兑卡/人数" width="120px" ellipsis align="right">
          <template #default="{ record }">
            <router-link :to="{ name: 'promotion-gift-card-code-list', query: { id: record.id, title: record.title, exchanged: giftCardCodeExchanged.yes } }">
              {{ record.exchanged_codes_count }}
            </router-link>
            /{{ record.exchanged_user_count }}
          </template>
        </a-table-column>

        <a-table-column title="使用卡片/人数" width="130px" ellipsis align="right">
          <template #default="{ record }">
            <router-link :to="{ name: 'promotion-gift-card-code-list', query: { id: record.id, title: record.title, status: giftCardCodeStatus.used } }">
              {{ record.used_codes_count }}
            </router-link>
            /{{ record.used_user_count }}
          </template>
        </a-table-column>

        <a-table-column title="PV/UV" width="120px" align="right">
          <template #default="{ record }">
            {{ record.pv }}/{{ record.uv }}
          </template>
        </a-table-column>

        <a-table-column title="下单人/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.order_statistics?.user }} /
            <router-link
              :to="{
                name: 'shop-list',
                query: {activity_id: record.id, activity_type: orderType.gift_card, activity_title: record.title}
              }"
            >
              {{ record.order_statistics?.number }}
            </router-link>
            / {{ $formatters.thousandSeparator(record.order_statistics?.amount) }}
          </template>
        </a-table-column>

        <a-table-column title="支付人数/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.pay_statistics?.user }} / {{ record.pay_statistics?.number }} / {{
              $formatters.thousandSeparator(record.pay_statistics?.amount)
            }}
          </template>
        </a-table-column>

        <a-table-column title="操作" width="170px">
          <template #default="{ record }">
            <a-space>
              <a-button type="link" class="link">
                <router-link :to="{ name: 'shop-list', query: { id: record.id, activity_type: orderType.gift_card, activity_title: record.title } }">订单</router-link>
              </a-button>
              <a-button type="link">
                <router-link :to="{name: 'promotion-gift-card-edit', params:{ id: record.id }}">编辑</router-link>
              </a-button>
              <a-popconfirm
                :disabled="record.code_total > 0"
                placement="left"
                title="你确定要删除该数据么？"
                ok-text="确定"
                cancel-text="取消"
                @confirm="handleDelete(record)"
              >
                <a-button type="link" class="danger" :disabled="record.code_total > 0">
                  删除
                </a-button>
              </a-popconfirm>
            </a-space>
            <a-space>
              <a-button type="link" class="link" @click="handleOpenModal(record)">
                导出卡号
              </a-button>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal :visible="modalVisible" title="导出卡号" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '110px' } }">
      <a-form-item label="卡片名称" class="required">
        <a-input v-model:value="modalFormState.title" disabled />
      </a-form-item>
      <a-form-item label="导出卡号数量" class="required">
        <a-input-number
          v-model:value="modalFormState.num"
          :min="1"
          :max="10000"
          placeholder="请输入导出数量"
          :formatter="$formatters.number"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleExportCode"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { validDateType, giftCardCodeExchanged, giftCardCodeStatus } from '../../enums'
import {orderType} from '@/modules/order/enums'
import { giftCardApi, exportGiftCardApi } from '../../api'
import { useModalVisible } from '@/composables/useToggles'

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  giftCardApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await giftCardApi.delete(id)
  message.success('删除完成')
  setPage()
}

const {
  formState: modalFormState,
  setFormState: setModalFormState,
  validateForm: validateModalForm,
  setFormRules: setModalFormRules
} = useFormState()

setModalFormRules({
  num: {
    required: true,
    message: '请输入导出号码数量'
  }
})

const { modalVisible, setModalVisible } = useModalVisible()

const handleOpenModal = e => {
  setModalFormState({ title: e.title, num: undefined, id: e.id })
  setModalVisible(true)
}

// 导出优惠码
const handleExportCode = async () => {
  if (!(await validateModalForm())) {
    return
  }
  setModalVisible(false)
  const { id, num } = modalFormState.value
  window.location.href = exportGiftCardApi(id, num)
  resetFormState()
}

</script>
