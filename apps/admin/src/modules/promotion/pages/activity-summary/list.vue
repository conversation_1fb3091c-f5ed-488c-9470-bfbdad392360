<template>
  <uc-layout-list title="活动汇总">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="活动状态" :options="statusList" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.timeRange"
          value-format="YYYY-MM-DD HH:mm:ss"
          :placeholder="['起始时间', '截止时间']"
          :allow-clear="false"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="loadData">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #list>
      <a-table :data-source="collection" row-key="uuid" :loading="loading" :pagination="false" :scroll="{ x: 1600 }">
        <a-table-column title="活动名称/时间" ellipsis>
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.title }}
            </div>
            <div class="text-ellipsis">
              {{ record.start_time }} ~ {{ record.end_time }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="活动类型" width="160px">
          <template #default="{ record }">
            {{ activitySummary.filter(record.activity_type)?.label }}
          </template>
        </a-table-column>
        <a-table-column title="UV/PV" width="120px" align="right">
          <template #default="{ record }">
            {{ record.uv }} / {{ record.pv }}
          </template>
        </a-table-column>
        <a-table-column title="下单人数/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.order_statistics.user }} / {{ record.order_statistics.number }}/
            {{ $formatters.thousandSeparator(record.order_statistics.amount) }}
          </template>
        </a-table-column>
        <a-table-column title="支付人数/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.pay_statistics.user }} / {{ record.pay_statistics.number }} /
            {{ $formatters.thousandSeparator(record.pay_statistics.amount) }}
          </template>
        </a-table-column>
        <a-table-column title="订单转化率" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.percentCalc(record.order_statistics.user, record.uv) }}
          </template>
        </a-table-column>
        <a-table-column title="点击人数/次数" width="130px" align="right">
          <template #default="{ record }">
            {{ record.send_user_num }} / {{ record.send_coupon_num }}
          </template>
        </a-table-column>
        <a-table-column title="分享人数/次数" width="130px" align="right">
          <template #default="{ record }">
            {{ record.ps }} / {{ record.us }}
          </template>
        </a-table-column>
        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="140px">
          <template #default="{ record }">
            <a-button type="link" @click="onLook(record)">查看</a-button>
            <a-button type="link" @click="onOrder(record)">订单</a-button>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import {useRouter} from 'vue-router'
import {message} from 'ant-design-vue'
import {useFormState} from '@/composables/useFormState'
import {useTransformQuery} from '@/composables/useTransformQuery'
import {usePaginatorApiRequest} from '@/composables/useApiRequest'
import {
  activityCouponApi, // 活动发券
  promotionFullMinusApi, // 满减促销
  giftApi, // 满赠促销
  promotionLimitedTimeDiscountApi, // 限时折扣
  bargainApi, // 砍价助力
  seckillApi, // 特价秒杀
  markUpApi, // 加价换购
  topGoodsApi, // 尖货抽签
  promotionActivitiesFreightApi, // 运费促销
  groupApi, // 多人拼团
  wholesaleApi, // 团购批发
  siteApi, // 现场活动
  giftBoxApi, // 定制礼盒活动
  giftCardApi, // 礼品卡
} from '../../api'
import {useTimeStatus} from '@/composables/useTimeStatus'
import {activitySummary} from '../../enums'
import {ref} from 'vue'
import moment from 'moment'
import {uuid} from '@/utils/functions'

const {statusList, statusFilter, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED} = useTimeStatus() // 时间状态

const router = useRouter()
const collection = ref([]) //活动集合
const loading = ref(false)
const {formState, onRestFormState, resetFormState} = useFormState({
  title: undefined,
  status: undefined,
  timeRange: [
    moment().subtract(1, 'months'),
    moment()
  ]
})

const loadData = async () => {
  loading.value = true
  const timeRange = formState.value.timeRange
  const query = {
    filters: {
      ...useTransformQuery(formState.value, {
        title: 'like'
      }),
      timeRange: undefined,
      begin_time: timeRange ? `${moment(timeRange[0]).format('YYYY-MM-DD')} 00:00:00` : undefined,
      finish_time: timeRange ? `${moment(timeRange[1]).format('YYYY-MM-DD')} 23:59:59` : undefined
    },
  }
  const res = await Promise.all([
    activityCouponApi.list(query), // 活动发券
    promotionFullMinusApi.list(query), // 满减促销
    giftApi.list(query), // 满赠促销
    promotionLimitedTimeDiscountApi.list(query), // 限时折扣
    bargainApi.list(query), // 砍价助力
    seckillApi.list(query), // 特价秒杀
    markUpApi.list(query), // 加价换购
    topGoodsApi.list(query), // 尖货抽签
    promotionActivitiesFreightApi.list(query), // 运费促销
    groupApi.list(query), // 多人拼团
    wholesaleApi.list(query), //团购批发
    siteApi.list(query), // 现场活动
    giftBoxApi.list(query), // 定制礼盒活动
    giftCardApi.list(query), // 礼品卡
  ])
  const arr = []
  const options = activitySummary.options()
  res.map((activityList, index) => {
    activityList = activityList.map(activity => {
      return {
        ...activity,
        activity_type: options[index].value,
        uuid: uuid()
      }
    })
    arr.push(...activityList)
  })
  // 按活动开始时间排序
  arr.sort((a, b) => {
    if (a.start_time > b.start_time) return -1
    if (a.start_time < b.start_time) return 1
    return 0
  })
  collection.value = arr
  loading.value = false
}

onRestFormState(() => loadData())

const onLook = ({id, activity_type}) => router.push({
  name: activitySummary.filter(activity_type)?.router,
  query: {activity_id: id}
})

const onOrder = ({id, activity_type, title}) => router.push({
  name: 'shop-list',
  query: {activity_id: id, activity_type, activity_title: title}
})

loadData()
</script>
