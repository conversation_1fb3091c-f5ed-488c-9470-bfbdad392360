<template>
  <uc-layout-list title="运费促销">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="活动状态" :options="statusList" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="toAdd">
        新增运费促销
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称/时间">
          <template #default="{ record }">
            <uc-img-text
              :title="record.title"
              :subtit="record.start_time + ' ~ ' + $formatters.transformActivityEndTime(record.end_time)"
            />
          </template>
        </a-table-column>
        <a-table-column title="UV/PV" data-index="value" width="120px" align="right">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.uv }}/{{ record.pv }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="下单人/单数/金额" data-index="send" width="160px" align="right">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.order_statistics.user }} / {{ record.order_statistics.number }} /
              {{ $formatters.thousandSeparator(record.order_statistics.amount) }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="支付人数/单数/金额" data-index="used" width="160px" align="right">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.pay_statistics.user }} / {{ record.pay_statistics.number }} /
              {{ $formatters.thousandSeparator(record.pay_statistics.amount) }}
            </div>
          </template>
        </a-table-column>
        <a-table-column title="活动状态" data-index="expired" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" @click="copyLinkByRoute('freightList', { id: record.id })">
              链接
            </a-button>
            <a-button v-if="record.status != TIME_STATUS_ENDED" type="link" @click="toEdit(record)">
              编辑
            </a-button>
            <a-button v-if="record.status == TIME_STATUS_ENDED" type="link" @click="toEdit(record)">
              查看
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.can_delete"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { promotionActivitiesFreightApi } from '../../api'
import { couponType } from '../../enums'
import { useTimeStatus } from '@/composables/useTimeStatus'

const { activity_id } = useRoute().query
const { statusList, statusFilter, TIME_STATUS_NOSTART, TIME_STATUS_ENDED } = useTimeStatus()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  promotionActivitiesFreightApi.paginator({
    filters: useTransformQuery(formState.value, {
      title: 'like',
      status: '='
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  type: 'freight',
  title: undefined,
  status: undefined,
  id: activity_id
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await promotionActivitiesFreightApi.delete(id)
  message.success('删除完成')
  setPage()
}

const linkPath = Object.freeze('/promotion/pages/standard/sales-freight/index?id=') // 链接

const router = useRouter()
const toAdd = () => router.push({ name: 'sales-freight-add' })
const toEdit = ({ id }) => router.push({ name: 'sales-freight-edit', params: { id } })
</script>
