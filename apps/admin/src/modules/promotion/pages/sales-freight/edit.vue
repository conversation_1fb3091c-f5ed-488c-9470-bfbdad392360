<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动海报">
            <uc-upload v-model:list="formState.poster_url" upload-text=" " :max-length="1" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="活动时间：" name="rangeTime" class="required range-time">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              :disabled="(isEdit && formState.status !== TIME_STATUS_NOSTART) || isRead"
              class="w-240"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              :disabled-date="disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="结束时间"
              :disabled="isRead"
              class="w-240"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input v-model:value="formState.title" placeholder="请输入活动名称，不超过100字" maxlength="100" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input v-model:value="formState.tag" placeholder="请输入活动标签，不超过10字" maxlength="10" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="活动描述">
            <a-textarea
              v-model:value="formState.intro"
              placeholder="请输入活动描述，不超过200字"
              maxlength="200"
              :auto-size="{ minRows: 4, maxRows: 10 }"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="使用规则">
          <a-form-item label="使用门槛" class="required">
            <a-input-group compact>
              <a-select
                v-model:value="formState.rule.condition_type"
                style="width: 120px"
                placeholder="请选择"
                :options="couponThreshold.options()"
                class="m-r-10"
                :disabled="isRead"
                @change="onChangeCouponThreshold"
              />
              <a-input
                v-if="formState.rule.condition_type == couponThreshold.amount"
                v-model:value.trim="formState.rule.condition"
                style="width: 370px"
                placeholder="请输入商品总金额，0表示不限制"
                :disabled="isRead"
              />
              <a-input
                v-else
                v-model:value.trim="formState.rule.condition"
                style="width: 370px"
                placeholder="请输入具体数值，0表示不限制"
                :disabled="isRead"
              />
            </a-input-group>
          </a-form-item>
          <a-form-item label="优惠额度" class="required">
            <a-input-number v-model:value="formState.rule.value" placeholder="请输入优惠折扣，0表示包邮" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="适用范围" class="required">
            <a-select
              v-model:value="formState.range.type"
              placeholder="请选择适用范围"
              :options="productRange.options()"
              :disabled="isRead"
              @change="initCouponRangeProduct"
            />
          </a-form-item>
          <a-form-item
            v-if="productRange.filterCategories(formState.range.type)"
            :label="productRange.filter(formState.range.type)"
            class="required"
          >
            <a-tree-select
              v-model:value="formState.range.config"
              placeholder="请选择商品分类"
              :tree-data="categories"
              :replace-fields="{ key: 'id', value: 'id' }"
              multiple
              allow-clear
            />
          </a-form-item>
          <a-form-item
            v-if="productRange.filterGroup(formState.range.type)"
            :label="productRange.filter(formState.range.type)"
            class="required"
          >
            <a-tree-select
              v-model:value="formState.range.config"
              placeholder="请选择商品分组"
              :tree-data="groups"
              :replace-fields="{ key: 'id', value: 'id' }"
              multiple
              allow-clear
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>

    <apply-sku-goods
      v-if="productRange.filterSku(formState.range.type)"
      v-model:list="formState.range.config"
      :title="productRange.filter(formState.range.type)"
      :disabled="isRead"
    />
    <apply-spu-goods
      v-if="productRange.filterSpu(formState.range.type)"
      v-model:list="formState.range.config"
      :title="productRange.filter(formState.range.type)"
      :disabled="isRead"
    />
    <uc-rich-text v-model="formState.desc" placeholder="请输入活动说明（不超过500字）" :disabled="isRead" />
  </uc-layout-form>
</template>
<script setup>
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { message } from 'ant-design-vue'
import { couponThreshold, productRange } from '../../enums'
import { cloneDeep, pull } from 'lodash'
import { useGoods } from '../../useGoods'
import { promotionActivitiesFreightApi } from '../../api'
import formatters from '@/utils/formatters'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useCategories } from '@/modules/goods/useCategory'
import { useGroups } from '@/modules/goods/useGroup'

const { categories } = useCategories()
const { groups } = useGroups()

const { TIME_STATUS_NOSTART, TIME_STATUS_ENDED } = useTimeStatus()

const router = useRouter()

const { id } = useRoute().params

const isEdit = ref(false)

const isRead = ref(false)

const init = () => {
  if (id) {
    isEdit.value = true
    const hideLoading = message.loading('正在加载数据...')
    promotionActivitiesFreightApi
      .get(id, { relations: ['range', 'rule'] })
      .then(res => {
        res.poster_url = [res.poster_url]
        res.rule.value = res.rule.value / 100
        res.rule.condition = couponThreshold.transformDisplayValue(res.rule.condition_type, res.rule.condition)


        if (res.status === TIME_STATUS_ENDED) isRead.value = true

        // 商品数据
        formState.value.range.type = res.range.type
        formState.value.range.config = res.range.config

        // 商品范围
        setFormState(res)
      })
      .finally(hideLoading)
  }
}

onMounted(init)

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  poster_url: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  intro: undefined,
  type: 'sales_freight', // 运费
  range: {
    type: productRange.all,
    biz_id: undefined,
    biz_type: 'promotion',
    config: [] // 范围商品
  },
  rule: {
    type: 'freight',
    biz_type: 'promotion',
    value: undefined,
    condition_type: couponThreshold.amount, // 优惠条件类型
    condition: undefined // 优惠条件
  },
  desc: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  rule: {
    validator(_, value) {
      const { value: quota, condition_type, condition } = value
      // 使用规则
      if (condition === undefined) {
        if (condition_type == couponThreshold.amount) return Promise.reject('请输入商品总金额')
        if (condition_type == couponThreshold.number) return Promise.reject('请输入商品总件数')
      }
      if (quota === undefined) return Promise.reject('请输入优惠额度')
      return Promise.resolve()
    }
  },
  range: {
    validator(_, value) {
      const { type, config } = value

      if (productRange.filterAll(type) && !config.length) return Promise.reject('请选择范围商品')

      return Promise.resolve()
    }
  },
  desc: { required: true, message: '请输入活动说明' }
})

// 初始化当前优惠使用范围的商品
const initCouponRangeProduct = () => {
  formState.value.range.config = []
}

const onChangeCouponThreshold = () => {
  Object.assign(formState.value.rule, {
    condition: undefined
  })
}

const handleSubmit = async () => {
  if (!(await validateForm())) return

  // handle params
  const params = cloneDeep(formState.value)
  params.poster_url = params.poster_url[0]
  params.rule.value *= 100
  params.rule.condition = couponThreshold.transformSubmitValue(params.rule.condition_type, params.rule.condition)

  if (id) {
    await promotionActivitiesFreightApi.update(id, params)
    message.success('编辑完成')
  } else {
    await promotionActivitiesFreightApi.create(params)
    message.success('创建完成')
  }
  router.back()
}
</script>
<style scoped lang="less">
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}

.input-addon {
  position: relative;
  padding: 0 11px;
  color: rgba(0, 0, 0, 0.85);
  font-weight: normal;
  font-size: 14px;
  text-align: center;
  background-color: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s;
}

.data_time-gap {
  width: 20px;
  line-height: 32px;
  text-align: center;
  display: inline-block;
}
</style>
