<template>
  <!-- 砍价编辑 -->
  <uc-layout-form :is-save="!isReadonly" @submit="handleSubmit">
    <a-card title="基本信息">
      <a-form-item label="活动海报" class="required">
        <uc-upload v-model:list="formState.banner_url" upload-text=" " :max-length="1" :disabled="isRead" />
      </a-form-item>

      <a-form-item label="活动时间" class="required">
        <a-date-picker
          v-model:value="formState.start_time"
          show-time
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          placeholder="请选择开始时间"
          class="w-240"
          :disabled="isReadonly || isStarted"
          :disabled-date="disabledStartTime"
          :disabled-time="disabledStartTime"
        />
        <span class="separator">~</span>
        <a-date-picker
          v-model:value="formState.end_time"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          show-time
          placeholder="请选择结束时间"
          class="w-240"
          :disabled="isReadonly"
          :disabled-date="disabledEndTime"
        />
      </a-form-item>
      <a-form-item label="活动名称" class="required">
        <a-input
          v-model:value.trim="formState.title"
          placeholder="请输入活动名称，不超过30字"
          :maxlength="30"
          :disabled="isReadonly"
        />
      </a-form-item>

      <a-form-item label="商品价格" class="required">
        <a-input-number
          v-model:value="formState.price"
          placeholder="请输入"
          :precision="2"
          :disabled="isReadonly"
          :min="0"
        />
      </a-form-item>
      <a-form-item label="活动介绍">
        <a-textarea
          v-model:value="formState.sub_title"
          :maxlength="200"
          placeholder="请输入活动介绍，不超过200字"
        />
      </a-form-item>
    </a-card>

    <a-card title="选择礼盒">
      <template #extra>
        <a-button type="primary" :disabled="isReadonly" @click="onAddSkuGoods('box')">
          添加礼盒
        </a-button>
      </template>
      <a-table
        :data-source="formState.box"
        row-key="sku"
        :pagination="false"
      >
        <a-table-column title="礼盒封面/礼盒名称" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.photo_url"
              :title="record.goods_title"
            />
          </template>
        </a-table-column>
        <a-table-column title="礼盒规格" width="250px">
          <template #default="{ record }">
            {{ `${record.sku}: ${Array.isArray(record.attrs) ? record.attrs.reduce(
              (prev, item, i) => prev + (i ? '/' : '') + item.value,
              ''
            ) : record.attrs}` }}
          </template>
        </a-table-column>
        <a-table-column title="SKU" width="120px">
          <template #default="{ record }">
            {{ record.sku }}
          </template>
        </a-table-column>
        <a-table-column title="销售价格" width="150px">
          <template #default="{ record }">
            {{ $formatters.thousandSeparator(record.price) }}
          </template>
        </a-table-column>
        <a-table-column title="库存" width="120px">
          <template #default="{ record }">
            {{ record.stock }}
          </template>
        </a-table-column>
        <a-table-column title="操作" width="110px">
          <template #default="{ index }">
            <a-button type="link" :disabled="isReadonly" @click="editBox(index)">
              编辑
            </a-button>
            <a-button type="link" class="danger" :disabled="isReadonly" @click="onRemoveBox(index)">
              移除
            </a-button>
          </template>
        </a-table-column>
      </a-table>
    </a-card>

    <a-card title="选择商品">
      <template #extra>
        <a-button type="primary" :disabled="isReadonly" @click="onAddCategory()">
          添加分类
        </a-button>
      </template>
      <div v-for="(item, categoryIndex) in formState.categories" :key="categoryIndex" class="category-wrapper">
        <div class="category-wrapper__head">
          <div>
            <a-form :model="item" layout="inline">
              <a-form-item label="分类名称" name="title" class="required">
                <a-input v-model:value.trim="item.title" placeholder="请输入分类名称" />
              </a-form-item>

              <a-form-item label="最多可购买商品数量" name="limit" class="required">
                <a-input v-model:value.trim="item.limit" placeholder="请输入最多可购买商品数量" />
              </a-form-item>
            </a-form>
          </div>
          <div>
            <a-button type="danger" :disabled="isReadonly" class="m-r-10" @click="onRemoveCategory(categoryIndex)">
              移除分类
            </a-button>
            <a-button type="primary" :disabled="isReadonly" @click="onAddSkuGoods('category', categoryIndex)">
              添加商品
            </a-button>
          </div>
        </div>
        <a-table
          :data-source="item.goods"
          row-key="sku"
          :pagination="false"
        >
          <a-table-column title="商品封面/商品名称" ellipsis>
            <template #default="{ record }">
              <uc-img-text
                :url="record.photo_url"
                :title="record.goods_title"
              />
            </template>
          </a-table-column>
          <a-table-column title="商品规格" width="250px">
            <template #default="{ record }">
              {{ `${record.sku}: ${Array.isArray(record.attrs) ? record.attrs.reduce(
                (prev, item, i) => prev + (i ? '/' : '') + item.value,
                ''
              ) : record.attrs}` }}
            </template>
          </a-table-column>
          <a-table-column title="SKU" width="120px">
            <template #default="{ record }">
              {{ record.sku }}
            </template>
          </a-table-column>
          <a-table-column title="销售价格" width="150px">
            <template #default="{ record }">
              {{ $formatters.thousandSeparator(record.price) }}
            </template>
          </a-table-column>
          <a-table-column title="库存" width="120px">
            <template #default="{ record }">
              {{ record.stock }}
            </template>
          </a-table-column>
          <a-table-column title="单次限购数" width="120px">
            <template #default="{ record }">
              {{ record.limit }}
            </template>
          </a-table-column>
          <a-table-column title="操作" width="110px">
            <template #default="{ index }">
              <a-button type="link" :disabled="isReadonly" @click="editCategoryGoods(categoryIndex, index)">
                编辑
              </a-button>
              <a-button type="link" class="danger" :disabled="isReadonly" @click="onRemoveCategoryGoods(categoryIndex, index)">
                移除
              </a-button>
            </template>
          </a-table-column>
        </a-table>
      </div>
    </a-card>
    <uc-rich-text v-model="formState.desc" :disabled="isReadonly" />
  </uc-layout-form>
  <select-sku-goods v-model:visible="modalSkuVisible" :scroll="{y:600}" :mode="goodsSelectType.multiple" @ok="onModalSkuSubmit" />
  <a-modal :visible="boxVisible" title="编辑礼盒" @ok="onBoxSubmit" @cancel="onBoxCancel">
    <a-form :model="currentBox">
      <a-form-item label="商品图片">
        <uc-upload v-model:list="currentBox.photo_url" upload-text=" " :max-length="1" />
      </a-form-item>
      <a-form-item label="商品标题" name="goods_title" class="required">
        <a-input v-model:value.trim="currentBox.goods_title" placeholder="请输入商品标题" />
      </a-form-item>
      <a-form-item label="规格名称" name="attrs">
        {{ Array.isArray(currentBox.attrs) ? currentBox.attrs.reduce(
          (prev, item, i) => prev + (i ? '/' : '') + item.value,
          ''
        ) : currentBox.attrs }}
      </a-form-item>

      <a-form-item label="商品sku" name="sku">
        {{ currentBox.sku }}
      </a-form-item>

      <a-form-item label="商品价格" name="price">
        {{ $formatters.thousandSeparator(currentBox.price) }}
      </a-form-item>

      <a-form-item label="商品库存" name="stock">
        {{ currentBox.stock }}
      </a-form-item>
    </a-form>
  </a-modal>
  <a-modal :visible="categoryGoodsVisible" title="编辑商品" @ok="onCategoryGoodsSubmit" @cancel="onCategoryGoodsCancel">
    <a-form :model="currentCategoryGoods">
      <a-form-item label="商品图片">
        <uc-upload v-model:list="currentCategoryGoods.photo_url" upload-text=" " :max-length="1" />
      </a-form-item>

      <a-form-item label="商品标题" name="goods_title" class="required">
        <a-input v-model:value.trim="currentCategoryGoods.goods_title" placeholder="请输入商品标题" />
      </a-form-item>
      <a-form-item label="规格名称" name="attrs">
        {{ Array.isArray(currentCategoryGoods.attrs) ? currentCategoryGoods.attrs.reduce(
          (prev, item, i) => prev + (i ? '/' : '') + item.value,
          ''
        ) : currentCategoryGoods.attrs }}
      </a-form-item>

      <a-form-item label="商品sku" name="sku">
        {{ currentCategoryGoods.sku }}
      </a-form-item>

      <a-form-item label="商品价格" name="price">
        {{ $formatters.thousandSeparator(currentCategoryGoods.price) }}
      </a-form-item>

      <a-form-item label="商品库存" name="stock">
        {{ currentCategoryGoods.stock }}
      </a-form-item>

      <a-form-item label="单次限购数" name="limit" class="required">
        <a-input-number v-model:value.trim="currentCategoryGoods.limit" min="0" placeholder="单次限购数" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { ref, getCurrentInstance } from 'vue'
import {useRoute, useRouter} from 'vue-router'
import {message} from 'ant-design-vue'
import {cloneDeep} from 'lodash'
import {useFormState} from '@/composables/useFormState'
import {useDisabledDate} from '@/composables/useDisabledDate'
import {useTimeStatus} from '@/composables/useTimeStatus'

import { giftBoxApi } from '../../api'
import { goodsSelectType } from '../../enums'

// 金额处理 (/100 *100)
const { appContext } = getCurrentInstance()
const thousandSeparator = appContext.config.globalProperties.$formatters.thousandSeparator
const priceToInteger = appContext.config.globalProperties.$formatters.priceToInteger

const {TIME_STATUS_NOSTART, TIME_STATUS_ENDED} = useTimeStatus()

const tableLoading = ref(true)

const {formState, setFormState, setFormRules, validateForm} = useFormState({
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  sub_title: undefined,
  price: undefined,
  banner_url: undefined,
  box: [],
  categories: [],
  desc: undefined
})
const router = useRouter()
const {id} = useRoute().params
const isReadonly = ref(false)
const isStarted = ref(false)

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  giftBoxApi
    .get(id, {relations: ['box', 'categories.goods']})
    .then(res => {
      isStarted.value = res.status !== TIME_STATUS_NOSTART
      isReadonly.value = res.status === TIME_STATUS_ENDED

      res.banner_url = res.banner_url ? [res.banner_url] : undefined
      res.box = [res.box]
      res.price = thousandSeparator(res.price)
      setFormState(res)
    })
    .finally(() => {
      hideLoading()
      tableLoading.value = false
    })
}

const {disabledStartTime, disabledEndTime} = useDisabledDate(formState)

const validatorCategories = (rule, value) => {
  if (!value.length) {
    return Promise.reject('请添加分类商品')
  } else {

    for(const item of value) {
      if(!item.title) {
        return Promise.reject('请输入分类名称')
      }
      if(!item.limit) {
        return Promise.reject('请输入分类下最多可购买商品数量')
      }
      if(!item.goods.length) {
        return Promise.reject('请选择分类商品')
      } else {
        for(const good of item.goods) {
          if(good.limit !== 0 && !good.limit) {
            return Promise.reject('请输入单次最多可购买商品数量')
          }
        }
      }
    }
  }
  return Promise.resolve(true)
}

const validatorBox = (rule, value) => {
  if(!value || value.length < 1) {
    return Promise.reject('请选择礼盒')
  }

  return Promise.resolve(true)
}

setFormRules({
  banner_url: {required: true, message: '请上传活动海报'},
  start_time: { required: true, message: '请选择开始时间' },
  end_time: { required: true, message: '请选择结束时间' },
  title: {required: true, message: '请输入活动名称'},
  price: {required: true, message: '请输入活动价格'},
  box: {validator: validatorBox},
  categories: {validator: validatorCategories},
  desc: {required: true, message: '请输入活动说明'},
})


const modalSkuVisible = ref(false)
const changeType = ref()
const changeCategoryIndex = ref()

const onAddSkuGoods = (type, categoryIndex = undefined) => {
  changeType.value = type
  modalSkuVisible.value = true
  changeCategoryIndex.value = categoryIndex
}

// 礼盒
const boxVisible = ref(false)
const currentBox = ref()
const editBox = (index) => {
  currentBox.value = cloneDeep(formState.value.box[index])
  currentBox.value.photo_url = [currentBox.value.photo_url]
  boxVisible.value = true
}
const onBoxSubmit = () => {
  formState.value.box[0].goods_title = currentBox.value.goods_title
  formState.value.box[0].photo_url = currentBox.value.photo_url[0]
  boxVisible.value = false
}
const onBoxCancel = () => {
  boxVisible.value = false
}
const onRemoveBox = index => {
  formState.value.box.splice(index, 1)
}

// 分类
const currentCategoryIndex = ref()
const currentCategoryGoodsIndex = ref()
const currentCategoryGoods = ref()
const categoryGoodsVisible = ref(false)
const onAddCategory = () => {
  formState.value.categories.push({
    title: undefined,
    limit: undefined,
    goods: []
  })
}
const onRemoveCategory = index => {
  formState.value.categories.splice(index, 1)
}

const editCategoryGoods = (cIndex, index) => {
  currentCategoryIndex.value = cIndex
  currentCategoryGoodsIndex.value = index
  currentCategoryGoods.value = cloneDeep(formState.value.categories[cIndex].goods[index])
  currentCategoryGoods.value.photo_url = [currentCategoryGoods.value.photo_url]
  categoryGoodsVisible.value = true
}
const onRemoveCategoryGoods = (cIndex, index) => {
  formState.value.categories[cIndex].goods.splice(index, 1)
}
const onCategoryGoodsSubmit = () => {
  formState.value.categories[currentCategoryIndex.value].goods[currentCategoryGoodsIndex.value].goods_title = currentCategoryGoods.value.goods_title
  formState.value.categories[currentCategoryIndex.value].goods[currentCategoryGoodsIndex.value].limit = currentCategoryGoods.value.limit
  formState.value.categories[currentCategoryIndex.value].goods[currentCategoryGoodsIndex.value].photo_url = currentCategoryGoods.value.photo_url[0]
  categoryGoodsVisible.value = false
}
const onCategoryGoodsCancel = () => {
  categoryGoodsVisible.value = false
}

const onModalSkuSubmit = async(specs) => {
  if(changeType.value == 'box') {
    if(specs.length > 1) {
      return message.error('礼盒只能选择一个')
    }
    if(formState.value.box?.length) {
      return message.error('请先移除礼盒再选择')
    }
  }
  for(const spec of specs) {
    const item = {
      goods_title: spec.goods.title,
      photo_url: spec.photo_url,
      attrs: spec.attrs,
      price: spec.price,
      goods_id: spec.goods_id,
      sku: spec.sku,
      stock: Math.max(0, spec.stock.stock - spec.stock.hold)
    }
    // 礼盒
    if(changeType.value == 'box') {
      formState.value.box.push(item)
    } else {
      // 去重提示
      try {
        checkCategoryGoods(item.sku)
      } catch (error) {
        return message.error(error.message)
      }

      item.limit = 1
      formState.value.categories[changeCategoryIndex.value].goods.push(item)
    }
  }
}

const checkCategoryGoods = (sku) => {
  formState.value.box.forEach(box => {
    if(box.sku == sku) {
        throw new Error(`商品与礼盒重复，sku：${sku}`)
      }
  })
  formState.value.categories.forEach(category => {
    category.goods.forEach(good => {
      if(good.sku == sku) {
        throw new Error(`商品重复，sku：${sku}`)
      }
    })
  })
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = cloneDeep(formState.value)
  params.price = priceToInteger(params.price)
  params.banner_url = params.banner_url?.length > 0 ? params.banner_url[0] : ''
  params.box = params.box[0]
  id ? await giftBoxApi.replace(id, params) : await giftBoxApi.create(params)
  message.success('操作成功')
  router.back()
}
</script>

<style scoped>
.sub-table{
  background-color: #fff;
  padding: 10px 10px;
}
:deep(.sub-table .ant-table-thead > tr > th){
  background: #fff;
}
.category-wrapper{
  margin-top: 20px;
}
.category-wrapper:first-child{
  margin-top: 0;
}
.category-wrapper__head{
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}
</style>
