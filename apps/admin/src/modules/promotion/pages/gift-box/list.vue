<template>
  <uc-layout-list title="定制礼盒">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="活动状态" :options="statusList" allow-clear />
      </a-form-item>
      <a-form-item>
        <a-range-picker
          v-model:value="formState.range_picker"
          show-time
          value-format="YYYY-MM-DD HH:mm:ss"
          separator="-"
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <router-link :to="{name: 'promotion-gift-box-add'}">
        <a-button type="primary">
          新增定制礼盒
        </a-button>
      </router-link>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称/时间">
          <template #default="{ record }">
            <div class="text-ellipsis">
              {{ record.title }}
            </div>
            <div class="text-ellipsis">{{ record.start_time }} ~ {{ record.end_time }}</div>
          </template>
        </a-table-column>
        <a-table-column title="PV/UV" width="120px" align="right">
          <template #default="{ record }">
            {{ record.pv }}/{{ record.uv }}
          </template>
        </a-table-column>

        <a-table-column title="下单人/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.order_statistics?.user }} /
            <router-link
              :to="{
                name: 'shop-list',
                query: {activity_id: record.id, activity_type: orderType.gift_box, activity_title: record.title}
              }"
            >
              {{ record.order_statistics?.number }}
            </router-link>
            / {{ $formatters.thousandSeparator(record.order_statistics?.amount) }}
          </template>
        </a-table-column>

        <a-table-column title="支付人数/单数/金额" width="160px" align="right">
          <template #default="{ record }">
            {{ record.pay_statistics?.user }} / {{ record.pay_statistics?.number }} / {{
              $formatters.thousandSeparator(record.pay_statistics?.amount)
            }}
          </template>
        </a-table-column>

        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>

        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-space direction="vertical">
              <a-space-compact>
                <a-button
                  type="link"
                  class="link"
                  @click="copyLinkByRoute('giftBoxList', { id: record.id })"
                >
                  链接
                </a-button>
                <router-link v-if="record.status !== TIME_STATUS_ENDED" :to="{name: 'promotion-gift-box-edit', params: {id: record.id}}">
                  编辑
                </router-link>
                <router-link v-else :to="{name: 'promotion-gift-box-edit', params: {id: record.id}}">
                  查看
                </router-link>
                <a-button type="link" class="link">
                  <router-link :to="{ name: 'shop-list', query: { id: record.id, activity_type: orderType.gift_box, activity_title: record.title } }">订单</router-link>
                </a-button>
              </a-space-compact>
              <a-space-compact>
                <a-popconfirm
                  placement="left"
                  title="你确定要删除该数据么？"
                  ok-text="确定"
                  cancel-text="取消"
                  @confirm="handleDelete(record)"
                >
                  <a-button type="link" class="danger"> 删除 </a-button>
                </a-popconfirm>
              </a-space-compact>
            </a-space>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { useRoute } from 'vue-router'
import {watch} from 'vue'
import {message} from 'ant-design-vue'
import {useFormState} from '@/composables/useFormState'
import {useTransformQuery} from '@/composables/useTransformQuery'
import {usePaginatorApiRequest} from '@/composables/useApiRequest'
import {giftBoxApi} from '../../api'
import {orderType} from '@/modules/order/enums'
import { useTimeStatus } from '@/composables/useTimeStatus'

const { activity_id } = useRoute().query
const { statusList, statusFilter, TIME_STATUS_ENDED } = useTimeStatus() // 时间状态

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  const params = Object.assign({}, formState.value)
  delete params.range_picker
  return giftBoxApi.paginator({
    filters: useTransformQuery(params, {
      title: 'like'
    }),
    offset,
    limit,
  })
})

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  begin_time: undefined,
  finish_time: undefined,
  range_picker: undefined,
  id: activity_id
})

onRestFormState(() => setPage())

const handleDelete = async ({ id }) => {
  await giftBoxApi.delete(id)
  message.success('操作成功')
  setPage()
}

watch(() => formState.value.range_picker, (val) => {
  if (val?.length) {
    formState.value.begin_time = val[0]
    formState.value.finish_time = val[1]
  } else {
    formState.value.begin_time = undefined
    formState.value.finish_time = undefined
  }
})

</script>
