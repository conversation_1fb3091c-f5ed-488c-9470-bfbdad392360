<template>
  <a-modal
    v-model:visible="visible"
    width="650px"
    title="编辑活动商品"
    @ok="handleSubmit"
  >
    <div v-if="spin" class="w-fill spin-loading">
      <a-spin />
    </div>
    <a-form v-else :label-col="layoutLabelCol" :wrapper-col="layoutWrapperCol">
      <a-form-item label="商品标题" name="goods_title" class="required">
        <a-input v-model:value.trim="formState.goods_title" placeholder="请输入商品标题" />
      </a-form-item>

      <a-form-item label="规格名称" name="attrs">
        {{ useSpecAttr(formState.spec?.attrs) }}
      </a-form-item>

      <a-form-item label="商品sku" name="sku">
        {{ formState.sku }}
      </a-form-item>

      <a-form-item label="商品价格" name="price">
        {{ $formatters.thousandSeparator(formState.spec?.price) }}
      </a-form-item>

      <a-form-item label="商品图片">
        <uc-upload v-model:list="formState.image_url" upload-text=" " :max-length="1" />
      </a-form-item>

      <a-form-item label="活动价格" name="activity_price" class="required">
        <a-input-number v-model:value.trim="formState.activity_price" min="0.01" placeholder="请输入活动价格" />
      </a-form-item>

      <a-form-item label="商品库存" name="stock" class="required">
        <!--        活动未开始时直接编辑setting_stock，开始后操作添加库存-->
        <a-input-number v-if="!isReadonly" v-model:value.trim="formState.setting_stock" min="0" placeholder="请输入商品库存" />
        <a-input-number v-else v-model:value.trim="formState.stock" min="0" disabled placeholder="请输入商品库存" />
      </a-form-item>

      <a-form-item v-if="isStarted" label="增加库存" name="stock">
        <a-input-group class="flex flex-cc">
          <a-input-number v-model:value.trim="formState.add_stock" min="0" :max="formState.spec?.stock?.stock - formState.spec?.stock?.hold" placeholder="请输入增加库存" style="flex: 1" />
          <div style="width: 200px;margin-left: 20px;">可用库存：{{ formState.spec?.stock?.stock - formState.spec?.stock?.hold }}</div>
        </a-input-group>
      </a-form-item>

      <a-form-item label="每人限购" name="limit" class="required">
        <a-input-number v-model:value.trim="formState.limit" min="0" placeholder="请输入每人限购" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useFormState } from '@/composables/useFormState'
import {
  siteGoodsActionApi
} from '../../api'
import {cloneDeep} from 'lodash'
import {message} from 'ant-design-vue'
import { useSpecAttr } from '@/modules/promotion/useTransform'

const visible = defineModel('visible')
const emit = defineEmits(['ok'])
const props = defineProps({
  actId: {
    type: [String, Number],
    required: true,
    default: null
  },
  goodsId: {
    type: [String, Number],
    required: true,
    default: null
  }
})

watch(() => visible.value, (val) => {
  if(val) {
    fetchData(val)
  }
}, {
  immediate: true,
})

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  id: undefined,
  stock: undefined,
  limit: undefined,
  activity_price: undefined,
  image_url: undefined,
  goods_title: undefined,
})

setFormRules({
  stock: {required: true, message: '请输入商品库存'},
  limit: {required: true, message: '请输入每人限购'},
  activity_price: {required: true, message: '请输入活动价格'},
  goods_title: {required: true, message: '请输入商品标题'},
})

const spin = ref(false)

const isReadonly = ref(false)
const isStarted = ref(false)

const fetchData = () => {
  spin.value = true
  siteGoodsActionApi(props.actId).get(props.goodsId, {
    relations: ['activity', 'spec.stock']
  }).then((res) => {
    res.activity_price = (res.activity_price / 100).toFixed(2)
    isReadonly.value = isCurrentTimeGreaterThan(res.activity.start_time)
    isStarted.value = isCurrentTimeInProgress(res.activity.start_time, res.activity.end_time)
    res.image_url = res.image_url ? [res.image_url] : undefined
    setFormState(res)
  }).finally(() => {
    spin.value = false
  })
}


const handleSubmit = async ()  =>  {
  if (!(await validateForm())) {
    return
  }
  const params = cloneDeep(formState.value)
  if(isReadonly.value) {
    delete params.setting_stock
  }
  params.activity_price = params.activity_price * 100
  params.image_url =  params.image_url?.length > 0 ? params.image_url[0] : undefined
  await siteGoodsActionApi(props.actId).replace(props.goodsId, params)

  message.success('操作成功')
  visible.value = false
  emit('ok')
}

const isCurrentTimeGreaterThan = (date) => {
    const specifiedDate = new Date(date);
    const currentDate = new Date();
    return currentDate > specifiedDate;
}

const isCurrentTimeInProgress = (startTime, endTime) => {
  const startDate = new Date(startTime)
  const endDate = new Date(endTime)
  const currentDate = new Date()
  if(endTime) {
    return startDate < currentDate && currentDate < endDate
  } else {
    return startDate < currentDate
  }
}

</script>
<style lang="less" scoped>
.spin-loading{
  text-align: center;
  height: 530px;
  line-height: 530px;
}
</style>
