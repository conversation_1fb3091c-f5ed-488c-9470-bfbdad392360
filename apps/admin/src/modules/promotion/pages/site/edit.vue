<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动海报">
            <uc-upload v-model:list="formState.photo_url" upload-text=" " :max-length="1" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="活动时间：" name="rangeTime" class="required range-time">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              :disabled="(isEdit && formState.status !== TIME_STATUS_NOSTART) || isRead"
              class="w-240"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              :disabled-date="disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="结束时间"
              class="w-240"
              :disabled="isRead"
            />
          </a-form-item>
          <a-form-item label="活动名称" class="required">
            <a-input v-model:value="formState.title" placeholder="请输入活动名称，不超过100字" :disabled="isRead" maxlength="100" />
          </a-form-item>
          <a-form-item label="发货方式" class="required">
            <a-checkbox-group
              v-model:value="formState.shipping_methods"
              :options="orderShippingMethod.options()"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="优惠规则" class="h-fill">
          <a-form-item label="活动发券">
            <coup-act-select v-model="formState.activity_dispatch_coupon_id" />
          </a-form-item>
          <a-form-item v-if="formState.activity_dispatch_coupon_id" label="优惠券弹窗图" class="required">
            <uc-upload
              v-model:list="formState.activity_dispatch_coupon_image"
              upload-text=" "
              :max-length="1"
              :disabled="isRead"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
    <uc-rich-text v-model="formState.desc" placeholder="请输入活动说明（不超过500字）" :disabled="isRead" />
  </uc-layout-form>
</template>
<script setup>
import { onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { siteApi } from '../../api'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { orderShippingMethod } from '@/modules/order/enums'

const { TIME_STATUS_NOSTART, TIME_STATUS_ENDED } = useTimeStatus()

const router = useRouter()

const { id } = useRoute().params

const isEdit = ref(false)

const isRead = ref(false)

const init = () => {
  if (id) {
    isEdit.value = true
    const hideLoading = message.loading('正在加载数据...')
    siteApi
      .get(id)
      .then(res => {
        if (res.status === TIME_STATUS_ENDED) isRead.value = true
        res.photo_url = [res.photo_url]
        res.activity_dispatch_coupon_image = [res.activity_dispatch_coupon_image]
        setFormState(res)
      })
      .finally(hideLoading)
  }
}

onMounted(init)

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  photo_url: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  shipping_methods: [orderShippingMethod.express, orderShippingMethod.pick_up],
  desc: undefined,
  activity_dispatch_coupon_id: undefined,
  activity_dispatch_coupon_image: undefined
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  title: { required: true, message: '请输入活动名称' },
  shipping_methods: { required: true, message: '请输入发货方式' },
  desc: { required: true, message: '请输入活动说明' },
  activity_dispatch_coupon_image: {
    validator(_, value) {
      if (!formState.value.activity_dispatch_coupon_id) return Promise.resolve()
      if (!value.length) return Promise.reject('请上传优惠券弹窗图')
      return Promise.resolve()
    }
  }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return

  const params = cloneDeep(formState.value)
  params.photo_url = params.photo_url[0]
  params.activity_dispatch_coupon_image = params.activity_dispatch_coupon_image
    ? params.activity_dispatch_coupon_image[0]
    : undefined

  if (id) {
    await siteApi.replace(id, params)
    message.success('编辑完成')
  } else {
    await siteApi.create(params)
    message.success('创建完成')
  }
  router.back()
}
</script>
<style scoped lang="less">
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}
</style>
