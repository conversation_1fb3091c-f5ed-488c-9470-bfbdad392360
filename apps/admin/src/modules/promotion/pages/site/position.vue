<template>
  <uc-layout-list title="现场活动点位">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="positionFormState.title" placeholder="请输入活动点位名称" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetPositionFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-space>
        <a-button type="primary" class="m-r-10" @click="router.back()"> 返回 </a-button>
        <a-button type="primary" :disabled="isRead" @click="setModalVisible(true)"> 新增点位 </a-button>
        <a-dropdown class="m-l-10">
          <template #overlay>
            <a-menu @click="exportExcel">
              <a-menu-item key="xlsx">
                导出Excel
              </a-menu-item>
              <a-menu-item key="csv">
                导出Csv
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>
            导出点位
          </a-button>
        </a-dropdown>
        <a-button class="m-l-10" @click="setExportModalVisible(true)"> 导出小程序码 </a-button>
        <a-button type="link" :href="exportSitePositionTemplateApi" class="flex flex-cc">
          <uc-svg-icon name="download" class="m-r-8" />
          <span>下载模板</span>
        </a-button>
        <a-upload :action="importSitePositionApi(act_id)" :show-upload-list="false" @change="fileChange">
          <a-button type="link" :disabled="isRead" class="flex flex-cc">
            <uc-svg-icon name="tolead" class="m-r-8" />
            <span>导入点位</span>
          </a-button>
        </a-upload>
      </a-space>
    </template>
    <template #list>
      <a-table
        :columns="columns"
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <template #operation="{ record }">
          <a-button type="link" @click="copyLinkByRoute('siteList', { id: act_id, pointId: record.id, tc: record.track_code})"> 链接 </a-button>
          <a-popconfirm
            placement="left"
            title="你确定要删除该数据么？"
            :disabled="isRead"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" class="danger" :disabled="isRead"> 删除 </a-button>
          </a-popconfirm>
        </template>
      </a-table>
      <a-modal v-model:visible="modalVisible" title="添加点位" @ok="onSubmit" @cancel="setModalVisible(false)">
        <div class="body">
          <a-form ref="formStateRef" :model="formState" autocomplete="off">
            <a-form-item label="点位名称" name="title" class="required">
              <a-input v-model:value="formState.title" />
            </a-form-item>
            <a-form-item label="详细地址" name="address" class="required">
              <a-input v-model:value="formState.address" />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
      <a-modal v-model:visible="exportModalVisible" title="点位导出" @cancel="setExportModalVisible(false)">
        <div class="body">
          <a-form :model="exportFormState" autocomplete="off">
            <a-form-item label="邮箱地址" name="email" class="required">
              <a-input v-model:value="exportFormState.email" />
            </a-form-item>
          </a-form>
        </div>
        <template #footer>
          <a-button @click="setExportModalVisible(false)"> 取消 </a-button>
          <a-button type="primary" :loading="exportLoading" @click="onExport"> 确定 </a-button>
        </template>
      </a-modal>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import {
  siteApi,
  sitePositionApi,
  deleteSitePositionApi,
  exportSitePositionTemplateApi,
  importSitePositionApi,
  exportSitePositionApi,
  exportSitePositionInfoApi
} from '../../api'
import { useModalVisible } from '@/composables/useToggles'
import { useLoading } from '@/composables/useToggles'
import { useTimeStatus } from '@/composables/useTimeStatus'

const router = useRouter()
const { act_id } = useRoute().params
const { TIME_STATUS_ENDED } = useTimeStatus()

const isRead = ref(false)
const init = () => {
  if (act_id) {
    siteApi.get(act_id).then(res => {
      if (res.status === TIME_STATUS_ENDED) isRead.value = true
    })
  }
}

onMounted(init)

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  sitePositionApi(act_id).paginator({
    filters: useTransformQuery(positionFormState.value, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const {
  formState: positionFormState,
  onRestFormState: onRestPositionFormState,
  resetFormState: resetPositionFormState
} = useFormState({
  title: undefined,
  address: undefined,
})
onRestPositionFormState(() => setPage())

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  address: undefined
})
onRestFormState(() => setPage())

const {
  formState: exportFormState,
  setFormRules,
  validateForm
} = useFormState({
  email: undefined
})

setFormRules({
  email: { required: true, message: '请输入有效的邮箱地址', type: 'email' }
})

const { modalVisible, setModalVisible } = useModalVisible()
const { modalVisible: exportModalVisible, setModalVisible: setExportModalVisible } = useModalVisible()
const { loading: exportLoading, setLoading: setExportLoading } = useLoading()

const handleCityChange = e => {
  formState.value.province = e[0]
  formState.value.city = e[1]
  formState.value.county = e[2]
}

const handleDelete = async ({ id }) => {
  await deleteSitePositionApi(act_id).delete(id)
  message.success('删除完成')
  setPage()
}

// 导入
const fileChange = ({ file: { status, response } }) => {
  switch (status) {
    case 'error':
      message.error(response?.tips ?? '上传文件失败')
      return
    case 'done':
      if (response.data) {
        message.success(`导入成功`)
        setPage()
      }
      break
  }
}

const onExport = async () => {
  if (!(await validateForm())) return
  setExportLoading(true)
  await exportSitePositionApi(act_id).create(exportFormState.value)
  setExportLoading(false)
  message.success(`导出成功，请前往邮箱查看`)
  setExportModalVisible(false)
}

const onSubmit = async () => {
  await sitePositionApi(act_id).create({
    ...formState.value,
  })
  setModalVisible(false)
  resetFormState()
}

const columns = [
  {
    title: '点位名称',
    dataIndex: 'title',
    key: 'title'
  },
  {
    title: '校验码',
    dataIndex: 'secret',
    key: 'secret'
  },
  {
    title: '详细地址',
    dataIndex: 'address',
    key: 'address'
  },
  {
    title: '操作',
    key: 'operation',
    slots: { customRender: 'operation' },
    width: 130
  }
]

const exportExcel = async ({ key }) => {
  const filters = useTransformQuery(positionFormState.value, {
    title: 'like',
  })
  let excelExportUrlQuery = encodeURI(`${exportSitePositionInfoApi(act_id, key)}&filters=${JSON.stringify(filters)}`)
  window.open(excelExportUrlQuery)
}
</script>
