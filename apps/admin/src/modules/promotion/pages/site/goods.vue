<template>
  <uc-layout-list title="现场活动商品">
    <!-- <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入活动商品名称" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template> -->
    <template #extra>
      <a-button type="primary" class="m-r-10" @click="router.back()"> 返回 </a-button>
      <a-button type="primary" :disabled="isRead" @click="setModalVisible(true)"> 新增活动商品 </a-button>
    </template>
    <template #list>
      <a-table
        :columns="columns"
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <template #image_url="{ record }">
          <uc-img-text
            v-bind="record"
            :url="record.image_url"
            :title="record.goods.title"
          />
        </template>
        <template #attrs="{ record }">
          {{ useSpecAttr(record.spec.attrs) }}
        </template>
        <template #price="{ record }">
          {{ `¥${$formatters.thousandSeparator(record.spec.price)}` }}
        </template>
        <template #activity_price="{ record }">
          {{ `¥${$formatters.thousandSeparator(record.activity_price)}` }}
        </template>
        <template #operation="{ record }">
          <a-button type="link" @click="editGoods(record)"> 编辑 </a-button>
          <a-popconfirm
            placement="left"
            title="你确定要删除该数据么？"
            :disabled="isRead"
            ok-text="确定"
            cancel-text="取消"
            @confirm="handleDelete(record)"
          >
            <a-button type="link" class="danger" :disabled="isRead"> 删除 </a-button>
          </a-popconfirm>
        </template>
      </a-table>
      <goods-select v-model:visible="modalVisible" :activity-id="act_id" @ok="onSubmit" />
      <goods-edit v-model:visible="editModalVisible" :goods-id="currentGoodsId" :act-id="act_id" @ok="onEditSubmit" />
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import {
  siteApi,
  siteGoodsApi,
  deleteSiteGoodsApi
} from '../../api'
import { useModalVisible } from '@/composables/useToggles'
import GoodsSelect from './goods-select.vue'
import GoodsEdit from './goods-edit.vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useSpecAttr } from '@/modules/promotion/useTransform'

const router = useRouter()
const { act_id } = useRoute().params
const { TIME_STATUS_ENDED } = useTimeStatus()

const isRead = ref(false)
const init = () => {
  if (act_id) {
    siteApi.get(act_id).then(res => {
      if (res.status === TIME_STATUS_ENDED) isRead.value = true
    })
  }
}

onMounted(init)

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  siteGoodsApi(act_id).paginator({
    filters: useTransformQuery(formState.value, {
      title: 'like'
    }),
    relations: ['goods', 'spec'],
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined
})

onRestFormState(() => setPage())

const { modalVisible, setModalVisible } = useModalVisible()

const handleDelete = async ({ id }) => {
  await deleteSiteGoodsApi(act_id).delete(id)
  message.success('删除完成')
  setPage()
}

const { modalVisible: editModalVisible, setModalVisible: setEditModalVisible } = useModalVisible()

const currentGoodsId = ref(0)

const editGoods = ({id}) => {
  currentGoodsId.value = id
  setEditModalVisible(true)
}

const onSubmit = async list => {
  const goods = list.map(item => ({
    goods_id: item.goods_id,
    sku: item.sku,
    activity_price: item.activity_price * 100,
    stock: item.activity_stock,
    limit: item.activity_limit,
    image_url: item.photo_url,
    goods_title: item.goods?.title,
  }))
  await siteGoodsApi(act_id).create({ goods })
  setPage()
}

const onEditSubmit = () => {
  setPage()
}

const columns = [
  {
    title: '商品名称',
    dataIndex: 'image_url',
    key: 'image_url',
    ellipsis: true,
    slots: { customRender: 'image_url' }
  },
  {
    title: '规格',
    dataIndex: 'spec.attrs',
    key: 'attrs',
    slots: { customRender: 'attrs' },
    ellipsis: true,
    width: 200,
  },
  {
    title: 'sku',
    dataIndex: 'sku',
    key: 'sku',
    width: 140,
  },
  {
    title: '销售价格',
    dataIndex: 'spec.price',
    key: 'price',
    slots: { customRender: 'price' },
    width: '120px',
  },
  {
    title: '活动价格',
    dataIndex: 'activity_price',
    key: 'activity_price',
    slots: { customRender: 'activity_price' },
    width: '120px',
  },
  {
    title: '库存',
    dataIndex: 'stock',
    key: 'stock',
    width: '100px',
  },
  {
    title: '每人限购',
    dataIndex: 'limit',
    key: 'limit',
    width: '120px',
  },
  {
    title: '操作',
    key: 'operation',
    slots: { customRender: 'operation' },
    width: 130
  }
]
</script>
