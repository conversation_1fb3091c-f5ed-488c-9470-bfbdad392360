<template>
  <a-modal v-bind="{ visible }" title="选择商品" width="1340px" @cancel="onClose">
    <a-form layout="inline" class="m-b-20">
      <a-form-item name="title">
        <a-input-group class="flex" compact>
          <a-select
            v-model:value="formState.conditionKey"
            class="w-120"
            :options="skuOrTitleType.options()"
            @change="changeConditionKey"
          />
          <a-input v-model:value.trim="formState.conditionValue" placeholder="请输入关键词" class="w-300" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-model:value="formState.category_id"
          placeholder="商品分类"
          :options="categories"
          :field-names="{ label: 'title', value: 'id' }"
          change-on-select
          class="w-120"
        />
      </a-form-item>
      <a-form-item>
        <a-input v-model:value="formState.minPrice" placeholder="最低价格" class="w-100" />
        <span class="p-10">-</span>
        <a-input v-model:value="formState.maxPrice" placeholder="最高价格" class="w-100" />
      </a-form-item>
      <a-form-item class="m-r-0">
        <a-button type="primary" class="m-r-10" @click="setPage()"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </a-form>
    <!-- 列表 -->
    <a-table
      :data-source="listData"
      row-key="id"
      :loading="loading"
      :pagination="stdPagination(data)"
      :row-selection="rowSelection"
      @change="setPage"
    >
      <a-table-column title="商品图片" width="100px">
        <template #default="{ record }">
          <a-image :src="record.photo_url" :width="50" :height="50" style="height: 100%" />
        </template>
      </a-table-column>
      <a-table-column title="商品名称" width="250px">
        <template #default="{ record }">
          {{ record.goods.title }}
        </template>
      </a-table-column>
      <a-table-column title="商品规格" width="200px">
        <template #default="{ record }">
          {{ getSpecs(record) }}
        </template>
      </a-table-column>
      <a-table-column title="sku" width="150px">
        <template #default="{ record }">
          {{ record.sku }}
        </template>
      </a-table-column>
      <a-table-column title="销售价格" width="100px">
        <template #default="{ record }">
          {{ `¥${$formatters.thousandSeparator(record.price)}` }}
        </template>
      </a-table-column>
      <a-table-column title="活动价格" width="100px">
        <template #default="{ record }">
          <a-input-number
            v-model:value="record.activity_price"
            :min="0"
            :disabled="!selectedKeys.includes(record.id)"
            style="width: 80px"
          />
        </template>
      </a-table-column>
      <a-table-column title="库存" width="100px">
        <template #default="{ record }">
          <a-input-number
            v-model:value="record.activity_stock"
            :min="0"
            :max="record.stock?.stock - record.stock?.hold"
            :disabled="!selectedKeys.includes(record.id)"
            style="width: 80px"
          />
        </template>
      </a-table-column>
      <a-table-column title="可用库存" width="100px">
        <template #default="{ record }">
          {{ record.stock?.stock - record.stock?.hold }}
        </template>
      </a-table-column>
      <a-table-column title="每人限购" width="100px">
        <template #default="{ record }">
          <a-input-number
            v-model:value="record.activity_limit"
            :min="0"
            :disabled="!selectedKeys.includes(record.id)"
            style="width: 80px"
          />
        </template>
      </a-table-column>
    </a-table>
    <template #footer>
      <div class="flex flex-sb flex-cc">
        <div>
          已选择 <span class="color-danger">{{ selectedKeys.length }}</span> 个商品
        </div>
        <div>
          <a-button @click="onClose"> 取消 </a-button>
          <a-button type="primary" @click="onSubmit"> 确认 </a-button>
        </div>
      </div>
    </template>
  </a-modal>
</template>
<script>
export default {
  name: 'GoodsSelect'
}
</script>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useCategories } from '@/modules/goods/useCategory'
import { useSpecAttr } from '@/modules/promotion/useTransform'
import { skuOrTitleType } from '@/modules/promotion/enums'
import { goodsSpecsApi } from '@/modules/promotion/api'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'

const emit = defineEmits(['update:visible', 'ok', 'cancel'])
const props = defineProps({
  // 显示状态
  visible: {
    type: Boolean,
    default: false
  },
  // 活动id
  activityId: {
    type: Number
  }
})

// 商品分类
const { categories } = useCategories()

const { formState, resetFormState, onRestFormState } = useFormState({
  conditionKey: 'sku',
  conditionValue: undefined,
  category_id: [],
  minPrice: undefined,
  maxPrice: undefined
})
const getSpecs = computed(() => item => useSpecAttr(item.attrs)) // 获取规格

const getSku = () => (formState.value.conditionKey === 'sku' ? formState.value.conditionValue : undefined)

const getCategoryId = () =>
  formState.value.category_id.length
    ? { goods: { category_id: formState.value.category_id[formState.value.category_id.length - 1] } }
    : {}

// 获取标题
const getTitle = () => {
  const option = {}
  const title = formState.value.conditionKey == 'title' ? formState.value.conditionValue : undefined
  title && (option.goods = { title: `%${title}%` })
  return option
}

// 获取价格
const getPrice = () => {
  const price = []
  if (formState.value.minPrice) {
    price[0] = formState.value.minPrice * 100
  }
  if (formState.value.maxPrice) {
    price[1] = formState.value.maxPrice * 100
  }
  return price
}

const { data, setPage, page, loading } = usePaginatorApiRequest(
  ({ offset, limit }) =>
    goodsSpecsApi.paginator({
      filters: {
        ...useTransformQuery(
          {
            price: getPrice(),
            sku: getSku()
          },
          { title: 'like', price: 'range', sku: 'like' }
        ),
        exclude_promotions: {
          site: props.activityId
        }
      },
      relation_filters: {
        ...getCategoryId(),
        ...getTitle()
      },
      relations: ['goods', 'stock'],
      offset,
      limit
    }),
  { current: 1, pageSize: 10 }
)

// 关闭显示状态
const onClose = () => {
  selectedGoods.value = []
  selectedKeys.value = []
  emit('update:visible', false)
  emit('cancel')
}

const listData = ref([]) // 表格数据
watch(
  data,
  ({ items }) => {
    listData.value = useTransformList(items)
  },
  { deep: true }
)

watch(
  () => props.visible,
  value => {
    if (value) {
      onRestFormState(() => {
        setPage()
      })
      resetFormState()
    }
  }
)

// 转换列表数据
const useTransformList = newList => {
  const listData = cloneDeep(newList)
  listData.forEach(item => {
    item.activity_price = item.price / 100
    item.activity_stock = 0
    item.activity_limit = 0
  })
  return listData
}

// 点击确定按钮
const onSubmit = () => {
  const list = selectedGoods.value
  if (!list.length) {
    message.error('请选择商品')
  } else {
    emit('ok', list)
    onClose()
  }
}

// table列表选择的id
const selectedGoods = ref([])
const selectedKeys = ref([])
const rowSelection = {
  selectedRowKeys: selectedKeys,
  onChange: selectedRowKeys => {
    selectedKeys.value = selectedRowKeys
    const ids = selectedGoods.value.map(item => item.id)
    const diffIds = selectedRowKeys.filter(key => !ids.includes(key))
    const addGoods = listData.value.filter(item => diffIds.includes(item.id))
    selectedGoods.value.push(...addGoods)
  }
}
</script>
