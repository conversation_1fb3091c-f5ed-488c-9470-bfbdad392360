<template>
  <uc-layout-list title="活动发券">
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.title" class="input-width" placeholder="请输入活动名称" />
      </a-form-item>
      <a-form-item>
        <a-select
          v-model:value="formState.status"
          class="w-120"
          :options="statusList"
          placeholder="活动状态"
          allow-clear
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="onAdd"> 新增活动发券 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="活动名称/时间" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :title="record.title"
              :subtit="record.start_time + ' ~ ' + $formatters.transformActivityEndTime(record.end_time)"
            />
          </template>
        </a-table-column>
        <a-table-column title="优惠码导出总数" width="130px" align="right">
          <template #default="{ record }">
            <router-link
              :to="{
                name: 'promotion-activity-dispatch-coupon-code',
                params: { id: record.id, title: record.title }
              }"
            >
              {{ record.code_num }}
            </router-link>
          </template>
        </a-table-column>
        <a-table-column title="领取人/张" width="130px" align="right">
          <template #default="{ record }"> {{ record.send_user_num }} / {{ record.send_coupon_num }} </template>
        </a-table-column>
        <a-table-column title="核销人/张" width="130px" align="right">
          <template #default="{ record }"> {{ record.used_user_num }} / {{ record.used_coupon_num }} </template>
        </a-table-column>
        <a-table-column title="转化率" width="120px" align="right">
          <template #default="{ record }">
            {{ $formatters.toPercent(record.convert_ratio) }}
          </template>
        </a-table-column>
        <a-table-column title="活动状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="210px">
          <!-- 编辑/删除（未开始）、数据/编辑（进行中）、数据/查看（已结束） -->
          <template #default="{ record }">
            <a-button v-if="!isEnded(record)" type="link" @click="handleOpenModal(record)"> 导出优惠码 </a-button>
            <a-button type="link" @click="copyLinkByRoute('couponReceive', { id: record.id })"> 链接 </a-button>
            <a-button v-if="!isEnded(record)" type="link" @click="onEdit(record)"> 编辑 </a-button>
            <a-button v-if="isEnded(record)" type="link" @click="onEdit(record)"> 查看 </a-button>
            <!-- v-if="record.status === 'not_start'" 到时候有数据页面将这个条件加入 -->
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              :disabled="!record.can_delete"
              @confirm="handleDelete(record)"
            >
              <template #icon>
                <uc-ant-icon name="QuestionCircleOutlined" type="danger" />
              </template>
              <a-button type="link" class="danger" :disabled="!record.can_delete"> 删除 </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
  <a-modal :visible="modalVisible" title="导出号码" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '110px' } }">
      <a-form-item label="优惠券名称" class="required">
        <a-input v-model:value="modalFormState.title" disabled />
      </a-form-item>
      <a-form-item label="导出号码数量" class="required">
        <a-input-number
          v-model:value="modalFormState.num"
          :min="1"
          :max="10000"
          placeholder="请输入导出号码数量"
          :formatter="$formatters.number"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)"> 取消 </a-button>
      <a-button type="primary" :loading="loading" @click="handleExportCode"> 确定 </a-button>
    </template>
  </a-modal>
</template>
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { message } from 'ant-design-vue'
import { activityCouponApi, exportActivityCouponApi } from '../../api'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { useModalVisible } from '@/composables/useToggles'

const { activity_id } = useRoute().query
const linkPath = Object.freeze(`/promotion/pages/standard/send-soupon/index?id=`)

const { statusList, statusFilter } = useTimeStatus()

const router = useRouter()

const { formState, resetFormState, onRestFormState } = useFormState({ id: activity_id })

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  activityCouponApi.paginator({
    filters: useTransformQuery(formState.value, { title: 'like', status: '=' }),
    offset,
    limit
  })
)

onRestFormState(() => setPage())

const isEnded = computed(() => record => record.status === 'ended')

// 新增
const onAdd = () => {
  router.push({
    name: 'promotion-activity-dispatch-coupon-add'
  })
}

// 编辑-数据
const onEdit = ({ id }) => {
  router.push({
    name: 'promotion-activity-dispatch-coupon-edit',
    params: { id }
  })
}

// 删除
const handleDelete = ({ id }) => {
  activityCouponApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}

const {
  formState: modalFormState,
  setFormState: setModalFormState,
  validateForm: validateModalForm,
  setFormRules: setModalFormRules
} = useFormState()

setModalFormRules({
  num: {
    required: true,
    message: '请输入导出号码数量'
  }
})

const { modalVisible, setModalVisible } = useModalVisible()

const handleOpenModal = e => {
  setModalFormState({ title: e.title, num: undefined, id: e.id })
  setModalVisible(true)
}

// 导出优惠码
const handleExportCode = async () => {
  if (!(await validateModalForm())) {
    return
  }
  setModalVisible(false)
  const { id, num } = modalFormState.value
  window.location.href = exportActivityCouponApi(id, num)
}
</script>
<style scoped lang="less"></style>
