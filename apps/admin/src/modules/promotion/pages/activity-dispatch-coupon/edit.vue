<template>
  <uc-layout-form class="m-edit" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="活动时间：" name="rangeTime" class="required range-time">
            <a-date-picker
              v-model:value="formState.start_time"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
              :disabled="isEdit && formState.status !== TIME_STATUS_NOSTART"
              class="w-240"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              :disabled-date="disabledEndTime"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              show-time
              placeholder="结束时间"
              class="w-240"
              :disabled="disabledAll"
            />
          </a-form-item>
          <a-form-item label="活动名称" name="title" class="required">
            <a-input
              v-model:value.trim="formState.title"
              class="w-500"
              placeholder="请输入活动名称，不超过100字"
              :maxlength="100"
              :disabled="disabledAll"
            />
          </a-form-item>
          <a-form-item label="活动标签">
            <a-input
              v-model:value.trim="formState.tag"
              class="w-500"
              placeholder="请输入活动标签，不超过10字"
              :max-length="10"
              :disabled="disabledAll"
            />
          </a-form-item>
          <a-form-item label="是否允许分享" name="is_share">
            <a-radio-group v-model:value="formState.is_share">
              <a-radio :value="1">允许</a-radio>
              <a-radio :value="0">不允许</a-radio>
            </a-radio-group>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="活动规则">
          <a-form-item label="领券条件" name="receive_rules">
            <a-select
              v-model:value="formState.receive_rules"
              mode="multiple"
              allow-clear
              placeholder="请选择领券条件"
              :options="receiveCouponsRules.options()"
            />
          </a-form-item>
          <a-form-item label="资格api" name="title">
            <a-input
              v-model:value.trim="formState.requirement"
              class="w-500"
              placeholder="请填写资格api"
            />
          </a-form-item>
          <a-form-item label="领券入口" name="range" class="required">
            <a-checkbox-group v-model:value="formState.range" :options="couponEntrance.options()" />
          </a-form-item>
          <a-form-item
            v-for="(rule, ruleIndex) in formState.rules"
            :key="ruleIndex"
            :label="ruleIndex ? ' ' : '活动发券'"
            :colon="!ruleIndex"
            :class="{ required: !ruleIndex }"
            class="m-b-10"
          >
            <div v-for="(item, index) in rule.award_settings" :key="index">
              <a-space :size="10" class="activity-rule-item m-b-0" wrap>
                <a-select
                  v-model:value="item.option_id"
                  :options="item.options"
                  placeholder="请选择优惠券"
                  class="w-300"
                  allow-clear
                  show-search
                  :show-arrow="false"
                  :filter-option="false"
                  :not-found-content="null"
                  :disabled="item.disabled"
                  @search="handleSearchGifts($event, item)"
                  @focus="handleShowGifts(item)"
                />
                <a-input-number
                  v-model:value="item.stock"
                  placeholder="总发放数量"
                  :min="0"
                  class="w-100"
                  :disabled="item.disabled"
                />
                <a-input-number
                  v-model:value="item.limit"
                  placeholder="每人限领"
                  :min="1"
                  class="w-100"
                  :disabled="item.disabled"
                />
                <!-- <a-input-number
              v-model:value="item.quantity"
              placeholder="发放数量"
              :min="1"
              class="w-100"
              :disabled="item.disabled"
            /> -->

                <a-button
                  shape="circle"
                  size="small"
                  class="delete-btn"
                  type="link"
                  :disabled="item.disabled || formState.rules.length == 1"
                >
                  <template #icon>
                    <uc-ant-icon name="CloseCircleFilled" type="danger" @click="onRemoveSend(ruleIndex)" />
                  </template>
                </a-button>
              </a-space>
              <a-space :size="10" class="activity-rule-item">
                <a-select
                  v-model:value="item.user_tags_range"
                  mode="multiple"
                  show-search
                  :filter-option="userTagFilterOption"
                  allow-clear
                  placeholder="用户标签限制"
                  :options="userTagOptions"
                  class="w-500 m-t-10"
                />
              </a-space>
            </div>
            <a-button v-if="ruleIndex === formState.rules.length - 1" type="link" class="p-0" @click="onAddSend">
              添加发放层级
            </a-button>
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="页面配置" class="h-fill">
          <a-form-item label="皮肤模式" class="required">
            <a-select
              v-model:value="formState.config.skin_mode"
              class="m-r-10 w-310"
              placeholder="请选择皮肤"
              allow-clear
              :options="skinMode.option()"
              :disabled="read"
            />
            <a-button class="p-lr-0 relative" type="link">
              预览效果图
              <div class="w-fill h-fill hide absolute top-0">
                <a-image class="opacity-0" :src="assets.couponPreviewUrl" />
              </div>
            </a-button>
          </a-form-item>
          <a-form-item label="颜色配置" class="required">
            <a-space :size="10">
              <a-input
                v-model:value.trim="formState.config.color.subject"
                style="width: 160px"
                placeholder="主题色，如#000000"
                :disabled="read"
              />
              <a-input
                v-model:value.trim="formState.config.color.bg"
                style="width: 160px"
                placeholder="背景色，如#000000"
                :disabled="read"
              />
            </a-space>
          </a-form-item>
          <a-form-item label="页面配置" name="title" class="required">
            <a-space>
              <uc-upload
                v-model:list="formState.config.page.activity_bg"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动背景"
                :disabled="read"
              />
              <uc-upload
                v-model:list="formState.config.page.activity_text"
                upload-text=" "
                :max-length="1"
                show-label
                label-text="活动文案"
                :disabled="read"
              />
            </a-space>
          </a-form-item>
          <!-- <a-form-item label="弹窗配置" name="title" class="required">
        <a-space>
          <uc-upload
            v-model:list="formState.config.popup.prepare"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="未开始"
            :disabled="read"
          />
          <uc-upload
            v-model:list="formState.config.popup.end"
            upload-text=" "
            :max-length="1"
            show-label
            label-text="已结束"
            :disabled="read"
          />
        </a-space>
      </a-form-item> -->
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.desc" placeholder="请输入活动说明（不超过500字）" :height="400" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { cloneDeep } from 'lodash'
import { activityCouponApi } from '../../api'
import { couponEntrance, skinMode, receiveCouponsRules } from '../../enums'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { debounce } from 'lodash-es'
import { useCouponOptions } from '../../useCoupon'
import { colorVerify } from '@/utils/index'
import { useTransformImg } from '@/composables/useTransformFormat'
import { usePrizeAwardUpdate, useTransformPrize } from '../../usePrizeAward'
import assets from '../../assets.config'
import { useUserTag } from '@/modules/member/useUserTag'

const { userTagOptions, userTagFilterOption } = useUserTag()

const { onPrizeDefault, handleAward, handleShowGifts, handleSearchGifts } = usePrizeAwardUpdate()
const { transformPrizesRequest } = useTransformPrize()
const { batchTransformImg } = useTransformImg()
const { TIME_STATUS_NOSTART } = useTimeStatus()

const defaultRule = () => ({
  award_settings: [onPrizeDefault({ type: 'coupon' })]
})

const router = useRouter()

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  status: TIME_STATUS_NOSTART,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  tag: undefined,
  range: undefined,
  receive_rules: undefined,
  requirement: undefined,
  rules: [defaultRule()],
  config: {
    skin_mode: skinMode.default,
    color: {},
    page: {},
    popup: {}
  },
  desc: undefined,
  is_share: 1,
})

const { id } = useRoute().params

const { disabledStartTime, disabledEndTime, disabledEndTimeBeforeNow } = useDisabledDate(formState)

const disabledPart = ref(false) // 部分禁用
const disabledAll = ref(false) // 全部禁用

const setDisabledState = status => {
  switch (status) {
    case 'normal': // 进行中部分禁用
      disabledPart.value = true
      break
    case 'ended': // 结束全部禁用
      disabledAll.value = true
      break
  }
}

let isEdit = ref(false)

if (id) {
  isEdit.value = true
  const hideLoading = message.loading('正在加载数据...')
  activityCouponApi
    .get(id, { relations: ['rules'] })
    .then(async data => {
      const { page, popup } = data.config
      // handle images
      batchTransformImg(page)
      batchTransformImg(popup)
      const showPrizePrm = []

      data.rules.forEach(rule => {
        rule.award_settings.forEach(item => {
          if (isEdit && data.status !== TIME_STATUS_NOSTART) item.disabled = true

          item.user_tags_range = item.user_tags_range ? item.user_tags_range : []
          showPrizePrm.push(handleAward(item, { id: item.option_id }))
        })
      })

      data.receive_rules = data.receive_rules || undefined

      await Promise.all(showPrizePrm)
      setFormState(data)
      setDisabledState(data.status)
    })
    .finally(hideLoading)
}

// 已经选中的优惠券
const excludeId = ref([])

// 监听rules---获取已经选中的优惠券id
watch(
  formState,
  newValue => {
    const { rules } = newValue
    if (rules) excludeId.value = rules.map(item => item.coupon_id).filter(item => item)
  },
  {
    deep: true,
    immediate: true
  }
)

// 优惠券列表
const couponList = ref([
  { label: 1, value: 1 },
  { label: 2, value: 2 },
  { label: 3, value: 3 }
])

// 初始化20条数--分类列表
const initList = async () => {
  const { couponOptions } = await useCouponOptions(20, {
    id: excludeId.value.length ? `![${[excludeId.value]}]` : undefined
  })
  couponList.value = couponOptions
}
initList()

const filterOption = (inputValue, { label }) => {
  return !!label && label.includes(inputValue)
}

let handleSearch = async value => {
  if (!!value) {
    const { couponOptions } = await useCouponOptions(20, {
      title: `%${value}%`,
      id: excludeId.value.length ? `![${[excludeId.value]}]` : undefined
    })
    couponList.value = couponOptions
  } else {
    initList()
  }
}

handleSearch = debounce(handleSearch, 300)

setFormRules({
  start_time: { required: true, message: '请选择活动开始时间' },
  end_time: { required: true, message: '请选择活动结束时间' },
  title: { required: true, message: '请输入活动名称' },
  range: { required: true, message: '请选择领券入口' },
  rules: {
    validator(_, value) {
      for (const { award_settings } of value) {
        for (const { option_id, stock, quantity, limit } of award_settings) {
          if (!option_id) return Promise.reject('请选择活动发券优惠券')
          if (stock === undefined) return Promise.reject('请输入活动发券总发放数量')
          if (!limit) return Promise.reject('请输入活动发券每人限领')
          // if (!quantity) return Promise.reject('请输入活动发券每人发放数量')
        }
      }
      return Promise.resolve()
    }
  },
  config: {
    validator(_, value) {
      const { color, page } = value
      if (!value.skin_mode) return Promise.reject('请选择皮肤模式')
      if (!colorVerify(color.subject)) return Promise.reject('请输入主题色(以#开头+6位数字字母组合)')
      if (!colorVerify(color.bg)) return Promise.reject('请输入背景色(以#开头+6位数字字母组合)')
      if (!page.activity_bg) return Promise.reject('请上传活动背景图片')
      if (!page.activity_text) return Promise.reject('请上传活动文案图片')
      // if (!popup.prepare) return Promise.reject('请上传未开始图片')
      // if (!popup.end) return Promise.reject('请上传已结束图片')
      return Promise.resolve()
    }
  },
  desc: { required: true, message: '请输入活动说明 ' }
})

const handleSubmit = async () => {
  if (!(await validateForm())) return

  const params = cloneDeep(formState.value)
  // handle images
  const { page } = params.config
  batchTransformImg(page, 'string')
  // batchTransformImg(popup, 'string')

  id ? await activityCouponApi.update(id, params) : await activityCouponApi.create(params)
  message.success('操作完成')
  router.back()
}

const onRemoveSend = index => formState.value.rules.splice(index, 1)

const onAddSend = () => {
  if (formState.value.rules.length == 5) return message.info('最多可添加5个层级')
  formState.value.rules.push(defaultRule())
}
</script>
<style scoped lang="less">
.m-edit {
  .upload-small-cover {
    :deep(.ant-upload-picture-card-wrapper) {
      width: 32px;
      height: 32px;
    }

    :deep(.ant-upload) {
      height: 32px;
      margin: 0 !important;
    }
  }

  .separator {
    .inline-block();
    width: 20px;
    text-align: center;
  }

  .delete-btn {
    padding-top: 2px;
    border: none;

    :deep(.anticon) {
      font-size: 20px;
    }
  }

  .delete-btn:disabled {
    :deep(.anticon) {
      color: #d9d9d9 !important;
    }
  }
}
</style>
