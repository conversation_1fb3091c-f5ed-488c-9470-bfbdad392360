<template>
  <uc-layout-list>
    <template #filter>
      <a-form-item>
        <a-input v-model:value.trim="formState.code" placeholder="请输入券码" />
      </a-form-item>
      <a-form-item>
        <a-select v-model:value="formState.status" placeholder="兑换状态" :options="exchangeState.options()" />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="formState.created_at" :placeholder="['导出开始时间', '导出结束时间']" />
      </a-form-item>
      <a-form-item>
        <a-range-picker v-model:value="formState.used_at" :placeholder="['兑换开始时间', '兑换结束时间']" />
      </a-form-item>
      <a-form-item>
        <a-input v-model:value.trim="formState.phone_number" placeholder="请输入用户手机号" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" :href="handleExport()"> 导出明细 </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="优惠码">
          <template #default="{ record }">{{ record.code }}</template>
        </a-table-column>
        <a-table-column title="导出时间">
          <template #default="{ record }">{{ record.created_at }}</template>
        </a-table-column>
        <a-table-column title="兑换时间">
          <template #default="{ record }">{{ record.used_at || '-' }}</template>
        </a-table-column>
        <a-table-column title="兑换用户手机号">
          <template #default="{ record }">
            {{ record?.user?.phone_number ? $formatters.numberEncryption(record.user.phone_number) : '-' }}
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useRouter, useRoute } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { activityCouponCodeApi, exportActivityCouponDataApi } from '../../api'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { exchangeState } from '@/modules/promotion/enums'

const { id, title } = useRoute().params

const { statusList, statusFilter } = useTimeStatus()

const router = useRouter()

const { formState, resetFormState, onRestFormState } = useFormState({
  code: undefined,
  created_at: undefined,
  used_at: undefined,
  phone_number: undefined
})

const queryParams = computed(() => {
  return {
    filters: useTransformQuery(
      {
        code: formState.value.code,
        created_at: formState.value.created_at,
        used_at: formState.value.used_at,
        status: formState.value.status,
        activity_dispatch_coupon_id: id
      },
      { code: 'like', created_at: 'dateRange', used_at: 'dateRange' }
    ),
    relation_filters: {
      user: {
        phone_number: formState.value.phone_number || undefined
      }
    }
  }
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  activityCouponCodeApi.paginator({
    filters: queryParams.value.filters,
    relation_filters: queryParams.value.relation_filters,
    offset,
    limit,
    relations: ['user']
  })
)

onRestFormState(() => setPage())

const handleExport = () => {
  const params = JSON.parse(JSON.stringify(queryParams.value))
  params.filters = JSON.stringify(params.filters)
  params.relation_filters = JSON.stringify(params.relation_filters)
  params.relations = 'user'
  const url = encodeURI(
    `${exportActivityCouponDataApi}&${Object.entries(params)
      .map(([key, value]) => `${key}=${value}`)
      .join('&')}`
  )
  return url
}
</script>
<style scoped lang="less"></style>
