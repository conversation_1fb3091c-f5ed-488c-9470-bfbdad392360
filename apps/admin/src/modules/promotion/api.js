import { apiFactory } from '@/api'
import { useStore } from '@/store/auth'
const { state } = useStore()

export const couponApi = apiFactory.restful('/promotion/coupons')
export const couponStatsApi = apiFactory.restful(`/promotion/coupons-statistics`)
export const goodsApi = apiFactory.restful('/goods/goods')

export const activityCouponApi = apiFactory.restful('/coupon/activity-dispatch-coupon') // 活动发券
export const activityCouponCodeApi = apiFactory.restful('/coupon/activity-dispatch-coupon-code') // code
export const exportActivityCouponApi = (id, num) =>
  `${import.meta.env.VITE_API_BASE_URL}/coupon/activity-dispatch-coupon/${id}/export/${num}?token=${state.token}`

export const exportActivityCouponDataApi = `${
  import.meta.env.VITE_API_BASE_URL
}/coupon/activity-dispatch-coupon-code/export?token=${state.token}`

export const directionalCouponApi = apiFactory.restful('/coupon/directional-dispatch-coupon') // 定向发券
export const promotionFullMinusApi = apiFactory.restful('/promotion/activities/cash') // 满减促销
export const promotionLimitedTimeDiscountApi = apiFactory.restful('/promotion/activities/discount') // 限时折扣

export const goodsCategoriesApi = apiFactory.restful('/goods/categories') // 商品分类
export const goodsSpecsApi = apiFactory.restful('/goods/goods-specs')
export const goodsSpecsAction = apiFactory.command('/goods/goods-specs')
export const bargainApi = apiFactory.restful('/promotion/bargain/bargain-activities') // 砍价列表
export const bargainGoodsApi = apiFactory.restful('/promotion/bargain/bargain-goods') // 砍价商品
export const bargainDataApi = id => apiFactory.command(`/promotion/bargain/bargain-activities/${id}/statistics`) // 砍价数据
export const bargainRecordApi = apiFactory.restful('/promotion/bargain/bargain-records') // 砍价记录
export const awardOptionsApi = apiFactory.restful('/user-dispatch-prize/options') // 奖项选项
export const topGoodsApi = apiFactory.restful('/promotion/top-goods-draw-activities')
export const seckillApi = apiFactory.restful('/promotion/seckills') // 秒杀特价列表

export const markUpApi = apiFactory.restful('/promotion/markup-activities') // 加价购
export const giftApi = apiFactory.restful('/promotion/gift-activities') // 赠品促销

export const groupApi = apiFactory.restful('/promotion/group/group-activity') // 拼团
export const groupRecordApi = apiFactory.restful('/promotion/group/group-buy') // 拼团记录
export const groupDataApi = apiFactory.restful('/promotion/group/group-statistics/statistics') // 拼团数据
export const promotionActivitiesFreightApi = apiFactory.restful('/promotion/activities/freight') //运费促销

export const wholesaleApi = apiFactory.restful('/promotion/wholesale-activities') // 团购批发
export const wholesaleUsersApi = apiFactory.restful('/promotion/wholesale-users') // 参与用户

export const giftBoxApi = apiFactory.restful('/promotion/gift-box-activities') // 定制礼盒

// 下载参与用户模板
export const wholesaleUserTemplateApi = `${
  import.meta.env.VITE_API_BASE_URL
}/promotion/wholesale-users/export/template?token=${state.token}`

// 导入参与用户
export const wholesaleUserImportApi = activity_id =>
  `${import.meta.env.VITE_API_BASE_URL}/promotion/wholesale-users/import/${activity_id}?token=${state.token}`

export const siteApi = apiFactory.restful('/promotion/site-activities') // 现场活动
export const siteGoodsApi = activityId => apiFactory.restful(`/promotion/site-activities/${activityId}/goods`) // 现场活动商品
export const siteGoodsActionApi = activityId => apiFactory.restful(`/promotion/site-activities/${activityId}/goods`) // 现场活动商品
export const deleteSiteGoodsApi = activityId => apiFactory.restful(`/promotion/site-activities/${activityId}/goods`) // 删除现场活动商品

export const sitePositionApi = activityId => apiFactory.restful(`/promotion/site-activities/${activityId}/position`) // 现场活动点位
export const deleteSitePositionApi = activityId =>
  apiFactory.restful(`/promotion/site-activities/${activityId}/position`) // 删除现场活动点位
export const exportSitePositionTemplateApi = `${
  import.meta.env.VITE_API_BASE_URL
}/promotion/site-activities/position/export/template?token=${state.token}` // 下载现场活动点位模板
export const importSitePositionApi = activityId =>
  `${import.meta.env.VITE_API_BASE_URL}/promotion/site-activities/${activityId}/position/import?token=${state.token}` // 导入现场活动点位
export const exportSitePositionApi = activityId =>
  apiFactory.restful(`/promotion/site-activities/${activityId}/position/export/mail`) // 点位导出
export const exportSitePositionInfoApi = (activityId, type) =>
  `${import.meta.env.VITE_API_BASE_URL}/promotion/site-activities/${activityId}/position/export/${type}?token=${
    state.token
  }` // 点位信息导出


  
export const giftCardApi = apiFactory.restful('/promotion/gift-card-activities') // 礼品卡
export const giftCardCodeApi = apiFactory.restful('/promotion/gift-card-code') // 礼品卡号

export const exportGiftCardApi = (id, num) =>
  `${import.meta.env.VITE_API_BASE_URL}/promotion/gift-card-activities/${id}/export/${num}?token=${state.token}`
