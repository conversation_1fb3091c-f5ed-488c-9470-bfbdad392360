<template>
  <a-select
    v-model:value.trim="productSku"
    :options="options"
    placeholder="请输入商品SKU"
    allow-clear
    show-search
    :filter-option="false"
    :not-found-content="null"
    @search="handleSearch($event)"
    @change="onChange"
  />
</template>
<script>
export default {
  name: 'InputSku'
}
</script>
<script setup>
import { goodsSpecsApi } from '../api'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { useTransformOptions } from '@/composables/useTransformOptions'

const emit = defineEmits(['update:modelValue','change-sku'])

const props = defineProps({
  sku: {
    type: String,
    default: undefined
  }
})

const productSku = ref()

watch(
  props.sku,
  value => {
    productSku.value = value
  },
  { deep: true }
)

const onChange = (e, { price }) => {
  emit('update:modelValue', productSku.value)
  emit('change-sku', price / 100)
}

const options = ref([])

const handleSearch = v => {
  if (v === '') return
  goodsSpecsApi
    .paginator({
      page: 1,
      limit: 10,
      filters: useTransformQuery({ sku: v }, { sku: 'like' }),
      relation_filters: { goods: { on_sale: 1 } }
    })
    .then(res => {
      res.items.forEach(item => {
        item.label = `${item.sku} - ${item.attrs[0].value}`
      })
      options.value = useTransformOptions(res.items, 'label', 'sku', ['price'])
    })
}
</script>
