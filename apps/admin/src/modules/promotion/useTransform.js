import { ref } from 'vue'
import { cloneDeep } from 'lodash'
import { useBatchTransformMedia, useTransformMedia } from '@/composables/useTransformFormat'
import { couponThreshold } from './enums'
import formatters from '@/utils/formatters'

// 转换列表数据
export const useTransformList = newList => {
  const listData = cloneDeep(newList)
  listData.forEach(item => {
    item.selectState = false
  })
  return listData
}

// 转换规格
export const useSpecAttr = attrs => attrs?.reduce((prev, { value }, i) => prev + (i ? ' / ' : '') + value, '')

/**
 * 转换砍价编辑数据
 */
export const useTransformBargain = () => {
  const pageConfig = ref([
    { label: '活动背景', value: [], field: 'activity_bg' },
    { label: '活动文案', value: [], field: 'activity_text' },
    { label: '助力文案', value: [], field: 'help_text' }
    // { label: '助力成功', value: [], field: 'help_success' },
    // { label: '助力失败', value: [], field: 'help_fail' },
    // { label: '倒计时', value: [], field: 'count_down' }
  ])
  const modalConfig = ref([
    // { label: '未开始', value: [], field: 'not_start' },
    // { label: '已结束', value: [], field: 'ended' },
    { label: '砍价成功', value: [], field: 'bargain_success' },
    { label: '助力成功', value: [], field: 'help_success' },
    { label: '助力超限', value: [], field: 'help_limit' },
    { label: '助力已过期', value: [], field: 'help_expire' },
    { label: '仅限N次', value: [], field: 'help_only_once' },
    { label: '仅限新用户', value: [], field: 'only_new_user' },
    { label: '好友砍价已完成', value: [], field: 'help_already_success' }
  ])
  const shareConfig = ref([
    { label: '会话', value: [], field: 'friend_poster' },
    { label: '本地', value: [], field: 'friend_circle_poster' }
  ])

  const transformMedia = (list, data, field, type = 'array') => {
    const fields = field.split('.')
    let fieldVal = data
    fields.forEach(key => {
      fieldVal = fieldVal[key]
    })
    list.value.forEach(item => {
      if (type === 'array') {
        item.value = useTransformMedia(fieldVal[item.field], type)
      } else {
        fieldVal[item.field] = useTransformMedia(item.value, type)
      }
    })
  }

  const transformToShow = data => {
    const res = cloneDeep(data)
    if(res.page_setting) {
      transformMedia(pageConfig, res, 'page_setting.page')
      transformMedia(modalConfig, res, 'page_setting.popup')
    }
    transformMedia(shareConfig, res, 'share_setting')
    return res
  }

  const transformToRequest = data => {
    const form = cloneDeep(data)
    if(form.page_setting) {
      form.page_setting.page = {}
      form.page_setting.popup = {}
      transformMedia(pageConfig, form, 'page_setting.page', 'string')
      transformMedia(modalConfig, form, 'page_setting.popup', 'string')
    }
    transformMedia(shareConfig, form, 'share_setting', 'string')
    form.goods.forEach(item => {
      String(item.id).length === 17 && (item.id = undefined)
    })
    return form
  }

  return { pageConfig, modalConfig, shareConfig, transformToShow, transformToRequest }
}

/**
 * 转换特价秒杀编辑数据
 */
export const useTransformSeckill = () => {
  const transformRequest = data => {
    const form = cloneDeep(data)
    form.goods.forEach(item => {
      String(item.id).length === 17 && (item.id = undefined)
    })
    return form
  }

  return { transformRequest }
}

export const useMarkUpEdit = () => {
  // 转换成显示数据
  const transformToShow = data => {
    const cloneData = cloneDeep(data)
    if (cloneData.condition_type === couponThreshold.amount) {
      cloneData.condition /= 100
    }
    return cloneData
  }

  // 转换成请求数据
  const transformToRequest = data => {
    const cloneData = cloneDeep(data)
    if (cloneData.condition_type === couponThreshold.amount) {
      cloneData.condition = formatters.priceToInteger(cloneData.condition)
    }
    return cloneData
  }

  return { transformToShow, transformToRequest }
}
