import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { awardOptionsApi } from './api'
import { giftType } from './enums'
import { cloneDeep } from 'lodash'

export async function useAward(callback = null, limit, filters) {
  const awards = await awardOptionsApi.paginator({
    filters: { ...filters },
    offset: 1,
    limit,
    status: 'normal'
  })
  return callback(awards)
}

export async function useAwardFilter(callback = null, filters) {
  const awards = await awardOptionsApi.list({
    filters: { ...filters }
    // ,status:'normal' 新增时不传
  })
  return callback(awards)
}

export async function useAwardOptions(query) {
  const giftOptions = await useAward(
    awards => useTransformOptions(awards.items, 'prize_title', 'biz_id', ['type', 'biz_type', 'prize_url']),
    query
  )
  return { giftOptions }
}

export async function useAwardOptionsFilter(query) {
  const giftOptionsFilter = await useAwardFilter(
    awards => useTransformOptions(awards, 'prize_title', 'biz_id', ['type', 'biz_type', 'prize_url']),
    query
  )
  return { giftOptionsFilter }
}
