import { ref } from 'vue'
import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { goodsApi } from './api'

export async function useGoods(query) {
  const goodList = ref()
  let res = await goodsApi.list({...query,relations:['specs']})
  res.forEach(item=>item.key = item.id)
  goodList.value = res

  return {
    goodList
  }
}

