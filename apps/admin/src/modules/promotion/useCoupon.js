import { useApiRequest } from '@/composables/useApiRequest'
import { useTransformOptions } from '@/composables/useTransformOptions'
import { couponApi } from './api'

export async function useCoupon(callback = null, limit, filter) {
  const coupon = await couponApi.paginator({
    filters: { ...filter },
    offset: 1,
    limit,
  })
  return callback(coupon)
}

export async function useCouponOptions(limit, filter) {
  const couponOptions = await useCoupon((coupon) => useTransformOptions(coupon.items, 'title', 'id'), limit, filter)
  return { couponOptions }
}

// 未过期优惠券 
export async function useCouponDateOptions(callback,filter) {
  const coupon = await couponApi.list({
    filters: { ...filter },
  })
  const dataCouponOptions=coupon.map(item => { return { label: item.code + '-' + item.title, value: item.id } })
  callback(dataCouponOptions)
}