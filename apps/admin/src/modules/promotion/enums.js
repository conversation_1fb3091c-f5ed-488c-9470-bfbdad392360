/**
 * 主题、手机号码 模糊搜索
 */
export const searchType = Object.freeze({
  /**
   * 主题
   */
  title: 'title',
  /**
   * 手机号码
   */
  userRange: 'user_range',

  options() {
    return [
      { label: '主题', value: this.title },
      { label: '手机号码', value: this.userRange }
    ]
  }
})

/**
 * 领券入口
 */
export const couponEntrance = Object.freeze({
  /**
   * 商品详情
   */
  productItem: 'product_item',
  /**
   * 渠道发放
   */
  channel: 'channel',

  options() {
    return [
      { label: '商品详情', value: this.productItem },
      { label: '渠道发放', value: this.channel }
    ]
  }
})

/**
 * 优惠卷类型
 */
export const couponType = Object.freeze({
  /**
   * 代金券
   */
  cash: 'cash',
  /**
   * 折扣券
   */
  discount: 'discount',
  /**
   * 兑换券
   */
  exchange: 'exchange',
  /**
   * 运费券
   */
  freight: 'freight',
  /**
   * 满赠券
   */
  gift: 'gift',

  options() {
    return [
      { label: '代金券', value: this.cash, unit: '元' },
      { label: '折扣券', value: this.discount, unit: '折' },
      { label: '兑换券', value: this.exchange, unit: '件' },
      { label: '运费券', value: this.freight, unit: '元' },
      { label: '满赠券', value: this.gift, unit: '件' }
    ]
  },
  disabled(value) {
    return [this.exchange].includes(value)
  }
})

/**
 * 优惠卷使用门槛
 */
export const couponThreshold = Object.freeze({
  /**
   * 商品总金额
   */
  amount: 'amount',
  /**
   * 商品总数量
   */
  number: 'number',

  checkoutAmount: 'checkout_amount',

  options() {
    return [
      { label: '商品总金额', value: this.amount },
      { label: '商品总件数', value: this.number },
      { label: '订单总金额', value: this.checkoutAmount }
    ]
  },

  transformDisplayValue(type, value) {
    if (type === this.amount || type == this.checkoutAmount) {
      return value / 100
    }
    return value
  },

  transformSubmitValue(type, value) {
    if (type === this.amount || type == this.checkoutAmount) {
      return value * 100
    }
    return value
  }
})

/**
 * 优惠卷有效期
 */
export const couponTerm = Object.freeze({
  /**
   * 固定日期
   */
  fixed: 'fixed',
  /**
   * 领券日起
   */
  receive: 'receive',

  options() {
    return [
      { label: '固定日期', value: this.fixed },
      { label: '领券日起', value: this.receive }
    ]
  }
})

/**
 * 优惠卷状态
 */
export const couponStatus = Object.freeze({
  /**
   * 未使用
   */
  unused: 'unused',
  /**
   * 已使用
   */
  used: 'used',
  /**
   * 未生效
   */
  invalid: 'invalid',
  /**
   * 已过期
   */
  expired: 'expired',

  options() {
    return [
      { label: '未使用', value: this.unused, colorType: 'processing' },
      { label: '已使用', value: this.used, colorType: 'success' },
      { label: '未生效', value: this.invalid, colorType: 'warning' },
      { label: '已过期', value: this.expired, colorType: 'default' }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value) ?? {}
  }
})

/**
 * 系统模版
 */
export const skinMode = Object.freeze({
  /**
   * 系统模版-默认皮肤
   */
  default: 'default',

  option() {
    return [{ label: '系统模版-默认皮肤', value: this.default }]
  }
})

/**
 * sku or 商品名称
 */
export const skuOrTitleType = Object.freeze({
  /**
   * sku
   */
  sku: 'sku',

  /**
   * 商品名称
   */
  title: 'title',

  options() {
    return [
      { label: '商品sku', value: this.sku },
      { label: '商品名称', value: this.title }
    ]
  }
})

/**
 * 商品单选或多选
 */
export const goodsSelectType = Object.freeze({
  /**
   * 单选
   */
  single: 'single',
  /**
   * 多选
   */
  multiple: 'multiple',

  options() {
    return [
      { label: '单选', value: this.single },
      { label: '多选', value: this.multiple }
    ]
  }
})

/**
 * id or 商品名称
 */
export const idOrTitleType = Object.freeze({
  /**
   * id
   */
  id: 'id',

  /**
   * 商品名称
   */
  title: 'title',

  options() {
    return [
      { label: '商品id', value: this.id },
      { label: '商品名称', value: this.title }
    ]
  }
})

/**
 * 助力资格
 */
export const helpList = Object.freeze({
  /**
   * 仅新用户可助力
   */
  new_user: 'new_user',
  /**
   * 全部用户可助力
   */
  all: 'all',

  options() {
    return [
      { label: '仅新用户可助力', value: this.new_user },
      { label: '全部用户可助力', value: this.all }
    ]
  }
})

export const userModeList = Object.freeze({
  /**
   * 用户昵称
   */
  nickname: 'nickname',
  /**
   * 手机号码
   */
  phone_number: 'phone_number',

  options() {
    return [
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phone_number }
    ]
  }
})

/**
 * 助力状态
 */
export const helpStatusList = Object.freeze({
  /**
   * 助力中
   */
  normal: 'normal',
  /**
   * 助力成功
   */
  complete: 'complete',
  /**
   * 助力失败
   */
  fail: 'fail',

  options() {
    return [
      { label: '助力中', value: this.normal, color: '#409eff' },
      { label: '助力成功', value: this.complete, color: '#67c23a' },
      { label: '助力失败', value: this.fail, color: '#f56c6c' }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 助力状态
 */
export const groupStatusList = Object.freeze({
  /**
   * 拼团中
   */
  normal: 'normal',
  /**
   * 拼团成功
   */
  complete: 'success',
  /**
   * 拼团失败
   */
  fail: 'fail',

  options() {
    return [
      { label: '拼团中', value: this.normal, color: '#409eff' },
      { label: '拼团成功', value: this.complete, color: '#67c23a' },
      { label: '拼团失败', value: this.fail, color: '#f56c6c' }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 支付状态
 */
export const payStatusList = Object.freeze({
  /**
   * 未支付
   */
  unpaid: 'unpaid',
  /**
   * 已支付
   */
  paid: 'paid',

  options() {
    return [
      { label: '未支付', value: this.unpaid, color: '#409eff' },
      { label: '已支付', value: this.paid, color: '#67c23a' }
    ]
  },
  filter(value) {
    const item = this.options().find(item => item.value === value)
    return item || { label: '已支付', value: this.paid, color: '#67c23a' }
  }
})

/**
 * 减免运费
 */
export const feeList = Object.freeze({
  /**
   * 包邮
   */
  fee: 1,
  /**
   * 不包邮
   */
  unfee: 0,
  options() {
    return [
      { label: '包邮', value: this.fee },
      { label: '不包邮', value: this.unfee }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)
  }
})

/**
 * 礼品类型
 */
export const giftType = Object.freeze({
  /**
   * 实体礼品
   */
  entity: 'entity',

  /**
   * 虚拟卡券
   */
  invented: 'invented',

  /**
   * 购物卡券
   */
  coupon: 'coupon',

  /**
   * 抽奖资格
   */
  draw: 'draw',

  /**
   * 积分
   */
  credit: 'credit',

  /**
   * 成长值
   */
  // growthValue: 'growth_value',

  options() {
    return [
      { label: '实物礼品', value: this.entity, biz_type: 'gift', placeholder: '请选择礼品' },
      { label: '虚拟卡券', value: this.invented, biz_type: 'gift', placeholder: '请选择礼品' },
      { label: '购物卡券', value: this.coupon, biz_type: 'coupon', placeholder: '请选择优惠券' },
      { label: '会员积分', value: this.credit, biz_type: 'credit', placeholder: '请输入积分值' }
      // { label: '抽奖资格', value: this.draw, biz_type: 'draw', placeholder: '请输入抽奖次数' } // 暂时不要,一下要,一下不要,不要删除
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value) ?? {}
  },
  getPlaceholder(value) {
    if (!value) return '请选择礼品'
    return this.options().find(item => item.value == value)?.placeholder || '请选择礼品'
  }
})

/**
 * 奖项业务类型
 *
 * 关联字段:
 * 积分      award_biz_type = 'credit'   award_biz_id = 0       award_credit = 输入值  prize_title = ''       prize_url = ''
 * 礼品      award_biz_type = 'gift'     award_biz_id = 礼品id  award_credit = 0       prize_title = 礼品标题  prize_url = 礼品图片
 * 谢谢参与  award_biz_type = 'none'     award_biz_id = 0       award_credit = 0       prize_title = ''       prize_url = ''
 */
export const awardBizType = Object.freeze({
  /**
   * 积分
   */
  credit: 'credit',
  /**
   * 礼品[实物|虚拟]
   */
  gift: 'gift',
  /**
   * 购物卡券
   */
  coupon: 'coupon',
  /**
   * 谢谢参与
   */
  none: 'none'
})

/**
 * 助力资格
 */
export const helpQualified = Object.freeze({
  /**
   * 仅新用户可助力
   */
  new_user: 'new_user',
  /**
   * 全部用户可助力
   */
  all: 'all',

  options() {
    return [
      { label: '仅新用户可助力', value: this.new_user },
      { label: '全部用户可助力', value: this.all }
    ]
  }
})

/**
 * 商品适用范围
 */
export const productRange = Object.freeze({
  /**
   * 全部商品可用
   */
  all: 'all',
  /**
   * 指定SKU可用
   */
  sku_include: 'sku_include',
  /**
   * 排除SKU可用
   */
  sku_exclude: 'sku_exclude',
  /**
   * 指定SPU可用
   */
  spu_include: 'spu_include',
  /**
   * 排除SPU可用
   */
  spu_exclude: 'spu_exclude',
  /**
   * 指定分类可用
   */
  categories_include: 'categories_include',
  /**
   * 排除分类可用
   */
  categories_exclude: 'categories_exclude',

  group_include: 'group_include',

  options() {
    return [
      { label: '全部商品可用', value: this.all },
      { label: '指定SKU可用', value: this.sku_include },
      { label: '排除SKU可用', value: this.sku_exclude },
      { label: '指定SPU可用', value: this.spu_include },
      { label: '排除SPU可用', value: this.spu_exclude },
      { label: '指定分类可用', value: this.categories_include },
      { label: '排除分类可用', value: this.categories_exclude },
      { label: '指定分组可用', value: this.group_include }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value)?.label
  },
  filterAll(value) {
    return value !== this.all
  },
  filterSku(value) {
    return value === this.sku_include || value === this.sku_exclude
  },
  filterSpu(value) {
    return value === this.spu_include || value === this.spu_exclude
  },
  filterCategories(value) {
    return value === this.categories_include || value === this.categories_exclude
  },
  filterGroup(value) {
    return value === this.group_include
  }
})

/**
 * 活动汇总
 */
export const activitySummary = Object.freeze({
  /**
   * 活动发券
   */
  coupon: 'coupon',
  /**
   * 满减促销
   */
  cash: 'cash',
  /**
   * 满赠促销
   */
  gift: 'gift',
  /**
   * 限时折扣
   */
  discount: 'discount',
  /**
   * 砍价助力
   */
  bargain: 'bargain',
  /**
   * 特价秒杀
   */
  seckill: 'seckill',
  /**
   * 加价换购
   */
  markup: 'markup',
  /**
   * 尖货抽签
   */
  top_goods: 'top_goods',
  /**
   * 运费促销
   */
  sales_freight: 'sales_freight',
  /**
   * 多人拼团
   */
  group: 'group',
  /**
   * 团购
   */
  wholesale: 'wholesale',
  /**
   * 现场活动
   */
  site: 'site',
  /**
   * 定制礼盒活动
   */
  gift_box: 'gift_box',
  /**
   * 礼品卡
   */
  gift_card: 'gift_card',

  options() {
    return [
      { label: '活动发券', value: this.coupon, router: 'promotion-activity-dispatch-coupon' },
      { label: '满减促销', value: this.cash, router: 'full-minus-list' },
      { label: '满赠促销', value: this.gift, router: 'promotion-gift-list' },
      { label: '限时折扣', value: this.discount, router: 'limited-time-discount-list' },
      { label: '砍价助力', value: this.bargain, router: 'promotion-bargain-list' },
      { label: '特价秒杀', value: this.seckill, router: 'seckill-list' },
      { label: '加价换购', value: this.markup, router: 'mark-up-list' },
      { label: '尖货抽签', value: this.top_goods, router: 'top-goods-list' },
      { label: '运费促销', value: this.sales_freight, router: 'sales-freight-list' },
      { label: '多人拼团', value: this.group, router: 'promotion-group-list' },
      { label: '团购批发', value: this.wholesale, router: 'promotion-wholesale-list' },
      { label: '现场活动', value: this.site, router: 'promotion-site-list' },
      { label: '定制礼盒活动', value: this.gift_box, router: 'promotion-gift-box' },
      { label: '礼品卡', value: this.gift_card, router: 'promotion-gift-card-list' },
    ]
  },
  filter(value) {
    return this.options().find(item => item.value === value) ?? {}
  }
})

export const userSource = Object.freeze({
  /**
   * 所有用户
   */
  all: 'all',

  /**
   * 团购用户列表
   */
  from_wholesale_users: 'from_wholesale_users',

  /**
   * 验证文件
   */
  from_api: 'from_api',

  options() {
    return [
      { label: '所有用户', value: this.all },
      { label: '指定用户', value: this.from_wholesale_users },
      { label: 'API验证', value: this.from_api }
    ]
  }
})

export const dispatchCouponsType = Object.freeze({
  /**
   * 根据电话号码发券
   */
  phone: 'phone',

  /**
   * 根据标签发券
   */
  tags: 'tags',

  options() {
    return [
      { label: '用户发放', value: this.phone },
      { label: '标签发放', value: this.tags }
    ]
  },

  filter(value) {
    return this.options().find(item => item.value == value)
  }
})

export const receiveCouponsRules = Object.freeze({
  /**
   * 未首购
   */
  not_first_purchase: 'not_first_purchase',

  register_time: 'register_time|24',

  options() {
    return [
      { label: '未首购', value: this.not_first_purchase },
      { label: '注册24小时内', value: this.register_time }
    ]
  },

  filter(value) {
    return this.options().find(item => item.value == value)
  }
})

// 优惠码兑换状态
export const exchangeState = Object.freeze({
  /**
   * 未兑换
   */
  normal: 'normal',

  /**
   * 已兑换
   */
  used: 'used',

  options() {
    return [
      { label: '未兑换', value: this.normal },
      { label: '已兑换', value: this.used }
    ]
  }
})

// 礼品卡

export const validDateType = Object.freeze({
  /**
   * 固定时间段
   */
  fixed: 'fixed',

  /**
   * 领取日起始算
   */
  receive: 'receive',

  options() {
    return [
      { label: '固定时间段', value: this.fixed },
      { label: '领取日起', value: this.receive }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value)
  }
})

export const giftCardCodeExchanged = Object.freeze({
  /**
   * 未兑换
   */
  no: 0,

  /**
   * 已兑换
   */
  yes: 1,

  options() {
    return [
      { label: '未兑换', value: this.no },
      { label: '已兑换', value: this.yes }
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value)
  }
})

export const giftCardCodeStatus = Object.freeze({
  /**
   * 未使用
   */
  unused: 'unused',

  /**
   * 已使用
   */
  used: 'used',

  /**
   * 已使用
   */
  invalid: 'invalid',

  /**
   * 已使用
   */
  expired: 'expired',

  options() {
    return [
      { label: '未使用', value: this.unused },
      { label: '已使用', value: this.used },
      { label: '未到期', value: this.invalid },
      { label: '已过期', value: this.expired },
    ]
  },
  filter(value) {
    return this.options().find(item => item.value == value)
  }
})

/**
 * sku or 商品名称
 */
export const giftCardCodeSearch = Object.freeze({
  /**
   * 卡号
   */
  code: 'code',

  /**
   * 用户昵称
   */
  nickname: 'nickname',

  /**
   * 手机号码
   */
  phone_number: 'phone_number',

  options() {
    return [
      { label: '卡号', value: this.code },
      { label: '用户昵称', value: this.nickname },
      { label: '手机号码', value: this.phone_number },
    ]
  }
})
