<template>
  <uc-layout-form :is-save="!isRead" @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="广告图片" class="required">
            <uc-upload v-model:list="formState.poster_url" upload-text=" " :max-length="1" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="广告时间" class="required">
            <a-date-picker
              v-model:value="formState.start_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isRead"
              placeholder="广告开始时间"
              :disabled-date="disabledStartTime"
              :disabled-time="disabledStartTime"
            />
            <span class="separator">~</span>
            <a-date-picker
              v-model:value="formState.end_time"
              style="width: 240px"
              show-time
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :disabled="isRead"
              placeholder="广告结束时间"
              :disabled-date="disabledEndTime"
            />
          </a-form-item>
          <a-form-item label="广告名称" class="required">
            <a-input v-model:value="formState.title" maxlength="30" placeholder="请输入广告名称，不超过30字" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="广告代码" class="required">
            <a-input v-model:value="formState.key" maxlength="30" placeholder="请输入广告代码，不超过20字" :disabled="isRead" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="广告配置" class="h-fill">
          <a-form-item label="广告排序" class="required">
            <a-input-number v-model:value="formState.sort" :min="0" placeholder="排序值越大排名越靠前" :disabled="isRead" />
          </a-form-item>
          <a-form-item label="广告跳转" class="required">
            <uc-jump v-model:value="formState.setting.jump" ignore-jumps="['contact', 'share']" />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { cloneDeep, debounce } from 'lodash'
import { adsApi } from '../../api'
import { useDisabledDate } from '@/composables/useDisabledDate'

const { TIME_STATUS_ENDED, TIME_STATUS_NORMAL } = useTimeStatus()

const { id } = useRoute().params

const isEdit = ref(false)

let isRead = ref(false)

if (id) {
  const hideLoading = message.loading('正在加载数据...')

  adsApi
    .get(id)
    .then(async res => {
      res.poster_url = [res.poster_url]
      isRead.value = res.status == TIME_STATUS_ENDED
      isEdit.value = res.status === TIME_STATUS_NORMAL
      setFormState(res)
    })
    .finally(hideLoading)
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  poster_url: undefined,
  start_time: undefined,
  end_time: undefined,
  title: undefined,
  key: undefined,
  sort: undefined,
  setting: {
    jump: {
      jump_type: undefined,
      jump_link: undefined,
      appid: undefined
    }
  }
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState)

setFormRules({
  poster_url: { required: true, message: '请选择广告图片' },
  start_time: { required: true, message: '请选择投放开始时间' },
  title: { required: true, message: '请输入广告名称' },
  key: { required: true, message: '请输入广告代码' },
  sort: { required: true, message: '请输入广告排序' }
})

const router = useRouter()

const handleSubmit = debounce(async () => {
  if (!(await validateForm())) return

  // handle params
  const params = cloneDeep(formState.value)
  params.poster_url = params.poster_url[0]

  if (id) {
    await adsApi.update(id, params)
    message.success('编辑完成')
  } else {
    await adsApi.create(params)
    message.success('创建完成')
  }
  router.back()
}, 500)
</script>

<style lang="less" scoped>
.separator {
  .inline-block();
  width: 20px;
  text-align: center;
}
</style>
