<template>
  <uc-layout-list title="广告位">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>

      
      <a-form-item name="status">
        <a-select v-model:value="formState.status" placeholder="广告状态" allow-clear :options="statusList" />
      </a-form-item>

      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary">
        <router-link :to="{name:'page-config-ads-add'}">
          新增广告位
        </router-link>
      </a-button>
    </template>

    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="广告名称" data-index="title" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              :url="record.poster_url"
              :title="record.title"
              :subtit="record.key"
            />
          </template>
        </a-table-column>

        <a-table-column title="点击UV/PV" width="150px" ellipsis align="right">
          <template #default="{ record }">
            {{ record.uv }}/{{ record.pv }}
          </template>
        </a-table-column>

        <a-table-column title="广告排序" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="input-center"
              style="width: 80px"
              :min="0"
              @blur="handleChangeSort(record)"
            />
          </template>
        </a-table-column>

        <a-table-column title="广告时间" width="200px">
          <template #default="{ record }">
            {{ record.start_time }}
            <div>至 {{ $formatters.transformActivityEndTime(record.end_time) }}</div>
          </template>
        </a-table-column>

        <a-table-column title="广告状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link">
              <router-link :to="{name:'page-config-ads-edit', params: {id: record.id}}">
                编辑
              </router-link>
            </a-button>
            <a-popconfirm
              :disabled="!record.can_delete"
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.can_delete">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { watch } from 'vue'
import { message } from 'ant-design-vue'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { adsSearchCondition } from '../../enums'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { adsApi } from '../../api'
import { cloneDeep } from 'lodash'

const { statusFilter, statusList, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus()

const conditionOptions = adsSearchCondition.options()
const conditionKey = ref(adsSearchCondition.title)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))

const queryFormBasic = Object.freeze({
  [adsSearchCondition.title]: undefined,
  [adsSearchCondition.key]: undefined
})

const { formState, resetFormState } = useFormState({
  ...cloneDeep(queryFormBasic),
  status: undefined
})

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {
  return adsApi.paginator({
    filters: useTransformQuery(formState.value, {
      title: 'like'
    }),
    offset,
    limit
  })
})

const handleDelete = async ({ id }) => {
  await adsApi.delete(id)
  message.success('操作成功')
  setPage()
}

const handleChangeSort = async ({ id, sort }) => {
  adsApi
    .update(id, { sort })
    .then(() => message.success('操作成功'))
    .finally(setPage)
}
</script>

<style lang="less" scoped>

</style>