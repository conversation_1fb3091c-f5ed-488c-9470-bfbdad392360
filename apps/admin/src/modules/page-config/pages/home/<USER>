<template>
  <!-- 首页配置 -->
  <layout-config
    v-model:list="listData"
    class="m-home-config"
    v-bind="{ sidebarList, initItemData, rectStyle }"
    @submit="onSubmit"
  >
    <template #cardInfo>
      <div class="flex flex-sb">
        <a-input
          v-model:value="formState.bg_color"
          addon-before="背景色"
          placeholder="如：#f5f5f5"
          class="w-310"
          :disabled="readonly"
        />
        <uc-upload
          :list="[]"
          class="bg-upload"
          :max-length="1"
          disabled-icon
          upload-text="上传背景"
          :disabled="readonly"
          @update:list="data => (formState.bg_url = data[0])"
        />
      </div>
    </template>
  </layout-config>
</template>
<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import assets from '../../assets.config'
import LayoutConfig from '../../components/layout-config'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useFormState } from '@/composables/useFormState'
import { colorVerify } from '@/utils/index'
import { customPageConfigApi, customPageConfigUpdateApi } from '../../api'
import { useSelectRect, useValidateRect } from '../../useRect'
import { RectType } from '../../enums'
import { useTransformData } from '../../useTransformData'

const defaultBgColor = '#f5f5f5'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  bg_color: defaultBgColor,
  bg_url: ''
})
const [transfromToForm] = useTransformData()

setFormRules({
  bg_color: {
    validator(_, value) {
      if (!colorVerify(value)) return Promise.reject('请输入背景颜色(以#开头+6位数字字母组合)')
      return Promise.resolve()
    }
  }
})

nextTick(() => {
  useLoadingMessage(
    customPageConfigApi('home').get().then(res => {
      listData.value = transfromToForm(res.config)
      res.bg_color = res.bg_color || defaultBgColor
      setFormState(res)
    }),
    {
      loadingText: '正在加载数据'
    }
  )
})

const router = useRouter() // 路由操作

const rectStyle = computed(
  () => `background:${formState.value.bg_color} url(${formState.value.bg_url}) no-repeat center top;`
)
// sidebar 新增初始化数据
const initItemData = {
  [RectType.image]: {
    bg_url: '',
    items: []
  },
  [RectType.navbar]: {
    style_type: 'style01',
    register_url: '',
    wechat_public_url: '',
    items: []
  },
  [RectType.register]: {
    style_type: 'style01',
    register_url: '',
    wechat_public_url: ''
  },
  [RectType.swiper]: {
    style_type: 'style01',
    items: []
  },
  [RectType.slide]: {
    style_type: 'style01',
    items: []
  },
  [RectType.video]: {
    style_type: 'style01'
  }
}

// 左侧菜单
const sidebarList = ref([
  {
    ...RectType.sidebar(RectType.register),
    state: true,
    noSelect: assets.homeLeftRegisterNoselect,
    selected: assets.homeLeftRegisterSelected
  },
  {
    ...RectType.sidebar(RectType.navbar),
    state: true,
    noSelect: assets.homeLeftRegisterNoselect,
    selected: assets.homeLeftRegisterSelected
  },
  {
    ...RectType.sidebar(RectType.swiper),
    state: true,
    noSelect: assets.homeLeftSwiperNoselect,
    selected: assets.homeLeftSwiperSelected
  },
  {
    ...RectType.sidebar(RectType.slide),
    state: true,
    noSelect: assets.leftSlideNoselect,
    selected: assets.leftSlideSelected
  },
  {
    ...RectType.sidebar(RectType.image),
    state: true,
    noSelect: assets.myImgNoselect,
    selected: assets.myLeftimgSelcted
  },
  {
    ...RectType.sidebar(RectType.video),
    state: true,
    noSelect: assets.leftVideoNoselect,
    selected: assets.leftVideoSelected
  }
])

const listData = ref([]) // 中间内容数组
const { onClearSelectAll } = useSelectRect(listData)

const onSubmit = async () => {
  const params = { ...formState.value, config: listData.value }
  if (!(await validateForm())) {
    onClearSelectAll()
    return
  }

  if (!useValidateRect(listData.value)) return
  await customPageConfigUpdateApi('home').post(params)
  message.success('操作成功')
}
</script>
<style scoped lang="less">
.m-home-config {
  .bg-upload(120px);
}
</style>
