<template>
  <!-- 登录注册配置 -->
  <div class="login-config">
    <div class="scroll-x">
      <!-- 头部 -->
      <div class="m-head">
        <div class="u-return" @click="onBack">
          <img :src="assets.return" />返回控制台
        </div>
        <div class="m-btns">
          <a-button type="primary" @click="onSubmit">
            保存
          </a-button>
        </div>
      </div>
      <!-- 左侧菜单 - 隐藏登录配置 -->
      <div class="m-sidebar">
        <ul class="scroll">
          <li v-for="(item,index) in sidebarList" v-show="index>0" :key="item.key" :class="{ on: item.state }" @click="onClickSide(item)">
            <img :src="item.noSelect" class="u-img-noselect" />
            <img :src="item.selected" class="u-img-selected" />
            <span class="u-text">{{ item.text }}</span>
          </li>
        </ul>
      </div>
      <!-- 中间小程序 -->
      <div class="m-applets">
        <div class="m-rect" @click.stop>
          <!-- 头部 -->
          <div class="u-hd" :style="`position:${cardForm.title_style === 'fixed' ? 'relative' : 'absolute'};`">
            <template v-if="cardForm.title_style === 'fixed'">
              <img :src="assets.headerBgBlack" />
              <h3 class="u-tit">
                测试标题
              </h3>
            </template>
            <img v-else :src="assets.headerBgWhite" />
          </div>
          <!-- 背景图 -->
          <img v-if="cardForm.bg_image" :src="cardForm.bg_image" class="u-bg-img" />
          <!-- 屏幕主体 -->
          <div v-if="selectedItem.key == 'login'" class="m-login-cont">
            <div class="u-btn">
              <img :src="assets.wechat" />
              微信授权登录
            </div>
            <p class="u-desc">
              登录即表示你已同意
              <span>《xxxxxxxx注册协议》</span>
            </p>
          </div>
          <div v-else-if="selectedItem.key == 'register' && cardForm.registerMode != registerType.info" class="m-login-cont">
            <div class="u-btn">
              注册会员
            </div>
            <p class="u-desc">
              注册即表示你已同意
              <span>《xxxxxxxx注册协议》</span>
            </p>
          </div>
          <div v-else class="m-register-cont">
            <div class="m-register-cont_wrap">
              <ul class="m-list-avatar">
                <li v-for="item in avatarList" :key="item.key" :class="{ on: item.state }">
                  <img :src="item.url" />
                </li>
              </ul>
              <div>
                <div v-for="item in inputList" :key="item.id" class="u-input">
                  <span>{{ item.placeholder }}</span>
                  <img v-if="item.select" :src="assets.arrowBottom" />
                </div>
              </div>
              <div class="m-register-cont_footer">
                <div class="u-btn">
                  注册会员
                </div>
                <p class="u-desc">
                  注册即表示你已同意
                  <span>《XXXXXXXXXXX注册协议》</span>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 右侧元素面板 -->
      <div class="m-element">
        <div class="scroll">
          <a-card :title="cardTitle" :bordered="false">
            <a-select v-model:value="cardForm.title_style" class="mg-bt select-style" placeholder="请选择">
              <a-select-option v-for="item in titleStyleList" :key="item.value" :value="item.value">
                {{ item.label }}
              </a-select-option>
            </a-select>
            <a-select v-if="selectedItem.key === 'register'" v-model:value="cardForm.registerMode" class="mg-bt select-style" placeholder="请选择">
              <a-select-option v-for="item in registerType.option()" :key="item.value" :value="item.value">
                {{ item.text }}
              </a-select-option>
            </a-select>
            <div class="mg-bt">
              <a-input v-model:value.trim="cardForm.theme_color" addon-before="主题色" placeholder="主题色，如#000000" class="m-b-20" />
              <a-input v-model:value.trim="cardForm.bg_color" addon-before="背景色" placeholder="背景色，如#ffffff" class="m-b-20" />
              <a-input v-model:value.trim="cardForm.title_color" addon-before="标题色" placeholder="标题色，如#000000" />
            </div>
            <uc-upload
              :list="[]"
              class="bg-upload m-b-20"
              :class="cardForm.bg_image.length ? 'already' : ''"
              :max-length="1"
              upload-text="更换背景图片"
              @update:list="data => cardForm.bg_image = data[0]"
            />
            <uc-upload
              v-if="selectedItem.key === 'register'"
              :list="cardForm.popup_success ? [cardForm.popup_success] : []"
              :max-length="1"
              show-label
              label-text="注册成功"
              @update:list="data => cardForm.popup_success = data[0]"
            />
          </a-card>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import assets from '../../assets.config'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { customPageConfigApi, customPageConfigUpdateApi } from '../../api'
import { registerType } from '../../enums'
import { cloneDeep } from 'lodash'
import { colorVerify } from '@/utils/index'

// 隐藏登录配置
// 页面 v-show="index>0"
// 校验 if(key == "login") return true
// 提交 // login: sidebarListValue[0].form,

const loadData = () => {
  customPageConfigApi('register').get().then((data) => {
    if (!data) return
    const { config } = data

    const { login, register } = config
    if (login) {
      // login.bg_image ? login.bg_image = [login.bg_image] : login.bg_image = []
      sidebarList.value[0].form = login
    }
    if (register) {
      // register.bg_image ? register.bg_image = [register.bg_image] : register.bg_image = []
      if (!register.registerMode) register.registerMode = registerType.phone
      sidebarList.value[1].form = config.register
    }
  })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

const router = useRouter()
const photo_urls = ref([])
// 左侧菜单
const sidebarList = ref([
  {
    key: "login",
    text: "登录",
    state: false,
    noSelect: assets.loginNoSelect,
    selected: assets.loginSelected,
    form: {
      title_style: "fixed", // 标题栏样式
      theme_color: "", // 主题色
      bg_color: "", // 背景色
      title_color: "", // 标题色
      bg_image: "", // 背景图片 https://dummyimage.com/750x1334/ff9db1/fff&text=Login
      registerMode: 'phone',
      popup_success: ""
    }
  },
  {
    key: 'register',
    text: '注册',
    state: true,
    noSelect: assets.registerNoSelect,
    selected: assets.registerSelected,
    form: {
      title_style: 'fixed', // 标题栏样式
      theme_color: '', // 主题色
      bg_color: '', // 背景色
      title_color: '', // 标题色
      bg_image: "", // 背景图片
      registerMode: 'phone',
      popup_success: ""
    }
  }
])
// 右侧标题栏样式下拉框
const titleStyleList = Object.freeze([
  { label: '固定页面标题栏', value: 'fixed' },
  { label: '自定义页面标题栏', value: 'custom' }
])

// 头像列表
const avatarList = Object.freeze([
  {
    key: 'male',
    state: true,
    url: assets.male
  },
  {
    key: 'female',
    state: false,
    url: assets.female
  }
])
// 输入列表
const inputList = Object.freeze([
  {
    id: 1,
    placeholder: '请输入您的昵称'
  },
  {
    id: 2,
    placeholder: '请选择出生日期',
    select: true
  },
  {
    id: 3,
    placeholder: '请选择所在城市',
    select: true
  }
])

const selectedItem = computed(() => sidebarList.value.find(({ state }) => state)) // 当前选中项
const cardTitle = computed(() => `${selectedItem.value.text}页面设置`) // 页面设置
// 获取主题色
const getThemeColor = computed(() => {
  const color = selectedItem.value.form.theme_color
  return colorVerify(color) ? color : '#fff'
})
// 获取标题色
const getTitleColor = computed(() => {
  const color = selectedItem.value.form.title_color
  return colorVerify(color) ? color : '#666'
})
// 获取背景色
const getBgColor = computed(() => {
  const color = selectedItem.value.form.bg_color
  return colorVerify(color) ? color : '#fff'
})
// 右侧内容
const cardForm = computed(() => selectedItem.value.form)
// 点击返回控制台
const onBack = () => {
  router.back()
}
// 点击提交按钮
const onSubmit = () => {
  let sidebarListValue = cloneDeep(sidebarList.value)
  const volidForm = sidebarListValue.some(({ key,form, text }) => {
    // 忽略登录配置
    if(key == "login") return false

    const { theme_color, bg_color, title_color, bg_image } = form
    if (!colorVerify(theme_color)) {
      message.error(`${text}页面设置-请输入以#开头+6位数字或字母组合的主题色`)
      return true
    } else if (!colorVerify(bg_color)) {
      message.error(`${text}页面设置-请输入以#开头+6位数字或字母组合的背景色`)
      return true
    } else if (!colorVerify(title_color)) {
      message.error(`${text}页面设置-请输入以#开头+6位数字或字母组合的标题色`)
      return true
    } else if (!bg_image) {
      message.error(`${text}页面设置-请上传背景图片`)
      return true
    } else {
      // form.bg_image = form.bg_image[0]
      return false
    }
  })

  if (!volidForm) {
    // 参数
    const params = {
      config: {
        login: {},//sidebarListValue[0].form,
        register: sidebarListValue[1].form
      }
    }
    useLoadingMessage(customPageConfigUpdateApi('register').post(params)).then(() => {
      message.success('操作成功：装修登录注册页面')
      onBack()
    })
  }
}
// 点击 sidebar
const onClickSide = ({ state, key }) => {
  if (state) return
  sidebarList.value.forEach((item) => {
    item.state = item.key === key
  })
}
</script>
<style scoped lang="less">
.login-config {
  @primary: #1890ff;
  @padding: 20px;
  @margin-bottom: 20px;
  .scroll {
  }
  width: 100%;
  height: 100%;
  overflow-x: auto;
  .scroll-x {
    display: flex;
    min-width: 1280px;
    height: 100vh;
    padding-top: 60px;
    background: #f0f2f5;

    .m-head {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 60px;
      z-index: 9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 @padding;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      .u-return {
        display: flex;
        align-items: center;
        color: @primary;
        cursor: pointer;
        font-size: 16px;
        img {
          width: 16px;
          margin-right: 8px;
        }
      }
      .m-btns {
        :deep(.ant-btn) {
          margin-left: 10px;
        }
      }
    }
    .m-sidebar {
      .scroll();
      width: 80px;
      height: 100%;
      background: #fff;
      overflow-y: auto;

      > ul > li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 80px;
        cursor: pointer;
        // &:hover,
        &.on {
          background: fade(@primary, 10%);
          .u-img-noselect {
            display: none;
            opacity: 0;
          }
          .u-img-selected {
            display: block;
            opacity: 1;
          }
          .u-text {
            color: @primary;
          }
        }

        .u-img-noselect {
          display: block;
          transition: all 1s;
          opacity: 1;
        }
        .u-img-selected {
          display: none;
          transition: all 1s;
          opacity: 0;
        }
        .u-text {
          margin-top: 5px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
    .m-applets {
      flex-grow: 1;
      width: 30%;
      height: 100%;
      overflow-y: auto;
      padding: 24px 0;
      background: #f0f2f5;
      .m-rect {
        position: relative;
        width: 375px;
        height: 667px;
        overflow: hidden;
        margin: 0 auto;
        background: v-bind(getBgColor);
        .welcome.welcome-position-top {
          top: 100px;
        }
        .welcome {
          width: 100%;
          position: absolute;
          left: 50%;
          top: 239px;
          transform: translateX(-50%);
          color: v-bind(getTitleColor);
          .text {
            font-size: 18px;
            font-family: PingFang SC;
            line-height: 50px;
            color: #ffffff;
          }
          .logo {
            font-size: 42px;
            font-family: PingFang SC;
            font-weight: bold;
            color: #ffffff;
          }
        }
        .u-hd {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 5;
          width: 100%;
          img {
            width: 100%;
          }
          .u-tit {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 44px;
            margin-bottom: 0;
            line-height: 44px;
            text-align: center;
            color: #000;
            font-size: 17px;
          }
        }
        .u-bg-img {
          display: block;
          width: 100%;
        }
        .m-login-cont {
          position: absolute;
          top: 558px;
          left: 0;
          width: 100%;
          .u-btn {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 275px;
            height: 40px;
            border-radius: 18px;
            margin: 0 auto 10px;
            background: v-bind(getThemeColor);
            color: #fff;
            font-size: 15px;
            font-family: 'PingFang SC';
            img {
              display: block;
              width: 25px;
              margin-right: 10px;
            }
          }
          .u-desc {
            text-align: center;
            font-size: 12px;
            color: v-bind(getTitleColor);
            span {
              color: v-bind(getThemeColor);
            }
          }
        }
        .m-register-cont {
          position: absolute;
          left: 50%;
          top: 239px;
          transform: translateX(-50%);
          width: 336px;
          min-height: 356px;
          border-radius: 10px;
          background: #fff;
          .m-register-cont_wrap {
            position: relative;
            min-height: 356px;
            .m-list-avatar {
              display: flex;
              justify-content: center;
              align-items: center;
              height: 90px;
              margin-bottom: 0;
              li {
                width: 50px;
                height: 50px;
                border-radius: 50%;
                background: #eee;
                overflow: hidden;
                &.on {
                  background: v-bind(getThemeColor);
                }
                &:first-child {
                  margin-right: 30px;
                }
                img {
                  display: block;
                  width: 100%;
                  height: 100%;
                }
              }
            }
          }

          .u-input {
            display: flex;
            align-items: center;
            justify-content: space-between;
            width: 265px;
            height: 40px;
            border-radius: 25px;
            margin: 0 auto 15px;
            padding: 0 20px;
            background: #f7f7f7;
            span {
              flex-grow: 1;
              width: 50%;
              display: block;
              color: rgba(0, 0, 0, 0.2);
              font-size: 14px;
              font-family: 'PingFang SC';
            }
            img {
              width: 14px;
            }
          }
          .u-btn {
            width: 265px;
            height: 40px;
            line-height: 40px;
            border-radius: 25px;
            margin: 5px auto 20px;
            background: v-bind(getThemeColor);
            text-align: center;
            color: #fff;
            font-size: 16px;
            font-weight: bold;
            font-family: 'PingFang SC';
          }
          .u-desc {
            text-align: center;
            font-size: 12px;
            color: v-bind(getTitleColor);
            span {
              color: v-bind(getThemeColor);
            }
          }
        }
      }
    }
    .m-element {
      .scroll();
      width: 500px;
      height: 100%;
      overflow-y: auto;
      background: #fff;
      .mg-bt {
        margin-bottom: @margin-bottom;
      }
      .select-style {
        width: 100%;
      }
      .bg-upload {
        :deep(.ant-upload-picture-card-wrapper) {
          width: 100%;
          height: 50px;
          .ant-upload-list-picture-card {
            display: none;
          }
          .ant-upload-select-picture-card {
            width: 100%;
            height: 100%;
            margin: 0;
            .ant-upload > div {
              display: flex;
              justify-content: center;
              align-items: center;
            }
          }
        }
        :deep(.ant-upload-list) {
          width: 100%;
          height: 100%;
          .ant-upload-list-picture-card-container {
            width: 100%;
            height: 100%;
            .ant-upload-list-item {
              width: 100%;
              height: 100%;
            }
          }
        }
      }
    }
  }
}
</style>
