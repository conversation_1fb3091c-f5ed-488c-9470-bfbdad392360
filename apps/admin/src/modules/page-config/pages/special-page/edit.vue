<template>
  <!-- 专题配置 -->
  <layout-config
    v-model:list="listData"
    class="m-special-config"
    v-bind="{ sidebarList, initItemData, rectStyle, disabled: readonly }"
    @submit="onSubmit"
  >
    <template #cardInfo>
      <a-space direction="vertical" :size="20" class="w-fill">
        <a-select v-model:value="formState.page_type" class="mg-bt select-style" placeholder="请选择">
          <a-select-option v-for="item in SpecialPageType.options()" :key="item.value" :value="item.value">
            {{ item.label }}
          </a-select-option>
        </a-select>
        <div v-if="formState.page_type != SpecialPageType.daily" class="flex flex-sb flex-cc">
          <a-date-picker
            v-model:value="formState.start_time"
            show-time
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            placeholder="请选择开始时间"
            :disabled-date="disabledStartTime"
            :disabled="disabledTime || readonly"
            class="w-210"
          />
          <span class="separator">~</span>
          <a-date-picker
            v-model:value="formState.end_time"
            :disabled-date="disabledEndTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            show-time
            class="w-210"
            placeholder="请选择结束时间"
            :disabled="readonly"
          />
        </div>
        <a-input
          v-model:value="formState.title"
          class="mg-bt"
          :maxlength="30"
          :disabled="readonly"
          placeholder="请输入专题活动页面名称，不超过30字"
        />
        <div class="flex flex-sb">
          <a-input
            v-model:value="formState.bg_color"
            addon-before="背景色"
            placeholder="如：#f5f5f5"
            class="w-310"
            :disabled="readonly"
          />
          <uc-upload
            :list="[]"
            class="bg-upload"
            :max-length="1"
            disabled-icon
            upload-text="上传背景"
            :disabled="readonly"
            @update:list="data => (formState.bg_url = data[0])"
          />
        </div>
        <div v-if="formState.page_type != SpecialPageType.daily" class="flex">
          <uc-upload
            v-model:list="formState.not_start_url"
            :max-length="1"
            show-label
            label-text="未开始"
            :disabled="readonly"
          />
          <uc-upload
            v-model:list="formState.ended_url"
            :max-length="1"
            show-label
            label-text="已结束"
            :disabled="readonly"
          />
        </div>
      </a-space>
    </template>
  </layout-config>
</template>
<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import moment from 'moment'
import assets from '../../assets.config'
import LayoutConfig from '../../components/layout-config'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useFormState } from '@/composables/useFormState'
import { useDisabledDate } from '@/composables/useDisabledDate'
import { useBatchTransformMedia } from '@/composables/useTransformFormat'
import { specialPageApi } from '../../api'
import { useSelectRect, useDrag, useValidateRect } from '../../useRect'
import { RectType, SpecialPageType } from '../../enums'
import { colorVerify } from '../../../../utils/index'

// 需要装换的字段
const transformKeys = Object.freeze(['not_start_url', 'ended_url'])
const defaultBgColor = '#f5f5f5'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  title: '',
  start_time: undefined,
  end_time: undefined,
  bg_color: defaultBgColor,
  bg_url: '',
  not_start_url: [],
  ended_url: [],
  page_type: SpecialPageType.daily
})

const validatorNotDailyType = (value, message) => {
  if (formState.value.page_type != SpecialPageType.daily && !value) return Promise.reject(message)
  return Promise.resolve()
}

setFormRules({
  start_time: {
    validator(_, value) {
      return validatorNotDailyType(value, '请选择开始时间')
    }
  },
  end_time: {
    validator(_, value) {
      return validatorNotDailyType(value, '请选择结束时间')
    }
  },
  title: { required: true, message: '请输入专题名称' },
  bg_color: {
    validator(_, value) {
      if (!colorVerify(value)) return Promise.reject('请输入背景颜色(以#开头+6位数字字母组合)')
      return Promise.resolve()
    }
  },
  not_start_url: {
    validator(_, value) {
      return validatorNotDailyType(value, '请上传未开始弹窗图')
    }
  },
  ended_url: {
    validator(_, value) {
      return validatorNotDailyType(value, '请上传已结束弹窗图')
    }
  }
})

const { disabledStartTime, disabledEndTime } = useDisabledDate(formState) // 禁止时间

const router = useRouter() // 路由操作

const { id, status } = useRoute().params

const readonly = status === 'look'
const rectStyle = computed(
  () => `background-color:${formState.value.bg_color};background-image:url(${formState.value.bg_url});`
)

if (id) {
  nextTick(() => {
    useLoadingMessage(
      specialPageApi.get(id, {}).then(res => {
        listData.value = res.config
        const { transformObject } = useBatchTransformMedia(res, transformKeys, 'array')
        res.bg_color = res.bg_color || defaultBgColor
        setFormState({ ...res, ...transformObject })
      }),
      {
        loadingText: '正在加载数据'
      }
    )
  })
}

const disabledTime = computed(() => id && moment(formState.value.start_time) < moment())

// sidebar 新增初始化数据
const initItemData = {
  [RectType.image]: {
    bg_url: '',
    items: []
  },
  [RectType.navbar]: {
    style_type: 'style01',
    items: []
  },
  [RectType.register]: {
    style_type: 'style01',
    register_url: '',
    wechat_public_url: ''
  },
  [RectType.swiper]: {
    style_type: 'style01',
    items: []
  },
  [RectType.slide]: {
    style_type: 'style01',
    items: []
  },
  [RectType.video]: {
    style_type: 'style01'
  }
}

// 左侧菜单
const sidebarList = ref([
  {
    ...RectType.sidebar(RectType.register),
    state: true,
    noSelect: assets.homeLeftRegisterNoselect,
    selected: assets.homeLeftRegisterSelected
  },
  {
    ...RectType.sidebar(RectType.navbar),
    state: true,
    noSelect: assets.leftNavbarNoselect,
    selected: assets.leftNavbarSelected
  },
  {
    ...RectType.sidebar(RectType.swiper),
    state: true,
    noSelect: assets.homeLeftSwiperNoselect,
    selected: assets.homeLeftSwiperSelected
  },
  {
    ...RectType.sidebar(RectType.slide),
    state: true,
    noSelect: assets.leftSlideNoselect,
    selected: assets.leftSlideSelected
  },
  {
    ...RectType.sidebar(RectType.image),
    state: true,
    noSelect: assets.myImgNoselect,
    selected: assets.myLeftimgSelcted
  },
  {
    ...RectType.sidebar(RectType.video),
    state: true,
    noSelect: assets.leftVideoNoselect,
    selected: assets.leftVideoSelected
  }
])

const listData = ref([]) // 中间内容数组
const { onClearSelectAll } = useSelectRect(listData)
// 点击提交按钮
const onSubmit = async () => {
  if (!(await validateForm())) {
    onClearSelectAll()
    return
  }

  if (!useValidateRect(listData.value)) return

  const { transformObject } = useBatchTransformMedia(formState.value, transformKeys, 'string')
  const params = { ...formState.value, ...transformObject, config: listData.value }
  // 发送请求
  if (id) {
    await specialPageApi.update(id, params)
  } else {
    await specialPageApi.create(params)
  }

  message.success('操作成功')
  router.back()
}
</script>
<style scoped lang="less">
.m-special-config {
  .bg-upload(120px);
}
.select-style {
  width: 100%;
}
</style>
