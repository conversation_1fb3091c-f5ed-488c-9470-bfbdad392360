<template>
  <uc-layout-list title="专题配置">
    <template #filter>
      <a-form-item name="title">
        <a-input v-model:value.trim="formState.title" placeholder="请输入专题名称" />
      </a-form-item>
      <a-form-item name="productType">
        <a-select v-model:value="formState.status" placeholder="活动状态" allow-clear :options="statusList" />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage">
          查询
        </a-button>
        <a-button @click="resetFormState">
          重置
        </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary" @click="toAdd">
        新增专题页面
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="专题名称" data-index="title" ellipsis />
        <a-table-column title="UV/PV" width="150px" ellipsis align="right">
          <template #default="{ record }">
            {{ record.uv }}/{{ record.pv }}
          </template>
        </a-table-column>
        <a-table-column title="页面类型" width="150px" ellipsis>
          <template #default="{ record }">
            {{ SpecialPageType.filterValue(record.page_type) }}
          </template>
        </a-table-column>
        <a-table-column title="活动时间" width="360px">
          <template #default="{ record }">
            <template v-if="record.page_type == SpecialPageType.daily">
              长期
            </template>
            <template v-else-if="record.page_type == SpecialPageType.activity">
              {{ record.start_time }} ~ {{ $formatters.transformActivityEndTime(record.end_time) }}
            </template>
          </template>
        </a-table-column>
        <a-table-column title="页面状态" width="120px">
          <template #default="{ record }">
            <a-badge :status="statusFilter(record.status).colorType" :text="statusFilter(record.status).label" />
          </template>
        </a-table-column>
        <a-table-column title="操作" width="150px">
          <template #default="{ record }">
            <a-button type="link" @click="copyLinkByRoute('specialPage', { id: record.id })">
              链接
            </a-button>
            <a-button
              v-if="record.status == TIME_STATUS_ENDED && record.page_type !=SpecialPageType.daily"
              type="link"
              class="link"
              @click="toEdit(record, 'look')"
            >
              查看
            </a-button>
            <a-button v-else type="link" @click="toEdit(record, 'edit')">
              编辑
            </a-button>
            <a-popconfirm
              :disabled="(record.status !== TIME_STATUS_NOSTART && record.page_type !=SpecialPageType.daily) || record.key"
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="(record.status !== TIME_STATUS_NOSTART && record.page_type !=SpecialPageType.daily) || record.key">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { message } from 'ant-design-vue'
import { useRouter } from 'vue-router'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { useTimeStatus } from '@/composables/useTimeStatus'
import { specialPageApi } from '../../api'
import { SpecialPageType } from '../../enums'
const linkPath = Object.freeze('/special-page/pages/standard/special-page/index?id=')

const { statusFilter, statusList, TIME_STATUS_NOSTART, TIME_STATUS_NORMAL, TIME_STATUS_ENDED } = useTimeStatus()

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) =>
  specialPageApi.paginator({
    filters: useTransformQuery(formState, {
      title: 'like'
    }),
    offset,
    limit
  })
)

const { formState, onRestFormState, resetFormState } = useFormState({
  title: undefined,
  status: undefined
})

onRestFormState(() => setPage())

const router = useRouter()

const toAdd = () => router.push({ name: 'special-page-add' })
const toEdit = ({ id }, status) => router.push({ name: 'special-page-edit', params: { id, status } })

const handleDelete = async ({ id }) => {
  await specialPageApi.delete(id)
  message.success('删除完成')
  setPage()
}
</script>
