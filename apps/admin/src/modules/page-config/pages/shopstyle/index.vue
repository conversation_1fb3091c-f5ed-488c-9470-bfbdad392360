<template>
  <uc-layout-form class="m-edit padding-bottom-80" :is-cancel="false" @submit="onSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="品牌logo" name="logo" class="required photo-flex">
            <uc-upload
              v-for="(item, index) in uploadLogoOptions"
              :key="index"
              v-model:list="formState[item.value]"
              :max-length="1"
              show-label
              :label-text="item.text"
            />
          </a-form-item>
          <a-form-item label="店铺名称" class="required">
            <a-input
              v-model:value.trim="formState.shop_name"
              placeholder="请输入店铺名称，不超过50字"
              :maxlength="50"
              class="width-500"
            />
          </a-form-item>
          <a-form-item label="品牌名称" class="required">
            <a-input
              v-model:value.trim="formState.brand_name"
              placeholder="请输入品牌名称，不超过30字"
              :maxlength="30"
              class="width-500"
            />
          </a-form-item>
          <a-form-item label="公众号链接" class="required">
            <a-input v-model:value.trim="formState.officlink" placeholder="请输入关注公众号链接" class="width-500" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="主体色系" class="h-fill">
          <a-form-item label="主题色" class="required">
            <a-input
              v-model:value.trim="formState.theme_colors"
              placeholder="主题色，如#000000"
              class="width-245 m-r-10"
            />
            <a-input v-model:value.trim="formState.theme_text" placeholder="文字色，如#ffffff" class="width-245" />
          </a-form-item>
          <a-form-item label="辅助色" class="required">
            <a-input
              v-model:value.trim="formState.assist_colors"
              placeholder="辅助色，如#000000"
              class="width-245 m-r-10"
            />
            <a-input v-model:value.trim="formState.assist_text" placeholder="文字色，如#ffffff" class="width-245" />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="缺省配置">
          <a-form-item label="页面缺省" class="required photo-flex">
            <uc-upload
              v-for="(item, index) in uploadPageOptions"
              :key="index"
              v-model:list="formState[item.value]"
              :max-length="1"
              show-label
              :label-text="item.text"
            />
          </a-form-item>
          <a-form-item label="通用缺省" class="required photo-flex">
            <uc-upload
              v-for="(item, index) in uploadUniversalOptions"
              :key="index"
              v-model:list="formState[item.value]"
              :max-length="1"
              show-label
              :label-text="item.text"
            />
          </a-form-item>
          <a-form-item label="通用弹窗" class="required photo-flex">
            <uc-upload
              v-for="(item, index) in uploadPopupOptions"
              :key="index"
              v-model:list="formState[item.value]"
              :max-length="1"
              show-label
              :label-text="item.text"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <a-card title="全局分享">
          <a-form-item label="分享海报" name="logo" class="required photo-flex">
            <uc-upload v-model:list="formState.share_posters" :max-length="1" size="large" upload-text="上传海报" />
          </a-form-item>
          <a-form-item label="分享文案" class="required">
            <a-input
              v-model:value.trim="formState.sharetext"
              placeholder="请输入分享文案，不超过20字"
              :maxlength="20"
              class="width-500"
            />
          </a-form-item>
        </a-card>
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>
<script setup>
import { nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import { useFormState, clearFormState } from '@/composables/useFormState'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useTransformMedia, useBatchTransformMedia } from '@/composables/useTransformFormat'
import { UploadLogo, UploadPage, UploadUniversal, UploadPopup } from '../../enums'
import { customPageConfigApi, customPageConfigUpdateApi } from '../../api'
import { colorVerify } from '@/utils/index'

const uploadLogoOptions = UploadLogo.option()
const uploadPageOptions = UploadPage.option()
const uploadUniversalOptions = UploadUniversal.option()
const uploadPopupOptions = UploadPopup.option()

const transformKeys = [
  'err_account',
  'logo_gray',
  'logo_highlight',
  'logo_white',
  'no_authority',
  'page_activity',
  'page_address',
  'page_comment',
  'page_dynamic',
  'page_error',
  'page_fitment',
  'page_gift',
  'page_lose',
  'page_order',
  'page_participate',
  'page_express',
  'share_posters',
  'version_up',
  'coupons_empty',
  'fav_empty',
  'after_empty',
  'prize_empty',
  'return_overrun',
  'integral_empty',
  'not_refund',
  'not_return',
  'composing',
  'custom_overtime',
  'page_product',
  'trial_recruit',
  'trial_report',
  'trial_apply_empty',
  'draw_empty',
  'write_off_failure',
  'write_off_success',
  'crowdfunding_page_order',
  'good_detail_not_exist',
  'distribution_user_empty',
  'distribution_withdrawals_empty',
  'voice_empty',
  'gift_card_empty',
  'employee_purchase_end',
  'employee_purchase_not_start',
  'no_limit_record'
]

const loadData = () => {
  customPageConfigApi('store')
    .get()
    .then(data => {
      const { config } = data
      if (config) {
        const { transformObject } = useBatchTransformMedia(config, transformKeys, 'array')
        Object.assign(config, transformObject)
        setFormState(config)
      }
    })
}

nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

const router = useRouter()

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  shop_name: undefined,
  brand_name: undefined,
  officlink: undefined,
  theme_colors: undefined,
  theme_text: undefined,
  assist_colors: undefined,
  assist_text: undefined,
  err_account: undefined,
  logo_gray: undefined,
  logo_highlight: undefined,
  logo_white: undefined,
  no_authority: undefined,
  page_activity: undefined,
  page_address: undefined,
  page_comment: undefined,
  page_dynamic: undefined,
  page_error: undefined,
  page_fitment: undefined,
  page_gift: undefined,
  page_lose: undefined,
  page_order: undefined,
  page_participate: undefined,
  page_express: undefined,
  version_up: undefined,
  coupons_empty: undefined,
  fav_empty: undefined,
  crowdfunding_page_order: undefined,
  good_detail_not_exist: undefined,
  distribution_user_empty: undefined,
  distribution_withdrawals_empty: undefined,
  gift_card_empty: undefined,
  employee_purchase_end: undefined,
  employee_purchase_not_start: undefined,
  no_limit_record: undefined,

  share_posters: undefined,
  sharetext: undefined,
  page_product: undefined,
  trial_recruit: undefined,
  trial_report: undefined,
  trial_apply_empty: undefined,
  draw_empty: undefined,
  writeOffFailure: undefined,
  writeOffSuccess: undefined,
  voice_empty: undefined
})

//组装formRules
const imgValidator = (rule, value) => {
  if (!value || !value.length) {
    return Promise.reject(rule.message)
  } else {
    return Promise.resolve()
  }
}
let imageRulesLogo = {}
let imageRulesDefault = {}
uploadLogoOptions.forEach(
  op =>
    (imageRulesLogo[op.value] = {
      require: true,
      message: `请上传${op.text}图片`,
      value: formState.value[op.value],
      validator: imgValidator
    })
)
uploadPageOptions.forEach(
  op =>
    (imageRulesDefault[op.value] = {
      require: true,
      message: `请上传${op.text}图片`,
      value: formState.value[op.value],
      validator: imgValidator
    })
)
uploadUniversalOptions.forEach(
  op =>
    (imageRulesDefault[op.value] = {
      require: true,
      message: `请上传${op.text}图片`,
      value: formState.value[op.value],
      validator: imgValidator
    })
)
uploadPopupOptions.forEach(
  op =>
    (imageRulesDefault[op.value] = {
      require: true,
      message: `请上传${op.text}图片`,
      value: formState.value[op.value],
      validator: imgValidator
    })
)

setFormRules({
  ...imageRulesLogo,
  shop_name: { required: true, message: '请填写店铺名称，不超过50字' },
  brand_name: { required: true, message: '请填写品牌名称，不超过30字' },
  officlink: { required: true, message: '请填写关注公众号链接' },
  theme_colors: {
    required: true,
    message: '请正确填写主题色',
    validator(rule, value) {
      if (value && colorVerify(value)) return Promise.resolve()
      return Promise.reject(rule.message)
    }
  },
  theme_text: {
    required: true,
    message: '请正确填写主题文字色',
    validator(rule, value) {
      if (value && colorVerify(value)) return Promise.resolve()
      return Promise.reject(rule.message)
    }
  },
  assist_colors: {
    required: true,
    message: '请正确填写辅助色',
    validator(rule, value) {
      if (value && colorVerify(value)) return Promise.resolve()
      return Promise.reject(rule.message)
    }
  },
  assist_text: {
    required: true,
    message: '请正确填写辅助文字色',
    validator(rule, value) {
      if (value && colorVerify(value)) return Promise.resolve()
      return Promise.reject(rule.message)
    }
  },
  ...imageRulesDefault,
  share_posters: { require: true, message: `请上传分享海报`, validator: imgValidator },
  sharetext: { required: true, message: '请填写分享文案' }
})

const onSubmit = async () => {
  if (!(await validateForm())) {
    return
  }

  const params = { config: { ...formState.value } }
  const { transformObject } = useBatchTransformMedia(params.config, transformKeys, 'string')
  Object.assign(params.config, transformObject)

  useLoadingMessage(customPageConfigUpdateApi('store').post(params)).then(() => {
    message.success('操作成功：设置店铺风格')
  })
}
</script>
<style scoped lang="less">
.m-edit {
  .photo-flex {
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      flex-wrap: wrap;
    }
  }
  .width-500 {
    width: 500px;
  }
  .width-245 {
    width: 245px;
  }
}
</style>
