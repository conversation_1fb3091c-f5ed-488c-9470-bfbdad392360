<template>
  <!-- 会员配置 -->
  <layout-config
    v-model:list="listData"
    class="m-home-config"
    v-bind="{ sidebarList, initItemData, rectStyle }"
    @submit="onSubmit"
  >
    <template #cardInfo>
      <div class="flex flex-sb">
        <a-input
          v-model:value="formState.bg_color"
          addon-before="背景色"
          placeholder="如：#f5f5f5"
          class="w-310"
          :disabled="readonly"
        />
        <uc-upload
          :list="[]"
          class="bg-upload"
          :max-length="1"
          disabled-icon
          upload-text="上传背景"
          :disabled="readonly"
          @update:list="data => (formState.bg_url = data[0])"
        />
      </div>
    </template>
  </layout-config>
</template>
<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import assets from '../../assets.config'
import LayoutConfig from '../../components/layout-config'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import { useFormState } from '@/composables/useFormState'
import { customPageConfigApi, customPageConfigUpdateApi } from '../../api'
import { useSelectRect, useValidateRect } from '../../useRect'
import { RectType } from '../../enums'
import { colorVerify } from '../../../../utils/index'

const defaultBgColor = '#f5f5f5'

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  bg_color: defaultBgColor,
  bg_url: ''
})

setFormRules({
  bg_color: {
    validator(_, value) {
      if (!colorVerify(value)) return Promise.reject('请输入背景颜色(以#开头+6位数字字母组合)')
      return Promise.resolve()
    }
  }
})

nextTick(() => {
  useLoadingMessage(
    customPageConfigApi('member').get().then(res => {
      listData.value = res.config
      res.bg_color = res.bg_color || defaultBgColor
      setFormState(res)
    }),
    {
      loadingText: '正在加载数据'
    }
  )
})

const router = useRouter() // 路由操作

const rectStyle = computed(
  () => `background-color:${formState.value.bg_color};background-image:url(${formState.value.bg_url});`
)

// sidebar 新增初始化数据
const initItemData = {
  [RectType.image]: {
    bg_url: '',
    items: []
  },
  [RectType.navbar]: {
    style_type: 'style01',
    items: []
  },
  [RectType.swiper]: {
    style_type: 'style01',
    items: []
  },
  [RectType.slide]: {
    style_type: 'style01',
    items: []
  },
  [RectType.video]: {
    style_type: 'style01'
  }
}

// 左侧菜单
const sidebarList = ref([
  {
    ...RectType.sidebar(RectType.navbar),
    state: true,
    noSelect: assets.leftNavbarNoselect,
    selected: assets.leftNavbarSelected
  },
  {
    ...RectType.sidebar(RectType.swiper),
    state: true,
    noSelect: assets.homeLeftSwiperNoselect,
    selected: assets.homeLeftSwiperSelected
  },
  {
    ...RectType.sidebar(RectType.slide),
    state: true,
    noSelect: assets.leftSlideNoselect,
    selected: assets.leftSlideSelected
  },
  {
    ...RectType.sidebar(RectType.image),
    state: true,
    noSelect: assets.myImgNoselect,
    selected: assets.myLeftimgSelcted
  },
  {
    ...RectType.sidebar(RectType.video),
    state: true,
    noSelect: assets.myImgNoselect,
    selected: assets.myLeftimgSelcted
  }
])

const listData = ref([]) // 中间内容数组
const { onClearSelectAll } = useSelectRect(listData)

// 点击提交按钮
const onSubmit = async () => {
  const params = { ...formState.value, config: listData.value }
  if (!(await validateForm())) {
    onClearSelectAll()
    return
  }

  if (!useValidateRect(listData.value)) return
  await customPageConfigUpdateApi('member').post(params)
  message.success('操作成功')
}
</script>
<style scoped lang="less">
.m-home-config {
  .bg-upload(120px);
}
</style>
