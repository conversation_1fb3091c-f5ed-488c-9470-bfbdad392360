<template>
  <!-- 我的配置 -->
  <div class="login-config">
    <div class="scroll-x">
      <!-- 头部 -->
      <div class="m-head">
        <div class="u-return" @click="onBack">
          <img :src="assets.return" />返回控制台
        </div>
        <div class="m-btns">
          <a-button type="primary" @click="onSubmit">
            保存
          </a-button>
        </div>
      </div>
      <!-- 左侧菜单 -->
      <div class="m-sidebar">
        <ul class="scroll">
          <li
            v-for="item in sidebarList"
            :key="item.type"
            class="u-drag"
            draggable="true"
            @dragstart="onDragstart($event, item)"
          >
            <img :src="item.noSelect" class="u-img-noselect" />
            <img :src="item.selected" class="u-img-selected" />
            <span class="u-text">{{ item.text }}</span>
          </li>
        </ul>
      </div>
      <!-- 中间小程序 -->
      <div class="m-applets" @click="onClearSelectAll">
        <div class="m-rect" @click.stop @drop.prevent="onDrop" @dragover.prevent>
          <!-- 固定内容 -->
          <div class="u-hd">
            <img v-if="initObj.bg_url" :src="initObj.bg_url" class="u-page-bg-img" />
            <img :src="assets.headerBgWhite" class="u-top-img" />
            <div class="content-box">
              <div class="content-left">
                <img :src="assets.myHeadimg" class="content-img" />
                <div class="conten-size">
                  <span>紫色风信子</span>
                  <div class="conten-size-flex">
                    <img :src="assets.myMember" />
                    <span>xxxx年xx月成为会员</span>
                  </div>
                </div>
              </div>
              <img :src="assets.mySet" class="content-right" />
            </div>
            <div class="initiation">
              <img v-if="initObj.register_url" :src="initObj.register_url" />
            </div>
          </div>
          <!-- 拖拽动态部分 -->
          <draggable v-model="listData" item-key="id">
            <template #item="{ element, index }">
              <div class="elem" @click="onSelect(element, index)" @dragstart="onDragApplets($event, element.state)">
                <!-- 删除 -->
                <div v-show="element.state" class="u-delete" @click.stop="onDelete(index)">
                  <img :src="assets.myDelete" alt />
                </div>
                <!-- 统计 -->
                <div v-if="element.type == 'statistics'">
                  <img :src="assets.myStatistics" class="w-fill" />
                </div>
                <!-- 订单 -->
                <template v-else-if="element.type == 'order'">
                  <template
                    v-if="
                      !element.wait_send &&
                        !element.wait_receive &&
                        !element.finished &&
                        !element.unpaid &&
                        !element.after_sale
                    "
                  >
                    <img :src="assets.myOrder" class="noorder" />
                  </template>
                  <template v-else>
                    <div class="my-order">
                      <div class="my-order-border">
                        <p>我的订单</p>
                        <div class="my-order-right">
                          <p>查看全部</p>
                          <img :src="assets.myArrowIcon" />
                        </div>
                      </div>
                    </div>
                    <div class="order">
                      <div class="order-item">
                        <img v-if="element.unpaid" :src="element.unpaid" />
                        <img v-else :src="assets.myImgSelect" class="placeholder" />
                        <p>待付款</p>
                      </div>
                      <div class="order-item">
                        <img v-if="element.wait_send" :src="element.wait_send" />
                        <img v-else :src="assets.myImgSelect" class="placeholder" />
                        <p>待发货</p>
                      </div>
                      <div class="order-item">
                        <img v-if="element.wait_receive" :src="element.wait_receive" />
                        <img v-else :src="assets.myImgSelect" class="placeholder" />
                        <p>待收货</p>
                      </div>
                      <div class="order-item">
                        <img v-if="element.finished" :src="element.finished" />
                        <img v-else :src="assets.myImgSelect" class="placeholder" />
                        <p>已完成</p>
                      </div>
                      <div class="order-item">
                        <img v-if="element.after_sale" :src="element.after_sale" />
                        <img v-else :src="assets.myImgSelect" class="placeholder" />
                        <p>售后退款</p>
                      </div>
                    </div>
                  </template>
                </template>
                <!-- 菜单 -->
                <div v-else-if="element.type == 'menu'" class="menu">
                  <div v-if="element.items.length == 0" class="menu-none"></div>
                  <template v-else>
                    <div v-for="(item, i) in element.items" :key="i" class="menu-item">
                      <div class="menu-item-left">
                        <img v-if="item.icon" :src="item.icon" />
                        <div v-else class="placeholder"></div>
                        <span>{{ item.name }}</span>
                      </div>
                      <img class="arrow" :src="assets.myArrowIcon" />
                    </div>
                  </template>
                </div>
                <!-- 图片 -->
                <drag-img
                  v-else-if="element.type === 'image'"
                  :info="element"
                  @update:info="updateInfo($event, element, index)"
                  @clear="onClearSelectAll(index)"
                />
                <!-- 视频 -->
                <drag-video v-else-if="element.type === 'video'" :info="element" @clear="onClearSelectAll(index)" />
                <!-- 边框 -->
                <div v-show="element.state" class="u-border"></div>
              </div>
            </template>
          </draggable>
          <!-- 浮窗 -->
          <div class="fab">
            <img v-for="(item,index) in initObj.fabList" :key="index" :src="item.photo_url" alt />
          </div>
          <!-- tabbar -->
          <div class="tabbar">
            <img :src="assets.myTabbar" alt />
          </div>
        </div>
      </div>
      <!-- 右侧元素面板 -->
      <div class="m-element">
        <div class="scroll">
          <card-info :card-form="cardForm" :init-obj="initObj" :select-img-size="selectImgSize" @updata="updata" />
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import assets from '../../assets.config'
import { useLoadingMessage } from '@/composables/useLoadingMessage'
import draggable from 'vuedraggable'
import cardInfo from './card-info'
import dragImg from './drag-img'
import DragVideo from './drag-video'
import { cloneDeep } from 'lodash'
import { customPageConfigApi, customPageConfigUpdateApi } from '../../api'
import { useTransformData } from '../../useTransformData'

const [transfromToForm] = useTransformData()

// 加载数据
const loadData = () => {
  customPageConfigApi('me').get().then(({ config }) => {
    listData.value = transfromToForm(config)
    const page = config.filter(item => item.type == 'page')
    initObj.value = page.length ? page[0] : defaultInitObj
  })
}
nextTick(() => {
  useLoadingMessage(loadData(), {
    loadingText: '正在加载数据'
  })
})

const router = useRouter() // 路由操作
// sidebar 新增初始化数据
const initItemData = {
  statistics: {
    integral: '', // 积分
    coupon: '', // 优惠券
    dynamic: '' // 动态
  },
  order: {
    type: 'order', // 订单
    wait_send: '', // 待发货
    wait_receive: '', // 待收货
    finished: '', // 已完成
    unpaid: '', // 待付款
    after_sale: '' // 售后/退款
  },
  menu: {
    type: 'menu', // 菜单
    style_type: 'style01', // 演示类型
    items: []
  },
  image: {
    type: 'image', // 图片
    bg_url: '', // 图片背景
    items: []
  },
  video: {
    type: 'video', // 视频
    cover_url: '', // 视频封面
    style_type: 'style01',
    src: ''
  }
}
// 左侧菜单
const sidebarList = ref([
  {
    key: 'statistics',
    text: '统计',
    state: true,
    noSelect: assets.myStatisticsNoselect,
    selected: assets.myStatisticsSelected
  },
  {
    key: 'order',
    text: '订单',
    state: true,
    noSelect: assets.myOrderNoSelect,
    selected: assets.myOrderSelected
  },
  {
    key: 'menu',
    text: '菜单',
    state: true,
    noSelect: assets.myMenuNoselect,
    selected: assets.myMenuSelected
  },
  {
    key: 'image',
    text: '图片',
    state: true,
    noSelect: assets.myImgNoselect,
    selected: assets.myLeftimgSelcted
  },
  {
    key: 'video',
    text: '视频',
    state: true,
    noSelect: assets.leftVideoNoselect,
    selected: assets.leftVideoSelected
  }
])
//右侧默认页面设置
const initObj = ref({})
// 默认值
const defaultInitObj = {
  type: 'page',
  bg_url: '',
  register_url: '',
  wechat_public_url: '',
  fabList: []
}
// 右侧内容
const cardForm = computed(() => listData.value.find(item => item.state) || {})
//中间内容数组
const listData = ref([])
// 当前选中图片大小
const selectImgSize = ref({})
// 点击返回控制台
const onBack = () => {
  router.back()
}
// 获取选择图片的大小
const getSelectImgSize = index => {
  const elemEls = document.querySelectorAll('.m-applets .m-rect .elem')
  const { width, height } = elemEls[index].getBoundingClientRect() // 选中图片区域大小
  selectImgSize.value = { width, height }
}
// 点击提交按钮
const onSubmit = async () => {
  listData.value = listData.value.filter(item => item.type != 'page')
  const obj = { config: [...listData.value, initObj.value] }
  try {
    if (!initObj.value.bg_url) {
      throw new Error('请上传背景图片')
    }
    listData.value.forEach(
      ({
        type,
        bg_url,
        items,
        wait_send,
        wait_receive,
        finished,
        unpaid,
        after_sale,
        integral,
        coupon,
        dynamic,
        style_type,
        cover_url,
        src
      }) => {
        switch (type) {
          // case "statistics":
          //   if (!integral) throw new Error("请输入统计积分");
          //   if (!coupon) throw new Error("请输入统计优惠券");
          //   if (!dynamic) throw new Error("请输入统计动态");
          //   break;
          case 'order':
            if (!unpaid) throw new Error('请上传待付款图片')
            if (!wait_send) throw new Error('请上传待发货图片')
            if (!wait_receive) throw new Error('请上传待收货图片')
            if (!finished) throw new Error('请上传已完成图片')
            if (!after_sale) throw new Error('请上传售后退款图片')
            break
          case 'menu':
            if (items.length == 0) throw new Error('菜单不能为空')
            break
          case 'image':
            if (!bg_url) throw new Error('请上传热区背景图片')
            const len = items.length
            if (len > 0) {
              // 验证热区
              const isInter = items.find(({ top, left, width, height }, i) => {
                if (i < len - 1) {
                  const Xa1 = left
                  const Ya1 = top
                  const Xa2 = left + width
                  const Ya2 = top + height
                  return items.slice(i + 1).some(item => {
                    const Xb1 = item.left
                    const Yb1 = item.top
                    const Xb2 = item.left + item.width
                    const Yb2 = item.top + item.height
                    return Math.max(Xa1, Xb1) <= Math.min(Xa2, Xb2) && Math.max(Ya1, Yb1) <= Math.min(Ya2, Yb2)
                  })
                }
              })
              if (isInter) throw new Error('热区不能重合')
            }
            break
          case 'video':
            // if (!cover_url) throw new Error('请上传视频封面图片')
            if (!src) throw new Error('请输入视频地址')
            break
        }
      }
    )
  } catch ({ message: err }) {
    message.error(`操作失败：${err}`)
    return
  }
  //发送请求
  onClearSelectAll()
  await customPageConfigUpdateApi('me').post(obj)
  message.success('操作成功')
}
// 清理所有选中
const onClearSelectAll = index => {
  listData.value.forEach((item, i) => {
    item.state = i === index
    if (item.state) {
      item.type === 'image' && getSelectImgSize(index)
      return
    }
    // 清理热区
    switch (item.type) {
      case 'image':
        item.items.forEach(it => {
          it.state = false
        })
        break
    }
  })
}
//删除选中
const onDelete = index => {
  listData.value.splice(index, 1)
}
// 选中当前区域
const onSelect = (item, index) => {
  onClearSelectAll()
  item.state = true
  item.type === 'image' && getSelectImgSize(index)
}
// 拖拽左侧 sidebar
const onDragstart = (e, item) => {
  e.dataTransfer.setData('leftValue', item.key)
}
// 松开
const onDrop = ({ clientY, dataTransfer }) => {
  const type = dataTransfer.getData('leftValue')
  if (type) {
    const elemEls = document.querySelectorAll('.m-applets .m-rect .elem')
    let index = Array.prototype.findIndex.call(elemEls, el => {
      const { top, height } = el.getBoundingClientRect()
      return clientY < top + height / 2
    })
    index < 0 && (index = elemEls.length)
    const data = { type, state: true, ...cloneDeep(initItemData[type]) }
    onClearSelectAll()
    // 我的订单只能拖拽一个进来
    if (type == 'order' || type == 'statistics') {
      const List = listData.value.filter(item => item.type == type)
      if (List.length > 0) {
        message.error(`${type == 'order' ? '我的订单' : '统计'}只能拖拽生成最多一个`)
        return
      }
    }
    listData.value.splice(index, 0, data)
    type === 'image' &&
      nextTick(() => {
        const imgEl = document.querySelector(`.m-applets .m-rect .elem:nth-child(${index + 1}) .u-bg-img>img`)
        imgEl.onload = () => {
          const { width, height } = imgEl.getBoundingClientRect() // 选中图片区域大小
          selectImgSize.value = { width, height }
        }
      })
  }
}
// 拖拽中间部分
const onDragApplets = (e, state) => {
  if (!state) {
    e.stopPropagation()
    e.preventDefault()
  }
}
//更新数据
const updata = ({ type, data }) => {
  // 默认配置--选中区域配置 对象
  if (type == 'initObj') {
    initObj.value = data
  } else {
    listData.value.forEach(item => {
      if (item.state) {
        for (const key in data) {
          item[key] = data[key]
        }
      }
    })
  }
}

const updateInfo = (date, obj, index) => {
  listData.value[index] = date
}
</script>
<style scoped lang="less">
.login-config {
  @primary: #1890ff;
  @padding: 20px;
  @margin-bottom: 20px;
  p {
    margin-bottom: 0;
  }
  .scroll() {
    .scroll {
    }
  }
  width: 100%;
  height: 100%;
  overflow-x: auto;
  .scroll-x {
    display: flex;
    min-width: 1280px;
    height: 100vh;
    padding-top: 60px;
    background: #f0f2f5;

    .m-head {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 60px;
      z-index: 9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 @padding;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      .u-return {
        display: flex;
        align-items: center;
        color: @primary;
        cursor: pointer;
        font-size: 16px;
        img {
          width: 16px;
          margin-right: 8px;
        }
      }
      .m-btns {
        :deep(.ant-btn) {
          margin-left: 10px;
        }
      }
    }
    .m-sidebar {
      .scroll();
      width: 80px;
      height: 100%;
      background: #fff;
      overflow-y: auto;

      > ul > li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 80px;
        cursor: pointer;
        &:hover {
          background: fade(@primary, 10%);
          .u-img-noselect {
            display: none;
            opacity: 0;
          }
          .u-img-selected {
            display: block;
            opacity: 1;
          }
          .u-text {
            color: @primary;
          }
        }
        &.u-drag {
          cursor: move;
        }
        .u-img-noselect {
          display: block;
          transition: all 1s;
          opacity: 1;
        }
        .u-img-selected {
          display: none;
          transition: all 1s;
          opacity: 0;
        }
        .u-text {
          margin-top: 5px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
    .m-applets {
      flex-grow: 1;
      width: 30%;
      height: 100%;
      overflow-y: auto;
      padding: 24px 0;
      // background: #f0f2f5;
      .m-rect {
        position: relative;
        width: 375px;
        min-height: 667px;
        margin: 0 auto;
        background: v-bind(getBgColor);
        padding-bottom: 53px;
        background: #f5f5f5;
        .u-hd {
          position: relative;
          top: 0;
          left: 0;
          z-index: 5;
          width: 100%;
          background: #000000;
          opacity: 0.7;
          .content-box {
            position: absolute;
            left: 0;
            top: 100px;
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 60px;
            padding: 0 20px;
            margin: 15px auto;
            font-size: 16px;
            font-family: PingFang SC;
            font-weight: bold;
            line-height: 16px;
            color: #ffffff;
            .content-left {
              display: flex;
              align-items: center;

              .content-img {
                width: 65px;
                height: 65px;
                border-radius: 65px;
                margin-right: 15px;
              }
              .conten-size {
                height: 65px;
                display: flex;
                flex-flow: column;
                padding: 3px 0;
                justify-content: space-around;
                font-size: 12px;
                span:first-child {
                  font-size: 18px;
                }
                .conten-size-flex {
                  img {
                    width: 12px;
                    height: 13px;
                    color: #ffffff;
                    margin-right: 4px;
                  }
                }
              }
            }
            .content-right {
              align-self: flex-start;
              margin-top: 9px;
              width: 20px;
              height: 21px;
            }
          }
          .initiation {
            width: 100%;
            position: absolute;
            bottom: 0;
            left: 0;
            img {
              width: 100%;
              height: 100%;
            }
          }

          img {
            width: 100%;
          }
          .u-top-img {
            display: block;
            width: 100%;
            position: absolute;
            top: 0;
            left: 0;
          }
          .u-page-bg-img {
            display: block;
            width: 100%;
          }
        }
        .my-order {
          padding: 10px 20px 0;
          background: #fff;
          font-size: 12px;
          font-family: PingFang SC;
          font-weight: bold;
          line-height: 16px;
          color: #333333;
          &-border {
            border-bottom: 1px solid #eeeeee;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-bottom: 11px;
          }
          .my-order-right {
            font-weight: 400;
            color: #000000;
            opacity: 0.65;
            display: flex;
            align-items: center;
            img {
              width: 10px;
              height: 10px;
            }
          }
        }
        .elem {
          position: relative;
          top: 0;
          left: 0;
          width: 100%;
          margin-bottom: 12px;
          .order {
            display: flex;
            justify-content: space-between;
            align-items: center;
            // margin-bottom: 12px;
            background: #fff;
            padding: 15px 0;
            font-size: 12px;
            font-family: PingFang SC;
            line-height: 16px;
            font-weight: 400;
            color: #000000;
            opacity: 0.85;
            .order-item {
              width: 20%;
              display: flex;
              flex-flow: column;
              align-items: center;
              img {
                width: 20px;
                margin-bottom: 5px;
              }
              .placeholder {
                width: 20px;
                height: 15px;
              }
            }
          }
          .noorder {
            width: 100%;
            height: 104px;
            background: #ffffff;
          }
          .menu {
            background: #fff;
            padding: 0 14px;
            &-none {
              width: 100%;
              height: 50px;
            }
            &-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px 0;
              border-bottom: 1px solid rgb(233, 232, 232);
              font-size: 12px;
              font-family: PingFang SC;
              font-weight: bold;
              line-height: 16px;
              color: #000000;
              opacity: 0.85;
              &-left {
                display: flex;
                align-items: center;
                img {
                  width: 20px;
                  // height: 20px;
                  margin-right: 12px;
                }
                .placeholder {
                  width: 30px;
                  height: 30px;
                  background: rgb(209, 209, 209);
                }
              }
              .arrow {
                width: 10px;
                height: 10px;
              }
            }
            &-item:last-child {
              border: none;
            }
          }
          .u-delete {
            position: absolute;
            top: 2px;
            right: 2px;
            z-index: 2;
            width: 20px;
            cursor: pointer;
            img {
              display: block;
              width: 100%;
              height: auto;
            }
          }
          .u-border {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;
            border: 2px solid @primary;
            background: transparent;
            box-shadow: 0 0 6px rgba(0, 0, 0, 0.4);
          }
        }
        .fab {
          position: absolute;
          bottom: 100px;
          right: 15px;
          img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            margin-bottom: 10px;
            display: block;
          }
        }
        .tabbar {
          position: absolute;
          bottom: 0;
          left: 0;
          display: flex;
          justify-content: space-around;
          width: 100%;
          height: 53px;
          background: #fff;
          img {
            width: 100%;
          }
        }
      }
    }
    .m-element {
      .scroll();
      width: 500px;
      height: 100%;
      overflow-y: auto;
      background: #fff;
    }
  }
}
</style>
