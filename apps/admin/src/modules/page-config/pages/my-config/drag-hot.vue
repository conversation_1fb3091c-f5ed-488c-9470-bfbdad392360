<template>
  <!-- 拖拽热区 -->
  <div class="m-hot-drag" @click.stop>
    <div
      v-for="(item, index) in listData"
      :key="item.id"
      class="u-hot-area"
      :style="setHotStyle(item)"
      @click="onHandleHot(item)"
      @mousedown.prevent="onMousedown($event, item)"
    >
      <div class="u-index">
        {{ index + 1 }}
      </div>
      <template v-if="item.state">
        <div class="u-delete" @click.stop="onDeleteHot(item)">
          <img :src="assets.delete" alt="" />
        </div>
        <div class="u-dot">
          <span v-for="i in 8" :key="i" @mousedown.stop.prevent="onDotMousedown($event, i, item)"></span>
        </div>
      </template>
    </div>
  </div>
</template>
<script setup>
import { cloneDeep } from 'lodash'
import { ref, watch, computed, getCurrentInstance, onMounted, onUnmounted } from 'vue'
import assets from '../../assets.config'

// 箭头 code 移动值
const arrowCodes = {
  ArrowUp: { x: 0, y: -1 },
  ArrowRight: { x: 1, y: 0 },
  ArrowDown: { x: 0, y: 1 },
  ArrowLeft: { x: -1, y: 0 }
}
const props = defineProps({
  // 列表数据
  list: {
    type: Array,
    default: () => []
  },
  // 父组件ref名称
  parentRefName: {
    type: String,
    default: 'bgImgRef'
  },
  // 最小值
  minSize: {
    type: Array,
    default: () => [40, 40]
  },
  // 禁用
  disabled: {
    type: Boolean,
    default: false
  }
})
const emit = defineEmits(['clear', 'update:list'])
const instance = getCurrentInstance()
const listData = ref([]) // 列表数据
// 设置热区样式
const setHotStyle = computed(() => {
  return ({ top, left, width, height, state }) => ({
    top: `${top}px`,
    left: `${left}px`,
    width: `${width}px`,
    height: `${height}px`,
    cursor: `${state ? 'move' : 'default'}`
  })
})
// 获取画图区域的rect
const getRect = () => {
  return instance.parent.exposed[props.parentRefName].value.getBoundingClientRect()
}
// 点击区域热区
const onHandleHot = ({ id, state }) => {
  if (props.disabled || state) return
  // 清理其它聚焦
  listData.value.forEach(item => {
    item.state = item.id === id
  })
  emit('clear')
}
// 移动位置
const moveTo = ({ x, y }, { top, left, width, height }, { width: rw, height: rh }, item) => {
  let disX = left + x
  let disY = top + y
  if (disX < 0) {
    disX = 0
  } else if (disX > rw - width) {
    disX = ~~(rw - width)
  }
  if (disY < 0) {
    disY = 0
  } else if (disY > rh - height) {
    disY = ~~(rh - height)
  }
  item.left = disX
  item.top = disY
}
// 点击热区
const onMousedown = ({ clientX, clientY }, item) => {
  if (props.disabled || !item.state) return
  const initItem = cloneDeep(item)
  const rect = getRect()
  document.onmousemove = e => {
    const x = e.clientX - clientX
    const y = e.clientY - clientY
    moveTo({ x, y }, initItem, rect, item)
  }
  document.onmouseup = () => {
    document.onmousemove = null
    document.onmouseup = null
  }
}
// 点击删除热区
const onDeleteHot = ({ id }) => {
  if (props.disabled) return
  const index = listData.value.findIndex(item => item.id === id)
  listData.value.splice(index, 1)
}
// 按下操作点
const onDotMousedown = ({ clientX, clientY }, index, item) => {
  if (props.disabled || !item.state) return
  const [iw, ih] = props.minSize
  const { top, left, width, height } = cloneDeep(item)
  const { width: rw, height: rh } = getRect()
  const ratio = width / height
  document.onmousemove = e => {
    const x = e.clientX - clientX
    const y = e.clientY - clientY
    switch (index - 1) {
      case 0:
        // 左上
        ;(() => {
          let w = width - x
          if (x > 0) {
            w < iw && (w = iw)
          } else {
            ;-x > left && (w = width + left)
          }
          // 设置高度
          let h = ~~(w / ratio)
          h < ih && (h = ih)
          h > top + height && (h = top + height)
          item.height = h
          item.top = top - h + height
          // 设置宽度
          w = h * ratio
          w < iw && (w = iw)
          item.width = w
          item.left = left + width - w
        })()
        break
      case 1:
        // 中上
        ;(() => {
          let h = height - y
          if (y > 0) {
            h < ih && (h = ih)
          } else {
            ;-y > top && (h = height + top)
          }
          item.height = h
          item.top = top + height - h
        })()
        break
      case 2:
        // 右上
        ;(() => {
          let w = width + x
          if (x < 0) {
            w < iw && (w = iw)
          } else {
            w > rw - left && (w = rw - left)
          }
          // 设置高度
          let h = ~~(w / ratio)
          h < ih && (h = ih)
          h > top + height && (h = top + height)
          item.height = h
          item.top = top - h + height
          // 设置宽度
          w = h * ratio
          w < iw && (w = iw)
          item.width = w
        })()
        break
      case 3:
        // 右中
        ;(() => {
          let w = width + x
          if (x < 0) {
            w < iw && (w = iw)
          } else {
            w > rw - left && (w = rw - left)
          }
          item.width = w
        })()
        break
      case 4:
        // 右下
        ;(() => {
          let w = width + x
          if (x < 0) {
            w < iw && (w = iw)
          } else {
            w > rw - left && (w = rw - left)
          }
          // 设置高度
          let h = ~~(w / ratio)
          h < ih && (h = ih)
          h > rh - top && (h = rh - top)
          item.height = h
          // 设置宽度
          w = h * ratio
          w < iw && (w = iw)
          item.width = w
        })()
        break
      case 5:
        // 中下
        ;(() => {
          let h = height + y
          if (y < 0) {
            h < ih && (h = ih)
          } else {
            h > rh - top && (h = rh - top)
          }
          item.height = h
        })()
        break
      case 6:
        // 左下
        ;(() => {
          let w = width - x
          if (x > 0) {
            w < iw && (w = iw)
          } else {
            ;-x > left && (w = width + left)
          }
          // 设置高度
          let h = ~~(w / ratio)
          h < ih && (h = ih)
          h > rh - top && (h = rh - top)
          item.height = h
          // 设置宽度
          w = h * ratio
          w < iw && (w = iw)
          item.width = w
          item.left = left + width - w
        })()
        break
      case 7:
        // 左中
        ;(() => {
          let w = width - x
          if (x > 0) {
            w < iw && (w = iw)
          } else {
            ;-x > left && (w = width + left)
          }
          item.width = w
          item.left = left + width - w
        })()
        break
    }
  }
  document.onmouseup = () => {
    document.onmousemove = null
    document.onmouseup = null
  }
}
// 点击鼠标
const onKeyup = ({ code }) => {
  if (props.disabled || !Object.keys(arrowCodes).includes(code)) return
  const index = listData.value.findIndex(item => item.state) // 是否存在选中热区
  if (index < 0) return
  const item = listData.value[index]
  const rect = getRect()
  moveTo(arrowCodes[code], item, rect, item) // 移动位置
}

watch(
  () => props.list,
  value => {
    listData.value = value
  },
  { deep: true, immediate: true }
)
watch(
  listData,
  value => {
    emit('update:list', value)
  },
  { deep: true }
)

onMounted(() => {
  document.addEventListener('keydown', onKeyup)
})
onUnmounted(() => {
  document.removeEventListener('keydown', onKeyup)
})
</script>
<style scoped lang="less">
@primary: #1890ff;
.m-hot-drag {
  img {
    display: block;
  }
  .u-hot-area {
    position: absolute;
    z-index: 8;
    border: 1px dashed @primary;
    background: rgba(0, 0, 0, 0.3);
    cursor: move;
    .u-index,
    .u-delete {
      position: absolute;
      top: 0;
    }
    .u-index {
      left: 0;
      width: 20px;
      height: 20px;
      line-height: 20px;
      background: @primary;
      text-align: center;
      color: #fff;
      font-size: 12px;
    }
    .u-delete {
      right: 0;
      width: 20px;
      cursor: pointer;
      img {
        width: 100%;
        height: auto;
      }
    }
    .u-dot {
      @dot-width: 10px;
      @dot-height: 10px;
      span {
        position: absolute;
        display: block;
        width: @dot-width;
        height: @dot-height;
        border: 1px solid @primary;
        background: #fff;
        &:nth-child(1) {
          top: -(@dot-height / 2);
          left: -(@dot-width / 2);
          cursor: nw-resize;
        }
        &:nth-child(2) {
          top: -(@dot-height / 2);
          left: 50%;
          transform: translateX(-50%);
          cursor: n-resize;
        }
        &:nth-child(3) {
          top: -(@dot-height / 2);
          right: -(@dot-width / 2);
          cursor: ne-resize;
        }
        &:nth-child(4) {
          top: 50%;
          right: -(@dot-width / 2);
          transform: translateY(-50%);
          cursor: e-resize;
        }
        &:nth-child(5) {
          right: -(@dot-width / 2);
          bottom: -(@dot-height / 2);
          cursor: se-resize;
        }
        &:nth-child(6) {
          left: 50%;
          bottom: -(@dot-height / 2);
          transform: translateX(-50%);
          cursor: s-resize;
        }
        &:nth-child(7) {
          left: -(@dot-width / 2);
          bottom: -(@dot-height / 2);
          cursor: sw-resize;
        }
        &:nth-child(8) {
          top: 50%;
          left: -(@dot-width / 2);
          transform: translateY(-50%);
          cursor: w-resize;
        }
      }
    }
  }
}
</style>
