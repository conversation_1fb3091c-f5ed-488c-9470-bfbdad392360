<template>
  <a-card :title="cardTitle" :bordered="false" class="card-box" @click="clearSelectAll">
    <!-- 默认配置 -->
    <div v-if="objLength(newCardForm)">
      <uc-upload
        :list="[]"
        class="bg-upload"
        :max-length="1"
        upload-text="更换背景图片"
        @update:list="data => (newInitObj.bg_url = data[0])"
      />
      <div class="img-flex">
        <uc-upload
          :list="newInitObj.register_url ? [newInitObj.register_url] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="注册入会"
          @update:list="data => (newInitObj.register_url = data[0])"
        />
        <uc-upload
          :list="newInitObj.wechat_public_url ? [newInitObj.wechat_public_url] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="关注公众号"
          @update:list="data => (newInitObj.wechat_public_url = data[0])"
        />
      </div>
      <div>
        <div class="flex flex-cc flex-sb">
          <div style="font-size: 16px">浮窗设置</div>
          <a-button type="primary" @click="onAddFab">添加浮窗</a-button>
        </div>
        <ul class="m-list-info m-fab">
          <draggable v-model="newInitObj.fabList" item-key="id">
            <template #item="{ element, index }">
              <li
                class="list-item"
                :aria-selected="element.select"
                @click="onClickFabItem(element)"
                @dragstart="onDragstart($event, element)"
              >
                <div v-if="element.photo_url" class="u-img">
                  <img :src="element.photo_url" alt />
                </div>
                <div class="u-text">
                  <p class="u-tit">浮窗{{ index + 1 }}</p>
                  <p class="u-subtit z-index-9 relative">
                    <uc-jump
                      :value="element"
                      :ignore-jumps="['prize', 'no']"
                      @update:value="onUpdateFabElement($event, element)"
                    />
                  </p>
                </div>
                <div v-if="noEdit" class="btn z-index-9 relative">
                  <a-button type="link" @click.stop="onFabEdit(element)">编辑</a-button>
                </div>
                <template v-if="element.select">
                  <div class="u-delete" @click="onDeleteFab(index)">
                    <img :src="assets.delete" alt />
                  </div>
                  <div class="u-border"></div>
                </template>
              </li>
            </template>
          </draggable>
        </ul>
        <add-fab-modal v-model:visible="isShowAddFabModal" :value="fabEditModalForm" @submit="onAddTabSubmit" />
      </div>
    </div>
    <!-- 当前选中配置 -->
    <div v-else @click.stop>
      <!-- 统计 -->

      <!-- 订单 -->
      <div v-if="newCardForm.type == 'order'" class="order">
        <uc-upload
          :list="newCardForm.unpaid ? [newCardForm.unpaid] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="待付款"
          @update:list="data => (newCardForm.unpaid = data[0])"
        />
        <uc-upload
          :list="newCardForm.wait_send ? [newCardForm.wait_send] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="待发货"
          @update:list="data => (newCardForm.wait_send = data[0])"
        />
        <uc-upload
          :list="newCardForm.wait_receive ? [newCardForm.wait_receive] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="待收货"
          @update:list="data => (newCardForm.wait_receive = data[0])"
        />
        <uc-upload
          :list="newCardForm.finished ? [newCardForm.finished] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="已完成"
          @update:list="data => (newCardForm.finished = data[0])"
        />
        <uc-upload
          :list="newCardForm.after_sale ? [newCardForm.after_sale] : []"
          :max-length="1"
          upload-text=" "
          show-label
          label-text="售后退款"
          @update:list="data => (newCardForm.after_sale = data[0])"
        />
      </div>
      <!-- 菜单样式 -->
      <ul v-if="isShowStyle()" class="m-list-style">
        <li v-for="item in styleList" :key="item.value" :class="isSelect(item.value)" @click="onClickStyle(item.value)">
          <img class="selected" :src="item.selectedUrl" alt="选中" />
          <img class="no-select" :src="item.noSelectUrl" alt="未选中" />
          <img class="u-triangle" :src="assets.selected" alt="选中三角" />
        </li>
      </ul>
      <!-- 菜单样式 -->
      <ul v-if="newCardForm.type === 'video'" class="m-list-style">
        <li
          v-for="item in videoStyleList"
          :key="item.value"
          :class="isSelect(item.value)"
          @click="onClickStyle(item.value)"
        >
          <img class="selected" :src="item.selectedUrl" alt="选中" />
          <img class="no-select" :src="item.noSelectUrl" alt="未选中" />
          <img class="u-triangle" :src="assets.selected" alt="选中三角" />
        </li>
      </ul>
      <!-- 图片 -->
      <div v-if="newCardForm.type == 'image'" class="image">
        <uc-upload
          :list="[]"
          class="bg-upload"
          :max-length="1"
          upload-text="更换背景图片"
          @update:list="data => (newCardForm.bg_url = data[0])"
        />
      </div>
      <!-- 视频 -->
      <div v-if="newCardForm.type === 'video'" class="u-register img-flex">
        <!-- <uc-upload
          :list="newCardForm.cover_url ? [newCardForm.cover_url] : []"
          :max-length="1"
          upload-text="上传封面"
          @update:list="data => (newCardForm.cover_url = data[0])"
        /> -->
        <a-textarea
          v-model:value="newCardForm.src"
          placeholder="添加视频可提升成交转化，有利于获取更多新流量,时长 9-30 秒，宽高比 16:9，视频不能超过5M，支持mp4视频格式"
          style="height: 104px"
          :rows="4"
        />
      </div>
      <!-- 菜单 -->
      <!-- 底部按钮 -->
      <div v-if="getBtnText()" class="u-btn-add">
        <a-button type="primary" block @click="onAdd">
          {{ getBtnText() }}
        </a-button>
      </div>
      <!-- 菜单样式 -->
      <ul v-if="isShowStyle()" class="m-list-style">
        <li v-for="item in styleList" :key="item.value" :class="isSelect(item.value)" @click="onClickStyle(item.value)">
          <img class="selected" :src="item.selectedUrl" alt="选中" />
          <img class="no-select" :src="item.noSelectUrl" alt="未选中" />
          <img class="u-triangle" :src="assets.selected" alt="选中三角" />
        </li>
      </ul>
      <!-- 列表 -->
      <ul v-if="isShowList()" class="m-list-info">
        <draggable v-model="newCardForm.items" item-key="id" @change="onChangeDrag">
          <template #item="{ element, index }">
            <li
              class="list-item"
              :aria-selected="element.select"
              @click="onClickItem(element)"
              @dragstart="onDragstart($event, element)"
            >
              <div v-if="element.icon" class="u-img">
                <img :src="element.icon" alt />
              </div>
              <div class="u-text">
                <p class="u-tit">
                  {{ getName(element, index) }}
                </p>
                <p class="u-subtit z-index-9 relative">
                  <uc-jump
                    :value="element"
                    :ignore-jumps="['prize', 'no']"
                    @update:value="onUpdateElement($event, element)"
                  />
                </p>
              </div>
              <div v-if="noEdit" class="btn z-index-9 relative">
                <a-button type="link" @click.stop="onEdit(element)">编辑</a-button>
              </div>
              <template v-if="element.select">
                <div class="u-delete" @click="onDelete(index)">
                  <img :src="assets.delete" alt />
                </div>
                <div class="u-border"></div>
              </template>
            </li>
          </template>
        </draggable>
      </ul>
      <!-- 新建模态框 -->
      <add-modal
        v-model:visible="isShowAddModal"
        :value="editModalForm"
        :type="newCardForm.type"
        @submit="onAddSubmit"
      />
    </div>
  </a-card>
</template>

<script setup>
import { computed, watch, ref } from 'vue'
import { cloneDeep } from 'lodash'
import addModal from './add-modal.vue'
import addFabModal from './add-fab-modal.vue'
import draggable from 'vuedraggable'
import dragImg from './drag-img.vue'
import DragVideo from './drag-video.vue'
import { generateRandom } from '@/utils/functions'
import assets from '../../assets.config'
const props = defineProps({
  // 默认配置对象
  initObj: {
    type: Object,
    required: true
  },
  // 选中的对象以及配置标题
  cardForm: {
    type: Object,
    required: true
  },
  // 选中图片区域大小
  selectImgSize: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['updata'])
const initHotSize = { width: 100, height: 100 } // 初始化热区大小
const { width: iw, height: ih } = initHotSize

const styleConfigs = {
  // 视频
  video: {
    title: '视频设置',
    styles: [
      {
        value: 'style01',
        selectedUrl: assets.slide01Selected,
        noSelectUrl: assets.slide01Noselect
      },
      {
        value: 'style02',
        selectedUrl: assets.slide02Selected,
        noSelectUrl: assets.slide02Noselect
      }
    ]
  }
}

//选中对象
const newCardForm = ref({})
watch(
  () => props.cardForm,
  newValue => (newCardForm.value = newValue),
  {
    deep: true,
    immediate: true
  }
)
//默认对象
const newInitObj = ref({})

watch(
  () => props.initObj,
  newValue => (newInitObj.value = newValue),
  {
    deep: true,
    immediate: true
  }
)

//更新父组件值
const updata = type => {
  let data = {}
  if (type == 'initObj') {
    data = newInitObj.value
  } else {
    data = newCardForm.value
  }
  emit('updata', { type, data })
}
//判断对象长度 是否为空
const objLength = obj => {
  return Object.keys(obj).length == 0
}
// 类型对应信息
const typeInfo = {
  statistics: {
    title: '统计设置'
  },
  order: {
    title: '订单设置'
  },
  menu: {
    title: '菜单设置',
    btnText: '添加导航菜单'
  },
  image: {
    title: '图片设置',
    btnText: '添加图片热区'
  }
}
const defaultTitle = '页面设置'
//标题
const cardTitle = () => {
  try {
    return typeInfo[newCardForm.value.type].title || defaultTitle
  } catch {
    return defaultTitle
  }
}
// 按钮文字
const getBtnText = () => {
  try {
    return typeInfo[newCardForm.value.type].btnText || false
  } catch {
    return false
  }
}
//菜单样式
const styleList = [
  {
    value: 'style01',
    selectedUrl: assets.style01NavbarSelected,
    noSelectUrl: assets.style01NavbarNoSelect
  }
]

//菜单样式
const videoStyleList = [
  {
    value: 'style01',
    selectedUrl: assets.video01Selected,
    noSelectUrl: assets.video01Noselect
  },
  {
    value: 'style02',
    selectedUrl: assets.video02Selected,
    noSelectUrl: assets.video02Noselect
  }
]
// 是否显示样式
const isShowStyle = () => {
  return ['menu'].includes(newCardForm.value.type)
}
// 是否显示列表
const isShowList = () => {
  return ['menu', 'image'].includes(newCardForm.value.type)
}
// 点击样式
const onClickStyle = value => {
  newCardForm.value.style_type = value
  updata('cardForm')
}
// 是否选中
const isSelect = value => {
  return newCardForm.value.style_type === value ? 'on' : ''
}
// 获取 name
const getName = (item, index) => {
  switch (newCardForm.value.type) {
    case 'image':
      return `热区${index + 1}`
    default:
      return item.name
  }
}
// 点击列表删除
const onDelete = index => {
  newCardForm.value.items.splice(index, 1)
  updata('cardForm')
}
// 清理选中
const clearSelectAll = () => {
  newCardForm.value.items &&
    newCardForm.value.items.forEach(item => {
      item.select = false
    })
}
// 点击列表
const onClickItem = item => {
  if (item.select) return
  clearSelectAll()
  item.select = true
}
// 拖拽更改
const onChangeDrag = () => {
  updata('cardForm')
}
// 拖拽开始
const onDragstart = (e, item) => {
  if (item.select) return
  e.stopPropagation()
  e.preventDefault()
}
//弹窗状态
const isShowAddModal = ref(false)
//打开弹窗
const onAdd = () => {
  editModalForm.value = null
  if (noModalType.includes(newCardForm.value.type)) {
    onAddSubmit({ jump_type: 'page' })
  } else {
    isShowAddModal.value = true
  }
}
const isShowAddFabModal = ref(false)
const onAddFab = () => {
  fabEditModalForm.value = null
  isShowAddFabModal.value = true
}
const onClickFabItem = item => {
  if (item.select) return
  clearSelectAll()
  newInitObj.value.fabList.forEach(item => (item.select = false))
  item.select = true
}
const onDeleteFab = index => {
  newInitObj.value.fabList.splice(index, 1)
}
//打开弹窗
const onEdit = elem => {
  editModalForm.value = { ...elem }
  isShowAddModal.value = true
}
const fabEditModalForm = ref(null)
const onFabEdit = elem => {
  fabEditModalForm.value = { ...elem }
  isShowAddFabModal.value = true
}
// 新增提交
const onAddSubmit = data => {
  if (newCardForm.value.type === 'image') {
    const { width, height } = props.selectImgSize
    const left = ~~((width - iw) / 2)
    const top = ~~((height - ih) / 2)
    data = { ...data, ...initHotSize, left, top, state: false }
  }
  if (editModalForm.value) {
    const item = newCardForm.value.items.find(item => item.id === editModalForm.value.id)
    Object.assign(item, data)
  } else {
    data.id = generateRandom()
    newCardForm.value.items.push(data)
  }

  updata('cardForm')
  isShowAddModal.value = false
}
const onAddTabSubmit = data => {
  newInitObj.value.fabList = newInitObj.value.fabList || []

  if (fabEditModalForm.value) {
    const item = newInitObj.value.fabList.find(item => item.id === fabEditModalForm.value.id)
    Object.assign(item, data)
  } else {
    data.id=generateRandom()
    newInitObj.value.fabList.push(data)
  }

  isShowAddFabModal.value = false
}
const editModalForm = ref(null)
const onUpdateElement = (e, elem) => {
  const item = newCardForm.value.items.find(item => item.id === elem.id)
  Object.assign(item, e)
}
const onUpdateFabElement = (e, elem) => {
  const item = newInitObj.value.fabList.find(item => item.id === elem.id)
  Object.assign(item, e)
}
const noModalType = ['image']
const noEdit = computed(() => !noModalType.includes(props.cardForm.type))
</script>
<style scoped lang="less">
.card-box {
  @margin-bottom: 20px;
  @mg-bottom: 12px;
  @bt-height: 80px;
  @ant-card-head-height: 57px;
  @page-min-height: 600px;
  @primary: #1890ff;
  position: relative;
  ul,
  li {
    list-style: none;
  }
  :deep(.ant-card-body) {
    min-height: calc(@page-min-height - @ant-card-head-height);
    height: calc(100vh - 60px - @ant-card-head-height);
    padding-bottom: @bt-height;
    overflow-y: auto;
  }
  .mg-bt {
    margin-bottom: @margin-bottom;
  }
  .img-flex {
    display: flex;
    margin-top: 20px;
  }
  .bg-upload {
    :deep(.ant-upload-picture-card-wrapper) {
      width: 100%;
      height: 50px;
      .ant-upload-list-picture-card {
        display: none;
      }
      .ant-upload-select-picture-card {
        width: 100% !important;
        height: 100% !important;
        margin: 0;
        .ant-upload > div {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    :deep(.ant-upload-list) {
      width: 100%;
      height: 100%;
      .ant-upload-list-picture-card-container {
        width: 100%;
        height: 100%;
        .ant-upload-list-item {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .order {
    display: flex;
    flex-wrap: wrap;
    width: 101%;
  }
  .image {
    margin-bottom: @margin-bottom;
  }
  .m-list-style {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
    > li {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 50px;
      margin: 0 @mg-bottom @mg-bottom 0;
      background-color: #f5f5f5;
      cursor: pointer;
      .u-triangle {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 22px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% auto;
      }
      .u-triangle,
      .selected {
        display: none;
      }
      .no-select {
        display: block;
      }
      &.on {
        .u-triangle,
        .selected {
          display: block;
        }
        .no-select {
          display: none;
        }
      }
    }
  }
  .u-btn-add {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: @bt-height;
    padding: 24px;
    background: #fff;
  }
  .m-list-info {
    width: 100%;
    .list-item {
      position: relative;
      display: flex;
      width: 100%;
      height: 75px;
      margin-bottom: @mg-bottom;
      padding: @mg-bottom;
      background: #f0f2f5;
      cursor: default;
      &[aria-selected='true'] {
        z-index: 6;
        cursor: move;
      }
      .u-img {
        width: 75px;
        height: 100%;
        border: 4px;
        margin-right: 10px;
        overflow: hidden;
        > img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .u-text {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        width: 50%;
        height: 100%;
        > p {
          display: flex;
          align-items: center;
          width: 100%;
          height: 50%;
          margin: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          &.u-tit {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
          }
          &.u-subtit {
            color: rgba(0, 0, 0, 0.65);
            font-size: 12px;
          }
        }
      }
      .u-delete {
        position: absolute;
        top: 2px;
        right: 2px;
        z-index: 2;
        width: 20px;
        cursor: pointer;
        img {
          display: block;
          width: 100%;
          height: auto;
        }
      }
      .u-border {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        border: 2px solid @primary;
        background: transparent;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
      }
    }
  }
  .m-fab {
    margin-top: 10px;
  }
}
</style>
