<template>
  <!-- 新增弹窗 -->
  <a-modal :title="titleInfo[type]" v-bind="{ visible }" class="m-modal-box" @ok="onSubmit" @cancel="onCancel">
    <a-form
      ref="addForm"
      layout="horizontal"
      :rules="addRules"
      :model="formState"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 19 }"
    >
      <template v-if="type === 'menu'">
        <a-form-item label="菜单图标" name="icon" class="required photo-flex">
          <uc-upload
            :list="formState.icon ? [formState.icon] : []"
            :max-length="1"
            upload-text=" "
            @update:list="data => (formState.icon = data[0])"
          />
        </a-form-item>
        <a-form-item label="菜单名称" name="name" class="required">
          <a-input v-model:value.trim="formState.name" :maxlength="10" placeholder="请输入菜单名称，不超过10字" />
        </a-form-item>
        <a-form-item label="菜单副标题" name="sub_title">
          <a-input
            v-model:value.trim="formState.sub_title"
            :maxlength="10"
            placeholder="请输入菜单副标题，不超过10字"
          />
        </a-form-item>
        <a-form-item label="菜单按钮" name="btn_title">
          <a-input v-model:value.trim="formState.btn_title" :maxlength="10" placeholder="请输入菜单按钮，不超过10字" />
        </a-form-item>
      </template>
    </a-form>
  </a-modal>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { computed, watch, ref } from 'vue'

const initFormState = { jump_type: 'page' }
const { formState, resetFormState, setFormState } = useFormState(initFormState) // 表单

const props = defineProps({
  value: {
    type: Object
  },
  // 显示状态
  visible: {
    type: Boolean,
    default: false
  },
  // 类型
  type: {
    type: String,
    default: ''
  }
})

watch(
  () => props.value,
  value => {
    setFormState(value || initFormState)
  },
  {
    immediate: true,
    deep: true
  }
)

const emit = defineEmits(['submit', 'update:visible'])
// 标题信息
const titleInfo = {
  image: '热区配置',
  menu: '添加菜单'
}
//验证规则
const addRules = ref({
  icon: { required: true, message: '请上传菜单图标', trigger: 'blur' },
  name: { required: true, message: '请输入菜单名称', trigger: 'blur' }
})

const addForm = ref(null)
// 点击提交按钮
const onSubmit = () => {
  //触发表单验证
  addForm.value.validate().then(
    () => {
      emit('submit', formState.value)
      resetFormState()
    },
    err => {
      // console.log(err);
    }
  )
}
// 点击取消按钮
const onCancel = () => {
  emit('update:visible', false)
  resetFormState()
}
</script>
<style scoped lang="less">
.m-modal-box {
  .photo-flex {
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      .m-upload {
        &:nth-child(2) {
          flex: 1;
        }
      }
    }
  }
}
</style>
