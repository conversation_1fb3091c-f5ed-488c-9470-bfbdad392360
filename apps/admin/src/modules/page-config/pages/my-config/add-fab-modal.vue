<template>
  <!-- 新增弹窗 -->
  <a-modal
    title="添加浮窗"
    v-bind="{ visible }"
    class="m-modal-box"
    @ok="onSubmit"
    @cancel="onCancel"
  >
    <a-form
      ref="addForm"
      layout="horizontal"
      :rules="addRules"
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <a-form-item label="浮窗图片" name="photo_url" class="required photo-flex">
        <uc-upload
          :list="formState.photo_url ? [formState.photo_url] : []"
          :max-length="1"
          upload-text=" "
          @update:list="data => formState.photo_url = data[0]"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script setup>
import { useFormState } from "@/composables/useFormState";
import { computed, watch, ref } from "vue";

const initFormState = { jump_type: 'page' }
const { formState, resetFormState,setFormState } = useFormState(initFormState); // 表单

const props = defineProps({
  value: {
    type: Object
  },
  // 显示状态
  visible: {
    type: Boolean,
    default: false
  },
  // 类型
  type: {
    type: String,
    default: ""
  }
});

watch(
  () => props.value,
  value => {
    setFormState(value || initFormState)
  },
  {
    immediate: true,
    deep: true
  }
)

const emit = defineEmits(["submit", "update:visible"]);

//验证规则
const addRules = ref({
  photo_url: { required: true, message: "浮窗图片", trigger: "blur" },
});

const addForm = ref(null);
// 点击提交按钮
const onSubmit = () => {
  //触发表单验证
  addForm.value.validate().then(
    () => {
      emit("submit", formState.value);
      resetFormState()
    },
    err => {
      // console.log(err);
    }
  );
};
// 点击取消按钮
const onCancel = () => {
  emit("update:visible", false);
  resetFormState()
};
</script>
<style scoped lang="less">
.m-modal-box {
  .photo-flex {
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      .m-upload {
        &:nth-child(2) {
          flex: 1;
        }
      }
    }
  }
}
</style>
