import { ref } from 'vue'
import { message } from 'ant-design-vue'
import { cloneDeep } from 'lodash'
import { RectType } from './enums'

// 选择区域操作
export const useSelectRect = (listData, selectImgSize = ref({})) => {
  // 获取选择图片的大小
  const getSelectImgSize = index => {
    const elemEl = document.querySelector(`.m-applets .m-rect .elem:nth-child(${index + 1})`)
    selectImgSize.value = elemEl.getBoundingClientRect() // 选中图片区域大小
  }

  // 特殊处理
  const handleSpecial = ({ item, index }) => {
    switch (item.type) {
      case RectType.image:
        getSelectImgSize(index)
        break
    }
  }

  // 清理所有选中
  const onClearSelectAll = index => {
    listData.value.forEach((item, i) => {
      item.state = i === index
      if (item.state) {
        handleSpecial({ item, index })
        return
      }
      // 清理热区
      switch (item.type) {
        case RectType.image:
          item.items.forEach(it => {
            it.state = false
          })
          break
      }
    })
  }

  // 选中当前区域
  const onSelect = (item, index) => {
    onClearSelectAll()
    item.state = true
    handleSpecial({ item, index })
  }
  // 删除选中
  const onDelete = index => {
    listData.value.splice(index, 1)
  }
  return { getSelectImgSize, onClearSelectAll, onSelect, onDelete }
}

// 拖拽
export const useDrag = (initItemData, listData, selectImgSize, onClearSelectAll) => {
  // 拖拽左侧 sidebar
  const onDragstart = (e, item) => {
    e.dataTransfer.setData('leftValue', item.key)
  }
  // 松开
  const onDrop = ({ clientY, dataTransfer }) => {
    const type = dataTransfer.getData('leftValue')
    if (type) {
      const elemEls = document.querySelectorAll('.m-applets .m-rect .elem')
      let index = Array.prototype.findIndex.call(elemEls, el => {
        const { top, height } = el.getBoundingClientRect()
        return clientY < top + height / 2
      })
      index < 0 && (index = elemEls.length)
      const data = { type, state: true, ...cloneDeep(initItemData[type]) }
      onClearSelectAll()
      listData.value.splice(index, 0, data)
      switch (type) {
        case RectType.image:
          nextTick(() => {
            const imgEl = document.querySelector(`.m-applets .m-rect .elem:nth-child(${index + 1}) .u-bg-img>img`)
            imgEl.onload = () => {
              const { width, height } = imgEl.getBoundingClientRect() // 选中图片区域大小
              selectImgSize.value = { width, height }
            }
          })
          break
      }
    }
  }
  // 拖拽中间部分
  const onDragApplets = (e, state) => {
    if (!state) {
      e.stopPropagation()
      e.preventDefault()
    }
  }
  return { onDragstart, onDrop, onDragApplets }
}

// 校验渲染列表数据
export const useValidateRect = list => {
  try {
    if (!list.length) throw new Error('请配置页面')
    list.forEach(
      ({ type, bg_url, items, register_url, registered_url, wechat_public_url, style_type, cover_url, src }) => {
        switch (type) {
          case 'register':
            if (!register_url) throw new Error('请上传注册图片')
            if (style_type == 'style01') {
              if (!wechat_public_url) throw new Error('请上传注册-公众号图片')
            } else {
              if (!registered_url) throw new Error('请上传已注册图片')
            }
            break
          case 'navbar':
            if (items.length == 0) throw new Error('导航卡片不能为空')
            break
          case 'slide':
            if (items.length == 0) throw new Error('滑块卡片不能为空')
            break
          case 'swiper':
            if (items.length == 0) throw new Error('轮播卡片不能为空')
            break
          case 'image':
            if (!bg_url) throw new Error('请上传热区背景图片')
            const len = items.length
            if (len > 0) {
              // 验证热区
              const isInter = items.find(({ top, left, width, height }, i) => {
                if (i < len - 1) {
                  const Xa1 = left
                  const Ya1 = top
                  const Xa2 = left + width
                  const Ya2 = top + height
                  return items.slice(i + 1).some(item => {
                    const Xb1 = item.left
                    const Yb1 = item.top
                    const Xb2 = item.left + item.width
                    const Yb2 = item.top + item.height
                    return Math.max(Xa1, Xb1) <= Math.min(Xa2, Xb2) && Math.max(Ya1, Yb1) <= Math.min(Ya2, Yb2)
                  })
                }
              })
              if (isInter) throw new Error('热区不能重合')
            }
            break
          case 'video':
            // if (!cover_url) throw new Error('请上传视频封面图片')
            if (!src) throw new Error('请上传视频地址')
            break
        }
      }
    )
    return true
  } catch ({ message: err }) {
    message.error(`操作失败：${err}`)
    return false
  }
}
