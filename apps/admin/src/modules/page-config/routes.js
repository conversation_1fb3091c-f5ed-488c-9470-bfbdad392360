export default {
  path: 'page-config',
  meta: {
    title: '装修',
    antIcon: 'FormatPainterOutlined'
  },
  children: [
    {
      path: 'shopstyle',
      name: 'page-config-shopstyle',
      meta: {
        title: '店铺风格'
      },
      component: () => import('./pages/shopstyle/index')
    },
    {
      path: 'login',
      name: 'login',
      meta: {
        title: '登录注册'
      },
      redirect: {
        name: 'page-config-login-config'
      }
    },
    {
      path: 'login-config',
      name: 'page-config-login-config',
      meta: {
        title: '登录注册配置',
        useLayout: false
      },
      hidden: true,
      component: () => import('./pages/login/index')
    },
    {
      path: 'home',
      name: 'home',
      meta: {
        title: '首页配置'
      },
      redirect: {
        name: 'page-config-home-config'
      }
    },
    {
      path: 'home-config',
      name: 'page-config-home-config',
      meta: {
        title: '首页配置',
        useLayout: false
      },
      hidden: true,
      component: () => import('./pages/home/<USER>')
    },
    {
      path: 'member-config-page',
      name: 'member-config-page',
      meta: {
        title: '会员配置',
        useLayout: false
      },
      component: () => import('./pages/member/config'),
      hidden: true
    },
    {
      path: 'my-config',
      name: 'page-config-my-config',
      meta: {
        title: '我的配置'
      },
      redirect: {
        name: 'page-config-my-config'
      }
    },
    {
      path: 'page-config-my-config',
      name: 'page-config-my-config',
      meta: {
        title: '装修我的配置',
        useLayout: false
      },
      hidden: true,
      component: () => import('./pages/my-config/config')
    },
    {
      path: 'special-page',
      name: 'special-page',
      meta: {
        title: '专题配置'
      },
      component: () => import('./pages/special-page/list')
    },
    {
      path: 'special-page-add',
      name: 'special-page-add',
      hidden: true,
      meta: {
        title: '新增专题配置',
        useLayout: false
      },
      component: () => import('./pages/special-page/edit')
    },
    {
      path: 'special-page-edit/:id/:status',
      name: 'special-page-edit',
      hidden: true,
      meta: {
        title: '编辑专题配置',
        useLayout: false
      },
      component: () => import('./pages/special-page/edit')
    },
    {
      path: 'ads',
      name: 'page-config-ads',
      meta: {
        title: '广告位',
        keepAlive: true
      },
      component: () => import('./pages/ads/list')
    },
    {
      path: 'ads-add',
      name: 'page-config-ads-add',
      hidden: true,
      meta: {
        title: '添加广告位',
      },
      component: () => import('./pages/ads/edit')
    },
    {
      path: 'ads-edit/:id',
      name: 'page-config-ads-edit',
      hidden: true,
      meta: {
        title: '编辑广告位',
      },
      component: () => import('./pages/ads/edit')
    }
  ]
}
