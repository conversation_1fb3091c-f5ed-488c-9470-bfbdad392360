export const UploadLogo = Object.freeze({
  /**
   * 高亮
   */
  logoHighlight: 'logo_highlight',
  /**
   * 灰度
   */
  logoGray: 'logo_gray',
  /**
   * 白色
   */
  logoWhite: 'logo_white',

  option() {
    return [
      { text: '高亮', value: this.logoHighlight },
      { text: '灰度', value: this.logoGray },
      { text: '白色', value: this.logoWhite }
    ]
  }
})

export const UploadPage = Object.freeze({
  /**
   * 地址为空
   */
  pageAddress: 'page_address',
  /**
   * 评论为空
   */
  pageComment: 'page_comment',
  /**
   * 动态为空
   */
  pageDynamic: 'page_dynamic',
  /**
   * 订单为空
   */
  pageOrder: 'page_order',
  /**
   * 产品为空
   */
  pageGift: 'page_gift',
  /**
   * 活动为空
   */
  pageActivity: 'page_activity',
  /**
   * 参与为空
   */
  pageParticipate: 'page_participate',
  /**
   * 物流为空
   */
  pageExpress: 'page_express',
  /**
   * 退货超限
   */
  returnOverrun: 'return_overrun',
  /**
   * 售后为空
   */
  afterEmpty: 'after_empty',
  /**
   * 积分为空
   */
  integralEmpty: 'integral_empty',
  /**
   * 奖品为空
   */
  prizeEmpty: 'prize_empty',
  /**
   * 优惠券为空
   */
  couponsEmpty: 'coupons_empty',
  /**
   * 收藏为空
   */
  favEmpty: 'fav_empty',
  /**
   * 定制合成中
   */
  composing: 'composing',
  /**
   * 定制图超时
   */
  customOvertime: 'custom_overtime',
  /**
   * 产品为空
   */
  page_product: 'page_product',
  /**
   * 试用招募
   */
  trialTecruit: 'trial_recruit',
  /**
   * 试用报告
   */
  trialReport: 'trial_report',
  /**
   * 试用申请
   */
  trialApplyEmpty: 'trial_apply_empty',
  /**
   * 抽签为空
   */
  drawEmpty: 'draw_empty',
  /**
   * 核销失败
   */
  writeOffFailure: 'write_off_failure',
  /**
   * 核销成功
   */
  writeOffSuccess: 'write_off_success',
  /**
   * 拼团订单为空
   */
  crowdfundingPageOrder: 'crowdfunding_page_order',
  /**
   * 商品不存在
   */
  goodDetailNotExist: 'good_detail_not_exist',
  /**
   * 分销客户为空
   */
  distributionUserEmpty: 'distribution_user_empty',
  /**
   * 提现记录为空
   */
  distributionWithdrawalsEmpty: 'distribution_withdrawals_empty',
  /**
   * 语音卡片为空
   */
  voiceEmpty: 'voice_empty',
  /**
   * 礼品卡为空
   */
  gift_card_empty: 'gift_card_empty',
  /**
   * 内购未开始
   */
  employeePurchaseNotStart: 'employee_purchase_not_start',
  /**
   * 内购已结束
   */
  employeePurchaseEnd: 'employee_purchase_end',
  /**
   * 暂无额度记录
   */
  noLimitRecord: 'no_limit_record',

  option() {
    return [
      { text: '礼品为空', value: this.pageGift },
      { text: '订单为空', value: this.pageOrder },
      { text: '地址为空', value: this.pageAddress },
      { text: '活动为空', value: this.pageActivity },
      { text: '积分为空', value: this.integralEmpty },
      { text: '动态为空', value: this.pageDynamic },
      { text: '评论为空', value: this.pageComment },
      { text: '奖品为空', value: this.prizeEmpty },
      { text: '退货超限', value: this.returnOverrun },
      { text: '售后为空', value: this.afterEmpty },
      { text: '优惠券为空', value: this.couponsEmpty },
      { text: '收藏为空', value: this.favEmpty },
      { text: '参与为空', value: this.pageParticipate },
      { text: '物流为空', value: this.pageExpress },
      { text: '定制合成中', value: this.composing },
      { text: '定制图超时', value: this.customOvertime },
      { text: '产品为空', value: this.page_product },
      { text: '试用招募', value: this.trialTecruit },
      { text: '试用报告', value: this.trialReport },
      { text: '试用申请', value: this.trialApplyEmpty },
      { text: '抽签为空', value: this.drawEmpty },
      { text: '核销失败', value: this.writeOffFailure },
      { text: '核销成功', value: this.writeOffSuccess },
      { text: '拼团订单为空', value: this.crowdfundingPageOrder },
      { text: '商品不存在', value: this.goodDetailNotExist },
      { text: '分销客户为空', value: this.distributionUserEmpty },
      { text: '提现记录为空', value: this.distributionWithdrawalsEmpty },
      { text: '语音卡片为空', value: this.voiceEmpty },
      { text: '礼品卡为空', value: this.gift_card_empty },
      { text: '内购未开始', value: this.employeePurchaseNotStart },
      { text: '内购已结束', value: this.employeePurchaseEnd },
      { text: '暂无额度记录', value: this.noLimitRecord }
    ]
  }
})

export const UploadPopup = Object.freeze({
  /**
   * 版本升级
   */
  versionUp: 'version_up',
  /**
   * 异常帐户
   */
  errAccount: 'err_account',

  option() {
    return [
      { text: '版本升级', value: this.versionUp },
      { text: '异常帐户', value: this.errAccount }
    ]
  }
})

export const UploadUniversal = Object.freeze({
  /**
   * 页面装修
   */
  pageFitment: 'page_fitment',
  /**
   * 页面丢失
   */
  pageLose: 'page_lose',
  /**
   * 页面报错
   */
  pageError: 'page_error',
  /**
   * 暂无权限
   */
  noAuthority: 'no_authority',

  option() {
    return [
      { text: '页面装修', value: this.pageFitment },
      { text: '页面丢失', value: this.pageLose },
      { text: '页面报错', value: this.pageError },
      { text: '暂无权限', value: this.noAuthority }
    ]
  }
})

export const registerType = Object.freeze({
  /**
   * 高亮
   */
  phone: 'phone',
  option() {
    return [{ text: '绑定手机号码注册', value: this.phone }]
  }
})

export const RectType = Object.freeze({
  /**
   * 注册
   */
  register: 'register',
  /**
   * 导航
   */
  navbar: 'navbar',
  /**
   * 轮播
   */
  swiper: 'swiper',
  /**
   * 滑块
   */
  slide: 'slide',
  /**
   * 图片
   */
  image: 'image',
  /**
   * 视频
   */
  video: 'video',

  option() {
    return [
      { key: this.register, text: '注册' },
      { key: this.navbar, text: '导航' },
      { key: this.swiper, text: '轮播' },
      { key: this.slide, text: '滑块' },
      { key: this.image, text: '图片' },
      { key: this.video, text: '视频' }
    ]
  },
  sidebar(value) {
    return this.option().find(item => item.key === value)
  }
})

export const SpecialPageType = Object.freeze({
  /**
   * 日常页
   */
  daily: 'daily',
  /**
   * 活动页
   */
  activity: 'activity',

  options() {
    return [
      { label: '日常页', value: this.daily },
      { label: '活动页', value: this.activity }
    ]
  },
  filterValue(value) {
    return this.options().find(item => item.value == value)?.label
  }
})

/**
 * 搜索条件
 */
export const adsSearchCondition = Object.freeze({
  /**
   * 广告名称
   */
  title: 'title',
  /**
   * 广告代码
   */
  key: 'key',

  options() {
    return [
      { label: '广告名称', value: this.title },
      { label: '广告代码', value: this.key }
    ]
  }
})

export const MediaType = Object.freeze({
  /**
   * 图片
   */
  image: 'image',
  /**
   * 视频
   */
  video: 'video',
  option() {
    return [
      { label: '图片', value: this.image },
      { label: '视频', value: this.video }
    ]
  }
})
