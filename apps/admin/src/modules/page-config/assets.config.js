const baseURL = 'https://yuqianji-mp-1308375884.cos.ap-shanghai.myqcloud.com/' // 前缀

const assets = {
  return: 'user-center/admin/renovation/login/icon/return.svg',
  loginNoSelect: 'user-center/admin/renovation/login/icon/login-noselect.svg',
  loginSelected: 'user-center/admin/renovation/login/icon/login-selected.svg',
  registerNoSelect: 'user-center/admin/renovation/login/icon/register-noselect.svg',
  registerSelected: 'user-center/admin/renovation/login/icon/register-selected.svg',
  headerBgBlack: 'user-center/admin/renovation/login/icon/black-top-navigationbar.svg',
  headerBgWhite: 'user-center/admin/renovation/login/icon/white-top-navigationbar.svg',
  wechat: 'user-center/admin/renovation/login/icon/wechat-icon.svg',
  male: 'user-center/admin/renovation/login/icon/man.svg',
  female: 'user-center/admin/renovation/login/icon/woman.svg',
  arrowBottom: 'user-center/admin/renovation/login/icon/drop-down-arrow.svg',
  myBottom: 'user-center/admin/myconfig/my-bottom.png',
  myTabbar: 'user-center/admin/myconfig/my-bottom.svg',
  myOrder: 'user-center/admin/myconfig/my-order.png',
  myMenuNoselect: 'user-center/admin/myconfig/my-menu-noselect.svg',
  myMenuSelected: 'user-center/admin/myconfig/my-menu-selected.svg',
  myOrderSelected: 'user-center/admin/myconfig/my-order-select.svg',
  myOrderNoSelect: 'user-center/admin/myconfig/my-order-noselect.svg',
  myLeftimgSelcted: 'user-center/admin/myconfig/my-leftimg-selcted.svg',
  myImgNoselect: 'user-center/admin/myconfig/my-img-noselect.svg',
  myImgSelect: 'user-center/admin/myconfig/my-img-select.png',
  myHeadimg: 'user-center/admin/myconfig/my-headimg.png',
  myDelete: 'user-center/admin/myconfig/delete.svg',
  myArrowIcon: 'user-center/admin/myconfig/arrow-icon.svg',
  myIntegral: 'user-center/admin/myconfig/integral.svg',
  myMember: 'user-center/admin/myconfig/member.svg',
  mySet: 'user-center/admin/myconfig/setting.svg',
  myStatistics: 'user-center/admin/myconfig/my-statistics.png',
  myStatisticsNoselect: 'user-center/admin/myconfig/my-statistics-noselect.svg',
  myStatisticsSelected: 'user-center/admin/myconfig/my-statistics-selected.svg',
  myRecommend: 'uploader/rk8zKc17217133000455.png',
  homeDelete: 'user-center/admin/home/<USER>',
  homeLeftRegisterNoselect: 'user-center/admin/home/<USER>',
  homeLeftRegisterSelected: 'user-center/admin/home/<USER>',
  homeLeftSwiperNoselect: 'user-center/admin/home/<USER>',
  homeLeftSwiperSelected: 'user-center/admin/home/<USER>',
  homeRegisterBgDefault: 'user-center/admin/home/<USER>',
  homeTabbar: 'user-center/admin/home/<USER>',
  homeswiper1Noselect: 'user-center/admin/home/<USER>',
  homeswiper1Selected: 'user-center/admin/home/<USER>',
  homeswiper2Noselect: 'user-center/admin/home/<USER>',
  homeswiper2Selected: 'user-center/admin/home/<USER>',
  homeswiper3Noselect: 'user-center/admin/home/<USER>',
  homeswiper3Selected: 'user-center/admin/home/<USER>',
  homeswiper4Noselect: 'user-center/admin/home/<USER>',
  homeswiper4Selected: 'user-center/admin/home/<USER>',
  homeregisterSelected: 'user-center/admin/home/<USER>',
  homeregisterNoselecte: 'user-center/admin/home/<USER>',
  homeregister2Selected: 'user-center/admin/home/<USER>',
  homeregister2Noselecte: 'user-center/admin/home/<USER>',
  delete: 'user-center/admin/page/delete.svg',
  imageBgDefault: 'user-center/admin/page/image_bg_default.svg',
  selected: 'user-center/admin/page/selected.svg',
  style01NavbarNoSelect: 'user-center/admin/page/style01_navbar_no_select.svg',
  style01NavbarSelected: 'user-center/admin/page/style01_navbar_selected.svg',
  swiperBgDefault01: 'user-center/admin/page/swiper_bg_default01.svg',
  swiperBgDefault02: 'user-center/admin/home/<USER>',
  swiperBgDefault03: 'user-center/admin/home/<USER>',
  swiperBgDefault04: 'user-center/admin/home/<USER>',
  leftSlideNoselect: 'user-center/admin/home/<USER>',
  leftSlideSelected: 'user-center/admin/home/<USER>',
  slide01Selected: 'user-center/admin/home/<USER>',
  slide01Noselect: 'user-center/admin/home/<USER>',
  slide02Selected: 'user-center/admin/home/<USER>',
  slide02Noselect: 'user-center/admin/home/<USER>',
  slide03Selected: 'user-center/admin/home/<USER>',
  slide03Noselect: 'user-center/admin/home/<USER>',
  slide04Selected: 'user-center/admin/home/<USER>',
  slide04Noselect: 'user-center/admin/home/<USER>',
  slideBgDefault01: 'user-center/admin/home/<USER>',
  slideBgDefault02: 'user-center/admin/home/<USER>',
  slideBgDefault03: 'user-center/admin/home/<USER>',
  navbarBgDefault01: 'user-center/admin/home/<USER>',
  navbarBgDefault02: 'user-center/admin/home/<USER>',
  navbarBgDefault03: 'user-center/admin/home/<USER>',
  leftNavbarNoselect: 'user-center/admin/home/<USER>',
  leftNavbarSelected: 'user-center/admin/home/<USER>',
  navbar01Noselect: 'user-center/admin/home/<USER>',
  navbar01Selected: 'user-center/admin/home/<USER>',
  navbar02Noselect: 'user-center/admin/home/<USER>',
  navbar02Selected: 'user-center/admin/home/<USER>',
  navbar03Noselect: 'user-center/admin/home/<USER>',
  navbar03Selected: 'user-center/admin/home/<USER>',
  leftVideoNoselect: 'user-center/admin/home/<USER>',
  leftVideoSelected: 'user-center/admin/home/<USER>',
  video01Noselect: 'user-center/admin/home/<USER>',
  video01Selected: 'user-center/admin/home/<USER>',
  video02Noselect: 'user-center/admin/home/<USER>',
  video02Selected: 'user-center/admin/home/<USER>',
  videoSuspend: 'user-center/admin/home/<USER>'
}
Object.entries(assets).forEach(([key, value]) => {
  assets[key] = baseURL + value
})
export default assets
