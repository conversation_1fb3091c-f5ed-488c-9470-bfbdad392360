import { cloneDeep } from 'lodash'
import { generateRandom } from '@/utils/functions'

/**
 * 转换数据
 */
export const useTransformData = () => {
  const transfromToForm = data => {
    const res = cloneDeep(data)
    res.forEach(item => {
      if (item.items) {
        item.items.forEach(it => {
          !it.id && (it.id = generateRandom())
        })
      }
      return item
    })
    return res
  }

  return [transfromToForm]
}
