<template>
  <!-- 小程序轮播图 -->
  <div class="m-applet-navbar" :class="`m-applet-navbar-${info.style_type}`" @click="onClickNavbar">
    <ul
      v-if="info.items.length > 0"
      ref="listRef"
      class="m-list"
      :class="`navbar-${info.style_type}`"
      :style="`transform:translateX(${translateX}px);`"
      @mousedown.prevent="onMousedown"
    >
      <li v-for="item in info.items" :key="item.title">
        <div v-if="info.style_type == 'style01'" class="style01">
          {{ item.title }}
        </div>
        <div v-if="info.style_type == 'style02'" class="style02">
          <div class="img">
            <img :src="item.url" :width="item.width" alt="" />
          </div>
          <p>{{ item.title }}</p>
        </div>
        <div v-if="info.style_type == 'style03'" class="style03">
          <div class="img">
            <img :src="item.url" :width="item.width" alt="" />
          </div>
        </div>
      </li>
    </ul>
    <div v-else class="u-bg-default-wrap">
      <img class="u-bg-default" :src="defaultImgUrl" alt="默认图" />
    </div>
  </div>
</template>
<script>
export default {
  name: 'AppletNavbar'
}
</script>
<script setup>
import { nextTick, ref, watch } from 'vue'
import assets from '../assets.config'

// 类型配置
const styleConfigs = {
  style01: {
    defaultImgUrl: assets.navbarBgDefault01
  },
  style02: {
    defaultImgUrl: assets.navbarBgDefault02
  },
  style03: {
    defaultImgUrl: assets.navbarBgDefault03
  }
}
const props = defineProps({
  // 数据信息
  info: {
    type: Object,
    default: () => ({})
  }
})
const translateX = ref(0) // 水平位移
let isDisableClick = false // 禁止点击状态
const defaultImgUrl = ref(styleConfigs[props.info.style_type].defaultImgUrl)

const listRef = ref(null)
// 获取列表宽度
const getMaxWidth = () => {
  return (listRef.value?.getBoundingClientRect()?.width || 0) - 375
}

// 横向拖动
const onMousedown = ({ clientX }) => {
  if (props.info.state) return
  const maxWidth = getMaxWidth()
  if (maxWidth <= 0) return
  document.onmousemove = e => {
    let distanceX = e.clientX - clientX + translateX.value
    if (distanceX < -maxWidth) {
      distanceX = -maxWidth
    } else if (distanceX > 0) {
      distanceX = 0
    }
    translateX.value = distanceX
  }
  document.onmouseup = e => {
    const moveX = Math.abs(e.clientX - clientX)
    isDisableClick = moveX > 5 // 禁止点击
    document.onmousemove = null
    document.onmouseup = null
  }
}

// 点击导航
const onClickNavbar = e => {
  isDisableClick && e.stopPropagation()
}

const getImgInfo = item =>
  new Promise(resolve => {
    const img = new Image()
    img.src = item.url
    img.onload = () => {
      item.width = img.width / 2
    }
  })

watch(
  () => props.info.items,
  v => {
    v.map(async item => await getImgInfo(item))
  },
  { immediate: true, deep: true }
)

watch(
  () => props.info.style_type,
  val => {
    defaultImgUrl.value = styleConfigs[val].defaultImgUrl
  }
)
</script>
<style scoped lang="less">
@height: 50px;
@height2: 100px;
@height3: 116px;
@bg1: #fff;
.m-applet-navbar {
  width: 100%;
  font-size: 0;
  overflow: hidden;
  background: #fff;
  // height: 50px;
  .m-list {
    display: inline-block;
    // height: 100%;
    // min-height: 100%;
    padding: 0 15px;
    white-space: nowrap;
    font-size: 0;
    li {
      display: inline-block;
      vertical-align: middle;
      // height: 100%;
      line-height: @height;
      margin-right: 41px;
      color: #000;
      font-size: 14px;
      font-weight: bold;
      .img {
        background: @bg1;
      }
      &:last-child {
        margin-right: 0;
      }
      .style02 {
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        // height: @height2;

        .img {
          // height: 24px;
          min-width: 10px;
          padding: 0;
          display: flex;
          align-items: center;
          margin-bottom: 10px;
          img {
            max-height: 100%;
          }
        }
        p {
          line-height: 20px;
          font-weight: normal;
          margin-bottom: 0;
          font-size: 14px;
        }
      }
      .style03 {
        position: relative;
        width: 75px;
        height: 75px;
        top: 20px;
        border-radius: 6px;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
        img {
          max-width: 100%;
          max-height: 100%;
        }
      }
    }
  }
  .u-bg-default-wrap {
    height: 100%;
    display: flex;
    align-items: center;
    .u-bg-default {
      display: block;
      width: 100%;
    }
  }
}

.m-applet-navbar-style02 {
  // height: @height2;
  // min-height: @height2;
  .m-list {
    padding: 20px 15px;
  }
}

.m-applet-navbar-style03 {
  height: @height3;
  min-height: @height3;
  .m-list {
  }
}
</style>
