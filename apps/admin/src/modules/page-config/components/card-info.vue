<template>
  <a-card :title="cardTitle" :bordered="false" class="card-box" @click="clearSelectAll">
    <!-- 默认配置 首页-->
    <div v-if="objLength(newCardForm)">
      <slot>
        <a-input disabled value="首页" class="mg-bt" />
        <a-input disabled value="pages/tabbar/home-act" class="mg-bt" />
      </slot>
    </div>
    <!-- 当前选中配置 -->
    <div v-else @click.stop>
      <!-- 轮播 -->
      <div v-if="newCardForm.type === 'swiper'" class="flex m-b-20">
        <div>是否显示轮播名称：</div>
        <a-switch v-model:checked="newCardForm.showTitle" />
      </div>
      <!-- 菜单样式 -->
      <ul v-if="curConfig && curConfig.styles" class="m-list-style">
        <li
          v-for="item in curConfig.styles"
          :key="item.value"
          :class="isSelect(item.value)"
          @click="onClickStyle(item.value)"
        >
          <img class="selected" :src="item.selectedUrl" alt="选中" />
          <img class="no-select" :src="item.noSelectUrl" alt="未选中" />
          <img class="u-triangle" :src="assets.selected" alt="选中三角" />
        </li>
      </ul>
      <!-- 注册 -->
      <div v-if="newCardForm.type === 'register'" class="u-register img-flex">
        <uc-upload
          :list="newCardForm.register_url ? [newCardForm.register_url] : []"
          show-label
          :max-length="1"
          upload-text=" "
          :label-text="newCardForm.style_type == 'style01' ? '注册' : '未注册'"
          @update:list="data => (newCardForm.register_url = data[0])"
        />
        <uc-upload
          v-if="newCardForm.style_type == 'style02'"
          :list="newCardForm.registered_url ? [newCardForm.registered_url] : []"
          show-label
          :max-length="1"
          upload-text=" "
          label-text="已注册"
          @update:list="data => (newCardForm.registered_url = data[0])"
        />
        <uc-upload
          v-else
          :list="newCardForm.wechat_public_url ? [newCardForm.wechat_public_url] : []"
          show-label
          :max-length="1"
          upload-text=" "
          label-text="公众号"
          @update:list="data => (newCardForm.wechat_public_url = data[0])"
        />
      </div>
      <!-- 视频 -->
      <div v-if="newCardForm.type === 'video'" class="u-register img-flex">
        <!-- <uc-upload
          :list="newCardForm.cover_url ? [newCardForm.cover_url] : []"
          :max-length="1"
          upload-text="上传封面"
          @update:list="data => (newCardForm.cover_url = data[0])"
        /> -->
        <a-textarea
          v-model:value="newCardForm.src"
          placeholder="添加视频可提升成交转化，有利于获取更多新流量,时长 9-30 秒，宽高比 16:9，视频不能超过5M，支持mp4视频格式"
          style="height: 104px"
          :rows="4"
        />
      </div>
      <!-- 底部按钮 -->
      <div v-if="curConfig && curConfig.btnText" class="u-btn-add">
        <a-button type="primary" block @click="onAdd">
          {{ curConfig.btnText }}
        </a-button>
      </div>
      <!-- 背景图片 -->
      <div v-if="showBgImg" class="image">
        <uc-upload
          :list="[]"
          class="bg-upload"
          :max-length="1"
          disabled-icon
          upload-text="更换背景图片"
          @update:list="data => (newCardForm.bg_url = data[0])"
        />
      </div>
      <!-- 列表 -->
      <ul v-if="newCardForm.items" class="m-list-info">
        <draggable v-model="newCardForm.items" item-key="id">
          <template #item="{ element, index }">
            <li
              class="list-item"
              :aria-selected="element.select"
              @click="onClickItem(element)"
              @dragstart="onDragstart($event, element)"
            >
              <div v-if="element.url" class="u-img" @click.stop="void 0">
                <video
                  v-if="element.media_type === MediaType.video"
                  :src="element.url"
                  :width="75"
                  style="object-fit: contain"
                ></video>
                <img v-else :src="element.url" :width="75" style="object-fit: contain" />
              </div>
              <div class="u-text">
                <p class="u-tit">
                  {{ getName(element, index) }}
                </p>
                <p class="u-subtit z-index-9 relative">
                  <uc-jump
                    :value="element"
                    :ignore-jumps="ignoreJumpType"
                    @update:value="onUpdateElement($event, element)"
                  />
                </p>
              </div>
              <div v-if="noEdit" class="btn z-index-9 relative">
                <a-button type="link" @click.stop="onEdit(element)">编辑</a-button>
              </div>
              <template v-if="element.select">
                <div class="u-delete" @click="onDelete(index)">
                  <img :src="assets.delete" alt />
                </div>
                <div class="u-border"></div>
              </template>
            </li>
          </template>
        </draggable>
      </ul>

      <!-- 新建模态框 -->
      <add-modal
        v-model:visible="isShowAddModal"
        :value="editModalForm"
        :style-type="newCardForm.style_type"
        :type="newCardForm.type"
        @submit="onAddSubmit"
      />
    </div>
  </a-card>
</template>

<script setup>
import { computed, watch, ref } from 'vue'
import addModal from './add-modal.vue'
import draggable from 'vuedraggable'
import { generateRandom } from '@/utils/functions'
import assets from '../assets.config'
import { MediaType } from '@/modules/page-config/enums'

const props = defineProps({
  // 选中的对象以及配置标题
  cardForm: {
    type: Object,
    required: true
  },
  // 选中图片区域大小
  selectImgSize: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['update:card-form'])
const initHotSize = { width: 100, height: 100 } // 初始化热区大小
const { width: iw, height: ih } = initHotSize
const styleConfigs = {
  // 图片
  image: {
    bg: true,
    title: '图片设置',
    btnText: '添加图片热区'
  },
  // 轮播
  swiper: {
    title: '轮播设置',
    btnText: '添加轮播素材',
    styles: [
      {
        value: 'style01',
        selectedUrl: assets.homeswiper1Selected,
        noSelectUrl: assets.homeswiper1Noselect
      },
      {
        value: 'style02',
        selectedUrl: assets.homeswiper2Selected,
        noSelectUrl: assets.homeswiper2Noselect
      },
      {
        value: 'style03',
        bg: true,
        selectedUrl: assets.homeswiper3Selected,
        noSelectUrl: assets.homeswiper3Noselect
      },
      {
        value: 'style04',
        bg: true,
        selectedUrl: assets.homeswiper4Selected,
        noSelectUrl: assets.homeswiper4Noselect
      }
    ]
  },
  // 导航
  navbar: {
    title: '导航设置',
    btnText: '添加导航菜单',
    styles: [
      {
        value: 'style01',
        selectedUrl: assets.navbar01Selected,
        noSelectUrl: assets.navbar01Noselect
      },
      {
        value: 'style02',
        selectedUrl: assets.navbar02Selected,
        noSelectUrl: assets.navbar02Noselect
      },
      {
        value: 'style03',
        selectedUrl: assets.navbar03Selected,
        noSelectUrl: assets.navbar03Noselect
      }
    ]
  },
  // 注册
  register: {
    styles: [
      {
        value: 'style01',
        selectedUrl: assets.homeregisterSelected,
        noSelectUrl: assets.homeregisterNoselecte
      },
      {
        value: 'style02',
        selectedUrl: assets.homeregister2Selected,
        noSelectUrl: assets.homeregister2Noselecte
      }
    ]
  },
  // 滑块
  slide: {
    title: '滑块设置',
    btnText: '添加滑块图片',
    styles: [
      {
        value: 'style01',
        selectedUrl: assets.slide01Selected,
        noSelectUrl: assets.slide01Noselect
      },
      {
        value: 'style02',
        selectedUrl: assets.slide02Selected,
        noSelectUrl: assets.slide02Noselect
      },
      {
        value: 'style03',
        bg: true,
        selectedUrl: assets.slide03Selected,
        noSelectUrl: assets.slide03Noselect
      },
      {
        value: 'style04',
        bg: true,
        selectedUrl: assets.slide04Selected,
        noSelectUrl: assets.slide04Noselect
      }
    ]
  },
  // 视频
  video: {
    title: '视频设置',
    styles: [
      {
        value: 'style01',
        selectedUrl: assets.video01Selected,
        noSelectUrl: assets.video01Noselect
      },
      {
        value: 'style02',
        selectedUrl: assets.video02Selected,
        noSelectUrl: assets.video02Noselect
      }
    ]
  }
}

const curConfig = computed(() => styleConfigs[props.cardForm.type]) // 当前配置
// 背景图显示按钮
const showBgImg = computed(
  () => curConfig.value?.bg || curConfig.value?.styles?.find(item => item.value === props.cardForm.style_type)?.bg
)

// 选中对象
const newCardForm = computed(() => props.cardForm)

// 判断对象长度 是否为空
const objLength = obj => !Object.keys(obj).length

// 标题
const cardTitle = computed(() => curConfig.value?.title || '页面设置')

// 点击样式
const onClickStyle = value => {
  newCardForm.value.style_type = value
  styleChangeAddImgDefault()
}

//导航样式切换时，如需显示图片，在无图片时增加默认图片
const styleChangeAddImgDefault = () => {
  const data = newCardForm.value
  if (data.type == 'navbar') {
    if (data.style_type !== 'style01') {
      data.items = data.items.map(m => {
        if (!m.url) m.url = assets.imageBgDefault
        return m
      })
    }
  }
}

// 是否选中
const isSelect = value => (newCardForm.value.style_type === value ? 'on' : '')

// 获取 name
const getName = (item, index) => {
  switch (newCardForm.value.type) {
    case 'image':
      return `热区${index + 1}`
    case 'navbar':
    case 'slide':
    case 'video':
    case 'swiper':
      return item.title
    default:
      return item.name
  }
}
// 点击列表删除
const onDelete = index => {
  newCardForm.value.items.splice(index, 1)
}
// 清理选中
const clearSelectAll = () => {
  newCardForm.value.items &&
    newCardForm.value.items.forEach(item => {
      item.select = false
    })
}
// 点击列表
const onClickItem = item => {
  if (item.select) return
  clearSelectAll()
  item.select = true
}
// 拖拽开始
const onDragstart = (e, item) => {
  if (item.select) return
  e.stopPropagation()
  e.preventDefault()
}

//弹窗状态
const isShowAddModal = ref(false)
//打开弹窗
const onAdd = () => {
  editModalForm.value = null
  if (noModalType.includes(newCardForm.value.type)) {
    onAddSubmit({ jump_type: 'page' })
  } else {
    isShowAddModal.value = true
  }
}
//打开弹窗
const onEdit = elem => {
  editModalForm.value = { ...elem }
  isShowAddModal.value = true
}
// 新增提交
const onAddSubmit = data => {
  if (newCardForm.value.type === 'image') {
    const { width, height } = props.selectImgSize
    const left = ~~((width - iw) / 2)
    const top = ~~((height - ih) / 2)
    data = { ...data, ...initHotSize, left, top, state: false }
  }
  if (editModalForm.value) {
    const item = newCardForm.value.items.find(item => item.id === editModalForm.value.id)
    Object.assign(item, data)
  } else {
    data.id = generateRandom()
    newCardForm.value.items.push(data)
  }

  isShowAddModal.value = false
}

const editModalForm = ref(null)

const ignoreType = {
  swiper: ['share', 'contact'],
  slide: ['share', 'contact'],
  navbar: ['share', 'contact', 'no'],
  image: ['contact', 'no']
}
const ignoreJumpType = computed(() => ignoreType[props.cardForm.type] || [])
const onUpdateElement = (e, elem) => {
  const item = newCardForm.value.items.find(item => item.id === elem.id)
  Object.assign(item, e)
}
const noModalType = ['image']
const noEdit = computed(() => !noModalType.includes(props.cardForm.type))
</script>
<style scoped lang="less">
.card-box {
  @margin-bottom: 20px;
  @mg-bottom: 12px;
  @bt-height: 80px;
  @ant-card-head-height: 57px;
  @page-min-height: 600px;
  @primary: #1890ff;
  position: relative;
  ul,
  li {
    list-style: none;
  }
  :deep(.ant-card-body) {
    min-height: calc(@page-min-height - @ant-card-head-height);
    height: calc(100vh - 60px - @ant-card-head-height);
    padding-bottom: @bt-height;
    overflow-y: auto;
  }
  .mg-bt {
    margin-bottom: @margin-bottom;
  }
  .img-flex {
    display: flex;
    margin-top: 20px;
  }
  .bg-upload {
    :deep(.ant-upload-picture-card-wrapper) {
      width: 100%;
      height: 34px;
      .ant-upload-list-picture-card {
        display: none;
      }
      .ant-upload-select-picture-card {
        width: 100% !important;
        height: 100% !important;
        margin: 0;
        .ant-upload > div {
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    :deep(.ant-upload-list) {
      width: 100%;
      height: 100%;
      .ant-upload-list-picture-card-container {
        width: 100%;
        height: 100%;
        .ant-upload-list-item {
          width: 100%;
          height: 100%;
        }
      }
    }
  }
  .order {
    display: flex;
  }
  .image {
    margin-bottom: @margin-bottom;
  }
  .m-list-style {
    display: flex;
    flex-wrap: wrap;
    margin-bottom: 0;
    > li {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 80px;
      height: 50px;
      margin: 0 @mg-bottom @mg-bottom 0;
      background-color: #f5f5f5;
      cursor: pointer;
      .u-triangle {
        position: absolute;
        right: 0;
        bottom: 0;
        width: 22px;
        background-repeat: no-repeat;
        background-position: center;
        background-size: 100% auto;
      }
      .u-triangle,
      .selected {
        display: none;
      }
      .no-select {
        display: block;
      }
      &.on {
        .u-triangle,
        .selected {
          display: block;
        }
        .no-select {
          display: none;
        }
      }
    }
  }
  .u-btn-add {
    position: absolute;
    bottom: 0;
    left: 0;
    z-index: 3;
    width: 100%;
    height: @bt-height;
    padding: 24px;
    background: #fff;
  }
  .m-list-info {
    width: 100%;
    .list-item {
      position: relative;
      display: flex;
      align-items: center;
      width: 100%;
      height: 75px;
      margin-bottom: @mg-bottom;
      padding: @mg-bottom;
      background: #f0f2f5;
      cursor: default;
      &[aria-selected='true'] {
        z-index: 6;
        cursor: move;
      }
      .u-img {
        width: 75px;
        height: 100%;
        border: 4px;
        margin-right: 10px;
        overflow: hidden;
        display: flex;
        align-items: center;
        > img {
          display: block;
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }
      .u-text {
        flex-grow: 1;
        display: flex;
        flex-direction: column;
        width: 50%;
        height: 100%;
        > p {
          display: flex;
          align-items: center;
          width: 100%;
          height: 50%;
          margin: 0;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          &.u-tit {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            font-size: 14px;
          }
          &.u-subtit {
            color: rgba(0, 0, 0, 0.65);
            font-size: 12px;
          }
        }
      }
      .u-delete {
        position: absolute;
        top: 2px;
        right: 2px;
        z-index: 2;
        width: 20px;
        cursor: pointer;
        img {
          display: block;
          width: 100%;
          height: auto;
        }
      }
      .u-border {
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        width: 100%;
        height: 100%;
        border: 2px solid @primary;
        background: transparent;
        box-shadow: 0 0 6px rgba(0, 0, 0, 0.3);
      }
    }
  }
}
</style>
