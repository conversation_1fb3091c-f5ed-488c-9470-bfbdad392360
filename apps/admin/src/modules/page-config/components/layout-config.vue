<template>
  <!-- 专题配置 -->
  <div class="layout-config">
    <div class="scroll-x">
      <!-- 头部 -->
      <div class="m-head">
        <div class="u-return" @click="router.back()">
          <img :src="assets.return" />返回控制台
        </div>
        <div class="m-btns">
          <a-button type="primary" v-bind="{ disabled }" @click="onSubmit">
            保存
          </a-button>
        </div>
      </div>
      <!-- 左侧菜单 -->
      <div class="m-sidebar">
        <ul class="scroll">
          <li
            v-for="item in sidebarList"
            :key="item.type"
            class="u-drag"
            draggable="true"
            @dragstart="onDragstart($event, item)"
          >
            <img :src="item.noSelect" class="u-img-noselect" />
            <img :src="item.selected" class="u-img-selected" />
            <span class="u-text">{{ item.text }}</span>
          </li>
        </ul>
      </div>
      <!-- 中间小程序 -->
      <div class="m-applets" @click="onClearSelectAll">
        <div class="m-rect" :style="rectStyle" @click.stop @drop.prevent="onDrop" @dragover.prevent>
          <!-- 头部 -->
          <div class="u-hd">
            <img :src="assets.headerBgWhite" />
          </div>
          <!-- 拖拽动态部分 -->
          <vue-draggable v-model="listData" item-key="id">
            <template #item="{ element, index }">
              <div
                class="elem"
                :class="{ active: element.state }"
                @click="onSelect(element, index)"
                @dragstart="onDragApplets($event, element.state)"
              >
                <!-- 删除 -->
                <div v-show="element.state" class="u-delete" @click.stop="onDelete(index)">
                  <img :src="assets.homeDelete" />
                </div>
                <!-- 注册 -->
                <div v-if="element.type === 'register'" class="item-register">
                  <img :src="element.register_url || assets.homeRegisterBgDefault" alt="注册" />
                </div>
                <!-- 图片 -->
                <drag-img v-else-if="element.type === 'image'" :info="element" @clear="onClearSelectAll(index)" />
                <!-- 视频 -->
                <drag-video v-else-if="element.type === 'video'" :info="element" @clear="onClearSelectAll(index)" />
                <!-- 轮播 -->
                <applet-swiper v-else-if="element.type === 'swiper'" :info="element" v-bind="{ index }" />
                <!-- 滑块 -->
                <applet-slide v-else-if="element.type === 'slide'" :info="element" v-bind="{ index }" />
                <applet-navbar v-else-if="element.type === 'navbar'" :info="element" />
                <!-- 边框 -->
                <div v-show="element.state" class="u-border"></div>
              </div>
            </template>
          </vue-draggable>
        </div>
      </div>
      <!-- 右侧元素面板 -->
      <div class="m-element">
        <div class="scroll">
          <card-info :card-form="cardForm" :select-img-size="selectImgSize">
            <template #default>
              <slot name="cardInfo"></slot>
            </template>
          </card-info>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: 'LayoutConfig'
}
</script>
<script setup>
import { useRouter } from 'vue-router'
import { cloneDeep } from 'lodash'
import assets from '../assets.config'
import VueDraggable from 'vuedraggable'
import CardInfo from './card-info'
import DragImg from './drag-img'
import DragVideo from './drag-video'
import AppletSwiper from './applet-swiper'
import AppletSlide from './applet-slide'
import AppletNavbar from './applet-navbar'
import { useSelectRect, useDrag } from '../useRect'
import { RectType } from '../enums'

const router = useRouter() // 路由操作

const emit = defineEmits(['submit', 'update:list'])
const props = defineProps({
  sidebarList: {
    type: Array,
    default: () => []
  },
  list: {
    type: Array,
    default: () => []
  },
  initItemData: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  },
  rectStyle: {
    type: String,
    default: ''
  }
})
const listData = ref([]) // 中间内容数组
const cardForm = computed(() => listData.value.find(item => item.state) || {}) // 右侧内容
const selectImgSize = ref({}) // 当前选中图片大小
const { getSelectImgSize, onClearSelectAll, onSelect, onDelete } = useSelectRect(listData, selectImgSize)
const { onDragstart, onDrop, onDragApplets } = useDrag(props.initItemData, listData, selectImgSize, onClearSelectAll)

const onSubmit = () => {
  emit('submit')
}

// 监听列表数据
watch(
  () => props.list,
  value => {
    listData.value = value
  },
  { deep: true, immediate: true }
)
// 监听当前组件列表数据
watch(
  listData,
  value => {
    emit('update:list', value)
  },
  { deep: true }
)
</script>
<style scoped lang="less">
.layout-config {
  @primary: #1890ff;
  @padding: 20px;
  @margin-bottom: 20px;
  p {
    margin-bottom: 0;
  }
  .scroll() {
    .scroll {
      min-height: 600px;
    }
  }
  width: 100%;
  height: 100%;
  overflow-x: auto;
  .scroll-x {
    display: flex;
    min-width: 1280px;
    height: 100vh;
    padding-top: 60px;
    background: #f0f2f5;

    .m-head {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 60px;
      z-index: 9;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 @padding;
      background: #fff;
      box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
      .u-return {
        display: flex;
        align-items: center;
        color: @primary;
        cursor: pointer;
        font-size: 16px;
        img {
          width: 16px;
          margin-right: 8px;
        }
      }
      .m-btns {
        :deep(.ant-btn) {
          margin-left: 10px;
        }
      }
    }
    .m-sidebar {
      .scroll();
      width: 80px;
      height: 100%;
      background: #fff;
      overflow-y: auto;

      > ul > li {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: 80px;
        cursor: pointer;
        &:hover {
          background: fade(@primary, 10%);
          .u-img-noselect {
            display: none;
            opacity: 0;
          }
          .u-img-selected {
            display: block;
            opacity: 1;
          }
          .u-text {
            color: @primary;
          }
        }
        &.u-drag {
          cursor: move;
          img {
            width: 20px;
            height: 20px;
          }
        }
        .u-img-noselect {
          display: block;
          transition: all 1s;
          opacity: 1;
        }
        .u-img-selected {
          display: none;
          transition: all 1s;
          opacity: 0;
        }
        .u-text {
          margin-top: 5px;
          color: rgba(0, 0, 0, 0.45);
        }
      }
    }
    .m-applets {
      flex-grow: 1;
      width: 30%;
      height: 100%;
      overflow-y: auto;
      padding: 24px 0;
      background: #f0f2f5;

      .m-rect {
        position: relative;
        width: 375px;
        min-height: 667px;
        border: 1px solid #797979;
        margin: 0 auto;
        background-color: #f5f5f5;
        background-position: top center;
        background-repeat: no-repeat;
        background-size: 100% auto;
        .u-hd {
          position: absolute;
          top: 0;
          left: 0;
          z-index: 5;
          width: 100%;
          pointer-events: none;
          img {
            width: 100%;
          }
        }
        .item-register {
          img {
            display: block;
            width: 100%;
          }
        }
        .elem {
          position: relative;
          top: 0;
          left: 0;
          width: 100%;
          &.active {
            z-index: 6;
          }
          .u-delete {
            position: absolute;
            top: 2px;
            right: 2px;
            z-index: 2;
            width: 20px;
            cursor: pointer;
            img {
              display: block;
              width: 100%;
              height: auto;
            }
          }
          .u-border {
            position: absolute;
            top: 0;
            left: 0;
            z-index: 1;
            width: 100%;
            height: 100%;
            border: 2px solid @primary;
            background: transparent;
            box-shadow: 0 0 6px rgba(0, 0, 0, 0.4);
          }
        }
      }
    }
    .m-element {
      .scroll();
      width: 500px;
      height: 100%;
      overflow-y: auto;
      background: #fff;
    }
  }
}
</style>
