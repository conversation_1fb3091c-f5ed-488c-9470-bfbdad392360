<template>
  <!-- 小程序轮播图 -->
  <div v-if="isShow" class="m-applet-swiper" :class="styleType" :style="`height:${imgHeight};--theme:${theme};`">
    <!-- 轮播 -->
    <div
      v-show="info.items.length > 0"
      :id="`swiper-container${index}`"
      class="swiper-container"
      :style="`position:${swiperConfig.position};bottom:${swiperConfig.bottom}px;`"
    >
      <!-- 图片 -->
      <div class="swiper-wrapper">
        <div
          v-for="(item, index2) in info.items"
          :key="index2"
          class="swiper-slide"
          :class="{ single: info.items.length % 2 === 1 }"
        >
          <img :src="item.url" />
        </div>
      </div>
      <!-- 分页器 -->
      <div v-show="info.items.length" class="swiper-pagination"></div>
    </div>
    <img v-show="swiperConfig.bg" :src="info.bg_url" class="u-bg" />
    <img v-if="!(styleConnect || infoBgUrl)" :src="swiperConfig.defaultImgUrl" alt="默认图" />
  </div>
</template>
<script>
export default {
  name: 'AppletNavbar'
}
</script>
<script setup>
import { computed, ref, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import Swiper from 'swiper'
import 'swiper/css'
import assets from '../assets.config'
import { useShop } from '@/store/shop'

const theme = ref(useShop().state.theme_colors)

// 类型配置
const styleConfigs = {
  style01: {
    styleType: {},
    position: 'static',
    defaultImgUrl: assets.swiperBgDefault01
  },
  style02: {
    styleType: {
      slidesPerView: 'auto',
      position: 'static',
      centeredSlides: true
    },
    defaultImgUrl: assets.swiperBgDefault02
  },
  style03: {
    styleType: {},
    position: 'absolute',
    bottom: 35,
    bg: true,
    defaultImgUrl: assets.swiperBgDefault03
  },
  style04: {
    styleType: { slidesPerView: 2, slidesPerGroup: 2 },
    position: 'absolute',
    bottom: 35,
    bg: true,
    defaultImgUrl: assets.swiperBgDefault04
  }
}
const props = defineProps({
  // 数据信息
  info: {
    type: Object,
    default: () => ({})
  },
  // 索引
  index: {
    type: Number,
    default: 0
  }
})

const isShow = ref(false) // 显示状态
const imgHeight = ref('auto') // 图片高度

// 样式类型
const styleType = computed(() => props.info.style_type)
const styleConnect = computed(() => props.info.items.length) // 和默认样式有关联的数据
const infoBgUrl = computed(() => props.info.bg_url) // 背景

// swiper config
const swiperConfig = computed(() => styleConfigs[styleType.value])

// 设置高度
const setHeight = () => {
  if (swiperConfig.value.bg && !props.info.bg_url && props.info.items.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        const { height } = document.querySelector(`#swiper-container${props.index}`).getBoundingClientRect()
        if (height === 0) {
          setHeight()
        } else {
          imgHeight.value = `${height + swiperConfig.value.bottom * 2}px`
          // console.log(imgHeight.value)
        }
      }, 0)
    })
  } else {
    imgHeight.value = 'auto'
  }
}
// 重新渲染轮播
const renderSwiper = () => {
  isShow.value = false
  nextTick(() => {
    isShow.value = true
    nextTick(() => {
      new Swiper(`#swiper-container${props.index}`, {
        loop: false,
        observer: true,
        observeSlideChildren: true,
        autoHeight: true,
        pagination: {
          el: '.swiper-pagination'
        },
        ...swiperConfig.value.styleType
      })
      setHeight()
    })
  })
}

watch(styleType, renderSwiper, {
  immediate: true
})

watch(styleConnect, setHeight, {
  immediate: true
})

watch(styleConnect, setHeight, {
  immediate: true
})
</script>
<style scoped lang="less">
.m-applet-swiper {
  .same-position() {
    width: 100%;
    left: 0;
    bottom: 0;
    overflow: initial;
  }
  position: relative;
  overflow: hidden;
  img {
    display: block;
    width: 100%;
  }
  .swiper-container {
    .same-position();
    .swiper-pagination {
      :deep(.swiper-pagination-bullet) {
        width: 5px;
        height: 5px;
        margin: 0 4px;
        background: #fff;
        opacity: 1;
        transition: all 0.4s;
        border-radius: 3px;
        &.swiper-pagination-bullet-active {
          width: 15px;
          background: var(--theme) !important;
        }
      }
    }
  }
  &.style02 {
    .swiper-slide {
      width: 300px;
      transform: scale(0.93);
    }
    .swiper-slide-active {
      transform: scale(1);
    }
  }
  &.style03 {
    .swiper-container {
      .swiper-pagination {
        bottom: -25px;
      }
      .swiper-slide {
        padding: 0 15px;
      }
    }
  }
  &.style04 {
    .swiper-container {
      .swiper-wrapper {
        width: 100%;
        .swiper-slide {
          padding: 0 5px;
          &:nth-child(2n + 1) {
            padding-left: 15px;
          }
          &:nth-child(2n + 2) {
            padding-right: 15px;
          }
          &.single {
            &:last-child {
              padding-left: 5px;
              padding-right: 15px;
            }
            &:nth-last-child(2) {
              padding-left: 15px;
              padding-right: 5px;
            }
          }
          img {
            display: block;
            width: 100%;
          }
        }
      }
      .swiper-pagination {
        bottom: -25px;
      }
    }
  }
}
</style>
