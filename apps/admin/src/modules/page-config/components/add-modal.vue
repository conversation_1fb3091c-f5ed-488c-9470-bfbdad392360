<template>
  <!-- 新增弹窗 -->
  <a-modal :title="`${titleInfo[type]}设置`" v-bind="{ visible }" class="m-modal-box" @ok="onSubmit" @cancel="onCancel">
    <a-form
      ref="addForm"
      layout="horizontal"
      :rules="addRules"
      :model="formState"
      :label-col="{ span: 4 }"
      :wrapper-col="{ span: 20 }"
    >
      <!-- 导航 -->
      <template v-if="type === 'navbar'">
        <a-form-item v-if="styleType !== 'style01'" label="导航图片:" name="url" class="required photo-flex">
          <uc-upload
            :list="formState.url ? [formState.url] : []"
            :max-length="1"
            upload-text=" "
            @update:list="data => (formState.url = data[0])"
          />
        </a-form-item>
        <a-form-item label="导航名称：" name="title" class="required">
          <a-input v-model:value.trim="formState.title" maxlength="10" placeholder="请输入导航名称" />
        </a-form-item>
      </template>
      <!-- 轮播 -->
      <template v-if="type === 'swiper'">
        <a-form-item label="轮播类型：" name="type" class="required">
          <a-radio-group
            v-model:value="formState.media_type"
            :options="MediaType.option()"
            @change="() => (formState.url = undefined)"
          />
        </a-form-item>
        <a-form-item label="轮播素材：" name="url" class="required">
          <div>
            <uc-upload
              :list="formState.url ? [formState.url] : []"
              :max-length="1"
              upload-text=" "
              :type="formState.media_type"
              :controls="false"
              accept="video/mp4"
              @update:list="data => (formState.url = data[0])"
            />
            <a-input
              v-if="formState.media_type == MediaType.video"
              v-model:value="formState.url"
              placeholder="轮播视频素材地址"
            />
          </div>
        </a-form-item>
        <a-form-item label="轮播名称：" name="title" class="required">
          <a-input v-model:value.trim="formState.title" placeholder="请输入轮播名称" />
        </a-form-item>
        <a-form-item label="选中颜色" name="selectd_color">
          <a-input v-model:value.trim="formState.selectd_color" placeholder="请输入选中颜色" />
        </a-form-item>
        <a-form-item label="未选颜色" name="unselectd_color">
          <a-input v-model:value.trim="formState.unselectd_color" placeholder="请输入未选颜色" />
        </a-form-item>
      </template>
      <!-- 滑块 -->
      <template v-if="type === 'slide'">
        <a-form-item label="滑块图片：" name="url" class="required photo-flex">
          <uc-upload
            :list="formState.url ? [formState.url] : []"
            :max-length="1"
            upload-text=" "
            @update:list="data => (formState.url = data[0])"
          />
        </a-form-item>
        <a-form-item label="滑块名称：" name="title" class="required">
          <a-input v-model:value.trim="formState.title" placeholder="请输入滑块名称" />
        </a-form-item>
      </template>
    </a-form>
  </a-modal>
</template>
<script setup>
import { debounce } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { watch, ref } from 'vue'
import { activityCouponApi } from '@/modules/promotion/api'
import { MediaType } from '@/modules/page-config/enums'

const initFormState = { jump_type: 'page', media_type: MediaType.image }
const { formState, resetFormState, setFormState } = useFormState(initFormState) // 表单
const props = defineProps({
  value: {
    type: Object
  },
  // 显示状态
  visible: {
    type: Boolean,
    default: false
  },
  // 类型
  type: {
    type: String,
    default: ''
  },
  // 样式类型
  styleType: {
    type: String,
    default: ''
  }
})

watch(
  () => props.value,
  value => {
    if (value && !value.media_type) {
      value.media_type = MediaType.image
    }
    setFormState(value || initFormState)
  },
  {
    immediate: true,
    deep: true
  }
)

const emit = defineEmits(['submit', 'update:visible'])

// 标题信息
const titleInfo = {
  image: '图片',
  swiper: '轮播',
  navbar: '导航',
  slide: '滑块'
}
const linkRef = ref()
// 验证规则
const addRules = ref({
  url: { required: true, message: '请上传图片', trigger: 'blur' },
  title: { required: true, message: '请输入名称', trigger: 'blur' }
})
// 活动发券列表
const activityList = ref([])
/* watch(
  () => formState.value.jump_type,
  newValue => {
    newValue == 'share' ? (formState.value.jump_link = 'pages/tabbar/index') : (formState.value.jump_link = '')
    if(newValue == 'prize') {
      useActivityCoupon({
        // status: 'normal'
      })
    }
  },
  {
    deep: true,
    immediate: true
  }
) */

const addForm = ref(null)
// 点击提交按钮
const onSubmit = () => {
  // 触发表单验证
  addForm.value.validate().then(
    () => {
      // 提交参数
      emit('submit', formState.value)
      resetFormState()
    },
    err => {
      // console.log(err)
    }
  )
}
// 点击取消按钮
const onCancel = () => {
  emit('update:visible', false)
  resetFormState()
  linkRef.value && linkRef.value.onFieldBlur()
}

// 搜索活动发券
const handleSearchActivityCoupon = debounce(async title => {
  useActivityCoupon({
    title: `%${title}%`
    // status: 'normal'
  })
}, 500)

// 处理活动发券
async function useActivityCoupon(filters) {
  const res = await activityCouponApi.paginator({
    filters,
    offset: 1,
    limit: 20
  })

  activityList.value = res.items.map(item => {
    return {
      value: item.id,
      label: item.title
    }
  })
}
</script>
<style scoped lang="less">
.m-modal-box {
  .photo-flex {
    :deep(.ant-form-item-control-input-content) {
      display: flex;
      .m-upload {
        &:nth-child(2) {
          flex: 1;
        }
      }
    }
  }
}
</style>
