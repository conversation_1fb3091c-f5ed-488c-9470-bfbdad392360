<template>
  <!-- 图片 -->
  <div ref="bgImgRef" class="u-bg-img">
    <img v-if="infoData.bg_url" :src="infoData.bg_url" alt="bg" />
    <img v-else :src="assets.imageBgDefault" alt="默认图" />
    <drag-hot v-model:list="infoData.items" @clear="onClearAlone" />
  </div>
</template>
<script setup>
import DragHot from './drag-hot'
import { ref, watch } from 'vue'
import assets from '../assets.config'

const bgImgRef = ref(null)

defineExpose({
  bgImgRef
})

const props = defineProps({
  // 数据信息
  info: {
    type: Object,
    default: () => ({})
  }
})
const emit = defineEmits(['clear'])
const infoData = ref({}) // 图片数据
// 清理选中区域
const onClearAlone = () => {
  emit('clear')
}
watch(
  () => props.info,
  value => {
    infoData.value = value
  },
  { deep: true, immediate: true }
)
</script>

<style scoped lang="less">
.u-bg-img {
  @primary: #1890ff;
  position: relative;
  width: 100%;
  img {
    display: block;
    width: 100%;
    height: auto;
  }
  .u-delete {
    position: absolute;
    top: 2px;
    right: 2px;
    z-index: 2;
    width: 20px;
    cursor: pointer;
  }
  .u-border {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    border: 2px dashed @primary;
    background: transparent;
  }
}
</style>
