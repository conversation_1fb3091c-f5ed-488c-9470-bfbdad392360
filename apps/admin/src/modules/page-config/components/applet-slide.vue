<template>
  <!-- 小程序滑块组件 -->
  <div class="m-applet-slide" :class="styleType" :style="`height:${imgHeight};`">
    <!-- 轮播 -->
    <div
      v-show="info.items.length > 0"
      :id="`slide-container${index}`"
      class="slide-container"
      :style="`position:${slideConfig.position};bottom:${slideConfig.bottom}px;transform:translateX(${translateX}px);--width:${slideConfig.width};`"
      @mousedown="onMousedown"
    >
      <div v-for="(item, i) in info.items" :key="i" class="slide-item">
        <img :src="item.url" alt="" />
      </div>
    </div>
    <img v-show="slideConfig.bg" :src="info.bg_url" class="u-bg" />
    <img v-if="!(styleConnect || infoBgUrl)" :src="slideConfig.defaultImgUrl" alt="默认图" />
  </div>
</template>
<script>
export default {
  name: 'AppletSlide'
}
</script>
<script setup>
import { computed, ref, watch, nextTick, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import assets from '../assets.config'

// 类型配置
const styleConfigs = {
  style01: {
    width: '240px',
    defaultImgUrl: assets.slideBgDefault01
  },
  style02: {
    width: '150px',
    defaultImgUrl: assets.slideBgDefault02
  },
  style03: {
    position: 'absolute',
    width: '240px',
    bg: true,
    bottom: 25,
    defaultImgUrl: assets.slideBgDefault03
  },
  style04: {
    position: 'absolute',
    width: '150px',
    bg: true,
    bottom: 25,
    defaultImgUrl: assets.slideBgDefault04
  }
}
const props = defineProps({
  // 数据信息
  info: {
    type: Object,
    default: () => ({})
  },
  // 索引
  index: {
    type: Number,
    default: 0
  }
})

const imgHeight = ref('auto') // 图片高度
const styleType = computed(() => props.info.style_type) // 样式类型
const styleConnect = computed(() => props.info.items.length) // 和默认样式有关联的数据
const infoBgUrl = computed(() => props.info.bg_url) // 背景

// slide config
const slideConfig = computed(() => ({
  position: 'static',
  ...styleConfigs[styleType.value]
}))

let containerRect = {} // 容器矩形
const getContainerRect = fn => {
  nextTick(() => {
    containerRect = document.querySelector(`#slide-container${props.index}`)?.getBoundingClientRect()
    typeof fn === 'function' && fn()
  })
}
onMounted(getContainerRect)

// 设置高度
const setHeight = () => {
  if (slideConfig.value.bg && !props.info.bg_url && props.info.items.length > 0) {
    nextTick(() => {
      setTimeout(() => {
        const { height } = containerRect
        if (height === 0) {
          setHeight()
        } else {
          imgHeight.value = `${slideConfig.value.bottom + height}px`
        }
      }, 0)
    })
  } else {
    imgHeight.value = 'auto'
  }
}

watch(
  styleConnect,
  () => {
    setHeight()
    nextTick(getContainerRect)
  },
  {
    immediate: true
  }
)

watch(infoBgUrl, setHeight, {
  immediate: true
})

watch(styleType, () => {
  getContainerRect(setHeight)
  translateX.value = 0
})

// 滑块滑动
const translateX = ref(0)
const onMousedown = ({ clientX }) => {
  const { width } = containerRect
  const canMoveX = width - 375 // 最大移动距离
  if (canMoveX > 0) {
    const initX = translateX.value
    document.onmousemove = e => {
      const diffX = e.clientX - clientX
      let distX = diffX + initX
      distX > 0 && (distX = 0)
      distX < -canMoveX && (distX = -canMoveX)
      translateX.value = distX
    }
    document.onmouseup = () => {
      document.onmousemove = null
      document.onmouseup = null
    }
  }
}
</script>
<style scoped lang="less">
.m-applet-slide {
  position: relative;
  overflow: hidden;
  font-size: 0;
  .slide-container {
    display: inline-block;
    padding: 0 15px;
    font-size: 0;
    white-space: nowrap;
    cursor: grab;
    .slide-item {
      display: inline-block;
      vertical-align: top;
      width: var(--width);
      height: auto;
      margin-right: 10px;
      &:last-child {
        margin-right: 0 !important;
      }
      img {
        width: 100%;
      }
    }
  }
  img {
    display: block;
    width: 100%;
  }
}
</style>
