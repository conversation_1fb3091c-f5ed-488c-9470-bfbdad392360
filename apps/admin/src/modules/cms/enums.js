/**
 * 搜索条件
 */
export const searchCondition = Object.freeze({
  /**
   * 标题
   */
  title: 'title',
  /**
   * 标识
   */
  key: 'key',

  options() {
    return [
      { label: '标题', value: this.title },
      { label: '标识', value: this.key }
    ]
  }
})

/**
 * 显示状态
 */
export const showStateList = Object.freeze({
  /**
   * 显示
   */
  show: 0,

  /**
   * 隐藏
   */
  hide: 1,

  options() {
    return [
      { label: '显示', value: this.show },
      { label: '隐藏', value: this.hide },
    ]
  }
})