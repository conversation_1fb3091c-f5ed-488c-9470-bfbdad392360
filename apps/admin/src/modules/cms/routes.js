export default {
  path: 'cms',
  meta: {
    title: '内容',
    antIcon: 'ReadOutlined'
  },
  children: [
    {
      path: 'article-categories',
      name: 'article-categories',
      meta: {
        title: '文章分类'
      },
      component: () => import('./pages/article-categories/list')
    },
    {
      path: 'articles',
      name: 'articles',
      meta: {
        title: '全部文章',
        keepAlive: true
      },
      component: () => import('./pages/articles/list')
    },
    {
      path: 'articles-add',
      name: 'articles-add',
      meta: {
        title: '新增文章'
      },
      component: () => import('./pages/articles/edit'),
      hidden: true
    },
    {
      path: 'articles-edit/:id',
      name: 'articles-edit',
      meta: {
        title: '编辑文章'
      },
      component: () => import('./pages/articles/edit'),
      hidden: true
    },
  ],
}
