<template>
  <uc-layout-list title="全部文章" class="flex">
    <template #filter>
      <a-form-item name="title">
        <a-input-group compact>
          <a-select v-model:value="conditionKey" placeholder="请选择" :options="conditionOptions" />
          <a-input v-model:value.trim="conditionValue" placeholder="请输入关键字" />
        </a-input-group>
      </a-form-item>
      <a-form-item>
        <a-cascader
          v-model:value="formState.category_id"
          placeholder="文章分类"
          :options="categories"
          :field-names="{ label: 'title', value: 'id' }"
          change-on-select
        />
      </a-form-item>
      <a-form-item>
        <a-button type="primary" @click="setPage"> 查询 </a-button>
        <a-button @click="resetFormState"> 重置 </a-button>
      </a-form-item>
    </template>
    <template #extra>
      <a-button type="primary">
        <router-link :to="{name: 'articles-add'}">
          新增文章
        </router-link>
      </a-button>
    </template>
    <template #list>
      <a-table
        :data-source="data.items"
        row-key="id"
        :loading="loading"
        :pagination="stdPagination(data)"
        @change="setPage"
      >
        <a-table-column title="文章名称/分类" ellipsis>
          <template #default="{ record }">
            <uc-img-text
              v-bind="record"
              :title="record.title"
              :label="record.id"
              symbol=":&nbsp"
              :subtit="getCategory(categories, record.category_id)"
            />
          </template>
        </a-table-column>

        <a-table-column title="文章排序" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="sort w-80"
              :min="0"
              :max="9999"
              @blur="handleUpdate(record.id, { sort: record.sort })"
            />
          </template>
        </a-table-column>

        <a-table-column title="是否显示">
          <template #default="{ record }">
            <a-switch v-model:checked="record.is_hidden" :un-checked-value="1" :checked-value="0" @change="handleUpdate(record.id, { is_hidden: $event }, { title: record.title })" />
          </template>
        </a-table-column>

        <a-table-column width="150px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="copyLinkByRoute('articleDetail', { id: record.id})"> 链接 </a-button>
            <a-button type="link">
              <router-link :to="{name: 'articles-edit', params: {id: record.id }}">
                编辑
              </router-link>
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              :disabled="!record.deletable"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger" :disabled="!record.deletable">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>

<script setup>
import { cloneDeep } from 'lodash'
import { useFormState } from '@/composables/useFormState'
import { useTransformQuery } from '@/composables/useTransformQuery'
import { usePaginatorApiRequest } from '@/composables/useApiRequest'
import { getCategory, useCategories } from '../../useCategory'
import { searchCondition } from '../../enums'
import { articlesApi } from '../../api'
import { message } from 'ant-design-vue'

const { categories } = useCategories()

const conditionOptions = searchCondition.options()
const conditionKey = ref(searchCondition.title)
const conditionValue = ref()
watch(conditionKey, () => (conditionValue.value = undefined))

const queryFormBasic = Object.freeze({
  [searchCondition.title]: undefined,
  [searchCondition.key]: undefined
})

const { formState, resetFormState, onRestFormState } = useFormState({
  ...cloneDeep(queryFormBasic),
  category_id: []
})

const getCategoryId = () =>
  formState.value.category_id.length
    ? { category_id: formState.value.category_id[formState.value.category_id.length - 1] }
    : {}

const { data, setPage, loading } = usePaginatorApiRequest(({ offset, limit }) => {

  return articlesApi.paginator({
    filters: useTransformQuery(
      {
        title: formState.value.title,
        key: formState.value.key,
        ...getCategoryId(),
      },
      { title: 'like' }
    ),
    offset,
    limit
  })
})

onRestFormState(() => setPage())

const handleUpdate = async (id, props, { title } = {}) => {
  await articlesApi.update(id, props)
  if (title) {
    message.success(`修改文章(${title})为${props.is_hidden ? '隐藏' : '显示'}`)
  } else {
    message.success('修改成功')
  }
}

const handleDelete = ({ id }) => {
  articlesApi.delete(id).then(res => {
    setPage()
    message.success('删除成功')
  })
}

</script>

<style lang="less" scoped>

</style>
