<template>
  <uc-layout-form @submit="handleSubmit">
    <uc-row>
      <uc-col>
        <a-card title="基本信息">
          <a-form-item label="文章分类" name="category_id" class="required">
            <a-cascader
              :value="dispose(formState.category_id)"
              placeholder="请选择文章分类"
              class="w-500"
              :options="categories"
              :field-names="{ label: 'title', value: 'id' }"
              change-on-select
              @change="onCategoryChange"
            />
          </a-form-item>
          <a-form-item label="文章标题" name="title" class="required">
            <a-input v-model:value="formState.title" placeholder="请输入文章标题" class="w-500" />
          </a-form-item>

          <a-form-item label="分类排序" name="sort" class="required">
            <a-input-number
              v-model:value="formState.sort"
              :min="0"
              :max="9999"
              placeholder="请输入分类排序值（排序值越大排名越靠前）"
            />
          </a-form-item>

          <a-form-item label="显示状态" name="is_hidden" class="required">
            <a-radio-group
              :value="formState.is_hidden"
              :options="showStateList.options()"
              @change="formState.is_hidden = !formState.is_hidden - 0"
            />
          </a-form-item>
        </a-card>
      </uc-col>
      <uc-col>
        <uc-rich-text v-model="formState.content" :height="600" placeholder="请输入文章内容" />
      </uc-col>
    </uc-row>
  </uc-layout-form>
</template>

<script setup>
import { useRoute, useRouter } from 'vue-router'
import { showStateList } from '../../enums'
import { useFormState } from '@/composables/useFormState'
import { message } from 'ant-design-vue'
import { articlesApi } from '../../api'
import { useCategories } from '../../useCategory'
import { getParentLabelList } from '@/utils/functions'

const { id } = useRoute().params
const router = useRouter()
const { categories } = useCategories()

// 处理分类id--用于展示
const dispose = data => {
  if (!data) {
    return
  }
  if (categories.value) {
    return getParentLabelList(categories.value, data, 'id')
  }
}

const onCategoryChange = value => {
  formState.value.category_id = value[value.length - 1]
}

const { formState, setFormState, setFormRules, validateForm } = useFormState({
  category_id: undefined,
  title: undefined,
  is_hidden: 0,
  sort: undefined,
  content: undefined
})

setFormRules({
  category_id: { required: true, message: '请选择文章分类' },
  title: { required: true, message: '请选择文章标题' },
  sort: { required: true, message: '请填写排序' },
  is_hidden: { required: true, message: '请选择显示状态' },
  content: { required: true, message: '请填写文章内容' }
})

if (id) {
  const hideLoading = message.loading('正在加载数据...')
  articlesApi
    .get(id)
    .then(res => {
      setFormState(res)
    })
    .finally(hideLoading)
}

const handleSubmit = async () => {
  if (!(await validateForm())) return
  const params = { ...formState.value }
  id ? await articlesApi.update(id, params) : await articlesApi.create(params)
  message.success(`保存成功：${formState.value.title}`)
  router.back()
}
</script>
