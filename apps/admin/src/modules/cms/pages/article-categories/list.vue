<template>
  <a-modal :visible="modalVisible" :title="formState.id ? '编辑文章分类' : '新增文章分类'" @ok="handleCreate" @cancel="setModalVisible(false)">
    <a-form :label-col="{ style: { width: '80px' } }">
      <a-form-item label="上级分类" name="parent_id">
        <a-tree-select
          v-model:value="formState.parent_id"
          placeholder="请选择上级分类（若为一级分类可不选）"
          :tree-data="categories"
          :replace-fields="{key:'id', value: 'id'}"
          :disabled="formState.parent_id == 0"
          allow-clear
        />
      </a-form-item>

      <a-form-item label="分类名称" name="title" class="required">
        <a-input v-model:value.trim="formState.title" placeholder="请输入分类名称，不超过20字" :maxlength="20" />
      </a-form-item>
      <a-form-item label="分类排序" name="sort" class="required">
        <a-input-number v-model:value="formState.sort" :min="0" :max="9999" placeholder="请输入分类排序值（排序值越大排名越靠前）" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button @click="setModalVisible(false)">
        取消
      </a-button>
      <a-button type="primary" :loading="loading" @click="handleCreate">
        确定
      </a-button>
    </template>
  </a-modal>

  <uc-layout-list title="文章分类" class="m-category flex">
    <template #extra>
      <a-button type="primary" @click="onCreate">
        新增分类
      </a-button>
    </template>
    <template #list>
      <a-table row-key="id" :pagination="false" :data-source="categories" :loading="categoryLoading">
        <a-table-column title="分类名称" ellipsis class="m-title">
          <template #default="{ record }">
            <span class="m-title-box">{{ record.title }}</span>
          </template>
        </a-table-column>
        <a-table-column title="文章" ellipsis>
          <template #default="{ record }">
            {{ record.articles_count }}
          </template>
        </a-table-column>
        <a-table-column title="分类排序" width="120px">
          <template #default="{ record }">
            <a-input-number
              v-model:value="record.sort"
              class="sort w-80"
              :min="0"
              :max="9999"
              @blur="handleUpdate(record.id, { sort: record.sort })"
            />
          </template>
        </a-table-column>
        <a-table-column title="是否显示">
          <template #default="{ record }">
            <a-switch v-model:checked="record.is_hidden" :un-checked-value="1" :checked-value="0" @change="handleUpdate(record.id, { is_hidden: $event }, { title: record.title })" />
          </template>
        </a-table-column>
        <a-table-column width="150px" title="操作">
          <template #default="{ record }">
            <a-button type="link" @click="copyLinkByRoute('articleList', { id: record.id})"> 链接 </a-button>
            <a-button type="link" @click="onEdit(record)">
              编辑
            </a-button>
            <a-popconfirm
              placement="left"
              title="你确定要删除该数据么？"
              ok-text="确定"
              cancel-text="取消"
              @confirm="handleDelete(record)"
            >
              <a-button type="link" class="danger">
                删除
              </a-button>
            </a-popconfirm>
          </template>
        </a-table-column>
      </a-table>
    </template>
  </uc-layout-list>
</template>
<script setup>
import { useFormState } from '@/composables/useFormState'
import { useModalVisible, useLoading } from '@/composables/useToggles'
import { useCategories } from '../../useCategory'
import { message } from 'ant-design-vue'
import { articleCategoriesApi } from '../../api'

const { categories, refreshCategories, categoryLoading } = useCategories()

const { formState, setFormState, resetFormState, setFormRules, validateForm } = useFormState({
  title: undefined,
  sort: undefined
})

const { modalVisible, setModalVisible } = useModalVisible()

watch(modalVisible, v => !v && resetFormState())

const onCreate = () => {
  setModalVisible(true)
}
const onEdit = props => {
  setFormState(props) || setModalVisible(true)
}

setFormRules({
  title: {
    required: true,
    message: '请填写文章分类名称'
  },
  sort: {
    required: true,
    message: '请填写文章分类排序'
  }
})

//  处理分类创建/更新
const { loading, setLoading } = useLoading()
const handleCreate = async () => {
  setLoading(true)

  if (!(await validateForm())) {
    setLoading(false)
    return
  }

  try {
    const { id } = formState.value
    if (id) {
      if(id == formState.value.parent_id) {
        message.warning('不能选择自己为上级分类')
        return
      }
      // 判断parent_id--为undefined传0
      const parent_id = formState.value.parent_id
      await articleCategoriesApi.update(id, { ...formState.value, parent_id: parent_id ? parent_id : 0 })
      message.success('文章分类更新完成')
    } else {
      await articleCategoriesApi.create(formState.value)
      message.success('文章分类创建完成')
    }
    setModalVisible(false)
    refreshCategories()
  } finally {
    setLoading(false)
  }
}

//  处理分类更新
const handleUpdate = async (id, props, { title } = {}) => {
  await articleCategoriesApi.update(id, props)
  if (title) {
    message.success(`修改文章分类(${title})为${props.is_hidden ? '隐藏' : '显示'}`)
  } else {
    message.success('修改成功')
  }
}

const handleDelete = ({ id }) => {
  articleCategoriesApi.delete(id).then(res => {
    message.success('删除成功')
    refreshCategories()
  })
}
</script>
<style scoped lang="less">
.m-category {
  .sort {
    :deep(.ant-input-number-input) {
      text-align: center !important;
    }
  }
  :deep(.ant-table-row-cell-ellipsis.m-title) {
    display: flex;
    align-items: center;
  }
  .m-title-box {
    height: 50px;
    display: flex;
    align-items: center;
  }
}
</style>
