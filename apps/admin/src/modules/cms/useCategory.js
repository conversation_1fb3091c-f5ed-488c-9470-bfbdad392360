import { useApiRequest } from '@/composables/useApiRequest'
import { useLoading } from '@/composables/useToggles'
import { articleCategoriesApi } from './api'
import { handleTree, getParentLabelList } from '@/utils/functions'

const setChildren = (categories) => {
  if (categories) {
    return handleTree(categories, 'id', 'parent_id')
  }
}

export function useCategories(filters = {}) {
  const { loading, setLoading } = useLoading()
  const { data: categories, getData: refreshCategories } = useApiRequest(() => {
    setLoading(true)
    return articleCategoriesApi.list({
      ...filters,
      relations: ['articles']
    })
      .then((cates) => setChildren(cates))
      .finally(() => setLoading(false))
  })
  return {
    categoryLoading: loading,
    categories,
    refreshCategories
  }
}

export const getCategory = (arr, categoryId) => {
  const list = getParentLabelList(arr, categoryId)
  return list ? list.join('/') : ''
}