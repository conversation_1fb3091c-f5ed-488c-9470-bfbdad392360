// generated by unplugin-vue-components
// We suggest you to commit this file into source control
// Read more: https://github.com/vuejs/vue-next/pull/3399

declare module 'vue' {
  export interface GlobalComponents {
    UcAntIcon: typeof import('./src/components/uc-ant-icon.vue')['default']
    UcImgText: typeof import('./src/components/uc-img-text.vue')['default']
    UcLayoutForm: typeof import('./src/components/uc-layout-form.vue')['default']
    UcLayoutList: typeof import('./src/components/uc-layout-list.vue')['default']
    UcLevelList: typeof import('./src/components/uc-level-list.vue')['default']
    UcLoading: typeof import('./src/components/uc-loading.vue')['default']
    UcPrizes: typeof import('./src/components/uc-prizes.vue')['default']
    UcRichText: typeof import('./src/components/uc-rich-text.vue')['default']
    UcSvgIcon: typeof import('./src/components/uc-svg-icon.vue')['default']
    UcUpload: typeof import('./src/components/uc-upload.vue')['default']
  }
}

export { }
