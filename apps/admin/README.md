# user-center

> 会员中心 👍

## 安装

```Basic
yarn
```

## 本地开发运行

```Basic
yarn dev
```

## 代码规范检查及自动格式化

```Basic
// 规范检查
yarn lint

// 自动格式化
yarn prettier
```

⚠⚠⚠⚠ 警告：提交代码前先自动格式化，然后需要针对 lint 的问题进行修复！！！！

## 打包

- 测试环境

```Basic
yarn build:test
```

- 生产环境

```Basic
yarn build:prod
```

## 规范

### 命名规范

- 组件命名：kebab-case (短横线分隔命名)
  - 公共组件以 `uc-` 开头
- 页面命名：kebab-case (短横线分隔命名)
- js 文件命名：小驼峰（useApiRequest）
- 变量和方法名命名：小驼峰（getData）

### 公共组件封装和使用

- 封装
  - 在路径 src/components 下，创建组件文件即可，⚠ 注意 kebab-case 命名和 `uc-` 开头
  - `.vue`文件里的 name 属性值即为组件使用名称
  - 使用时组件(包括 antd 组件)会自动按需导入，无需手动注册或 import

```JavaScript
export default {
  name: "uc-camel-case"
}
```

- 组件使用：uc-kebab-case (短横线分隔命名)

### 模块化

- 后台应用使用模块化方式搭建，除了公用基础设施，项目功能实现应该放在各自的模块内

  一个典型的功能模块如下：

```Markdown
  # demo模块

  src/modules/demo
    |- pages // 该模块下的页面
    |--   demo1.vue
    |--   demo2.vue
    |- assets // 该模块使用的静态资源
    |--   svg-icons // 该模块使用的svg图标， 使用 <uc-svg-icon name="${svg_filename}" />
    |- api.js // 该模块下的 http api 调用处理
    |- routes.js // 该模块下的路由，在 src/router/module-routes.js 中自动导入
    |- enums.js // 该模块下的各类状态枚举定义


```

### 接口定义

- 定义文件：`src/modules/demo/api.js`

```JavaScript
import {factory} from '@/api'

export const apis = {
  demo: factory.restful("demo")
};
```

- 使用

```JavaScript

// demo/pages/demo1.vue

import { demo } from "../api";

// 根据id获取单个数据
demo.get(id, {fields: [], relations: []})

// 请求所有数据
demo.all({filters: {}, fields: [], relations: [], relation_filters: {}})

// 请求分页数据
demo.pagiator({limit:10, offset: 1, filters:{}, fields:[], relations:[], relation_filters:{}})

// 创建
demo.post(data)

// 更新
demo.update(id, data)

// 删除
demo.delete(id)

```

## Vue 文件初始化

### 初始化列表页面

- 使用命令：`init-list`

<details>
<summary>👀 输出内容</summary>

```Vue
<template>
  <!--  -->
  <uc-layout-list title="">
    <template #filter>

    </template>
    <template #list>

    </template>
  </uc-layout-list>
</template>

<script setup>
import { useFormState } from "@/composables/useFormState";
import { useTransformQuery } from "@/composables/useTransformQuery";
import { usePaginatorApiRequest } from "@/composables/useApiRequest"
import { demoApi } from "../api"

const { formState, resetFormState } = useFormState(); // 查询表单

// 表格请求
const { data, setPage, loading, refresh } = usePaginatorApiRequest(({ offset, limit }) =>
  demoApi.paginator({
    filters: useTransformQuery(formState, { title: "like" }),
    offset,
    limit
  })
)

</script>

<style scoped lang="less">
</style>

```

</details>

### 初始化详情页面

- 使用命令：`init-details`

<details>
<summary>👀 输出内容</summary>

```Vue
<template>
  <!--  -->
  <uc-layout-form @submit="onSubmit" v-if="!props.loading">

  </uc-layout-form>
</template>
<script setup>


import { message } from "ant-design-vue";
import { useFormState } from "@/composables/useFormState";
import { demoAPi } from "@/api";

const { formState, setFormState } = useFormState(); // 表单
const { id } = useRoute().params; // 参数

// 点击提交按钮
const onSubmit = () => {}; // 点击提交按钮

</script>
<style scoped lang="less">
</style>
```

</details>

### 初始化组件

- 使用命令：`init-component`

<details>
<summary>👀 输出内容</summary>

```Vue
<template>
  <!--  -->
  <div class="">

  </div>
</template>
<script>
export default {
  name: "uc-"
};
</script>
<script setup>
import { ref } from "vue";

</script>
<style scoped lang="less">

</style>
```

</details>

## 目录

<details>
<summary>👀 查看详情</summary>

```Markdown
|-- user-center-admin
    |-- .env.development ---------------- 开发环境
    |-- .env.production ----------------- 生产环境
    |-- .env.test ----------------------- 测试环境
    |-- .env.local ----------------------- 本地环境
    |-- .gitignore ---------------------- git 忽略文件
    |-- index.html ---------------------- SPA 的页面
    |-- jsconfig.json ------------------- JS 配置文件
    |-- package-lock.json --------------- 依赖包来源地址信息
    |-- package.json -------------------- 包管理文件
    |-- README.md ----------------------- 项目文档
    |-- vite.config.js ------------------ vite 配置文件
    |-- dist ---------------------------- 打包出口目录
    |-- public -------------------------- 公共资源目录
    |-- types --------------------------- 类型扩充，便于开发时代码提示于自动完成
    |-- src ----------------------------- 项目主目录
        |-- App.vue --------------------- vue 入口
        |-- main.js --------------------- JS 入口
        |-- api ------------------------- API基础设施（各模块通用）
        |-- assets ---------------------- 图片资源目录（各模块通用）
        |-- components ------------------ 公共组件（各模块通用）
        |-- composables ----------------- 公共组合式 API 组件（各模块通用）
        |-- config ---------------------- 公共配置项（各模块通用）
        |-- mixin ----------------------- mixin 混合（各模块通用）
        |-- router ---------------------- 路由
        |-- store ----------------------- 数据共享
        |-- styles ---------------------- 公共样式
        |-- utils ----------------------- 工具包
        |-- views ----------------------- 全局页面(layout,403,404,login等)
        |-- modules ----------------------- 模块目录
            |-- community --------------- 社区
            |-- gift -------------------- 礼品
            |-- activity ---------------- 互动中心
            |-- order ------------------- 订单
            |-- trial ------------------- 试用
```

</details>

## 变量理命名规范

### 场景一：属性状态类

解释：通常用来描述实体（例如：HTML 标签、组件、对象）的功能属性，而且定法比较固定。

```
{
  disabled: '是否禁用',
  editable: '是否可编辑',
  clearable: '是否可清除',
  readonly: '只读',
  expandable: '是否可展开',
  checked: '是否选中',
  enumberable: '是否可枚举',
  iterable: '是否可迭代',
  clickable: '是否可点击',
  draggable: '是否可拖拽'
}
```

### 场景二：配置类、选项类

解释：主要是指组件功能的开启与关闭，功能属性的配置。

这是一种比较常见的情景，目前命名方式也有很多种，但是归纳起来也不多。推荐使用 withXx 来表示组件在基本功能形态外的其它功能，比如组件的基础功能到高级功能的开启；使用 enableXx 来表示组件某些功能的开启；使用 allowXx 来表示功能属性的配置；使用 noXx 用于建议功能使用者这个不建议开启。

```
{
  withTab: '是否带选项卡',
  withoutTab: '不带选项卡',
  enableFilter: '开启过滤',
  allownCustomScale: '允许自定义缩放',
  shouldClear: '是否清除',
  canSelectItem: '是否能选中元素',
  noColon: '不显示label后面的冒号',
  checkJs: '检查Js',
  emitBOM: 'Emit a UTF-8 Byte Order Mark (BOM) in the beginning of output files.'
}
```

### 场景三：事件处理

事件处理函数是前端平时用到最多的，包括浏览器原生事件、异步事件和组件自定义事件。在写法上最常见的两种命名分别为 onXX、onXXClick 和 handleXx、handleXxChange。

这里如何在二者之间选择，可以从二方面来归类。 对于需要处理后端提交的，统一用 handleXXX，例如 handleSubmit, handleDelete, handleUpdate，反之使用 onXXX

## vite 自动化组件

- unplugin-auto-import https://github.com/antfu/unplugin-auto-import 自动导入 API（目前包含 vue/vue-router 系列 API 自动导入）
- unplugin-vue-components https://github.com/antfu/unplugin-vue-components 自动导入组件（公共自定义组件及 antd 组件自动导入）

## 贡献者

- lvbiao\<<EMAIL>\>
- string\<<EMAIL>\>
