{"name": "user-center-admin", "version": "1.0.0", "engines": {"node": ">=16.0.0"}, "private": "true", "scripts": {"start": "npm run dev", "dev": "npm run lint && vite --mode development", "build:test": "npm run lint && vite build --mode testing", "build:staging": "npm run lint && vite build --mode staging", "build:prod": "npm run lint && vite build --mode production", "serve": "vite preview", "lint": "npm run lint:js", "lint:js": "eslint --cache --ext .js,.vue --format=pretty --fix ./src", "prettier": "prettier --write '**/*.{js,less,md,json,vue}'"}, "dependencies": {"@ant-design/icons-vue": "^6.0.1", "@antv/data-set": "^0.11.8", "@antv/g2": "^4.1.32", "@map-component/vue-tmap": "^0.1.8", "ant-design-vue": "^2.2.6", "axios": "^0.21.1", "clipboard": "^2.0.8", "dayjs": "^1.10.7", "echarts": "^5.5.0", "ignore": "^5.3.1", "js-base64": "^3.7.7", "jsencrypt": "^3.3.2", "moment": "^2.29.1", "nprogress": "^0.2.0", "qs": "^6.14.0", "swiper": "^8.4.7", "vue": "^3.4.30", "vue-awesome-swiper": "^5.0.1", "vue-baidu-map-3x": "^1.0.31", "vue-clipboard3": "^2.0.0", "vue-drag-resize": "^1.5.4", "vue-draggable-next": "^2.2.1", "vue-router": "^4.0.11", "vuedraggable": "^4.1.0", "wangeditor": "^4.7.7"}, "devDependencies": {"@vitejs/plugin-vue": "^1.6.0", "@vue/compiler-sfc": "^3.0.5", "eslint": "^8.2.0", "eslint-formatter-pretty": "^4.1.0", "eslint-plugin-vue": "^8.0.3", "glob": "^7.2.0", "less": "^4.1.1", "mockjs": "^1.1.0", "prettier": "^2.4.1", "unplugin-auto-import": "^0.4.13", "unplugin-vue-components": "^0.17.2", "vite": "^2.5.1", "vite-plugin-html": "^3.2.2", "vite-plugin-mock": "^2.9.6", "vite-plugin-svg-icons": "^1.0.5", "vue-eslint-parser": "^9.0.3"}}